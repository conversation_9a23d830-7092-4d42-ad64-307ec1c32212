from chonkie import <PERSON><PERSON><PERSON><PERSON><PERSON>, RecursiveChunker, NeuralChunker
from chonkie.genie import GeminiGenie, OpenAIGenie
from chonkie.chunker.base import Chunk
import os
import re
import json
import sys
import argparse
import shutil
from typing import List, Dict, Tuple

# Import post-processing function
from post_process_chunks import process_chunks_sequentially

def load_extracted_tables(tables_dir: str) -> Dict[str, str]:
    """
    Load extracted table summaries from the tables summaries directory.
    Now the mapping uses random placeholder strings as keys and table IDs as values.
    
    Args:
        tables_dir (str): Directory containing table summaries and tags
        
    Returns:
        Dict mapping placeholder_string to table_id for file reference
    """
    placeholder_to_id = {}
    
    # Load table index if it exists
    index_file = os.path.join(tables_dir, "table_index.json")
    if os.path.exists(index_file):
        with open(index_file, 'r', encoding='utf-8') as f:
            table_index = json.load(f)
        
        print(f"Table index contains {len(table_index)} entries")
        
        for placeholder_string, table_info in table_index.items():
            table_id = table_info['id']
            placeholder_to_id[placeholder_string] = table_id
            char_count = table_info.get('char_count', len(placeholder_string))
            print(f"Loaded mapping for {table_id} (placeholder length: {char_count} chars)")
    else:
        print("No table_index.json found, cannot proceed with new format")
        return {}
    
    print(f"Final loaded table mappings: {len(placeholder_to_id)}")
    return placeholder_to_id

def find_table_placeholders_in_chunks(chunks: List, placeholder_to_id: Dict[str, str]) -> Dict[str, List[Tuple[int, int, int]]]:
    """
    Find which chunks contain which table placeholder strings wrapped in curly braces.
    Also handles cases where placeholders might be split across chunks.
    
    Returns:
        Dict mapping placeholder_string to list of (chunk_idx, start_pos, end_pos) tuples
    """
    table_references = {placeholder: [] for placeholder in placeholder_to_id.keys()}
    
    print(f"Looking for {len(placeholder_to_id)} table placeholders in chunks")
    
    # First, try to find complete placeholders in chunks
    for chunk_idx, chunk in enumerate(chunks):
        chunk_text = chunk.text
        
        for placeholder_string in placeholder_to_id.keys():
            # Look for the placeholder wrapped in curly braces
            wrapped_placeholder = f"{{{placeholder_string}}}"
            if wrapped_placeholder in chunk_text:
                start_pos = chunk_text.find(wrapped_placeholder)
                end_pos = start_pos + len(wrapped_placeholder)
                table_references[placeholder_string].append((chunk_idx, start_pos, end_pos))
                table_id = placeholder_to_id[placeholder_string]
                print(f"Found complete table {table_id} placeholder in chunk {chunk_idx}")
    
    # Check for placeholders split across chunks
    for i in range(len(chunks) - 1):
        current_chunk = chunks[i].text
        next_chunk = chunks[i + 1].text
        
        # Check if current chunk ends with an opening brace
        if current_chunk.rstrip().endswith('{'):
            # Look for potential split placeholders
            for placeholder_string in placeholder_to_id.keys():
                wrapped_placeholder = f"{{{placeholder_string}}}"
                
                # Try to reconstruct across chunk boundary
                overlap_size = min(len(current_chunk), len(wrapped_placeholder))
                for j in range(1, overlap_size + 1):
                    if current_chunk[-j:] + next_chunk[:len(wrapped_placeholder)-j] == wrapped_placeholder[:len(wrapped_placeholder)]:
                        table_id = placeholder_to_id[placeholder_string]
                        print(f"WARNING: Table {table_id} placeholder is split across chunks {i} and {i+1}")
                        # Mark both chunks as containing this placeholder
                        table_references[placeholder_string].append((i, len(current_chunk)-j, len(current_chunk)))
                        table_references[placeholder_string].append((i+1, 0, len(wrapped_placeholder)-j))
                        break
    
    return table_references

def replace_placeholders_with_formatted_tables(chunks: List, placeholder_to_id: Dict[str, str], tables_dir: str) -> List:
    """
    Replace placeholder strings (wrapped in curly braces) in chunks with table summaries and tags.
    Handles both complete placeholders and placeholders split across chunks.
    """
    updated_chunks = []
    
    # First pass: identify all placeholders and their locations
    placeholder_locations = find_table_placeholders_in_chunks(chunks, placeholder_to_id)
    
    # Track which chunks need special handling for split placeholders
    chunks_with_split_placeholders = set()
    for placeholder_string, locations in placeholder_locations.items():
        if len(locations) > 1:
            for chunk_idx, _, _ in locations:
                chunks_with_split_placeholders.add(chunk_idx)
    
    for chunk_idx, chunk in enumerate(chunks):
        chunk_text = chunk.text
        original_length = len(chunk_text)
        
        # Find all placeholders in this chunk
        placeholders_in_chunk = []
        for placeholder_string, locations in placeholder_locations.items():
            for loc_chunk_idx, start_pos, end_pos in locations:
                if loc_chunk_idx == chunk_idx:
                    wrapped_placeholder = f"{{{placeholder_string}}}"
                    placeholders_in_chunk.append((placeholder_string, wrapped_placeholder, start_pos, end_pos))
        
        if placeholders_in_chunk:
            print(f"Chunk {chunk_idx}: Found {len(placeholders_in_chunk)} placeholder references to process")
            
            # Sort by position to replace from end to beginning (to maintain positions)
            placeholders_in_chunk.sort(key=lambda x: x[2], reverse=True)
            
            for placeholder_string, wrapped_placeholder, start_pos, end_pos in placeholders_in_chunk:
                table_id = placeholder_to_id[placeholder_string]
                
                # Load the table summary from file
                table_file = os.path.join(tables_dir, f"{table_id}.txt")
                if os.path.exists(table_file):
                    with open(table_file, 'r', encoding='utf-8') as f:
                        table_summary = f.read()
                    
                    # Check if this is a complete placeholder or partial
                    if wrapped_placeholder in chunk_text:
                        # Complete placeholder - simple replacement
                        chunk_text = chunk_text.replace(wrapped_placeholder, table_summary)
                        print(f"Replaced complete placeholder for table {table_id} with summary in chunk {chunk_idx}")
                    else:
                        # Partial placeholder - need special handling
                        print(f"Handling partial placeholder for table {table_id} summary in chunk {chunk_idx}")
                        if chunk_idx in chunks_with_split_placeholders:
                            print(f"  WARNING: Table {table_id} placeholder is split across chunks - will merge chunks")
                    
                    print(f"  Placeholder length: {len(wrapped_placeholder)} chars")
                    print(f"  Table summary length: {len(table_summary)} chars")
                else:
                    print(f"WARNING: Table summary file {table_file} not found for {table_id}")
        
        # Clean up extra whitespace
        chunk_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', chunk_text).strip()
        
        # Update chunk properties
        chunk.text = chunk_text
        chunk.end_index = chunk.start_index + len(chunk_text)
        chunk.token_count = len(chunk_text.split())
        
        updated_chunks.append(chunk)
    
    # Second pass: merge chunks with split placeholders if needed
    final_chunks = []
    i = 0
    while i < len(updated_chunks):
        current_chunk = updated_chunks[i]
        
        # Check if this chunk has a split placeholder at the end
        if i in chunks_with_split_placeholders and i + 1 < len(updated_chunks):
            # Check if we should merge with next chunk
            current_text = current_chunk.text
            next_text = updated_chunks[i + 1].text if i + 1 < len(updated_chunks) else ""
            
            # Look for incomplete placeholder patterns
            if '{' in current_text and '}' not in current_text[current_text.rfind('{'):]:
                print(f"Merging chunks {i} and {i+1} due to split placeholder")
                # Merge chunks
                merged_text = current_text + "\n" + next_text
                current_chunk.text = merged_text
                current_chunk.end_index = updated_chunks[i + 1].end_index
                current_chunk.token_count = len(merged_text.split())
                final_chunks.append(current_chunk)
                i += 2  # Skip next chunk since we merged it
                continue
        
        final_chunks.append(current_chunk)
        i += 1
    
    return final_chunks

def detect_unreplaced_placeholders(text: str) -> List[str]:
    """
    Detect potential unreplaced placeholders in text.
    Look for long alphanumeric strings that might be placeholders.
    
    Returns:
        List of potential unreplaced placeholders
    """
    import re
    
    # Pattern to find long alphanumeric strings (likely placeholders)
    # Look for strings of 50+ alphanumeric characters
    pattern = r'[a-zA-Z0-9]{50,}'
    
    potential_placeholders = re.findall(pattern, text)
    
    # Also look for strings wrapped in curly braces
    brace_pattern = r'\{([a-zA-Z0-9]+)\}'
    brace_matches = re.findall(brace_pattern, text)
    
    # Combine and deduplicate
    all_potential = list(set(potential_placeholders + brace_matches))
    
    # Filter out common words that might match (unlikely to be 50+ chars, but just in case)
    return [p for p in all_potential if len(p) >= 50]

def verify_and_fix_placeholders(chunks: List, placeholder_to_id: Dict[str, str], tables_dir: str) -> List:
    """
    Additional pass to verify all placeholders are replaced and fix any missed ones.
    """
    print("\n=== Verifying placeholder replacement ===")
    
    for chunk_idx, chunk in enumerate(chunks):
        unreplaced = detect_unreplaced_placeholders(chunk.text)
        
        if unreplaced:
            print(f"Chunk {chunk_idx}: Found {len(unreplaced)} potential unreplaced placeholders")
            
            for placeholder in unreplaced:
                # Check if this matches any known placeholder
                if placeholder in placeholder_to_id:
                    table_id = placeholder_to_id[placeholder]
                    print(f"  Found unreplaced placeholder for table {table_id}")
                    
                    # Load the table summary file
                    table_file = os.path.join(tables_dir, f"{table_id}.txt")
                    if os.path.exists(table_file):
                        with open(table_file, 'r', encoding='utf-8') as f:
                            table_summary = f.read()
                        
                        # Replace the placeholder (with or without braces)
                        chunk.text = chunk.text.replace(f"{{{placeholder}}}", table_summary)
                        chunk.text = chunk.text.replace(placeholder, table_summary)
                        print(f"  Replaced placeholder for table {table_id} with summary")
                    else:
                        print(f"  WARNING: Table summary file not found for {table_id}")
                else:
                    # Check if it's close to any known placeholder (might be truncated)
                    for known_placeholder in placeholder_to_id.keys():
                        if placeholder in known_placeholder or known_placeholder in placeholder:
                            table_id = placeholder_to_id[known_placeholder]
                            print(f"  Potential match with table {table_id} (partial placeholder)")
                            print(f"    Unreplaced: {placeholder[:50]}...")
                            print(f"    Known: {known_placeholder[:50]}...")
    
    return chunks

# =============================== FIGURE HELPERS ===============================

def load_extracted_figures(figures_dir: str) -> Dict[str, str]:
    """Load figure placeholder→figure_id mapping from figure_index.json."""
    placeholder_to_id: Dict[str, str] = {}
    index_file = os.path.join(figures_dir, "figure_index.json")
    if not os.path.exists(index_file):
        print(f"No figure_index.json found in '{figures_dir}'. Skipping figure replacement.")
        return {}

    with open(index_file, "r", encoding="utf-8") as f:
        figure_index = json.load(f)

    print(f"Figure index contains {len(figure_index)} entries")
    for placeholder, info in figure_index.items():
        fig_id = info.get("id", placeholder)
        placeholder_to_id[placeholder] = fig_id

    print(f"Loaded {len(placeholder_to_id)} figure mappings")
    return placeholder_to_id


def find_figure_placeholders_in_chunks(chunks: List, placeholder_to_id: Dict[str, str]) -> Dict[str, List[Tuple[int, int, int]]]:
    """Locate figure placeholders (wrapped in curly braces) in chunks."""
    references = {pl: [] for pl in placeholder_to_id.keys()}

    for idx, chunk in enumerate(chunks):
        text = chunk.text
        for pl in placeholder_to_id.keys():
            wrapped = f"{{{pl}}}"
            pos = text.find(wrapped)
            while pos != -1:
                references[pl].append((idx, pos, pos + len(wrapped)))
                pos = text.find(wrapped, pos + 1)
    return references


def replace_placeholders_with_figure_summaries(chunks: List, placeholder_to_id: Dict[str, str], figures_dir: str) -> List:
    """Replace figure placeholders ({F#}) or full 'Figure Content: {F#}' tokens."""
    for chunk in chunks:
        original_text = chunk.text
        for pl, fig_id in placeholder_to_id.items():
            fig_path = os.path.join(figures_dir, f"{fig_id}.txt")
            if not os.path.exists(fig_path):
                continue
            with open(fig_path, "r", encoding="utf-8") as f:
                summary = f.read()

            full_token = f"Figure Content: {{{pl}}}"
            wrapped = f"{{{pl}}}"
            if full_token in chunk.text:
                chunk.text = chunk.text.replace(full_token, summary)
            elif wrapped in chunk.text:
                chunk.text = chunk.text.replace(wrapped, summary)

        # clean excess blank lines
        chunk.text = re.sub(r"\n\s*\n\s*\n+", "\n\n", chunk.text).strip()
        chunk.end_index = chunk.start_index + len(chunk.text)
        chunk.token_count = len(chunk.text.split())

    return chunks

# ============================ END FIGURE HELPERS ==============================

def main(txt_file_path, chunker_type="neural", output_dir=None):
    """
    Main function to process a text file with Chonkie chunking.
    Process flow:
    1. Chunk the original text (containing placeholder strings)
    2. Identify which chunks contain table placeholders
    3. Replace placeholders with table summaries in the existing chunks
    
    Args:
        txt_file_path (str): Path to the input text file (containing placeholder strings)
        chunker_type (str): Type of chunker to use ("neural", "recursive", "slumber")
        output_dir (str): Directory to save chunks (optional)
    """
    # Initialize the chunker based on type
    if chunker_type == "neural":
        chunker = NeuralChunker(
            model="mirth/chonky_modernbert_base_1",  
            device_map="cpu",                        
            min_characters_per_chunk=1000,             
            return_type="chunks"                    
        )
    elif chunker_type == "recursive":
        chunker = RecursiveChunker(chunk_size=2000)
    elif chunker_type == "slumber":
        # Note: Slumber chunker requires API keys
        try:
            genie = OpenAIGenie("gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
            chunker = SlumberChunker(
                genie=genie,                       
                tokenizer_or_token_counter="gpt2",  
                chunk_size=2000,                    
                candidate_size=1000,                 
                min_characters_per_chunk=800,        
                verbose=True                      
            )
        except Exception as e:
            print(f"Failed to initialize Slumber chunker: {e}")
            print("Falling back to Neural chunker...")
            chunker = NeuralChunker(
                model="mirth/chonky_modernbert_base_1",
                device_map="cpu",
                min_characters_per_chunk=500,
                return_type="chunks"
            )
    else:
        raise ValueError(f"Unknown chunker type: {chunker_type}")

    print(f"Using {chunker_type} chunker")
    
    # Validate input file
    if not os.path.exists(txt_file_path):
        print(f"Error: Input file '{txt_file_path}' not found.")
        return False
    
    # Read the document (should now contain actual table content wrapped in curly braces)
    with open(txt_file_path, "r") as f:
        document_text = f.read()

    print("Original document length:", len(document_text))
    
    # Check for old-style placeholders (shouldn't exist with new approach)
    old_placeholder_pattern = r'\{TABLE_([^}]+)\}'
    found_old_placeholders = re.findall(old_placeholder_pattern, document_text)
    if found_old_placeholders:
        print(f"WARNING: Found old-style placeholders: {found_old_placeholders}")
        print("This suggests the document was processed with the old script version.")
        print("Please re-run the PDF to TXT conversion with the updated script.")
        return False
    
    # Check for new-style placeholders (curly braces with content)
    new_placeholder_pattern = r'\{([^}]+)\}'
    found_new_placeholders = re.findall(new_placeholder_pattern, document_text)
    print(f"Found {len(found_new_placeholders)} potential table placeholders in document")
    for i, placeholder in enumerate(found_new_placeholders[:3]):  # Show first 3
        preview = placeholder[:50] + "..." if len(placeholder) > 50 else placeholder
        print(f"  Placeholder {i+1}: {preview}")

    print("\n=== Loading table mappings ===")
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    tables_dir = os.path.join(os.path.dirname(txt_file_path), f"{base_name}_tables_with_summaries_and_tags")
    
    if not os.path.exists(tables_dir):
        print(f"Tables summary directory '{tables_dir}' not found.")
        print("This suggests the document hasn't been processed with table extraction and summarization.")
        print("Please run the PDF to TXT conversion script with summarization first.")
        return False
    
    placeholder_to_id = load_extracted_tables(tables_dir)
    if not placeholder_to_id:
        print("No table mappings loaded. Cannot proceed.")
        return False
    
    print(f"Loaded {len(placeholder_to_id)} table mappings from '{tables_dir}'")

    print("\n=== Step 1: Chunking original text (with placeholders intact) ===")
    chunks = chunker(document_text)
    print(f"Created {len(chunks)} chunks from original text containing placeholders")
    
    for i, chunk in enumerate(chunks):
        preview = chunk.text[:100].replace('\n', ' ')
        print(f"  Chunk {i}: {len(chunk.text)} characters - '{preview}...'")

    print("\n=== Step 2: Locating table placeholders in existing chunks ===")
    table_references = find_table_placeholders_in_chunks(chunks, placeholder_to_id)
    
    total_tables_found = sum(len(refs) for refs in table_references.values())
    tables_with_references = len([refs for refs in table_references.values() if refs])
    
    print(f"Table placeholders found in chunks: {tables_with_references}/{len(placeholder_to_id)}")
    print(f"Total placeholder occurrences: {total_tables_found}")
    
    for placeholder_string, locations in table_references.items():
        if locations:
            table_id = placeholder_to_id[placeholder_string]
            chunk_indices = list(set([loc[0] for loc in locations]))  # Extract unique chunk indices
            print(f"Table {table_id} placeholder found in chunks: {chunk_indices}")

    print("\n=== Step 3a: Replacing placeholders with table summaries (post-chunking) ===")
    final_chunks = replace_placeholders_with_formatted_tables(chunks, placeholder_to_id, tables_dir)
    
    print(f"Final chunks count: {len(final_chunks)}")
    
    # Additional verification pass
    final_chunks = verify_and_fix_placeholders(final_chunks, placeholder_to_id, tables_dir)
    
    # Final check for any remaining placeholders
    print("\n=== Final verification ===")
    remaining_placeholders = 0
    for i, chunk in enumerate(final_chunks):
        unreplaced = detect_unreplaced_placeholders(chunk.text)
        if unreplaced:
            print(f"WARNING: Chunk {i} still has {len(unreplaced)} unreplaced placeholders")
            for placeholder in unreplaced[:3]:  # Show first 3
                print(f"  {placeholder[:50]}...")
            remaining_placeholders += len(unreplaced)
    
    if remaining_placeholders == 0:
        print("✓ All placeholders successfully replaced")
    else:
        print(f"✗ {remaining_placeholders} placeholders remain unreplaced")
    
    for i, chunk in enumerate(final_chunks):
        print(f"  Final Chunk {i}: {len(chunk.text)} characters")

    # ================= FIGURE PROCESSING (must happen before saving chunks) =================
    print("\n=== Loading figure mappings ===")
    figures_dir = os.path.join(os.path.dirname(txt_file_path), f"{base_name}_figures_with_summaries_and_tags")
    figure_placeholder_to_id = load_extracted_figures(figures_dir)
    if figure_placeholder_to_id:
        print(f"Loaded {len(figure_placeholder_to_id)} figure mappings from '{figures_dir}'")

        print("\n=== Replacing figure placeholders with summaries ===")
        final_chunks = replace_placeholders_with_figure_summaries(final_chunks, figure_placeholder_to_id, figures_dir)
    else:
        print("No figure mappings found; skipping figure replacement")

    # Save the chunks
    if output_dir is None:
        chunks_dir = os.path.join(os.path.dirname(txt_file_path), "chunks_with_table_summaries")
    else:
        chunks_dir = os.path.join(output_dir, "chunks_with_table_summaries")
    
    os.makedirs(chunks_dir, exist_ok=True)
    
    for i, chunk in enumerate(final_chunks):
        with open(os.path.join(chunks_dir, f"chunk_{i}.txt"), "w", encoding='utf-8') as f:
            f.write(chunk.text)

    print(f"\n=== Initial chunking complete ===")
    print(f"Saved {len(final_chunks)} chunks to '{chunks_dir}' directory")
    
    # Post-process chunks to handle tables at chunk boundaries
    print(f"\n=== Post-processing chunks ===")
    print("Moving tables from chunk start to previous chunk...")
    
    # Determine final output directory
    if output_dir is None:
        final_chunks_dir = os.path.join(os.path.dirname(txt_file_path), "final_chunks_with_summaries")
    else:
        final_chunks_dir = os.path.join(output_dir, "final_chunks_with_summaries")
    
    try:
        # Call post-processing function
        process_chunks_sequentially(chunks_dir, final_chunks_dir)
        
        # Delete the intermediate chunks directory
        print(f"\n=== Cleaning up intermediate files ===")
        print(f"Removing intermediate chunks directory: {chunks_dir}")
        shutil.rmtree(chunks_dir)
        print("✓ Intermediate files cleaned up")
        
    except Exception as e:
        print(f"Error during post-processing: {e}")
        print("Intermediate chunks are still available in:", chunks_dir)
        return False

    print(f"\n=== Final Summary ===")
    print(f"Original document: {txt_file_path}")
    print(f"Table summaries processed: {len(placeholder_to_id)}")
    print(f"Final processed chunks saved to: {final_chunks_dir}")
    print(f"Average chunk length: {sum(len(chunk.text) for chunk in final_chunks) / len(final_chunks):.0f} characters")
    
    return True

if __name__ == "__main__":
    ## Usage: Neural Chunker performing best
    # python chonkie_test.py "dev reports/Stability Section - Developmental Report.txt" --output-dir chunks
    parser = argparse.ArgumentParser(
        description="Chunk text files with Chonkie using table summaries and tags approach",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python chonkie_test.py "document.txt"
    python chonkie_test.py "document.txt" --chunker recursive
    python chonkie_test.py "document.txt" --chunker slumber --output-dir "./chunks"
    python chonkie_test.py "document.txt" --chunker neural --output-dir "./output"
        """
    )
    
    parser.add_argument(
        'input_file',
        help='Path to the input text file (should contain embedded table placeholders)'
    )
    
    parser.add_argument(
        '--chunker',
        choices=['neural', 'recursive', 'slumber'],
        default='neural',
        help='Type of chunker to use (default: neural)'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Directory to save chunks (default: same as input file directory)'
    )
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found.")
        sys.exit(1)
    
    if not args.input_file.lower().endswith('.txt'):
        print(f"Warning: Input file '{args.input_file}' is not a .txt file.")
        print("Proceeding anyway...")
    
    # Run the chunking process
    print("=" * 60)
    print("CHONKIE TEXT CHUNKING WITH TABLE SUMMARIES AND TAGS")
    print("=" * 60)
    print(f"Input file: {args.input_file}")
    print(f"Chunker type: {args.chunker}")
    print(f"Output directory: {args.output_dir or 'same as input file'}")
    print()
    
    success = main(
        txt_file_path=args.input_file,
        chunker_type=args.chunker,
        output_dir=args.output_dir
    )
    
    if success:
        print("\n" + "=" * 60)
        print("✓ CHUNKING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("✗ CHUNKING FAILED!")
        print("=" * 60)
        sys.exit(1)