from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
import os
import json
import asyncio
import shutil
from pathlib import Path

load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

generator_client = OpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)
async_generator_client = AsyncOpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)

def extract_summary_from_tags(content: str) -> str:
    """
    Extract summary from <summary> tags if present, otherwise return content as is.
    
    Args:
        content: The raw content from the LLM response
        
    Returns:
        Extracted summary or original content if tags not found
    """
    if not content:
        return content
        
    # Look for <summary> tags
    import re
    summary_match = re.search(r'<summary>(.*?)</summary>', content, re.DOTALL | re.IGNORECASE)
    
    if summary_match:
        # Extract and clean the summary content
        summary = summary_match.group(1).strip()
        return summary
    else:
        # Return original content if no tags found
        return content

async def create_summary_of_table_async(table_heading: str, table_content: str) -> str | None:
    """
    Asynchronously create a summary of a table.
    
    Args:
        table_heading: The heading/title of the table
        table_content: The content of the table
        
    Returns:
        Summary string or None if failed
    """
    try:
        response = await async_generator_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME, 
            messages=[  
                {"role": "system", "content": f"""You are a highly specialized pharmaceutical documentation expert with extensive experience in creating summaries for New Drug Application (NDA) documentation. Your task is to analyze and summarize technical tables from pharmaceutical development reports, adhering strictly to ICH eCTD (Electronic Common Technical Document) guidelines."""},
                {"role": "user", "content": f"""
Here is the table content you need to summarize:

<table_content>
{table_content}
</table_content>

Your objective is to create a concise, scientifically accurate summary of this table that can be directly incorporated into regulatory documentation for review by health authorities.

Process:
1. Carefully analyze the table content.
2. Wrap your analysis inside <table_analysis> tags in your thinking block, covering the following points:
   a. Identify and list each column in the table
   b. Identify the key quality attributes or parameters being tested
   c. Note the testing conditions or timepoints represented
   d. Extract significant numerical values and their relationship to specifications
   e. Identify any trends, patterns, or notable findings
   f. Note any missing or unclear information
   g. Assess the regulatory significance of these results
   h. Consider potential implications for drug safety and efficacy
3. Based on your analysis, compose a final summary in <summary> tags.

Guidelines for your analysis and summary:
- Maintain utmost scientific accuracy and precision in all data interpretations
- Preserve all critical numerical values, specifications, and acceptance criteria
- Use standardized pharmaceutical terminology consistent with ICH guidelines
- Focus on the regulatory significance of the data (stability, quality attributes, etc.)
- Structure the information logically, moving from test parameters to results to conclusions
- Be concise yet comprehensive, capturing all essential information for regulatory review
- Highlight any results that approach or exceed specification limits
- Include relevant timepoints, conditions, and methodologies when summarizing stability or testing data

After completing your analysis and summary, perform a self-review to ensure:
1. All critical information from the table is accurately represented
2. The summary adheres to ICH eCTD guidelines
3. The language is clear, concise, and scientifically precise
4. The regulatory significance of the data is clearly communicated

Begin your response with the analysis, followed by the final summary. Ensure that your final summary can stand alone as a comprehensive yet concise representation of the table content, suitable for inclusion in an NDA submission. Your output should consist only of the summary and should not duplicate or rehash any of the work you did in the table analysis section.
"""}
            ]
        )

        raw_content = response.choices[0].message.content
        if raw_content is None:
            return None
        return extract_summary_from_tags(raw_content)
    except Exception as e:
        print(f"Error creating summary: {e}")
        return None

def load_table_index(tables_dir: Path) -> dict:
    """Load the table index JSON file."""
    index_file = tables_dir / "table_index.json"
    try:
        with open(index_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading table index: {e}")
        return {}

def read_table_content(tables_dir: Path, table_file: str) -> str:
    """Read the content of a table file."""
    table_path = tables_dir / table_file
    try:
        with open(table_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading table file {table_file}: {e}")
        return ""

def save_summary_to_file(output_dir: Path, table_id: str, table_heading: str, summary: str, table_metadata: dict):
    """Save the table summary to a file with heading and metadata."""
    output_file = output_dir / f"{table_id}.txt"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:  
            # Write heading
            # if table_heading:
            #     f.write(f"{table_heading}")
            #     f.write("\n\n")

            # Write summary
            f.write(f"Table Summary: {summary}")
            f.write("\n\n")
            
            # Write table tag
            f.write(f"`<TABLE_TAG_{table_id}>`")
        
        print(f"✓ Saved summary for {table_id} to {output_file}")
        return True
    except Exception as e:
        print(f"✗ Error saving summary for {table_id}: {e}")
        return False

def copy_table_index(tables_dir: Path, output_dir: Path) -> bool:
    """Copy the table_index.json file to the output directory."""
    source_file = tables_dir / "table_index.json"
    dest_file = output_dir / "table_index.json"
    
    try:
        shutil.copy2(source_file, dest_file)
        print(f"✓ Copied table_index.json to {dest_file}")
        return True
    except Exception as e:
        print(f"✗ Error copying table_index.json: {e}")
        return False

async def process_single_table(tables_dir: Path, output_dir: Path, table_id: str, table_metadata: dict) -> bool:
    """Process a single table and create its summary."""
    table_heading = table_metadata.get('heading', '')
    table_file = table_metadata.get('file', '')
    
    if not table_file:
        print(f"✗ No file specified for {table_id}")
        return False
    
    # Read table content
    table_content = read_table_content(tables_dir, table_file)
    if not table_content:
        print(f"✗ Could not read content for {table_id}")
        return False
    
    print(f"📊 Processing {table_id}: {table_heading[:60]}...")
    
    # Create summary
    summary = await create_summary_of_table_async(table_heading, table_content)
    if not summary:
        print(f"✗ Failed to create summary for {table_id}")
        return False
    
    # Save summary to file
    return save_summary_to_file(output_dir, table_id, table_heading, summary, table_metadata)

async def process_all_tables():
    """Main function to process all tables and create summaries."""
    # Define paths
    tables_dir = Path("/Users/<USER>/projects/scalegen/MedNova/Pre-IND/Input Docs/Pre-IND-Sample_tables")
    output_dir = Path("/Users/<USER>/projects/scalegen/MedNova/Pre-IND/Input Docs/Pre-IND-Sample_tables_with_summaries_and_tags")
    
    # Ensure output directory exists
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy table index to output directory
    print("📋 Copying table index...")
    copy_table_index(tables_dir, output_dir)
    
    # Load table index
    print("📋 Loading table index...")
    table_index = load_table_index(tables_dir)
    
    if not table_index:
        print("✗ No tables found in index")
        return
    
    print(f"📊 Found {len(table_index)} tables to process")
    
    # Process tables
    successful = 0
    failed = 0
    
    # Process tables in batches to avoid overwhelming the API
    batch_size = 10
    table_items = list(table_index.items())
    
    for i in range(0, len(table_items), batch_size):
        batch = table_items[i:i + batch_size]
        print(f"\n🔄 Processing batch {i//batch_size + 1}/{(len(table_items) + batch_size - 1)//batch_size}")
        
        # Create tasks for this batch
        tasks = []
        for hash_key, table_metadata in batch:
            table_id = table_metadata.get('id', hash_key)
            task = process_single_table(tables_dir, output_dir, table_id, table_metadata)
            tasks.append(task)
        
        # Execute batch
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count results
        for result in results:
            if isinstance(result, Exception):
                print(f"✗ Exception occurred: {result}")
                failed += 1
            elif result:
                successful += 1
            else:
                failed += 1
        
        # Small delay between batches
        if i + batch_size < len(table_items):
            await asyncio.sleep(1)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"📊 PROCESSING COMPLETE")
    print(f"{'='*60}")
    print(f"✓ Successfully processed: {successful} tables")
    print(f"✗ Failed to process: {failed} tables")
    print(f"📁 Output directory: {output_dir}")
    print(f"{'='*60}")

if __name__ == "__main__":
    print("🚀 Starting table summarization process...")
    asyncio.run(process_all_tables())