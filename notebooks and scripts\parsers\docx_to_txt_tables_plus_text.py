from docx import Document
import os
import json
import textwrap
import random
import string
import subprocess
import shutil
import re
import tempfile

def check_pandoc_available():
    """
    Check if Pandoc is available on the system.
    
    Returns:
        bool: True if Pandoc is available, False otherwise
    """
    return shutil.which('pandoc') is not None

def convert_docx_with_pandoc(input_docx_path, output_txt_path):
    """
    Convert DOCX to TXT using Pandoc.
    
    Args:
        input_docx_path (str): Path to input DOCX file
        output_txt_path (str): Path to output TXT file
        
    Returns:
        str: The converted text content
    """
    try:
        # Run pandoc command
        result = subprocess.run([
            'pandoc', 
            input_docx_path, 
            '-t', 'plain',
            '--wrap=none',
            '-o', output_txt_path
        ], capture_output=True, text=True, check=True)
        
        # Read the converted content
        with open(output_txt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return content
    except subprocess.CalledProcessError as e:
        print(f"Error running Pandoc: {e}")
        print(f"Pandoc stderr: {e.stderr}")
        return None
    except Exception as e:
        print(f"Error in Pandoc conversion: {e}")
        return None

def extract_tables_from_pandoc_output(pandoc_content):
    """
    Extract tables from Pandoc-converted text output.
    Pandoc formats tables with specific ASCII borders that we can detect accurately.
    
    Args:
        pandoc_content (str): The text content from Pandoc conversion
        
    Returns:
        list: List of extracted table strings
    """
    tables = []
    lines = pandoc_content.split('\n')
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for Pandoc table borders - they start with + and have multiple + and - characters
        # This is much more specific than the previous approach
        is_table_border = (
            line.startswith('+') and 
            line.endswith('+') and 
            line.count('+') >= 3 and  # At least 3 + characters (start, middle, end)
            line.count('-') >= 10 and  # At least 10 - characters for actual table borders
            len(line) > 20  # Reasonably long line for a table
        )
        
        if is_table_border:
            # Found a table border, extract the complete table
            table_lines = []
            table_start = i
            
            # Look backwards for table title/caption (usually 1-3 lines before)
            j = i - 1
            caption_lines = []
            while j >= 0 and j >= i - 3:  # Look at most 3 lines back
                prev_line = lines[j].strip()
                if not prev_line:
                    j -= 1
                    continue
                
                # Check if it looks like a table caption (contains "Table" and a colon or number)
                if 'table' in prev_line.lower() and (':' in prev_line or re.search(r'\d+', prev_line)):
                    caption_lines.insert(0, lines[j])
                    j -= 1
                    # Look for one more line that might be part of the caption
                    if j >= 0 and lines[j].strip():
                        caption_lines.insert(0, lines[j])
                    break
                else:
                    break
            
            # Add caption lines to table
            table_lines.extend(caption_lines)
            if caption_lines and table_lines:
                table_lines.append('')  # Add separator between caption and table
            
            # Add the table content starting from the border
            table_lines.append(lines[i])  # First border
            
            # Continue reading until we hit the end of the table
            i += 1
            while i < len(lines):
                current_line = lines[i]
                
                # Check if this is still part of the table
                if (current_line.strip().startswith('|') or 
                    current_line.strip().startswith('+') or 
                    (current_line.strip().startswith('=') and '=' in current_line) or  # Header separator
                    not current_line.strip()):  # Empty line within table
                    
                    table_lines.append(current_line)
                    
                    # Check if this is the final border (bottom of table)
                    if (current_line.strip().startswith('+') and 
                        current_line.strip().endswith('+') and
                        current_line.count('+') >= 3):
                        # This might be the end, but let's check the next few lines
                        end_found = True
                        for k in range(i + 1, min(i + 3, len(lines))):
                            if (lines[k].strip().startswith('|') or 
                                lines[k].strip().startswith('+')):
                                end_found = False
                                break
                        if end_found:
                            i += 1
                            break
                    
                    i += 1
                else:
                    # Not part of table anymore
                    break
            
            # Clean up the table content
            table_content = '\n'.join(table_lines).strip()
            
            # Validate this is actually a substantial table
            if (len(table_lines) >= 5 and  # At least 5 lines (borders + content)
                table_content.count('|') >= 8 and  # Multiple pipe characters
                table_content.count('+') >= 6):  # Multiple border characters
                
                tables.append(table_content)
                print(f"  Extracted table with {len(table_lines)} lines, {table_content.count('|')} pipes")
            
            continue
        
        i += 1
    
    return tables

def find_table_context_in_pandoc(pandoc_content, table_index):
    """
    Find the context (heading/caption) for a table in pandoc output.
    
    Args:
        pandoc_content (str): The pandoc converted content
        table_index (int): Index of the table to find context for
        
    Returns:
        str: The table context/heading
    """
    tables = extract_tables_from_pandoc_output(pandoc_content)
    if table_index >= len(tables):
        return ""
    
    target_table = tables[table_index]
    lines = pandoc_content.split('\n')
    
    # Find where this table appears in the content
    table_lines = target_table.split('\n')
    if not table_lines:
        return ""
    
    first_table_line = table_lines[0].strip()
    
    # Look for the table in the content
    for i, line in enumerate(lines):
        if line.strip() == first_table_line:
            # Found table start, look backwards for heading
            j = i - 1
            context_lines = []
            
            while j >= 0 and len(context_lines) < 5:  # Look at most 5 lines back
                prev_line = lines[j].strip()
                if prev_line:
                    # Check if this looks like a table heading
                    if ('table' in prev_line.lower() or 
                        'batch' in prev_line.lower() or
                        prev_line.endswith(':') or
                        len(prev_line) < 100):  # Likely a heading
                        context_lines.insert(0, prev_line)
                    else:
                        break
                j -= 1
            
            return ' '.join(context_lines)
    
    return ""

def generate_placeholder_string(table_data):
    """
    Generate a random alphanumeric string with the same character count as the table content.
    This ensures proper token representation without problematic characters.
    
    Args:
        table_data (list): Table data as a list of lists
        
    Returns:
        str: Random string with same length as concatenated table content
    """
    if not table_data or not any(table_data):
        return ""
    
    # Calculate total character count from table
    total_chars = 0
    for row in table_data:
        for cell in row:
            if cell is not None:
                cell_str = str(cell).strip()
                if cell_str:
                    total_chars += len(cell_str)
    
    # Generate random string of same length
    # Use alphanumeric characters to avoid any special character issues
    chars = string.ascii_letters + string.digits
    placeholder = ''.join(random.choice(chars) for _ in range(total_chars// 2))
    
    return placeholder

def extract_bullet_or_numbering(paragraph_element, doc):
    """
    Extract bullet point or numbering information from a paragraph element.
    
    Args:
        paragraph_element: The paragraph XML element
        doc: The Document object
        
    Returns:
        str: The bullet character or number prefix, or empty string if none
    """
    try:
        # Find paragraph properties
        p_pr = paragraph_element.find('./w:pPr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        if p_pr is None:
            return ""
        
        # Find numbering properties
        num_pr = p_pr.find('./w:numPr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        if num_pr is None:
            return ""
        
        # Get numbering ID and level
        num_id_elem = num_pr.find('./w:numId', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        ilvl_elem = num_pr.find('./w:ilvl', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        
        if num_id_elem is None:
            return ""
        
        num_id = num_id_elem.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
        level = 0 if ilvl_elem is None else int(ilvl_elem.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val', '0'))
        
        # Try to get numbering format from the document's numbering part
        numbering_part = None
        try:
            numbering_part = doc.part.package.part_related_by(
                'http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering'
            )
        except:
            pass
        
        if numbering_part:
            # Parse numbering definitions to get actual format
            bullet_char = get_numbering_format(numbering_part, num_id, level)
            if bullet_char:
                return bullet_char
        
        # Fallback: Use simple indentation and bullet based on level
        indent = "  " * level
        if level == 0:
            return indent + "• "
        elif level == 1:
            return indent + "○ "
        elif level == 2:
            return indent + "▪ "
        else:
            return indent + "- "
            
    except Exception as e:
        # If anything goes wrong, just return empty string
        print(f"Warning: Could not extract bullet/numbering: {e}")
        return ""

def get_numbering_format(numbering_part, num_id, level):
    """
    Get the actual numbering format from the numbering part.
    
    Args:
        numbering_part: The numbering part of the document
        num_id: The numbering ID
        level: The numbering level
        
    Returns:
        str: The formatted bullet/number prefix
    """
    try:
        # Parse the numbering XML
        numbering_root = numbering_part.element
        
        # Find the abstract numbering definition
        abstract_num_id = None
        for num in numbering_root.findall('.//w:num', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}):
            if num.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}numId') == num_id:
                abstract_num_id_elem = num.find('./w:abstractNumId', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                if abstract_num_id_elem is not None:
                    abstract_num_id = abstract_num_id_elem.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                break
        
        if not abstract_num_id:
            return None
        
        # Find the abstract numbering definition
        for abstract_num in numbering_root.findall('.//w:abstractNum', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}):
            if abstract_num.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}abstractNumId') == abstract_num_id:
                # Find the level definition
                for lvl in abstract_num.findall('.//w:lvl', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}):
                    if lvl.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}ilvl') == str(level):
                        # Get the numbering format
                        num_fmt = lvl.find('./w:numFmt', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                        lvl_text = lvl.find('./w:lvlText', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                        
                        if num_fmt is not None and lvl_text is not None:
                            fmt_val = num_fmt.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                            text_val = lvl_text.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                            
                            # Handle different numbering formats
                            indent = "  " * level
                            if fmt_val == 'bullet':
                                # Use the actual bullet character from lvlText
                                if text_val:
                                    return indent + text_val + " "
                                else:
                                    return indent + "• "
                            elif fmt_val == 'decimal':
                                return indent + "1. "  # Placeholder for numbering
                            elif fmt_val == 'lowerLetter':
                                return indent + "a. "
                            elif fmt_val == 'upperLetter':
                                return indent + "A. "
                            elif fmt_val == 'lowerRoman':
                                return indent + "i. "
                            elif fmt_val == 'upperRoman':
                                return indent + "I. "
                            else:
                                # Try to use the level text directly
                                if text_val:
                                    return indent + text_val + " "
                        break
                break
    except Exception as e:
        print(f"Warning: Could not parse numbering format: {e}")
    
    return None

def process_docx_to_txt(input_docx_path, output_txt_path, tables_dir=None, figures_dir=None):
    """
    Read a DOCX file and write its content to a TXT file while extracting tables separately.
    Tables are stored as separate files and replaced with random placeholder strings of equivalent length.
    Now uses Pandoc for better table extraction, especially for merged cells.
    
    Args:
        input_docx_path (str): Path to input DOCX file
        output_txt_path (str): Path to output TXT file
        tables_dir (str): Directory to store extracted tables (optional)
        figures_dir (str): Directory to store extracted figures (optional)
    """
    if tables_dir is None:
        base_name = os.path.splitext(os.path.basename(output_txt_path))[0]
        tables_dir = os.path.join(os.path.dirname(output_txt_path), f"{base_name}_tables")
    
    os.makedirs(tables_dir, exist_ok=True)
    
    # Set up directory for figures
    if figures_dir is None:
        base_name = os.path.splitext(os.path.basename(output_txt_path))[0]
        figures_dir = os.path.join(os.path.dirname(output_txt_path), f"{base_name}_figures")

    os.makedirs(figures_dir, exist_ok=True)

    # Containers for figure extraction
    extracted_figures = {}
    figure_counter = 1
    
    extracted_tables = {}
    table_counter = 1
    written_placeholders = {}  # Track placeholders written to file
    
    # Set random seed for reproducibility (optional - remove for true randomness)
    random.seed(42)
    
    # Check if Pandoc is available
    if not check_pandoc_available():
        print("WARNING: Pandoc is not available. Install Pandoc for better table extraction.")
        print("Falling back to original table extraction method...")
        pandoc_content = None
        pandoc_tables = []
    else:
        # Convert using Pandoc first to get better table formatting
        print("Converting document using Pandoc for better table extraction...")
        temp_pandoc_file = output_txt_path + '.pandoc_temp'
        pandoc_content = convert_docx_with_pandoc(input_docx_path, temp_pandoc_file)
        
        if pandoc_content:
            pandoc_tables = extract_tables_from_pandoc_output(pandoc_content)
            print(f"Pandoc extracted {len(pandoc_tables)} tables")
            # Clean up temp file
            if os.path.exists(temp_pandoc_file):
                os.remove(temp_pandoc_file)
        else:
            print("Pandoc conversion failed, falling back to original method...")
            pandoc_content = None
            pandoc_tables = []
    
    doc = Document(input_docx_path)
    
    # First, find all tables in the document using the high-level API
    all_tables = doc.tables
    print(f"Found {len(all_tables)} tables using high-level API")
    
    # Create a mapping of table elements to their indices
    table_element_map = {}
    for i, table in enumerate(all_tables):
        table_element_map[table._element] = i

    # Track recent paragraphs for table headings (keep last 10 paragraphs)
    recent_paragraphs = []
    pending_paragraphs = []  # Track paragraphs that haven't been written yet

    def extract_table_heading_from_recent_paragraphs():
        """Extract complete table heading from recent paragraphs."""
        if not recent_paragraphs:
            return ""
        
        # Combine all recent paragraphs into one text block
        combined_text = "\n".join(recent_paragraphs)
        
        # Find the last occurrence of "Table" (case-insensitive)
        combined_lower = combined_text.lower()
        table_index = combined_lower.rfind('table')
        
        if table_index == -1:
            # If no "Table" found, check if any paragraph looks like a table heading
            for paragraph in reversed(recent_paragraphs):
                if ':' in paragraph or 'batch' in paragraph.lower() or 'data' in paragraph.lower():
                    return paragraph.strip()
            return ""
        
        # Extract text from the last "Table" occurrence to the end
        # Find the start of the line containing "Table"
        lines_before_table = combined_text[:table_index].split('\n')
        lines_from_table = combined_text[table_index:].split('\n')
        
        # Get the line that contains "Table" and all lines after it
        table_line_start = len('\n'.join(lines_before_table[:-1]))
        if len(lines_before_table) > 1:
            table_line_start += 1  # Add 1 for the newline character
        
        heading_text = combined_text[table_line_start:].strip()
        return heading_text

    def write_pending_paragraphs_except_heading(heading_text):
        """Write pending paragraphs to file, excluding the table heading."""
        if not pending_paragraphs:
            return
        
        # Find paragraphs that are part of the heading
        heading_paragraphs = set()
        if heading_text:
            heading_lower = heading_text.lower()
            for i, (paragraph_text, style_name) in enumerate(pending_paragraphs):
                if 'table' in paragraph_text.lower() and paragraph_text.lower() in heading_lower:
                    heading_paragraphs.add(i)
        
        # Write non-heading paragraphs
        for i, (paragraph_text, style_name) in enumerate(pending_paragraphs):
            if i not in heading_paragraphs:
                if 'heading' in style_name:
                    txt_file.write('\n' + paragraph_text.upper() + '\n')
                    txt_file.write('=' * len(paragraph_text) + '\n')
                else:
                    txt_file.write(paragraph_text + '\n')
            else:
                txt_file.write("\n" + paragraph_text + '\n')
                print(f"  Skipping table heading: {paragraph_text[:60]}...")
        
        # Clear pending paragraphs
        pending_paragraphs.clear()
    
    with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
        for element in doc.element.body:
            if element.tag.endswith('tbl'):
                # Check if this table element is in our mapping
                if element in table_element_map:
                    table_index = table_element_map[element]
                    table = all_tables[table_index]
                    
                    # Always process tables, even if they appear empty
                    current_table_id = f"table_{table_counter}"
                    
                    # Use Pandoc table if available, otherwise fall back to original method
                    if pandoc_tables and (table_counter - 1) < len(pandoc_tables):
                        # Use Pandoc extracted table
                        formatted_table = pandoc_tables[table_counter - 1]
                        print(f"  Using Pandoc table for {current_table_id}")
                        
                        # Generate placeholder based on pandoc table content
                        placeholder_string = generate_placeholder_string([[formatted_table]])
                        if not placeholder_string:
                            placeholder_string = 'EmptyTable'
                    else:
                        # Fall back to original extraction method
                        print(f"  Using original extraction for {current_table_id}")
                        table_data_xml = extract_table_data(element)
                        table_data_api = extract_table_data_api(table)
                        
                        # Use the method that gives more data
                        table_data = table_data_api if len(table_data_api) >= len(table_data_xml) else table_data_xml
                        
                        # Generate placeholder string based on table content
                        placeholder_string = generate_placeholder_string(table_data)
                        if not placeholder_string:  # Empty table fallback
                            placeholder_string = 'EmptyTable'
                        
                        # Format the table
                        formatted_table = format_table(table_data) if table_data else "EMPTY TABLE"
                    
                    table_counter += 1

                    # Extract complete table heading from recent paragraphs
                    table_heading = extract_table_heading_from_recent_paragraphs()
                    
                    # Write pending paragraphs (excluding heading) before table placeholder
                    write_pending_paragraphs_except_heading(table_heading)
                    
                    # Calculate table dimensions
                    if pandoc_tables and (table_counter - 2) < len(pandoc_tables):
                        # For Pandoc tables, estimate dimensions from content
                        table_lines = formatted_table.split('\n')
                        data_lines = [line for line in table_lines if line.strip() and '|' in line and not re.match(r'^[\s\-\+\|=]+$', line)]
                        rows = len(data_lines)
                        columns = max([line.count('|') - 1 for line in data_lines]) if data_lines else 0
                        is_empty = rows == 0
                    else:
                        # For original method, use table_data
                        rows = len(table_data) if 'table_data' in locals() else 0
                        columns = len(table_data[0]) if 'table_data' in locals() and table_data else 0
                        is_empty = len(table_data) == 0 if 'table_data' in locals() else True
                    
                    # Store table information using placeholder as key
                    extracted_tables[placeholder_string] = {
                        'id': current_table_id,
                        'heading': table_heading,
                        'formatted_content': formatted_table,
                        'char_count': len(placeholder_string),
                        'position': {
                            'element_index': table_counter - 2,
                            'api_index': table_index
                        },
                        'rows': rows,
                        'columns': columns,
                        'is_empty': is_empty,
                        'extraction_method': 'pandoc' if pandoc_tables and (table_counter - 2) < len(pandoc_tables) else 'original'
                    }
                    
                    # Write placeholder reference to main text
                    table_placeholder = f"Table Content: {{{placeholder_string}}}\n"
                    txt_file.write(table_placeholder + "\n")
                    written_placeholders[placeholder_string] = current_table_id
                    print(f"  Inserted placeholder for {current_table_id} (length: {len(placeholder_string)} chars)")
                    
                    # Clear recent paragraphs after processing table
                    recent_paragraphs = []
                else:
                    # Fallback for tables not found in high-level API
                    current_table_id = f"table_{table_counter}"
                    
                    # Use Pandoc table if available, otherwise fall back to original method
                    if pandoc_tables and (table_counter - 1) < len(pandoc_tables):
                        # Use Pandoc extracted table
                        formatted_table = pandoc_tables[table_counter - 1]
                        print(f"  Using Pandoc table for {current_table_id} (fallback)")
                        
                        # Generate placeholder based on pandoc table content
                        placeholder_string = generate_placeholder_string([[formatted_table]])
                        if not placeholder_string:
                            placeholder_string = 'EmptyTable'
                    else:
                        # Fall back to original extraction method
                        print(f"  Using original extraction for {current_table_id} (fallback)")
                        table_data = extract_table_data(element)
                        
                        # Generate placeholder string
                        placeholder_string = generate_placeholder_string(table_data)
                        if not placeholder_string:  # Empty table fallback
                            placeholder_string = 'EmptyTable'
                        
                        formatted_table = format_table(table_data) if table_data else "EMPTY TABLE"
                    
                    table_counter += 1

                    # Extract complete table heading from recent paragraphs
                    table_heading = extract_table_heading_from_recent_paragraphs()
                    
                    # Write pending paragraphs (excluding heading) before table placeholder
                    write_pending_paragraphs_except_heading(table_heading)
                    
                    # Calculate table dimensions
                    if pandoc_tables and (table_counter - 2) < len(pandoc_tables):
                        # For Pandoc tables, estimate dimensions from content
                        table_lines = formatted_table.split('\n')
                        data_lines = [line for line in table_lines if line.strip() and '|' in line and not re.match(r'^[\s\-\+\|=]+$', line)]
                        rows = len(data_lines)
                        columns = max([line.count('|') - 1 for line in data_lines]) if data_lines else 0
                        is_empty = rows == 0
                    else:
                        # For original method, use table_data
                        rows = len(table_data) if 'table_data' in locals() else 0
                        columns = len(table_data[0]) if 'table_data' in locals() and table_data else 0
                        is_empty = len(table_data) == 0 if 'table_data' in locals() else True

                    extracted_tables[placeholder_string] = {
                        'id': current_table_id,
                        'heading': table_heading,
                        'formatted_content': formatted_table,
                        'char_count': len(placeholder_string),
                        'position': {
                            'element_index': table_counter - 2
                        },
                        'rows': rows,
                        'columns': columns,
                        'is_empty': is_empty,
                        'extraction_method': 'pandoc' if pandoc_tables and (table_counter - 2) < len(pandoc_tables) else 'original'
                    }
                    
                    table_placeholder = f"Table Content: {{{placeholder_string}}}\n"
                    txt_file.write(table_placeholder + "\n")
                    written_placeholders[placeholder_string] = current_table_id
                    # Clear recent paragraphs after processing a table
                    recent_paragraphs = []
                    print(f"  Inserted placeholder for {current_table_id} (length: {len(placeholder_string)} chars)")
                
            elif element.tag.endswith('p'):
                # --------------------------------------------------------
                # FIGURE HANDLING
                # --------------------------------------------------------
                figure_data = extract_figure_data(element, doc)
                if figure_data:
                    current_figure_id = f"figure_{figure_counter}"
                    placeholder_fig = generate_short_placeholder('F', figure_counter)
                    # Save images
                    image_files = extract_and_save_images(element, doc, figures_dir, current_figure_id)

                    # Flush any pending paragraphs (excluding figure heading)
                    write_pending_paragraphs_except_heading(figure_data['heading'])

                    # Write heading, summary, placeholder
                    if figure_data['heading']:
                        txt_file.write('\n' + figure_data['heading'] + '\n')
                    if figure_data['summary']:
                        txt_file.write(figure_data['summary'] + '\n')
                    txt_file.write(f"Figure Content: {placeholder_fig}\n\n")

                    # Store metadata
                    figure_key = f"F{figure_counter}"
                    extracted_figures[figure_key] = {
                        'id': current_figure_id,
                        'heading': figure_data['heading'],
                        'summary': figure_data['summary'],
                        'alt_text': figure_data['alt_text'],
                        'dimensions': {
                            'width': figure_data['width'],
                            'height': figure_data['height']
                        },
                        'images': image_files,
                        'file': f"{current_figure_id}.txt"
                    }

                    figure_counter += 1
                    # Clear recent paragraphs after processing figure
                    recent_paragraphs = []
                    continue  # Skip normal paragraph processing
                # --------------------------------------------------------
                # END FIGURE HANDLING
                # --------------------------------------------------------
                # Extract paragraph text
                text_elements = element.findall('.//w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                text = ' '.join(elem.text for elem in text_elements if elem.text).strip()
                
                if text:
                    # Check for bullet points and numbering
                    bullet_prefix = extract_bullet_or_numbering(element, doc)
                    
                    # Add bullet/numbering prefix if found
                    if bullet_prefix:
                        text = bullet_prefix + text
                    
                    # Add to recent paragraphs (keep last 10)
                    recent_paragraphs.append(text)
                    if len(recent_paragraphs) > 10:
                        recent_paragraphs.pop(0)
                    
                    # Check for heading styles
                    style_name = ""
                    p_pr = element.find('./w:pPr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                    if p_pr is not None:
                        style = p_pr.find('./w:pStyle', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
                        if style is not None and 'val' in style.attrib:
                            style_name = style.attrib['{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val'].lower()
                    
                    # Add to pending paragraphs instead of writing immediately
                    pending_paragraphs.append((text, style_name))
        
        # Write any remaining pending paragraphs at the end
        if pending_paragraphs:
            for paragraph_text, style_name in pending_paragraphs:
                if 'heading' in style_name:
                    txt_file.write('\n' + paragraph_text.upper() + '\n')
                    txt_file.write('=' * len(paragraph_text) + '\n')
                else:
                    txt_file.write("\n" + paragraph_text + '\n')
    
    # Check if we missed any tables by comparing with high-level API
    if len(set(info['id'] for info in extracted_tables.values())) != len(all_tables):
        print(f"Warning: Extracted {len(set(info['id'] for info in extracted_tables.values()))} tables but document contains {len(all_tables)} tables")
        print("Processing remaining tables...")
        
        # Keep track of processed table elements
        processed_elements = set()
        for placeholder_string, table_info in extracted_tables.items():
            if 'api_index' in table_info['position']:
                api_index = table_info['position']['api_index']
                if api_index < len(all_tables):
                    processed_elements.add(all_tables[api_index]._element)
        
        # Process any missed tables
        for i, table in enumerate(all_tables):
            if table._element not in processed_elements:
                current_table_id = f"table_{table_counter}"
                
                # Use Pandoc table if available, otherwise fall back to original method
                if pandoc_tables and (table_counter - 1) < len(pandoc_tables):
                    # Use Pandoc extracted table
                    formatted_table = pandoc_tables[table_counter - 1]
                    print(f"  Using Pandoc table for missed {current_table_id}")
                    
                    # Generate placeholder based on pandoc table content
                    placeholder_string = generate_placeholder_string([[formatted_table]])
                    if not placeholder_string:
                        placeholder_string = f'EmptyTable{table_counter}'
                else:
                    # Fall back to original extraction method
                    print(f"  Using original extraction for missed {current_table_id}")
                    table_data = extract_table_data_api(table)
                    
                    # Generate placeholder string
                    placeholder_string = generate_placeholder_string(table_data)
                    if not placeholder_string:  # Empty table fallback
                        placeholder_string = f'EmptyTable{table_counter}'
                    
                    formatted_table = format_table(table_data) if table_data else "EMPTY TABLE"
                
                table_counter += 1
                
                # Calculate table dimensions
                if pandoc_tables and (table_counter - 2) < len(pandoc_tables):
                    # For Pandoc tables, estimate dimensions from content
                    table_lines = formatted_table.split('\n')
                    data_lines = [line for line in table_lines if line.strip() and '|' in line and not re.match(r'^[\s\-\+\|=]+$', line)]
                    rows = len(data_lines)
                    columns = max([line.count('|') - 1 for line in data_lines]) if data_lines else 0
                    is_empty = rows == 0
                else:
                    # For original method, use table_data
                    rows = len(table_data) if 'table_data' in locals() else 0
                    columns = len(table_data[0]) if 'table_data' in locals() and table_data else 0
                    is_empty = len(table_data) == 0 if 'table_data' in locals() else True

                extracted_tables[placeholder_string] = {
                    'id': current_table_id,
                    'heading': "",  # No heading available for missed tables
                    'formatted_content': formatted_table,
                    'char_count': len(placeholder_string),
                    'position': {
                        'api_index': i,
                        'missed_table': True
                    },
                    'rows': rows,
                    'columns': columns,
                    'is_empty': is_empty,
                    'extraction_method': 'pandoc' if pandoc_tables and (table_counter - 2) < len(pandoc_tables) else 'original'
                }
                
                print(f"Added missed table: {current_table_id} (API index: {i})")
    
    # Final cleanup: ensure all written placeholders are in the index
    print("\nVerifying all written placeholders are in index...")
    for placeholder, table_id in written_placeholders.items():
        if placeholder not in extracted_tables:
            print(f"  WARNING: Written placeholder for {table_id} not in final index!")
            # Try to find the table by ID and add the placeholder mapping
            for p, info in extracted_tables.items():
                if info['id'] == table_id:
                    extracted_tables[placeholder] = info.copy()
                    print(f"  Added missing placeholder mapping for {table_id}")
                    break
    
    # Save extracted tables and create index
    save_extracted_tables(extracted_tables, tables_dir)
    create_table_index(extracted_tables, tables_dir)
    
    # Add missing headings to table files
    add_missing_headings_to_table_files(tables_dir)
    
    # Save figures and create index
    if extracted_figures:
        save_extracted_figures(extracted_figures, figures_dir)
        create_figure_index(extracted_figures, figures_dir)

    print(f"\nExtracted {len(set(info['id'] for info in extracted_tables.values()))} unique tables")
    print(f"Total placeholder mappings (tables): {len(extracted_tables)}")
    print(f"Extracted {len(extracted_figures)} figures")
    return extracted_tables, extracted_figures

def extract_table_data(table_element):
    """
    Extract table data from a DOCX table element using XML parsing.
    
    Args:
        table_element: The table element from the DOCX document
        
    Returns:
        list: Table data as a list of lists (rows and cells)
    """
    table_data = []
    
    rows = table_element.findall('.//w:tr', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
    
    for row in rows:
        row_data = []
        cells = row.findall('.//w:tc', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        
        for cell in cells:
            # Extract text from all text elements in the cell
            text_elements = cell.findall('.//w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
            cell_text = ' '.join(elem.text for elem in text_elements if elem.text)
            cell_text = cell_text.strip()
            row_data.append(cell_text)
        
        # Add all rows, even if they appear empty (they might have formatting)
        table_data.append(row_data)
    
    return table_data

def extract_table_data_api(table):
    """
    Extract table data from a DOCX table using the high-level python-docx API.
    
    Args:
        table: A python-docx Table object
        
    Returns:
        list: Table data as a list of lists (rows and cells)
    """
    table_data = []
    
    for row in table.rows:
        row_data = []
        for cell in row.cells:
            # Get all text from the cell, including from multiple paragraphs
            cell_text = ""
            for paragraph in cell.paragraphs:
                if paragraph.text.strip():
                    if cell_text:
                        cell_text += " " + paragraph.text.strip()
                    else:
                        cell_text = paragraph.text.strip()
            row_data.append(cell_text)
        
        # Add all rows, even if they appear empty
        table_data.append(row_data)
    
    return table_data

def save_extracted_tables(extracted_tables, tables_dir):
    """
    Save each extracted table as a separate text file with formatted content.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save tables
    """
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        formatted_content = table_info['formatted_content']
        table_file_path = os.path.join(tables_dir, f"{table_id}.txt")
        with open(table_file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_content)

def create_table_index(extracted_tables, tables_dir):
    """
    Create an index file with metadata about all extracted tables.
    The mapping now uses placeholder strings as keys and metadata as values.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save the index
    """
    index_data = {}
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        # Store mapping from placeholder to file info
        index_data[placeholder_string] = {
            'id': table_id,
            'heading': table_info.get('heading', ''),
            'char_count': table_info['char_count'],
            'position': table_info['position'],
            'rows': table_info.get('rows', 0),
            'columns': table_info.get('columns', 0),
            'is_empty': table_info.get('is_empty', False),
            'extraction_method': table_info.get('extraction_method', 'unknown'),
            'file': f"{table_id}.txt"
        }
    
    index_file_path = os.path.join(tables_dir, "table_index.json")
    with open(index_file_path, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, indent=2)

def add_missing_headings_to_table_files(tables_dir):
    """
    Add missing table headings to individual table files using information from the JSON index.
    Only adds headings if they're not already present in the file.
    
    Args:
        tables_dir (str): Directory containing the table files and index
    """
    index_file_path = os.path.join(tables_dir, "table_index.json")
    
    if not os.path.exists(index_file_path):
        print("No table index found, skipping heading addition")
        return
    
    # Load the index
    with open(index_file_path, 'r', encoding='utf-8') as f:
        index_data = json.load(f)
    
    print("\nAdding missing table headings...")
    
    for placeholder_string, table_info in index_data.items():
        table_id = table_info['id']
        heading = table_info.get('heading', '').strip()
        table_file = table_info['file']
        table_file_path = os.path.join(tables_dir, table_file)
        
        if not heading or not os.path.exists(table_file_path):
            continue
        
        # Read the current table content
        with open(table_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if heading is already present
        lines = content.split('\n')
        has_heading = False
        
        # Look for heading in first few lines
        for i, line in enumerate(lines[:5]):  # Check first 5 lines
            line_lower = line.strip().lower()
            heading_lower = heading.lower()
            
            # Check if this line contains the table heading
            if line_lower and heading_lower:
                # Method 1: Check if both contain "table" and share significant words
                if ('table' in line_lower and 'table' in heading_lower):
                    # Extract significant words (length > 3) from both
                    line_words = set(word for word in line_lower.split() if len(word) > 3)
                    heading_words = set(word for word in heading_lower.split() if len(word) > 3)
                    
                    # If they share at least 2 significant words, consider it a match
                    if len(line_words.intersection(heading_words)) >= 2:
                        has_heading = True
                        break
                
                # Method 2: Check for direct substring match (at least 20 chars)
                if len(heading_lower) >= 20:
                    # Check if most of the heading appears in the line
                    heading_words = heading_lower.split()
                    if len(heading_words) >= 3:
                        # Check if at least 70% of heading words appear in the line
                        matches = sum(1 for word in heading_words if word in line_lower)
                        if matches / len(heading_words) >= 0.7:
                            has_heading = True
                            break
        
        # Add heading if not present
        if not has_heading:
            print(f"  Adding heading to {table_file}: {heading[:60]}...")
            
            # Prepare the new content with heading
            new_content = heading + '\n\n' + content
            
            # Write back to file
            with open(table_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
        else:
            print(f"  {table_file} already has heading")
    
    print("Finished adding missing headings")

def format_table(table_data, is_continued=False):
    """
    Format a table as ASCII text with borders, handling long cell content properly.
    
    Args:
        table_data (list): Table data as a list of lists
        is_continued (bool): Whether this table is a continuation from previous page
        
    Returns:
        str: Formatted table as ASCII text
    """
    if not table_data or not any(table_data):
        return ""
    
    # Clean the table data
    cleaned_table = []
    for row in table_data:
        cleaned_row = []
        for cell in row:
            cell_str = str(cell).strip() if cell is not None else ""
            cell_str = cell_str.replace('\n', ' ').replace('\r', ' ').strip()
            cleaned_row.append(cell_str)
        cleaned_table.append(cleaned_row)
    
    if not cleaned_table:
        return ""
    
    # Calculate column widths
    max_col_width = 40  # Maximum width for any column
    min_col_width = 10  # Minimum width for any column
    
    # Find the maximum number of columns
    max_cols = max(len(row) for row in cleaned_table)
    
    # Calculate column widths based on content
    col_widths = []
    for col_idx in range(max_cols):
        max_width = min_col_width
        for row in cleaned_table:
            if col_idx < len(row):
                cell_width = len(row[col_idx])
                max_width = max(max_width, cell_width)
        col_widths.append(min(max_width, max_col_width))
    
    output = []
    
    # Create border
    border = '+' + '+'.join('-' * (width + 2) for width in col_widths) + '+'
    
    if is_continued:
        output.append(border)
        continued_note = "| " + "TABLE CONTINUED FROM PREVIOUS SECTION".center(sum(col_widths) + len(col_widths)*3 - 4) + " |"
        output.append(continued_note)
    
    output.append(border)
    
    # Process each row
    for row_idx, row in enumerate(cleaned_table):
        # Ensure row has enough cells
        while len(row) < max_cols:
            row.append("")
        
        # Handle text wrapping for long cells
        wrapped_cells = []
        for i, cell in enumerate(row):
            if i < len(col_widths):
                if len(cell) > col_widths[i]:
                    wrapped_text = textwrap.wrap(cell, col_widths[i])
                    wrapped_cells.append(wrapped_text)
                else:
                    wrapped_cells.append([cell])
            else:
                wrapped_cells.append([cell])
        
        # Find maximum number of lines needed for this row
        max_lines = max(len(cell_lines) for cell_lines in wrapped_cells) if wrapped_cells else 1
        
        # Output each line of the row
        for line_idx in range(max_lines):
            line_cells = []
            for i, cell_lines in enumerate(wrapped_cells):
                if line_idx < len(cell_lines):
                    text = cell_lines[line_idx]
                else:
                    text = ""
                
                if i < len(col_widths):
                    line_cells.append(f" {text:<{col_widths[i]}} ")
                else:
                    line_cells.append(f" {text} ")
            
            output.append('|' + '|'.join(line_cells) + '|')
        
        output.append(border)
    
    return '\n'.join(output)

def debug_table_detection(input_docx_path):
    """
    Debug function to analyze table detection in a DOCX file.
    
    Args:
        input_docx_path (str): Path to input DOCX file
    """
    doc = Document(input_docx_path)
    
    print("=== TABLE DETECTION DEBUG ===")
    print(f"High-level API found: {len(doc.tables)} tables")
    
    # Count table elements in XML
    xml_tables = doc.element.body.findall('.//w:tbl', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
    print(f"XML parsing found: {len(xml_tables)} table elements")
    
    # Count direct body table elements
    direct_tables = [elem for elem in doc.element.body if elem.tag.endswith('tbl')]
    print(f"Direct body tables: {len(direct_tables)}")
    
    # Analyze each table
    for i, table in enumerate(doc.tables):
        rows = len(table.rows)
        cols = len(table.columns) if table.rows else 0
        
        # Check if table has any content
        has_content = False
        for row in table.rows:
            for cell in row.cells:
                if cell.text.strip():
                    has_content = True
                    break
            if has_content:
                break
        
        print(f"Table {i+1}: {rows} rows, {cols} cols, has_content: {has_content}")
    
    print("=== END DEBUG ===\n")

# ------------------------------------------------------------
# FIGURE-HANDLING UTILITIES (added)
# ------------------------------------------------------------

def generate_short_placeholder(prefix, counter):
    """Return a very short placeholder like {F1}."""
    return f"{{{prefix}{counter}}}"


def extract_figure_data(paragraph_element, doc):
    """Extract heading, summary, alt-text, width, height for a paragraph that
    contains one or more images. Returns None if no image present."""

    figure_data = {
        'heading': '',
        'summary': '',
        'alt_text': '',
        'width': None,
        'height': None
    }

    # Detect inline drawings
    inline_shapes = paragraph_element.findall(
        './/wp:inline',
        namespaces={'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing'}
    )

    if not inline_shapes:
        return None

    # Dimensions & alt-text from first inline shape
    for shape in inline_shapes:
        extent = shape.find('.//wp:extent', namespaces={'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing'})
        if extent is not None:
            figure_data['width'] = int(extent.get('cx', 0)) / 914400  # EMU→inch
            figure_data['height'] = int(extent.get('cy', 0)) / 914400

        docPr = shape.find('.//wp:docPr', namespaces={'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing'})
        if docPr is not None:
            figure_data['alt_text'] = docPr.get('descr', '')
        break

    # Heading: look back a few paragraphs for a line starting with "Figure"
    prev = paragraph_element.getprevious()
    while prev is not None and len(figure_data['heading']) < 1000:
        if prev.tag.endswith('p'):
            txt = ' '.join(t.text for t in prev.findall('.//w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}) if t.text).strip()
            if txt.lower().startswith('figure'):
                figure_data['heading'] = txt
                break
        prev = prev.getprevious()

    # Summary: first non-empty paragraph after the image paragraph that is not another heading
    nxt = paragraph_element.getnext()
    while nxt is not None and len(figure_data['summary']) < 1000:
        if nxt.tag.endswith('p'):
            txt = ' '.join(t.text for t in nxt.findall('.//w:t', {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}) if t.text).strip()
            if txt and not txt.lower().startswith('figure'):
                figure_data['summary'] = txt
                break
        nxt = nxt.getnext()

    return figure_data


# --- figure image extraction helper ---
def extract_and_save_images(paragraph_element, doc, figures_dir, figure_id):
    """Save images inside the given paragraph to files, return list of filenames."""
    image_files = []

    blips = paragraph_element.findall(
        './/a:blip',
        namespaces={
            'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
            'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
        }
    )

    for idx, blip in enumerate(blips, 1):
        rid = blip.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
        if not rid:
            continue
        image_part = doc.part.related_parts.get(rid)
        if image_part is None:
            continue

        content_type = image_part.content_type  # e.g. image/png
        ext = content_type.split('/')[-1]
        if ext == 'jpeg':
            ext = 'jpg'

        filename = f"{figure_id}_{idx}.{ext}"
        filepath = os.path.join(figures_dir, filename)
        dup = 1
        while os.path.exists(filepath):
            filename = f"{figure_id}_{idx}_{dup}.{ext}"
            filepath = os.path.join(figures_dir, filename)
            dup += 1

        with open(filepath, 'wb') as img_f:
            img_f.write(image_part.blob)

        image_files.append(filename)

    return image_files


# ------------------------------------------------------------
# END FIGURE UTILITIES
# ------------------------------------------------------------

# ------------------------------------------------------------
# FIGURE OUTPUT HELPERS
# ------------------------------------------------------------

def save_extracted_figures(extracted_figures, figures_dir):
    """Write each figure's metadata and summary to its own .txt file."""
    for placeholder, info in extracted_figures.items():
        fig_path = os.path.join(figures_dir, info['file'])
        with open(fig_path, 'w', encoding='utf-8') as f:
            if info['heading']:
                f.write(info['heading'] + '\n\n')
            if info.get('images'):
                f.write('Images: ' + ', '.join(info['images']) + '\n')
            if info.get('alt_text'):
                f.write(f"Alt Text: {info['alt_text']}\n")
            if info['dimensions']['width'] and info['dimensions']['height']:
                f.write(f"Dimensions: {info['dimensions']['width']:.2f}\" x {info['dimensions']['height']:.2f}\"\n")
            if info.get('summary'):
                f.write('\n' + info['summary'])


def create_figure_index(extracted_figures, figures_dir):
    """Create JSON index mapping figure placeholders to metadata."""
    index_path = os.path.join(figures_dir, 'figure_index.json')
    with open(index_path, 'w', encoding='utf-8') as f:
        json.dump(extracted_figures, f, indent=2)

# ------------------------------------------------------------
# END FIGURE OUTPUT HELPERS
# ------------------------------------------------------------

# Get the absolute path to the workspace root
workspace_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))

# Construct absolute paths for input and output files
docx_path = "/Users/<USER>/projects/scalegen/MedNova/Pre-IND/Input Docs/Pre-IND-Sample.docx"
output_path = "/Users/<USER>/projects/scalegen/MedNova/Pre-IND/Input Docs/Pre-IND-Sample.txt"

# Simple CLI usage: if the script is executed with two positional arguments they are treated as input and output paths.
if __name__ == "__main__":
    import sys
    if len(sys.argv) >= 3:
        in_docx = sys.argv[1]
        out_txt = sys.argv[2]
    else:
        in_docx = docx_path
        out_txt = output_path

    # debug_table_detection(in_docx)  # Uncomment for diagnostics
    extracted_tables, extracted_figures = process_docx_to_txt(in_docx, out_txt)
    print(f"Finished. Tables={len(extracted_tables)}, Figures={len(extracted_figures)}")