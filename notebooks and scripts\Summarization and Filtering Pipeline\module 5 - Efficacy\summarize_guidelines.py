import os
import json
import asyncio
from openai import AsyncOpenAI
from dotenv import load_dotenv
import glob
from db_utils import insert_chunks_in_db_for_efficacy_guideline, get_mongodb_client
from bson.objectid import ObjectId
from typing import List, Dict, Any
from transformers import AutoTokenizer
from retry_on_empty_response import retry_on_empty_response

# Load environment variables
load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

print(GENERATOR_MODEL_NAME, GENERATOR_MODEL_BASE_URL, GENERATOR_MODEL_API_KEY)

# Maximum token limit for input (keeping some buffer for the prompt and output)
MAX_TOKEN_LIMIT = 20000

# Initialize AsyncOpenAI client
openai_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL,
    api_key=GENERATOR_MODEL_API_KEY
)

# Initialize tokenizer
tokenizer = None

def extract_final_summary_from_tags(content: str) -> str:
    """
    Extract final summary from <final_summary> tags if present, otherwise return content as is.
    
    Args:
        content: The raw content from the LLM response
        
    Returns:
        Extracted final summary or original content if tags not found
    """
    if not content:
        return content
        
    # Look for <final_summary> tags
    import re
    summary_match = re.search(r'<final_summary>(.*?)</final_summary>', content, re.DOTALL | re.IGNORECASE)
    
    if summary_match:
        # Extract and clean the summary content
        summary = summary_match.group(1).strip()
        return summary
    else:
        # Return original content if no tags found
        return content

def calculate_number_of_tokens(text):
    """Calculate the number of tokens in a text using Mistral tokenizer."""
    global tokenizer
    if tokenizer is None:
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")
    
    tokens = tokenizer.encode(text, add_special_tokens=False)
    return len(tokens)

def read_chunk(file_path):
    """Read a chunk file and return its contents."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

async def get_individual_summary(chunk_text: str) -> str:
    """Get summary of a single chunk using the LLM."""
    system_prompt = """You are an expert in pharmaceutical efficacy guidelines and regulatory compliance. Your task is to provide an exhaustive yet concise summary that captures EVERY piece of information from the provided text, while maintaining organization and clarity.

    OUTPUT FORMAT:
    - Provide the summary in plain text format only
    - Do not use any special formatting, markdown, or symbols
    - Use simple bullet points with hyphens (-) when needed
    - Separate paragraphs with blank lines
    - Use parentheses for references or additional information

    CRITICAL REQUIREMENTS:
    1. Information Capture and Conciseness:
       - Retain 100% of ALL critical information - no exceptions
       - Use precise, clear language to minimize verbosity
       - Avoid redundant or repetitive statements

    2. Decision Tree Handling:
       - When encountering a decision tree:
         * Present it in a clear, linear text format
         * Use indentation to show decision hierarchy
         * List conditions and outcomes clearly
         * Use "If/Then" statements for clarity
         * Include only essential explanatory notes

    3. Technical Accuracy and Efficiency:
       - Use precise technical terminology
       - Present specifications and requirements clearly
       - Include exact numerical values and units
       - Document methods and criteria efficiently
       - Present validation requirements concisely"""
    
    user_prompt = f"""Please provide an exhaustive yet concise summary in plain text format for the following content. The summary must capture EVERY important piece of information while staying efficient and clear.

    Content to Summarize:
    {chunk_text}
    
    Format Requirements:
    - Use plain text only - no special formatting or markdown
    - Use clear numbered sections
    - Use simple indentation for hierarchy
    - Use hyphens (-) for bullet points
    - Separate paragraphs with blank lines
    - Present decision trees in a clear, linear format using indentation and If/Then statements

    Content Requirements:
    1. Capture ALL critical information without unnecessary verbosity
    2. Include complete decision trees and logic in plain text format
    3. Present technical specifications and requirements clearly
    4. State numerical values and thresholds precisely
    5. Document regulatory requirements concisely"""

    async def make_api_call():
        response = await openai_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            temperature=0.2,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )
        
        return response.choices[0].message.content
    
    raw_summary = await retry_on_empty_response(make_api_call)
    
    # Extract summary from tags if present
    if raw_summary is None:
        summary = ""
    else:
        summary = extract_final_summary_from_tags(raw_summary)
    
    return summary

async def create_final_summary(individual_summaries: List[str]) -> str:
    """Create a final summary from all individual summaries by combining multiple summaries at once while respecting token limits."""
    
    print(f"\nStarting final summary creation with {len(individual_summaries)} individual summaries")
    
    # Handle empty input
    if not individual_summaries:
        print("No summaries provided, returning empty string")
        return ""
        
    # Handle single summary case
    if len(individual_summaries) == 1:
        print("Only one summary provided, returning it as is")
        return individual_summaries[0]
    
    async def combine_summaries(summaries_to_combine):
        """Helper function to combine multiple summaries while preserving all information."""
        total_summaries = len(summaries_to_combine)
        print(f"\nCombining {total_summaries} summaries:")
        
        total_tokens = sum(calculate_number_of_tokens(summary) for summary in summaries_to_combine)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are an expert in pharmaceutical efficacy guidelines and regulatory compliance. Your task is to combine multiple summaries into one comprehensive combined summary. CRITICAL: You must preserve ALL information from all summaries - no information should be lost or omitted.

        OUTPUT FORMAT:
        - Provide the summary in plain text format only
        - Do not use any special formatting, markdown, or symbols
        - Use simple bullet points with hyphens (-) when needed
        - Separate paragraphs with blank lines
        - Use parentheses for references or additional information
        - Length is not a concern - include ALL details

        CRITICAL REQUIREMENTS:
        1. Information Preservation (HIGHEST PRIORITY):
           - Preserve EVERY piece of information from all summaries
           - Do not omit or summarize any details
           - Include all specific numbers, measurements, and criteria
           - Maintain all technical specifications
           - Keep all references and citations
           - Preserve all examples and case studies
           - Include all requirements and recommendations
           - Maintain all validation and verification details
           - Keep all risk assessments and considerations
           - Preserve all cross-references and dependencies

        2. Integration Strategy:
           - Merge content from all summaries efficiently
           - Maintain logical flow while preserving all details
           - Avoid duplicating information
           - Preserve cross-references without redundancy
           - Document dependencies comprehensively
           - Focus on creating a cohesive, unified document
           - Use clear transitions between different topics
           - Maintain chronological order where relevant
           - Preserve hierarchical relationships
           - Keep all contextual information

        3. Quality Assurance:
           - Verify that no information is lost
           - Ensure all technical terms are preserved
           - Maintain all regulatory requirements
           - Keep all compliance-related information
           - Preserve all quality control measures
           - Maintain all safety considerations
           - Keep all efficacy-related information
           - Preserve all manufacturing details
           - Maintain all storage and handling instructions
           - Keep all stability information"""

        user_prompt = f"""Please create a comprehensive combined summary that combines the following {total_summaries} summaries into one cohesive document. CRITICAL: You must preserve ALL information from all summaries - no information should be lost or omitted.

        """
        
        # Add each summary with a header
        for i, summary in enumerate(summaries_to_combine, 1):
            user_prompt += f"""
        Summary {i}:
        {summary}
        
        """
        
        user_prompt += f"""
        VERIFICATION CHECKLIST:
        1. Does the combined summary include ALL information from all {total_summaries} summaries?
        2. Are ALL specific details, numbers, and measurements preserved?
        3. Are ALL technical specifications maintained?
        4. Are ALL references and citations included?
        5. Are ALL examples and case studies preserved?
        6. Are ALL requirements and recommendations kept?
        7. Are ALL validation and verification details included?
        8. Are ALL risk assessments preserved?
        9. Are ALL cross-references maintained?
        10. Is ALL contextual information preserved?

        Requirements:
        1. Preserve ALL information from all {total_summaries} summaries
        2. Do not omit or summarize any details
        3. Include all specific numbers, measurements, and criteria
        4. Maintain all technical specifications
        5. Keep all references and citations
        6. Preserve all examples and case studies
        7. Include all requirements and recommendations
        8. Maintain all validation and verification details
        9. Keep all risk assessments and considerations
        10. Preserve all cross-references and dependencies
        11. Ensure logical flow and organization
        12. Preserve technical accuracy and precision
        13. Length is not a concern - include ALL details

        After creating the combined summary, verify that ALL information from all summaries has been preserved by checking against the verification checklist."""

        print(f"Sending request to LLM to combine {total_summaries} summaries...")
        
        async def make_api_call():
            response = await openai_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )
            return response.choices[0].message.content
        
        raw_combined_summary = await retry_on_empty_response(make_api_call)
        
        # Extract final summary from tags if present
        if raw_combined_summary is None:
            combined_summary = ""
        else:
            combined_summary = extract_final_summary_from_tags(raw_combined_summary)
        
        print(f"Combined summary length: {len(combined_summary)} characters")
        print(f"Combined summary contains {len(combined_summary.split())} words")
        print(f"Combined summary token count: {calculate_number_of_tokens(combined_summary)}")
        
        return combined_summary

    # Process summaries iteratively, combining optimal batches in parallel
    current_summaries = individual_summaries.copy()
    iteration = 1
    
    while len(current_summaries) > 1:
        print(f"\nIteration {iteration}:")
        print(f"Number of summaries to process: {len(current_summaries)}")
        
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for summary in current_summaries:
            summary_tokens = calculate_number_of_tokens(summary)
            
            # If adding this summary would exceed the token limit, start a new batch
            if current_batch and current_token_count + summary_tokens > MAX_TOKEN_LIMIT:
                batches.append(current_batch)
                current_batch = [summary]
                current_token_count = summary_tokens
            else:
                current_batch.append(summary)
                current_token_count += summary_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for processing")
        for i, batch in enumerate(batches):
            print(f"Batch {i+1} contains {len(batch)} summaries with {sum(calculate_number_of_tokens(s) for s in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_summaries(batch) for batch in batches]
        
        # Wait for all combinations to complete
        print("Waiting for all batch combinations to complete...")
        new_summaries = await asyncio.gather(*tasks)
        current_summaries = new_summaries
        
        print(f"Iteration {iteration} complete. {len(current_summaries)} summaries remaining")
        iteration += 1
    
    print(f"\nFinal summary creation complete. Final summary length: {len(current_summaries[0])} characters")
    print(f"Final summary contains {len(current_summaries[0].split())} words")
    print(f"Final summary token count: {calculate_number_of_tokens(current_summaries[0])}")
    
    return current_summaries[0]

async def process_chunk(chunk_file: str, efficacy_guideline: str) -> Dict[str, Any]:
    """Process a single chunk and return its summary and metadata."""
    print(f"Processing individual summary for {os.path.basename(chunk_file)}...")
    
    # Read the chunk
    chunk_text = read_chunk(chunk_file)
    
    # Store original chunk in MongoDB
    chunk_id = insert_chunks_in_db_for_efficacy_guideline(
        chunks_data=chunk_text,
        chunk_filename=os.path.basename(chunk_file),
        efficacy_guideline=efficacy_guideline,
        is_summary=False
    )
    
    # Get individual summary
    summary = await get_individual_summary(chunk_text)
    
    # Store the individual summary in MongoDB
    summary_id = insert_chunks_in_db_for_efficacy_guideline(
        chunks_data=summary,
        chunk_filename=None,
        efficacy_guideline=efficacy_guideline,
        is_summary=True,
        summary_level=2,
        chunk_reference_ids=[str(chunk_id)]
    )
    
    return {
        "chunk": os.path.basename(chunk_file),
        "individual_summary": summary,
        "chunk_id": str(chunk_id),
        "summary_id": str(summary_id)
    }

async def process_chunks(chunks_dir: str) -> Dict[str, Any]:
    """Process all chunks in parallel and create a final summary."""
    # Get all chunk files and sort them numerically
    chunk_files = sorted(
        glob.glob(os.path.join(chunks_dir, "chunk_*.txt")),
        key=lambda x: int(os.path.basename(x).split('_')[1].split('.')[0])
    )
    
    # Extract efficacy guideline from directory path
    efficacy_guideline = chunks_dir.split("/")[-1].split("chunks")[0].strip()
    
    # Process all chunks in parallel
    tasks = [process_chunk(chunk_file, efficacy_guideline) for chunk_file in chunk_files]
    all_summaries = await asyncio.gather(*tasks)
    
    # Extract individual summaries for final summary creation
    individual_summaries = [summary["individual_summary"] for summary in all_summaries]
    
    # Create final summary
    print("\nCreating final summary...")
    final_summary = await create_final_summary(individual_summaries)
    
    # Store final summary in MongoDB
    final_summary_id = insert_chunks_in_db_for_efficacy_guideline(
        chunks_data=final_summary,
        chunk_filename=None,
        efficacy_guideline=efficacy_guideline,
        is_summary=True,
        summary_level=1,
        summary_reference_ids=[summary["summary_id"] for summary in all_summaries]
    )
    
    # Save all summaries to a JSON file
    output_file = os.path.join(chunks_dir, "all_summaries.json")
    final_data = {
        "individual_summaries": all_summaries,
        "final_summary": final_summary,
        "final_summary_id": str(final_summary_id)
    }
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(final_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nAll summaries saved to {output_file}")
    
    # Save final summary to a separate file
    with open(os.path.join(chunks_dir, "final_summary.txt"), 'w', encoding='utf-8') as f:
        f.write(final_summary)
    
    print(f"Final summary saved to {os.path.join(chunks_dir, 'final_summary.txt')}")
    
    return final_data

def get_missing_guidelines(base_dir: str) -> List[str]:
    """Check for efficacy guidelines that exist in local folders but are missing from MongoDB."""
    # Get all directories that end with 'chunks' and start with 'E'
    local_guidelines = [d[:-7] for d in os.listdir(base_dir) 
                       if os.path.isdir(os.path.join(base_dir, d)) 
                       and d.startswith('E') 
                       and d.endswith('chunks')]
    
    # Get all unique efficacy guidelines from MongoDB
    client = get_mongodb_client()
    db = client['mednova']
    chunks_collection = db['efficacy_docs_chunks']
    summaries_collection = db['efficacy_docs_summaries']
    
    # Get unique guidelines from both collections
    mongo_guidelines = set()
    for doc in chunks_collection.find({}, {"efficacy_guideline": 1}):
        mongo_guidelines.add(doc["efficacy_guideline"])
    for doc in summaries_collection.find({}, {"efficacy_guideline": 1}):
        mongo_guidelines.add(doc["efficacy_guideline"])
    
    client.close()
    
    # Find guidelines that are in local folders but not in MongoDB
    missing_guidelines = list(set(local_guidelines) - mongo_guidelines)
    
    return missing_guidelines

async def main():
    base_dir = "Input Docs/Efficacy Guidelines"
    
    # Check for missing guidelines
    missing_guidelines = get_missing_guidelines(base_dir)
    if missing_guidelines:
        print("\nThe following efficacy guidelines exist in local folders but are missing from MongoDB:")
        for guideline in sorted(missing_guidelines):
            print(f"- {guideline}")
        print("\nProcessing these missing guidelines...")
        
        # Process only the missing guidelines
        for guideline in sorted(missing_guidelines):
            chunks_dir = os.path.join(base_dir, f"{guideline} chunks")
            if not os.path.exists(chunks_dir):
                print(f"Warning: Directory {chunks_dir} not found. Skipping {guideline}")
                continue
                
            print(f"\nProcessing efficacy guideline: {guideline}")
            try:
                await process_chunks(chunks_dir)
                print(f"Successfully processed {guideline}")
            except Exception as e:
                print(f"Error processing {guideline}: {str(e)}")
                continue
        
        print("\nAll missing guidelines have been processed.")
        return
    
    # If no missing guidelines, ask user before processing all directories
    print("\nNo missing guidelines found. All guidelines are up to date in MongoDB.")
    print("Would you like to process all guidelines anyway? (y/n)")
    response = input().strip().lower()
    if response != 'y':
        print("Exiting...")
        return
    
    # Get all directories that end with 'chunks' and start with 'E'
    chunks_dirs = [d for d in os.listdir(base_dir) 
                  if os.path.isdir(os.path.join(base_dir, d)) 
                  and d.startswith('E') 
                  and d.endswith('chunks')]
    
    print(f"\nFound {len(chunks_dirs)} efficacy guideline directories to process")
    
    for chunks_dir_name in chunks_dirs:
        # Extract the efficacy guideline name by removing ' chunks' from the end
        efficacy_guideline = chunks_dir_name[:-7]  # Remove ' chunks' (7 characters)
        chunks_dir = os.path.join(base_dir, chunks_dir_name)
        
        print(f"\nProcessing efficacy guideline: {efficacy_guideline}")
        try:
            await process_chunks(chunks_dir)
            print(f"Successfully processed {efficacy_guideline}")
        except Exception as e:
            print(f"Error processing {efficacy_guideline}: {str(e)}")
            continue

if __name__ == "__main__":
    asyncio.run(main()) 