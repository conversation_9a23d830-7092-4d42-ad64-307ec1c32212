from docx import Document
from docx.document import Document as DocumentObject
from docx.shared import Inches
from copy import deepcopy
import os
import shutil
import zipfile
from docx.oxml.ns import qn
from docx.table import Table
from docx.section import Paragraph
from typing import List
import json

def remove_all_tables(doc: DocumentObject):
    body = doc.element.body
    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]
    for tbl in tables_to_remove:
        body.remove(tbl)
        
def extract_images(docx_path, extract_folder):
    with zipfile.ZipFile(docx_path, 'r') as zip_ref:
        zip_ref.extractall(extract_folder)

def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:
    return [para for para in doc.paragraphs if search_text in para.text]

def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with heading and line break after"""
    # Create a new paragraph for the heading before the table tag paragraph
    heading_paragraph = insert_after_paragraph.insert_paragraph_before()
    heading_run = heading_paragraph.add_run(table_heading)
    heading_run.bold = True
    
    # Copy the table after the table tag paragraph
    tbl_element = source_table._tbl
    tbl_copy = deepcopy(tbl_element)
    insert_after_paragraph._element.addnext(tbl_copy)
    
    # Insert an empty paragraph after the table (acts as a line break)
    from docx.oxml import OxmlElement
    p = OxmlElement('w:p')
    tbl_copy.addnext(p)
    
    # Clear the table tag paragraph content instead of removing it
    insert_after_paragraph.clear()

def load_table_headings(json_file_path: str):
    """Load table headings from JSON file"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        table_index = json.load(f)
    
    table_headings = {}
    for table_hash, table_info in table_index.items():
        table_id = table_info['id']
        table_heading = table_info['heading']
        table_headings[table_id] = table_heading
    
    return table_headings

def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):
    run = paragraph.insert_paragraph_before().add_run()
    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed

def main():
    source_docx = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report.docx'
    target_docx = '/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/formatted_answer.docx'
    extract_folder = 'source_extracted'
    
    # Path for table headings
    json_file_path = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report_tables copy/table_index.json'

    # Step 1: Extract images from source.docx
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
    extract_images(source_docx, extract_folder)

    media_folder = os.path.join(extract_folder, 'word', 'media')
    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []

    # Load table headings from JSON file
    table_headings = load_table_headings(json_file_path)
    print(f"📊 Loaded headings for {len(table_headings)} tables from JSON file")

    source_doc = Document(source_docx)
    target_doc = Document(target_docx)

    # Step 1.5: Remove all existing tables from target doc
    remove_all_tables(target_doc)
    print("🗑️ Removed all existing tables from target document")

    # Step 2: Process tables with headings and line break after
    # First, collect all table tag matches to avoid displacement issues
    table_matches = []
    for idx, source_table in enumerate(source_doc.tables, start=1):
        table_id = f"table_{idx}"
        reference_text = f"`<TABLE_TAG_{table_id}>`"
        
        # Get the heading for this table from JSON, fallback to generic heading
        table_heading = table_headings.get(table_id, f"Table {idx}")
        
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        if matched_paragraphs:
            for para in matched_paragraphs:
                table_matches.append((para, source_table, table_heading, reference_text, idx))
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")
    
    # Process matches in reverse order to avoid displacement issues
    for para, source_table, table_heading, reference_text, idx in reversed(table_matches):
        copy_table_with_heading_and_break(source_table, para, table_heading)
        print(f"✅ Inserted Table {idx} with heading '{table_heading}' + line break after '{reference_text}'")

    # Step 3: Process figures/images
    for idx, image_file in enumerate(media_files, start=1):
        reference_text = f"Figure {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        image_path = os.path.join(media_folder, image_file)
        if matched_paragraphs:
            for para in matched_paragraphs:
                insert_image_before_paragraph(target_doc, image_path, para)
            print(f"✅ Inserted Figure {idx} before all '{reference_text}' occurrences")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 4: Save output
    target_doc.save('/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/table_and_image_formatted_answer.docx')
    print("✅ All done. Saved as 'table_and_image_formatted_answer.docx'")
    
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
        print(f"🗑️ Cleaned up temporary folder: {extract_folder}")

if __name__ == "__main__":
    main()