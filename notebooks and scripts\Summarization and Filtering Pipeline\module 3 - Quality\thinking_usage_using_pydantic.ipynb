{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/projects/scalegen/virtualenvs/NDA/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GENERATOR_MODEL_NAME agentic-large\n", "GENERATOR_MODEL_BASE_URL https://api.theagentic.ai/v1\n", "GENERATOR_MODEL_API_KEY kYElszzLTUKUT16jsdCPcamIcpABo7D3\n", "CRITIQUE_MODEL_NAME agentic-large\n", "CRITIQUE_MODEL_BASE_URL https://api.theagentic.ai/v1\n", "CRITIQUE_MODEL_API_KEY kYElszzLTUKUT16jsdCPcamIcpABo7D3\n"]}], "source": ["import re\n", "import json\n", "import os\n", "from openai import OpenAI, AsyncOpenAI\n", "from dotenv import load_dotenv\n", "from pymongo import MongoClient\n", "from bson.objectid import ObjectId\n", "from typing import List, Dict, Any, Tuple\n", "import numpy as np\n", "import asyncio\n", "from transformers import AutoTokenizer\n", "import traceback  # Added import for stack trace information\n", "import time  # Added import for timestamp logging\n", "import inspect    # For frame inspection\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent\n", "from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings\n", "from pydantic_ai.providers.anthropic import AnthropicProvider\n", "from pydantic_ai.models.openai import OpenAIModel, OpenAIResponsesModel, OpenAIResponsesModelSettings, ReasoningEffort\n", "from pydantic_ai.providers.openai import OpenAIProvider\n", "\n", "load_dotenv()\n", "\n", "GENERATOR_MODEL_NAME = os.getenv(\"GENERATOR_MODEL_NAME\")\n", "GENERATOR_MODEL_BASE_URL = os.getenv(\"GENERATOR_MODEL_BASE_URL\")\n", "GENERATOR_MODEL_API_KEY = os.getenv(\"GENERATOR_MODEL_API_KEY\")\n", "\n", "CRITIQUE_MODEL_NAME = os.getenv(\"CRITIQUE_MODEL_NAME\")\n", "CRITIQUE_MODEL_BASE_URL = os.getenv(\"CRITIQUE_MODEL_BASE_URL\")\n", "CRITIQUE_MODEL_API_KEY = os.getenv(\"CRITIQUE_MODEL_API_KEY\")\n", "\n", "INCLUDE_JSON_OUTPUT_TAGS = False\n", "\n", "TOPMOST_SUMMARY_THRESHOLD = 3.0\n", "MIDLEVEL_SUMMARY_THRESHOLD = 5.0\n", "CHUNK_SUMMARY_THRESHOLD = 7.0\n", "\n", "MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000\n", "MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000\n", "\n", "critique_client = AsyncOpenAI(\n", "    base_url=CRITIQUE_MODEL_BASE_URL, \n", "    api_key=CRITIQUE_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "generator_client = AsyncOpenAI(\n", "    base_url=GENERATOR_MODEL_BASE_URL, \n", "    api_key=GENERATOR_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "\n", "critique_model = AnthropicModel(\n", "    model_name=CRITIQUE_MODEL_NAME,\n", "    provider=AnthropicProvider(\n", "        api_key=CRITIQUE_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "generator_model = OpenAIModel(\n", "    model_name=GENERATOR_MODEL_NAME,\n", "    provider=OpenAIProvider(\n", "        base_url=GENERATOR_MODEL_BASE_URL,\n", "        api_key=GENERATOR_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "print(\"GENERATOR_MODEL_NAME\", GENERATOR_MODEL_NAME)\n", "print(\"GENERATOR_MODEL_BASE_URL\", GENERATOR_MODEL_BASE_URL)\n", "print(\"GENERATOR_MODEL_API_KEY\", GENERATOR_MODEL_API_KEY)\n", "\n", "print(\"CRITIQUE_MODEL_NAME\", CRITIQUE_MODEL_NAME)\n", "print(\"CRITIQUE_MODEL_BASE_URL\", CRITIQUE_MODEL_BASE_URL)\n", "print(\"CRITIQUE_MODEL_API_KEY\", CRITIQUE_MODEL_API_KEY)\n", "\n", "# Pydantic schema for structured output\n", "class CheckpointList(BaseModel):\n", "    \"\"\"Schema for checkpoint list generation\"\"\"\n", "    checkpoints: List[str] = Field(\n", "        description=\"List of specific checkpoint questions that should be answered in summaries for this section\",\n", "        min_items=2\n", "    )\n", "\n", "class RelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for summary relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers the checkpoint questions\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "\n", "class CritiqueEvaluation(BaseModel):\n", "    \"\"\"Schema for NDA critique evaluation\"\"\"\n", "    overall_rating: float = Field(\n", "        description=\"Overall rating from 1-10 for the NDA section quality\",\n", "        ge=1.0,\n", "        le=10.0\n", "    )\n", "    key_strengths: List[str] = Field(\n", "        description=\"List of main strengths identified in the NDA section\",\n", "        default=[]\n", "    )\n", "    critical_issues: List[str] = Field(\n", "        description=\"List of critical issues that need to be addressed\",\n", "        default=[]\n", "    )\n", "    required_improvements: List[str] = Field(\n", "        description=\"List of specific improvements required\",\n", "        default=[]\n", "    )\n", "    additional_recommendations: List[str] = Field(\n", "        description=\"List of additional recommendations for enhancement\",\n", "        default=[]\n", "    )\n", "\n", "def get_json_output_tags():\n", "    if INCLUDE_JSON_OUTPUT_TAGS:\n", "        return \"Give your json in <text_output> json </text_output> tags.\"\n", "    else:\n", "        return \"\"\n", "\n", "async def check_if_llm_is_available(client: AsyncOpenAI, model: str):\n", "    try:\n", "        response = await client.chat.completions.create(model=model, messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "            {\"role\": \"user\", \"content\": \"Hello, world!\"}\n", "        ])\n", "        print(f\"LLM {model} is available, response: {response.choices[0].message.content}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error in checking if LLM {model} is available: {e}\")\n", "        return False\n", "\n", "def get_relevance_threshold(summary_level):\n", "    if summary_level == 1:\n", "        return TOPMOST_SUMMARY_THRESHOLD\n", "    elif summary_level == 2:\n", "        return MIDLEVEL_SUMMARY_THRESHOLD\n", "    else:\n", "        return CHUNK_SUMMARY_THRESHOLD\n", "\n", "def write_to_json(data, filename):\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, indent=2, ensure_ascii=False)\n", "    print(f\"JSON saved to {filename}\")\n", "\n", "def read_json(filename):\n", "    with open(filename, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "def get_mongodb_client():\n", "    \"\"\"Get MongoDB client connection.\"\"\"\n", "    return MongoClient(os.getenv(\"MONGO_DB_URL\"))\n", "\n", "def parse_json_response(response_text: str) -> Any:\n", "    \"\"\"\n", "    Parse a JSON response that may be wrapped in backticks.\n", "    \n", "    Args:\n", "        response_text: The response text to parse\n", "        \n", "    Returns:\n", "        Any: The parsed JSON object\n", "    \"\"\"\n", "    # Remove any markdown code block syntax\n", "    response_text = re.sub(r'```json\\n?', '', response_text)\n", "    response_text = re.sub(r'```\\n?', '', response_text)\n", "    response_text = response_text.strip()\n", "    \n", "    try:\n", "        # print(f\"parse_json_response, response_text: {json.loads(response_text)}\")\n", "        return json.loads(response_text)\n", "    except json.JSONDecodeError:\n", "        # If the response is not valid JSON, try to extract a list from the text\n", "        # Look for lines that start with numbers, bullets, or dashes\n", "        lines = re.findall(r'^[\\d\\-\\*\\.]+\\.?\\s*(.+)$', response_text, re.MULTILINE)\n", "        if lines:\n", "            return lines\n", "        # If no lines found, split by newlines and clean up\n", "        # print(f\"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\\n') if line.strip()]}\")\n", "        return [line.strip() for line in response_text.split('\\n') if line.strip()]\n", "    \n", "def calculate_number_of_tokens(text):\n", "    # Load tokenizer for Mistral model\n", "    tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-32B\")\n", "\n", "    def count_tokens(text):\n", "        tokens = tokenizer.encode(text, add_special_tokens=False)\n", "        return len(tokens)\n", "\n", "    token_count = count_tokens(text)\n", "    return token_count\n", "\n", "async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):\n", "    \"\"\"\n", "    Wrapper function that retries an async LLM API call when the response is empty.\n", "    \n", "    Args:\n", "        async_func: The async function to call (usually an LLM API call)\n", "        *args: Positional arguments to pass to async_func\n", "        max_retries: Maximum number of retry attempts (default: 3)\n", "        **kwargs: Keyword arguments to pass to async_func\n", "        \n", "    Returns:\n", "        The result of the async_func call, ensuring it's not empty\n", "        \n", "    Raises:\n", "        Exception: If max_retries is reached and the response is still empty\n", "    \"\"\"\n", "    # Create logs directory if it doesn't exist\n", "    log_dir = \"error_logs\"\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    # Extract the function name for logging purposes\n", "    func_name = async_func.__name__ if hasattr(async_func, \"__name__\") else \"unknown_function\"\n", "    \n", "    # Try to get the caller's name from the stack\n", "    try:\n", "        caller_frame = inspect.currentframe().f_back\n", "        caller_name = caller_frame.f_code.co_name if caller_frame else \"unknown_caller\"\n", "    except Exception:\n", "        caller_name = \"unknown_caller\"\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            result = await async_func(*args, **kwargs)\n", "            \n", "            # Check if result is None or an empty string or just whitespace\n", "            if result is None or (isinstance(result, str) and result.strip() == \"\"):\n", "                print(f\"Warning: Received empty response from LLM (attempt {attempt+1}/{max_retries}), retrying...\")\n", "                \n", "                # Get debug information to log\n", "                debug_info = {\n", "                    'error_type': 'empty_response',\n", "                    'function': func_name,\n", "                    'caller': caller_name,\n", "                    'attempt': attempt + 1,\n", "                    'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "                }\n", "                \n", "                # Extract prompt information based on different API patterns\n", "                # For the direct messages pattern in kwargs\n", "                if 'messages' in kwargs:\n", "                    debug_info['messages'] = kwargs['messages']\n", "                    \n", "                # For the pattern where the func is a closure with local make_api_call\n", "                # Try to get source code of the async_func to check for patterns\n", "                try:\n", "                    source = inspect.getsource(async_func)\n", "                    if \"chat.completions.create\" in source:\n", "                        debug_info['api_pattern'] = \"chat_completions_closure\"\n", "                except Exception:\n", "                    pass\n", "                \n", "                # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "                try:\n", "                    if caller_frame:\n", "                        caller_locals = caller_frame.f_locals\n", "                        # Capture common patterns in this codebase\n", "                        if 'system_prompt' in caller_locals:\n", "                            debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                        # If this is using the OpenAI client pattern, get the model too\n", "                        if 'model' in caller_locals:\n", "                            debug_info['model'] = caller_locals['model']\n", "                        # For the antropic calls\n", "                        if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                        elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                        elif 'M<PERSON>' in caller_locals:\n", "                            debug_info['model'] = caller_locals['MODEL']\n", "                except Exception as e:\n", "                    debug_info['frame_inspection_error'] = str(e)\n", "                \n", "                # Save the debug information\n", "                timestamp = int(time.time())\n", "                log_filename = f\"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "                \n", "                try:\n", "                    with open(log_filename, 'w', encoding='utf-8') as f:\n", "                        json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                    print(f\"Logged empty response details to {log_filename}\")\n", "                except Exception as log_error:\n", "                    print(f\"Failed to log empty response details: {str(log_error)}\")\n", "                \n", "                # Continue to the next retry attempt\n", "                continue\n", "                \n", "            # If we get here, we have a non-empty response\n", "            return result\n", "            \n", "        except Exception as e:\n", "            error_type = type(e).__name__\n", "            error_msg = str(e)\n", "            print(f\"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}\")\n", "            \n", "            # Get debug information to log\n", "            debug_info = {\n", "                'error_type': error_type,\n", "                'error_message': error_msg,\n", "                'function': func_name,\n", "                'caller': caller_name,\n", "                'attempt': attempt + 1,\n", "                'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                'stack_trace': traceback.format_exc()\n", "            }\n", "            \n", "            # Extract prompt information based on different API patterns\n", "            # For the direct messages pattern in kwargs\n", "            if 'messages' in kwargs:\n", "                debug_info['messages'] = kwargs['messages']\n", "                \n", "            # For the pattern where the func is a closure with local make_api_call\n", "            # Try to get source code of the async_func to check for patterns\n", "            try:\n", "                source = inspect.getsource(async_func)\n", "                if \"chat.completions.create\" in source:\n", "                    debug_info['api_pattern'] = \"chat_completions_closure\"\n", "            except Exception:\n", "                pass\n", "            \n", "            # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "            try:\n", "                if caller_frame:\n", "                    caller_locals = caller_frame.f_locals\n", "                    # Capture common patterns in this codebase\n", "                    if 'system_prompt' in caller_locals:\n", "                        debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                    # If this is using the OpenAI client pattern, get the model too\n", "                    if 'model' in caller_locals:\n", "                        debug_info['model'] = caller_locals['model']\n", "                    # For the antropic calls\n", "                    if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                    elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                    elif 'M<PERSON>' in caller_locals:\n", "                        debug_info['model'] = caller_locals['MODEL']\n", "            except Exception as frame_error:\n", "                debug_info['frame_inspection_error'] = str(frame_error)\n", "            \n", "            # Save the debug information\n", "            timestamp = int(time.time())\n", "            log_filename = f\"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "            \n", "            try:\n", "                with open(log_filename, 'w', encoding='utf-8') as f:\n", "                    json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                print(f\"Logged error details to {log_filename}\")\n", "            except Exception as log_error:\n", "                print(f\"Failed to log error details: {str(log_error)}\")\n", "            \n", "            if attempt == max_retries - 1:\n", "                # If we've exhausted all retries and still have an error\n", "                print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "                return None\n", "            \n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** attempt))\n", "    \n", "    # If we've exhausted all retries and still have an empty response\n", "    print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "    return None\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\n", "You are a logical reasoning assistant. Your task is to solve the user's problem by thinking step-by-step. First, carefully analyze the user's query to identify all the core requirements, constraints, and the final objective.\n", "\n", "Break down the problem into a logical sequence of steps. In a 'Reasoning' section, explain your thought process for each step, how you are applying the given constraints, and how one step leads to the next.\n", "\n", "After you have reasoned through the entire problem, provide the final, conclusive answer under a separate 'Final Answer' heading.\n", "\"\"\"\n", "\n", "user_prompt = \"\"\"\n", "A team is planning a three-day work sprint from Monday to Wednesday. They need to schedule three main tasks: Market Research, Prototype Design, and User Testing. The scheduling must follow these rules:\n", "\n", "1.  Each day can only have one main task assigned to it.\n", "2.  Market Research must happen before User Testing.\n", "3.  Prototype Design cannot be the first task on Monday.\n", "\n", "Based on these rules, what is the correct schedule of tasks for Monday, Tuesday, and Wednesday?\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["relevance_evaluation_agent = Agent(\n", "    model=critique_model,\n", "    system_prompt=system_prompt,\n", "    model_settings=AnthropicModelSettings({\n", "        \"max_tokens\": 64000,\n", "        \"extra_body\": {\n", "            \"stream\": True\n", "        },\n", "        \"anthropic_thinking\": {\n", "            \"type\": \"enabled\",\n", "            \"budget_tokens\": 63999\n", "        },\n", "    }),\n", "    retries=3\n", ")\n", "\n", "async with relevance_evaluation_agent.run_stream(user_prompt) as response:\n", "    print(await response.get_output())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The correct schedule must adhere to the following rules:\n", "1. Each day has one unique task.\n", "2. Market Research (MR) occurs before User Testing (UT).\n", "3. Prototype Design (PD) cannot be scheduled on Monday.\n", "\n", "**Reasoning**:\n", "- **Monday**: Cannot be PD (Rule 3). If Monday were User Testing, Rule 2 would require Market Research to be scheduled before it, which is impossible. Thus, **Monday must be Market Research**.\n", "- **Tuesday and Wednesday**: The remaining tasks are PD and UT. Since MR is already on Monday, UT can be scheduled on Tuesday or Wednesday. PD can be on Tuesday or Wednesday (but not Monday). This creates two valid permutations:\n", "  - **Option 1**: Monday (MR), Tuesday (PD), Wednesday (UT)\n", "  - **Option 2**: Monday (MR), Tuesday (UT), Wednesday (PD)\n", "\n", "Both options satisfy all constraints. However, if the problem assumes a sequential workflow (e.g., Prototype Design logically precedes User Testing), **Option 1** is the most coherent solution.\n", "\n", "**Final Answer**:\n", "- **Monday**: Market Research  \n", "- **Tuesday**: Prototype Design  \n", "- **Wednesday**: User Testing\n"]}], "source": ["relevance_evaluation_agent = Agent(\n", "    model=generator_model,\n", "    system_prompt=system_prompt,\n", "    model_settings={\n", "        \"temperature\": 0.1,\n", "        \"extra_body\": {\n", "            # \"return_reasoning\": True\n", "        }\n", "    },\n", "    retries=3\n", ")\n", "\n", "response = await relevance_evaluation_agent.run(user_prompt)\n", "\n", "print(response.output)"]}], "metadata": {"kernelspec": {"display_name": "NDA", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}