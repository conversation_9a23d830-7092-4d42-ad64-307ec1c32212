#!/usr/bin/env python3
"""
Simple PDF Image Extractor
Extracts XObject images from PDF files.
"""

import argparse
import sys
from pathlib import Path

try:
    import fitz  # PyMuPDF
except ImportError:
    print("Error: PyMuPDF is required. Install with: pip install PyMuPDF")
    sys.exit(1)


def extract_images_from_pdf(pdf_path, output_dir):
    """Extract all images from a PDF file."""
    pdf_path = Path(pdf_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    images_extracted = 0
    print(f"Extracting images from: {pdf_path}")
    
    with fitz.open(pdf_path) as doc:
        for page_num in range(len(doc)):
            page = doc[page_num]
            print(f"Processing page {page_num + 1}/{len(doc)}")
            
            # Extract XObject images
            images_extracted += extract_xobject_images(doc, page, page_num, output_dir)
    
    print(f"✅ Extracted {images_extracted} images to: {output_dir}")
    return images_extracted


def extract_xobject_images(doc, page, page_num, output_dir):
    """Extract XObject images from a PDF page."""
    extracted_count = 0
    image_list = page.get_images(full=True)
    
    for img_index, img in enumerate(image_list):
        xref = img[0]
        
        try:
            # Extract raw image data
            img_dict = doc.extract_image(xref)
            img_data = img_dict["image"]
            img_ext = img_dict["ext"]
            
            # Skip if no actual image data
            if not img_data or len(img_data) < 100:
                continue
            
            # Validate image (skip headers/footers/tiny images)
            try:
                temp_pix = fitz.Pixmap(doc, xref)
                if temp_pix.colorspace is None:  # Skip mask images
                    temp_pix = None
                    continue
                
                # Skip headers/footers (very wide and short)
                aspect_ratio = temp_pix.width / temp_pix.height if temp_pix.height > 0 else 0
                if aspect_ratio > 5.0 and temp_pix.height < 100:
                    temp_pix = None
                    continue
                    
                # Skip tiny images
                if temp_pix.width < 50 or temp_pix.height < 50:
                    temp_pix = None
                    continue
                    
                temp_pix = None
            except:
                continue
            
            # Set file extension
            if img_ext.lower() in ['jpeg', 'jpg', 'jpx']:
                file_ext = 'jpg'
            elif img_ext.lower() == 'png':
                file_ext = 'png'
            else:
                file_ext = img_ext.lower() if img_ext else 'bin'
            
            filename = f"page_{page_num + 1:03d}_img_{img_index:02d}.{file_ext}"
            img_path = output_dir / filename
            
            # Save raw image data directly
            with open(img_path, 'wb') as f:
                f.write(img_data)
            
            extracted_count += 1
            print(f"  Saved: {filename}")
            
        except Exception as e:
            print(f"  Failed to extract image {img_index}: {e}")
            continue
    
        return extracted_count


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Extract images from PDF files')
    parser.add_argument('pdf_file', help='PDF file to process')
    parser.add_argument('-o', '--output', help='Output directory (default: pdf_name_images)')
    
    args = parser.parse_args()
    
    # Validate input
    pdf_path = Path(args.pdf_file)
    if not pdf_path.exists():
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.suffix.lower() == '.pdf':
        print(f"Error: File must be a PDF: {pdf_path}")
        sys.exit(1)
    
    # Set output directory
    if args.output:
        output_dir = Path(args.output)
    else:
        output_dir = pdf_path.parent / f"{pdf_path.stem}_images"
    
    # Extract images
    extract_images_from_pdf(str(pdf_path), str(output_dir))


if __name__ == '__main__':
    main() 