import streamlit as st
import json
import base64
import os

def get_logo_base64():
    """Load the logo file and convert it to base64 for embedding in HTML"""
    try:
        logo_path = os.path.join(os.path.dirname(__file__), 'assets', 'cellics_logo.webp')
        with open(logo_path, 'rb') as f:
            logo_data = f.read()
        return base64.b64encode(logo_data).decode()
    except FileNotFoundError:
        st.error("Logo file not found. Please check the assets/cellics_logo.webp file exists.")
        return ""

# Set page config
st.set_page_config(
    page_title="Pre-IND Gap Analysis Report",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 2.5rem;
        color: #1f77b4;
        margin-bottom: 2rem;
        border-bottom: 3px solid #1f77b4;
        padding-bottom: 1rem;
        gap: 1rem;
    }
    
    .header-logo {
        height: 60px;
        width: auto;
        flex-shrink: 0;
    }
    
    .header-text {
        flex-grow: 1;
        text-align: center;
        padding-right: 60px; /* Offset for logo width to center text properly */
    }
    
    .section-header {
        font-size: 1.8rem;
        color: #2e8b57;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-left: 4px solid #2e8b57;
        padding-left: 1rem;
    }
    
    .subsection-header {
        font-size: 1.5rem;
        color: #4682b4;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .summary-card {
        background-color: #f0f8ff;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #e0e0e0;
        margin-bottom: 1rem;
    }
    
    .coverage-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: bold;
        margin: 0.25rem;
    }
    
    .excellent { background-color: #28a745; color: white; }
    .good { background-color: #17a2b8; color: white; }
    .partial { background-color: #ffc107; color: black; }
    .poor { background-color: #fd7e14; color: white; }
    .no-coverage { background-color: #dc3545; color: white; }
    
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #ddd;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

def load_json_data():
    """Load the JSON data from the file"""
    try:
        with open('scripts/comprehensive_gap_analysis_report.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        st.error("JSON file not found. Please check the file path.")
        return None

def get_coverage_badge_class(category):
    """Get CSS class for coverage category"""
    mapping = {
        'excellent_coverage': 'excellent',
        'good_coverage': 'good',
        'partial_coverage': 'partial',
        'poor_coverage': 'poor',
        'no_coverage': 'no-coverage'
    }
    return mapping.get(category,  'partial')

def get_coverage_display_name(category):
    """Get display name for coverage category"""
    mapping = {
        'excellent_coverage': 'Excellent Coverage',
        'good_coverage': 'Good Coverage',
        'partial_coverage': 'Partial Coverage',
        'poor_coverage': 'Poor Coverage',
        'no_coverage': 'No Coverage'
    }
    return mapping.get(category, category.replace('_', ' ').title())

def render_section_with_data(section_key, section_data):
    """Render a section that has analysis data"""
    st.markdown(f'<div class="subsection-header">{section_key} - {section_data["section_title"]}</div>', unsafe_allow_html=True)
    
    # Summary section with visualization
    summary = section_data['summary']
    
    # st.markdown('<div class="summary-card">', unsafe_allow_html=True)
    st.subheader("📊 Summary")
    
    # Metrics in columns
    met_col1, met_col2, met_col3, met_col4 = st.columns(4)
    
    with met_col1:
        st.metric("Total Checkpoints", summary['total_checkpoints'])
    
    with met_col2:
        st.metric("Covered Checkpoints", summary['covered_checkpoints'])
    
    with met_col3:
        st.metric("Coverage %", f"{summary['coverage_percentage']:.1f}%")
    
    with met_col4:
        st.metric("Input Chunks", summary['total_input_chunks_analyzed'])
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # with col2:
    #     # Coverage wheel
    #     fig = create_coverage_wheel(summary['coverage_percentage'], "Coverage Percentage")
    #     st.plotly_chart(fig, use_container_width=True)
    
    # Coverage Categories
    if 'coverage_categories' in section_data:
        st.subheader("🎯 Coverage Categories")
        
        for category_key, category_data in section_data['coverage_categories'].items():
            display_name = get_coverage_display_name(category_key)
            badge_class = get_coverage_badge_class(category_key)
            
            with st.expander(f"{display_name} ({category_data['checkpoint_count']} checkpoints)"):
                
                # Category metrics
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("Checkpoint Count", category_data['checkpoint_count'])
                
                with col2:
                    score_range = category_data['score_range']
                    st.metric("Score Range", f"{score_range[0]:.1f} - {score_range[1]:.1f}")
                
                with col3:
                    st.markdown(f'<div class="coverage-badge {badge_class}">{display_name}</div>', unsafe_allow_html=True)
                
                # Combined Supporting Evidence
                st.subheader("✅ Combined Supporting Evidence")
                st.write(category_data['combined_supporting_evidence'])
                
                # Combined Gap Analysis
                st.subheader("🔍 Combined Gap Analysis")
                st.write(category_data['combined_gap_analysis'])
                
                # Checkpoint Details
                if 'checkpoint_details' in category_data:
                    with st.expander("📋 Checkpoint Details"):
                        for i, checkpoint in enumerate(category_data['checkpoint_details'], 1):
                            st.markdown(f"**Checkpoint {i}:**")
                            st.write(f"**Question:** {checkpoint['checkpoint']}")
                            st.write(f"**ICH Guideline Source:** {checkpoint['ich_guideline_source']}")
                            st.write(f"**Coverage Score:** {checkpoint['coverage_score']:.1f}")
                            st.write(f"**Coverage Quality:** {checkpoint['coverage_quality']}")
                            
                            st.write("**Supporting Evidence:**")
                            st.write(checkpoint['supporting_evidence'])
                            
                            st.write("**Identified Gaps:**")
                            st.write(checkpoint['identified_gaps'])
                            
                            if 'supporting_chunks' in checkpoint:
                                with st.expander("📄 Supporting Chunks"):
                                    for chunk in checkpoint['supporting_chunks']:
                                        st.write(f"**Chunk ID:** {chunk['chunk_id']}")
                                        st.write(f"**Relevance Score:** {chunk['relevance_score']}")
                                        st.write(f"**Evidence:** {chunk['evidence_excerpt'][:200]}...")
                            
                            st.markdown("---")
    
    # Strategic Recommendations
    if 'strategic_recommendations' in section_data:
        st.subheader("🎯 Top Strategic Recommendations")
        st.write(section_data['strategic_recommendations'])

def render_section_without_data(section_num, subsections):
    """Render a section that doesn't have analysis data"""
    st.markdown(f'<div class="section-header">Section {section_num}</div>', unsafe_allow_html=True)
    
    if subsections:
        for subsection in subsections:
            st.markdown(f'<div class="subsection-header">{section_num}.{subsection}</div>', unsafe_allow_html=True)
            st.info(f"Analysis for section {section_num}.{subsection} is not available yet.")
    else:
        st.info(f"Analysis for section {section_num} is not available yet.")

def generate_job_id():
    """Generate a dummy job ID"""
    import random
    import string
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

def submit_analysis_tab():
    """Render the submit analysis tab"""
    st.header("🔄 Submit Gap Analysis")
    
    st.markdown("""
    Upload your document below to perform a comprehensive Pre-IND gap analysis. 
    The system will analyze your document against ICH guidelines and provide detailed coverage reports.
    """)
    
    # File upload section
    st.subheader("📁 Upload Document")
    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['pdf', 'docx', 'txt', 'md'],
        help="Supported formats: PDF, DOCX, TXT, MD"
    )
    
    if uploaded_file is not None:
        # File details, can be displayed or used later
        file_details = {
            "Filename": uploaded_file.name,
            "File Type": uploaded_file.type,
            "File Size": f"{uploaded_file.size} bytes"
        }
        
        st.success("✅ File uploaded successfully!")
    
    if st.button("🚀 Submit Analysis", type="primary", use_container_width=True):
        if uploaded_file is not None:
            
            # Show processing animation
            with st.spinner("Submitting analysis job..."):
                import time
                time.sleep(2)
                # Generate job ID and add gap analysis as BG task
                job_id = generate_job_id()
                # Add gap analysis function here when ready
                # gap_analysis_results = await perform_comprehensive_gap_analysis(uploaded_file)
            
            # Success message with job ID
            st.success("🎉 Analysis job submitted successfully!")
            st.info(f"**Job ID:** {job_id}")
            
            st.markdown(f"""
            **Analysis Details:**
            - **Document:** {uploaded_file.name}
            
            Please save your **Job ID** and use it in the "Check Results" tab to view your analysis results.
            """)
            
        else:
            st.error("❌ Please upload a document before submitting.")

def check_results_tab():
    """Render the check results tab"""
    # Initialize session state for results view
    if 'show_results' not in st.session_state:
        st.session_state.show_results = False
    if 'current_job_id' not in st.session_state:
        st.session_state.current_job_id = ''
    
    # If showing results, render the results view
    if st.session_state.show_results:
        render_results_view()
        return
    
    # Otherwise show the job ID input form
    st.header("🔍 Check Analysis Results")
    
    st.markdown("""
    Enter your Job ID below to check the status and view results of your gap analysis.
    """)
    
    # Job ID input
    col1, col2 = st.columns([3, 1])
    
    with col1:
        job_id = st.text_input(
            "Job ID",
            placeholder="Enter your 8-character Job ID",
            help="The Job ID you received when submitting your analysis"
        )
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)  # Add spacing
        check_button = st.button("🔍 Check Status", type="primary")
    
    if check_button:
        if job_id and len(job_id) == 8:
            # Simulate job checking
            with st.spinner("Checking job status..."):
                import time
                time.sleep(1)
            
            # For now, every job ID points to the existing JSON file
            st.success(f"✅ Analysis completed for Job ID: `{job_id}`")
            
            # Show job details
            st.markdown(f"""
            **Job Details:**
            - **Job ID:** {job_id}
            - **Status:** Completed
            - **Completion Time:** 2024-01-15 14:30:00 UTC
            - **Analysis Type:** Complete Gap Analysis
            """)
            
            # Set session state to show results
            st.session_state.show_results = True
            st.session_state.current_job_id = job_id
            st.rerun()
                
        elif job_id:
            st.error("❌ Invalid Job ID format. Please enter an 8-character Job ID.")
        else:
            st.error("❌ Please enter a Job ID.")

def render_results_view():
    """Render the results view with back button and sidebar"""
    # Back button
    col1, col2 = st.columns([1, 5])
    with col1:
        if st.button("← Back", type="secondary"):
            st.session_state.show_results = False
            st.rerun()
    
    with col2:
        st.markdown(f'''
        <div class="main-header">
            <div class="header-text">Gap Analysis Results - Job ID: {st.session_state.current_job_id}</div>
        </div>
        ''', unsafe_allow_html=True)
    
    # Load and display results
    data = load_json_data()
    if data:
        render_results_section(data)
    else:
        st.error("❌ Unable to load analysis results. Please try again later.")
        if st.button("← Back to Job ID Entry"):
            st.session_state.show_results = False
            st.rerun()

def render_results_section(data):
    """Render the results section (original main content)"""
    # Extract report data
    report_data = data['gap_analysis_report']
    
    # Sidebar navigation
    st.sidebar.markdown("**Report Sections:**")
    
    # Section structure definition
    section_structure = {
        1: list(range(1, 6)),  # 1.1 to 1.5
        2: list(range(1, 4)),  # 2.1 to 2.3
        3: list(range(1, 4)),  # 3.1 to 3.3
        4: [1],                # 4.1 only
        5: list(range(1, 4)),  # 5.1 to 5.3
        6: [1],                # 6.1 only
        7: list(range(1, 5)),  # 7.1 to 7.4
        8: []                  # no sub section
    }
    
    # Create navigation
    section_options = ["All"] + list(range(1, 9))
    selected_section = st.sidebar.selectbox(
        "Select Section",
        options=section_options,
        format_func=lambda x: "All Sections" if x == "All" else f"Section {x}",
        index=0  # Default to "All"
    )
    
    # Display metadata
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Report Metadata:**")
    metadata = report_data['metadata']
    st.sidebar.metric("Sections Analyzed", metadata['sections_analyzed'])
    st.sidebar.metric("Total Checkpoints", metadata['total_checkpoints'])
    st.sidebar.metric("Overall Coverage", f"{metadata['overall_coverage_percentage']:.1f}%")
    st.sidebar.metric("Input Chunks", metadata['total_input_chunks_analyzed'])
    
    # Main content based on selected section
    if selected_section == "All":
        # Show all sections
        section_analyses = report_data.get('section_analyses', {})
        
        for section_num in range(1, 9):
            if section_num in section_structure:
                subsections = section_structure[section_num]
                
                # Check if this section has any data
                has_data = any(f"{section_num}.{subsection}" in section_analyses for subsection in subsections)
                
                if has_data:
                    st.markdown(f'<div class="section-header">Section {section_num}</div>', unsafe_allow_html=True)
                    
                    for subsection in subsections:
                        section_key = f"{section_num}.{subsection}"
                        
                        if section_key in section_analyses:
                            render_section_with_data(section_key, section_analyses[section_key])
                        else:
                            st.markdown(f'<div class="subsection-header">{section_key}</div>', unsafe_allow_html=True)
                            st.info(f"Analysis for section {section_key} is not available yet.")
                else:
                    # Render section without data
                    render_section_without_data(section_num, subsections)
    elif selected_section in section_structure:
        # Show specific section
        subsections = section_structure[selected_section]
        section_analyses = report_data.get('section_analyses', {})
        
        # Check if this section has any data
        has_data = any(f"{selected_section}.{subsection}" in section_analyses for subsection in subsections)
        
        if has_data:
            st.markdown(f'<div class="section-header">Section {selected_section}</div>', unsafe_allow_html=True)
            
            for subsection in subsections:
                section_key = f"{selected_section}.{subsection}"
                
                if section_key in section_analyses:
                    render_section_with_data(section_key, section_analyses[section_key])
                else:
                    st.markdown(f'<div class="subsection-header">{section_key}</div>', unsafe_allow_html=True)
                    st.info(f"Analysis for section {section_key} is not available yet.")
        else:
            # Render section without data
            render_section_without_data(selected_section, subsections)

def main():
    # Main header with logo
    st.markdown('''
    <div class="main-header">
        <img src="data:image/webp;base64,{}" class="header-logo" alt="Cellics Logo">
        <div class="header-text">Pre-IND Gap Analysis System</div>
    </div>
    '''.format(get_logo_base64()), unsafe_allow_html=True)
    
    tab1, tab2 = st.tabs(["🔄 Submit Analysis", "🔍 Check Results"])
    
    with tab1:
        submit_analysis_tab()
    
    with tab2:
        check_results_tab()
    
    # Footer
    st.markdown("---")
    st.markdown("**Cellics Therapeutics Gap Analysis System** | Version 1.0 | 2025")
    
if __name__ == "__main__":
    main() 