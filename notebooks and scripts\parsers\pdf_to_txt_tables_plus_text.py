import pdfplumber
import textwrap
import os
import json
import random
import string
from collections import Counter
import re

def generate_placeholder_string(table_data):
    """
    Generate a random alphanumeric string with the same character count as the table content.
    This ensures proper token representation without problematic characters.
    
    Args:
        table_data (list): Table data as a list of lists
        
    Returns:
        str: Random string with same length as concatenated table content
    """
    if not table_data or not any(table_data):
        return ""
    
    # Calculate total character count from table
    total_chars = 0
    for row in table_data:
        for cell in row:
            if cell is not None:
                cell_str = str(cell).strip()
                if cell_str:
                    total_chars += len(cell_str)
    
    # Generate random string of same length
    # Use alphanumeric characters to avoid any special character issues
    chars = string.ascii_letters + string.digits
    placeholder = ''.join(random.choice(chars) for _ in range(total_chars))
    
    return placeholder

def detect_headers_footers(pdf_path, header_height=80, footer_height=80, min_pages=3):
    """
    Detect repeated headers and footers across pages
    
    Args:
        pdf_path: Path to PDF file
        header_height: Height of header region from top (in points)
        footer_height: Height of footer region from bottom (in points)
        min_pages: Minimum number of pages where text must appear to be considered header/footer
        
    Returns:
        Tuple of (header_patterns, footer_patterns) - sets of text lines to filter out
    """
    print("Detecting headers and footers...")
    
    header_lines = []
    footer_lines = []
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            
            for page_num, page in enumerate(pdf.pages):
                # Extract header region (top portion)
                header_bbox = (0, 0, page.width, header_height)
                header_text = page.crop(header_bbox).extract_text()
                if header_text:
                    # Split into lines and clean up
                    lines = [line.strip() for line in header_text.split('\n') if line.strip()]
                    header_lines.extend(lines)
                
                # Extract footer region (bottom portion)
                footer_bbox = (0, page.height - footer_height, page.width, page.height)
                footer_text = page.crop(footer_bbox).extract_text()
                if footer_text:
                    # Split into lines and clean up
                    lines = [line.strip() for line in footer_text.split('\n') if line.strip()]
                    footer_lines.extend(lines)
    
        # Count occurrences of each line
        header_counter = Counter(header_lines)
        footer_counter = Counter(footer_lines)
        
        # Find lines that appear on multiple pages (likely headers/footers)
        min_occurrences = max(2, min_pages)  # At least 2 pages, or user-specified minimum
        
        header_patterns = set()
        footer_patterns = set()
        
        for line, count in header_counter.items():
            if count >= min_occurrences and len(line) > 3:  # Ignore very short lines
                header_patterns.add(line)
                print(f"  Header pattern detected: '{line}' (appears {count} times)")
        
        for line, count in footer_counter.items():
            if count >= min_occurrences and len(line) > 3:  # Ignore very short lines
                footer_patterns.add(line)
                print(f"  Footer pattern detected: '{line}' (appears {count} times)")
        
        print(f"Found {len(header_patterns)} header patterns and {len(footer_patterns)} footer patterns")
        return header_patterns, footer_patterns
        
    except Exception as e:
        print(f"Error detecting headers/footers: {e}")
        return set(), set()

def clean_text_content(text, header_patterns, footer_patterns):
    """
    Remove header and footer patterns from text content
    
    Args:
        text: Text content to clean
        header_patterns: Set of header patterns to remove
        footer_patterns: Set of footer patterns to remove
        
    Returns:
        Cleaned text with headers/footers removed
    """
    if not text:
        return text
    
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        
        # Skip if line matches any header or footer pattern
        is_header_footer = False
        for pattern in header_patterns.union(footer_patterns):
            # Check for exact match or if the line contains the pattern
            if (line_stripped == pattern or 
                (len(pattern) > 10 and pattern in line_stripped) or
                (len(line_stripped) > 10 and line_stripped in pattern)):
                is_header_footer = True
                break
        
        if not is_header_footer:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def detect_table_headings(text_between_tables):
    """
    Detect if there are table headings or significant content between table placeholders
    
    Args:
        text_between_tables: Text content between two table placeholders
        
    Returns:
        Boolean indicating if there's a table heading or significant separator
    """
    if not text_between_tables or not text_between_tables.strip():
        return False
    
    text = text_between_tables.strip()
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # If no meaningful lines, no heading
    if not lines:
        return False
    
    # Check for table heading patterns
    heading_patterns = [
        r'^Table\s+\d+.*:',           # "Table 1:", "Table 2.1:", etc.
        r'^Table\s+\d+\..*',          # "Table 1. Description"
        r'^TABLE\s+\d+.*:',           # "TABLE 1:", uppercase
        r'^Table\s+[A-Z]\d*.*:',      # "Table A:", "Table A1:", etc.
        r'^\d+\.\d+.*Table',          # "3.2 Table Analysis"
        r'^Figure\s+\d+.*:',          # "Figure 1:", in case it's mixed content
        r'^\d+\.\s*[A-Z]',            # "1. Introduction", "2. Methods" (numbered sections)
        r'^[A-Z][A-Z\s]{10,}$',       # ALL CAPS HEADINGS (at least 10 chars)
    ]
    
    for line in lines:
        # Check against heading patterns
        for pattern in heading_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                print(f"    Found heading pattern: '{line}'")
                return True
        
        # Check for lines that look like section headers (short, title-case, etc.)
        if (len(line) < 100 and                    # Not too long
            len(line.split()) >= 2 and             # At least 2 words
            line[0].isupper() and                  # Starts with capital
            ':' in line):                          # Contains colon (common in headings)
            print(f"    Found heading-like text: '{line}'")
            return True
    
    # If we have multiple lines of text, it's probably content separating tables
    if len(lines) > 2:
        print(f"    Found substantial content ({len(lines)} lines) between tables")
        return True
    
    # Single line with substantial content
    if len(lines) == 1 and len(lines[0]) > 50:
        print(f"    Found substantial single line: '{lines[0][:50]}...'")
        return True
    
    print(f"    No significant separator found: '{text[:50]}{'...' if len(text) > 50 else ''}'")
    return False

def merge_consecutive_tables(output_txt_path, extracted_tables, tables_dir):
    """
    Post-process the output file to merge consecutive table placeholders that don't have headings between them
    
    Args:
        output_txt_path: Path to the output text file
        extracted_tables: Dictionary of extracted tables
        tables_dir: Directory containing table files
    """
    print("\nPost-processing: Checking for consecutive tables to merge...")
    
    # Read the current output file
    with open(output_txt_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all table placeholders in order
    table_pattern = r'Table Content: \{([^}]+)\}'
    matches = list(re.finditer(table_pattern, content))
    
    if len(matches) < 2:
        print("  No consecutive tables found to check")
        return extracted_tables
    
    print(f"  Found {len(matches)} table placeholders to analyze")
    
    # Track which tables to merge
    tables_to_merge = []  # List of lists - each inner list contains tables to merge together
    current_merge_group = [matches[0]]
    
    for i in range(1, len(matches)):
        prev_match = matches[i-1]
        curr_match = matches[i]
        
        # Get text between the table placeholders
        text_between = content[prev_match.end():curr_match.start()]
        
        print(f"  Checking between table {i} and {i+1}:")
        print(f"    Previous placeholder: {prev_match.group(1)[:20]}...")
        print(f"    Current placeholder: {curr_match.group(1)[:20]}...")
        
        # Check if there's a heading between them
        has_heading = detect_table_headings(text_between)
        
        if has_heading:
            # There's a heading, so these are separate tables
            if len(current_merge_group) > 1:
                tables_to_merge.append(current_merge_group)
            current_merge_group = [curr_match]
            print(f"    → Separate tables (heading found)")
        else:
            # No heading, these tables should be merged
            current_merge_group.append(curr_match)
            print(f"    → Tables should be merged (no heading)")
    
    # Don't forget the last group
    if len(current_merge_group) > 1:
        tables_to_merge.append(current_merge_group)
    
    if not tables_to_merge:
        print("  No consecutive tables need merging")
        return extracted_tables
    
    print(f"  Found {len(tables_to_merge)} groups of tables to merge")
    
    # Perform the merging
    new_content = content
    updated_tables = extracted_tables.copy()
    
    for group_idx, merge_group in enumerate(tables_to_merge):
        print(f"  Merging group {group_idx + 1}: {len(merge_group)} tables")
        
        # Get the table data for all tables in this group
        all_table_data = []
        all_pages = set()
        total_original_char_count = 0
        first_placeholder = merge_group[0].group(1)
        
        for match in merge_group:
            placeholder = match.group(1)
            if placeholder in updated_tables:
                table_info = updated_tables[placeholder]
                table_id = table_info['id']
                
                # Accumulate original character counts for accurate placeholder sizing
                total_original_char_count += table_info.get('char_count', 0)
                
                # Load the actual table data
                table_file = os.path.join(tables_dir, f"{table_id}.txt")
                if os.path.exists(table_file):
                    with open(table_file, 'r', encoding='utf-8') as f:
                        table_content = f.read()
                    
                    # Parse the table content back to data (simplified)
                    # For now, just concatenate the formatted tables
                    all_table_data.append(table_content)
                    all_pages.update(table_info.get('pages', []))
        
        if all_table_data:
            # Create new merged table
            merged_table_id = f"table_merged_{group_idx + 1}"
            merged_content = "\n\n".join(all_table_data)
            
            # Add continuation note
            if len(all_table_data) > 1:
                merged_content = merged_content.replace(
                    "TABLE CONTINUED FROM PREVIOUS PAGE", 
                    "MERGED TABLE FROM MULTIPLE SECTIONS"
                )
            
            # Generate new placeholder based on original table data character counts
            # This maintains consistency with the original placeholder generation logic
            chars = string.ascii_letters + string.digits
            new_placeholder = ''.join(random.choice(chars) for _ in range(total_original_char_count))
            
            print(f"    Generated placeholder of length {len(new_placeholder)} chars (sum of original tables: {total_original_char_count})")
            
            # Save merged table
            merged_table_file = os.path.join(tables_dir, f"{merged_table_id}.txt")
            with open(merged_table_file, 'w', encoding='utf-8') as f:
                f.write(merged_content)
            
            # Update tables dictionary
            updated_tables[new_placeholder] = {
                'id': merged_table_id,
                'formatted_content': merged_content,
                'char_count': total_original_char_count,  # Use sum of original char counts
                'pages': sorted(list(all_pages)),
                'position': updated_tables[first_placeholder].get('position', {}),
                'merged_from': [match.group(1) for match in merge_group]
            }
            
            # Replace all placeholders in this group with the new one
            for match in merge_group:
                old_placeholder_full = match.group(0)  # Full match including "Table Content: {...}"
                if match == merge_group[0]:
                    # Replace first occurrence with new placeholder
                    new_placeholder_full = f"Table Content: {{{new_placeholder}}}"
                    new_content = new_content.replace(old_placeholder_full, new_placeholder_full, 1)
                else:
                    # Remove subsequent occurrences
                    new_content = new_content.replace(old_placeholder_full, "", 1)
            
            # Clean up old table entries and files
            for match in merge_group:
                old_placeholder = match.group(1)
                if old_placeholder in updated_tables and old_placeholder != new_placeholder:
                    # Get the old table info before deleting
                    old_table_info = updated_tables[old_placeholder]
                    old_table_id = old_table_info['id']
                    
                    # Delete the old table file
                    old_table_file = os.path.join(tables_dir, f"{old_table_id}.txt")
                    if os.path.exists(old_table_file):
                        os.remove(old_table_file)
                        print(f"    Removed old table file: {old_table_id}.txt")
                    
                    # Remove from tables dictionary
                    del updated_tables[old_placeholder]
            
            print(f"    Created merged table: {merged_table_id} spanning pages {sorted(list(all_pages))}")
    
    # Write updated content
    with open(output_txt_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    # Update table index
    create_table_index(updated_tables, tables_dir)
    
    print(f"  Merging complete: {len(updated_tables)} final tables")
    return updated_tables

def analyze_table_continuation_info(extracted_tables):
    """
    Analyze the extracted tables to identify table spans and continuation patterns
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables
        
    Returns:
        dict: Analysis results containing:
            - spanning_tables: Tables that span multiple pages
            - page_table_mapping: Which tables appear on which pages
            - continuation_chains: Groups of tables that are continuations
            - single_page_tables: Tables that appear only on one page
    """
    analysis = {
        'spanning_tables': [],
        'page_table_mapping': {},  # page_num -> [table_info, ...]
        'continuation_chains': [],
        'single_page_tables': [],
        'table_positions': {}  # table_id -> position info
    }
    
    # Group tables by ID to find spanning/continuation patterns
    table_groups = {}
    for placeholder, info in extracted_tables.items():
        table_id = info['id']
        if table_id not in table_groups:
            table_groups[table_id] = []
        table_groups[table_id].append({
            'placeholder': placeholder,
            'info': info,
            'pages': info.get('pages', []),
            'position': info.get('position', {}),
            'char_count': info.get('char_count', 0)
        })
    
    # Analyze each table group
    for table_id, table_entries in table_groups.items():
        # Collect all pages for this table
        all_pages = set()
        total_char_count = 0
        position_info = {}
        
        for entry in table_entries:
            all_pages.update(entry['pages'])
            total_char_count += entry['char_count']
            if entry['position']:
                page_num = entry['position'].get('page', 0)
                position_info[f"page_{page_num}"] = entry['position']
        
        all_pages = sorted(list(all_pages))
        
        table_analysis = {
            'table_id': table_id,
            'pages': all_pages,
            'total_char_count': total_char_count,
            'position_info': position_info,
            'is_spanning': len(all_pages) > 1,
            'is_merged': 'merged_from' in table_entries[0]['info'],
            'start_page': min(all_pages) if all_pages else 0,
            'end_page': max(all_pages) if all_pages else 0
        }
        
        # Store position info for table
        analysis['table_positions'][table_id] = position_info
        
        # Categorize the table
        if len(all_pages) > 1:
            analysis['spanning_tables'].append(table_analysis)
            # This is a continuation chain
            analysis['continuation_chains'].append({
                'table_id': table_id,
                'pages': all_pages,
                'start_page': min(all_pages),
                'end_page': max(all_pages),
                'is_continuation': True
            })
        else:
            analysis['single_page_tables'].append(table_analysis)
        
        # Update page mapping
        for page_num in all_pages:
            if page_num not in analysis['page_table_mapping']:
                analysis['page_table_mapping'][page_num] = []
            
            analysis['page_table_mapping'][page_num].append({
                'table_id': table_id,
                'char_count': total_char_count,
                'position': position_info.get(f"page_{page_num}", {}),
                'is_spanning': len(all_pages) > 1,
                'is_merged': 'merged_from' in table_entries[0]['info'],
                'total_pages': all_pages
            })
    
    return analysis

def process_pdf_to_txt(input_pdf_path, output_txt_path, tables_dir=None):
    """
    Read a PDF file and write its content to a TXT file while extracting tables separately.
    Tables are stored as separate files and replaced with random placeholder strings of equivalent length.
    Headers and footers are automatically detected and removed.
    
    Args:
        input_pdf_path (str): Path to input PDF file
        output_txt_path (str): Path to output TXT file
        tables_dir (str): Directory to store extracted tables (optional)
        
    Returns:
        Tuple of (extracted_tables, table_analysis, page_wise_content)
        where page_wise_content is a dict mapping page numbers to formatted content with tables filled in
    """
    if tables_dir is None:
        base_name = os.path.splitext(os.path.basename(output_txt_path))[0]
        tables_dir = os.path.join(os.path.dirname(output_txt_path), f"{base_name}_tables")
    
    os.makedirs(tables_dir, exist_ok=True)
    
    extracted_tables = {}
    table_counter = 1
    written_placeholders = {}  # Track placeholders written to file
    page_wise_content = {}  # Track content for each page with tables filled in
    
    # Set random seed for reproducibility (optional - remove for true randomness)
    random.seed(42)
    
    # Step 1: Detect repeated headers and footers
    header_patterns, footer_patterns = detect_headers_footers(input_pdf_path)
    
    with pdfplumber.open(input_pdf_path) as pdf:
        with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
            prev_page_table = None
            prev_page_headers = None
            prev_page_placeholder = None
            
            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}...")
                page_content_parts = []  # Collect content parts for this page
                
                table_areas = page.find_tables()
                
                if table_areas:
                    print(f"  Found {len(table_areas)} table areas on page {page_num + 1}")
                    tables = page.extract_tables()
                    
                    table_bbox_areas = []
                    for table_area in table_areas:
                        table_bbox_areas.append({
                            'top': table_area.bbox[1],
                            'bottom': table_area.bbox[3],
                            'x0': table_area.bbox[0],
                            'x1': table_area.bbox[2]
                        })
                    
                    table_bbox_areas.sort(key=lambda t: t['top'])
                    
                    current_y = 0
                    page_height = page.height
                    
                    for idx, table_pos in enumerate(table_bbox_areas):
                        # Text above table
                        if current_y < table_pos['top']:
                            crop_area = (
                                0,                  # x0
                                current_y,          # top
                                page.width,         # x1
                                table_pos['top']    # bottom
                            )
                            
                            above_text = page.crop(crop_area).extract_text()
                            if above_text:
                                # Clean headers/footers from above text
                                cleaned_above_text = clean_text_content(above_text, header_patterns, footer_patterns)
                                if cleaned_above_text.strip():  # Only write if there's content after cleaning
                                    txt_file.write(cleaned_above_text + "\n\n")
                                    page_content_parts.append(cleaned_above_text.strip())
                        
                        table_index = table_bbox_areas.index(table_pos)
                        table_data = tables[table_index]
                        
                        is_continuation = False
                        current_table_id = None
                        placeholder_string = None
                        formatted_table = None
                        write_placeholder = True  # Flag to control whether to write placeholder
                        
                        if idx == 0 and prev_page_table is not None and table_pos['top'] < 100:
                            # Check if this could be a table continuation
                            prev_headers = prev_page_headers
                            current_headers = table_data[0] if table_data else None
                            
                            # More robust continuation check
                            continuation_checks = []
                            
                            # Check 1: Same number of columns
                            if table_data and prev_headers:
                                same_columns = len(table_data[0]) == len(prev_headers)
                                continuation_checks.append(f"Same columns: {same_columns} ({len(table_data[0]) if table_data else 0} vs {len(prev_headers)})")
                            else:
                                same_columns = False
                                continuation_checks.append(f"Same columns: False (missing data)")
                            
                            # Check 2: Header similarity (if current row looks like headers)
                            header_similarity = False
                            if current_headers and prev_headers:
                                # Check if headers are similar (allowing for some variation)
                                matches = sum(1 for a, b in zip(prev_headers, current_headers) 
                                            if a and b and (a.strip().lower() == b.strip().lower() or 
                                                          (len(a.strip()) > 3 and a.strip().lower() in b.strip().lower()) or
                                                          (len(b.strip()) > 3 and b.strip().lower() in a.strip().lower())))
                                header_similarity = matches >= len(prev_headers) * 0.7  # At least 70% match
                                continuation_checks.append(f"Header similarity: {header_similarity} ({matches}/{len(prev_headers)} matches)")
                            else:
                                continuation_checks.append(f"Header similarity: False (missing headers)")
                            
                            print(f"  Table continuation checks: {'; '.join(continuation_checks)}")
                            
                            if same_columns and (header_similarity or not current_headers):
                                is_continuation = True
                                
                                current_table_id = prev_page_table['id']
                                print(f"  ✓ Continuing table {current_table_id} from previous page")
                                
                                # If first row looks like headers, skip it in continuation
                                table_data_to_add = table_data[1:] if header_similarity and current_headers else table_data
                                
                                # Combine with previous page data
                                full_table = prev_page_table['data'] + table_data_to_add
                                placeholder_string = generate_placeholder_string(full_table)
                                formatted_table = format_table(full_table, is_continued=True)
                                
                                # Update the mapping with the complete table
                                extracted_tables[placeholder_string] = {
                                    'id': current_table_id,
                                    'formatted_content': formatted_table,
                                    'char_count': len(placeholder_string),
                                    'pages': prev_page_table.get('pages', []) + [page_num + 1],
                                    'position': prev_page_table.get('position', {})
                                }
                                
                                # Also keep the old placeholder mapping for compatibility
                                if prev_page_placeholder and prev_page_placeholder in written_placeholders:
                                    extracted_tables[prev_page_placeholder] = extracted_tables[placeholder_string].copy()
                                    print(f"  Keeping old placeholder mapping for {current_table_id}")
                                
                                print(f"  ✓ Updated continued table {current_table_id} with {len(table_data_to_add)} additional rows")
                            else:
                                is_continuation = False
                                print(f"  ✗ Table structure mismatch, treating as new table")
                        
                        if not is_continuation:
                            current_table_id = f"table_{table_counter}"
                            print(f"  Creating new table {current_table_id}")
                            table_counter += 1
                            
                            placeholder_string = generate_placeholder_string(table_data)
                            formatted_table = format_table(table_data)
                            
                            # Check if this table might continue on next page
                            if table_pos['bottom'] > (page_height - 100):
                                # This table might continue, so we'll write placeholder but may update later
                                prev_page_table = {
                                    'id': current_table_id,
                                    'data': table_data,
                                    'pages': [page_num + 1],
                                    'position': {
                                        'page': page_num + 1,
                                        'top': table_pos['top'],
                                        'bottom': table_pos['bottom']
                                    }
                                }
                                prev_page_headers = table_data[0] if table_data else None
                                prev_page_placeholder = placeholder_string
                                print(f"  Table {current_table_id} extends to page bottom, may continue")
                            else:
                                prev_page_table = None
                                prev_page_headers = None
                                prev_page_placeholder = None
                            
                            # Always add to extracted_tables for now
                            extracted_tables[placeholder_string] = {
                                'id': current_table_id,
                                'formatted_content': formatted_table,
                                'char_count': len(placeholder_string),
                                'pages': [page_num + 1],
                                'position': {
                                    'page': page_num + 1,
                                    'top': table_pos['top'],
                                    'bottom': table_pos['bottom']
                                }
                            }
                        
                        # Add placeholder to page content for now - will be replaced with final merged tables later
                        if placeholder_string:
                            page_content_parts.append(f"Table Content: {{{placeholder_string}}}")
                        
                        # Write placeholder to file only if this table_id hasn't been written before
                        if write_placeholder and placeholder_string:
                            # Check if this table_id has already been written as a placeholder
                            table_already_written = any(table_id == current_table_id for table_id in written_placeholders.values())
                            
                            if not table_already_written:
                                table_placeholder = f"\nTable Content: {{{placeholder_string}}}\n"
                                txt_file.write(table_placeholder + "\n")
                                written_placeholders[placeholder_string] = current_table_id
                                print(f"  Inserted placeholder for {current_table_id} (length: {len(placeholder_string)} chars)")
                            else:
                                print(f"  Skipping placeholder for {current_table_id} - already written to file")
                                # If this is a continuation, update the original placeholder mapping with the complete table
                                if is_continuation and prev_page_placeholder in extracted_tables:
                                    print(f"  Updating original placeholder mapping with complete table content")
                                    extracted_tables[prev_page_placeholder] = extracted_tables[placeholder_string].copy()
                                
                                # Remove the duplicate placeholder from extracted_tables since we're not using it
                                if placeholder_string in extracted_tables and placeholder_string != prev_page_placeholder:
                                    del extracted_tables[placeholder_string]
                        
                        current_y = table_pos['bottom']
                    
                    # Text below tables
                    if current_y < page_height:
                        crop_area = (
                            0,              # x0
                            current_y,      # top
                            page.width,     # x1
                            page_height     # bottom
                        )
                        
                        below_text = page.crop(crop_area).extract_text()
                        if below_text:
                            # Clean headers/footers from below text
                            cleaned_below_text = clean_text_content(below_text, header_patterns, footer_patterns)
                            if cleaned_below_text.strip():  # Only write if there's content after cleaning
                                txt_file.write(cleaned_below_text + "\n\n")
                                page_content_parts.append(cleaned_below_text.strip())
                else:
                    print(f"  No tables found on page {page_num + 1}")
                    text = page.extract_text()
                    # Clean headers/footers from full page text
                    cleaned_text = clean_text_content(text, header_patterns, footer_patterns)
                    if cleaned_text.strip():  # Only write if there's content after cleaning
                        txt_file.write(cleaned_text + "\n\n")
                        page_content_parts.append(cleaned_text.strip())

                    prev_page_table = None
                    prev_page_headers = None
                    prev_page_placeholder = None
                
                # Store page content with tables filled in
                if page_content_parts:
                    page_wise_content[page_num + 1] = "\n\n".join(page_content_parts)
                else:
                    page_wise_content[page_num + 1] = ""
    
    # Final cleanup: ensure all written placeholders are in the index
    print("\nVerifying all written placeholders are in index...")
    for placeholder, table_id in written_placeholders.items():
        if placeholder not in extracted_tables:
            print(f"  WARNING: Written placeholder for {table_id} not in final index!")
            # Try to find the table by ID and add the placeholder mapping
            for p, info in extracted_tables.items():
                if info['id'] == table_id:
                    extracted_tables[placeholder] = info.copy()
                    print(f"  Added missing placeholder mapping for {table_id}")
                    break
    
    save_extracted_tables(extracted_tables, tables_dir)
    
    create_table_index(extracted_tables, tables_dir)
    
    # Post-process to merge consecutive tables without headings between them
    extracted_tables = merge_consecutive_tables(output_txt_path, extracted_tables, tables_dir)
    
    # Update page_wise_content after merging tables
    page_wise_content = update_page_wise_content_after_merge(page_wise_content, extracted_tables, tables_dir)
    
    # Analyze table continuation patterns
    table_analysis = analyze_table_continuation_info(extracted_tables)
    
    print(f"\nFINAL RESULTS:")
    print(f"Extracted {len(set(info['id'] for info in extracted_tables.values()))} unique tables")
    print(f"Total placeholder mappings: {len(extracted_tables)}")
    print(f"Table IDs created: {sorted(set(info['id'] for info in extracted_tables.values()))}")
    print(f"Headers/footers removed: {len(header_patterns)} header patterns, {len(footer_patterns)} footer patterns")
    print(f"Spanning tables: {len(table_analysis['spanning_tables'])}")
    print(f"Single page tables: {len(table_analysis['single_page_tables'])}")
    print(f"Continuation chains: {len(table_analysis['continuation_chains'])}")
    print(f"Page-wise content generated for {len(page_wise_content)} pages")
    
    return extracted_tables, table_analysis, page_wise_content

def save_extracted_tables(extracted_tables, tables_dir):
    """
    Save each extracted table as a separate text file with formatted content.
    For continuation and merged tables, saves as single combined files.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save tables
    """
    # Track which table IDs have been saved to avoid duplicates
    saved_table_ids = set()
    
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        
        # Skip if this table ID has already been saved (handles continuation duplicates)
        if table_id in saved_table_ids:
            continue
            
        formatted_content = table_info['formatted_content']
        table_file_path = os.path.join(tables_dir, f"{table_id}.txt")
        
        with open(table_file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_content)
        
        saved_table_ids.add(table_id)
        
        # Log what type of table was saved
        pages = table_info.get('pages', [])
        if len(pages) > 1:
            print(f"  Saved continuation table: {table_id} spanning pages {pages}")
        elif 'merged_from' in table_info:
            print(f"  Saved merged table: {table_id} from {len(table_info['merged_from'])} original tables")
        else:
            print(f"  Saved single table: {table_id} on page {pages[0] if pages else 'unknown'}")

def create_table_index(extracted_tables, tables_dir):
    """
    Create an index file with metadata about all extracted tables.
    The mapping now uses placeholder strings as keys and metadata as values.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save the index
    """
    index_data = {}
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        # Store mapping from placeholder to file info
        index_data[placeholder_string] = {
            'id': table_id,
            'char_count': table_info['char_count'],
            'pages': table_info['pages'],
            'position': table_info['position'],
            'file': f"{table_id}.txt"
        }
    
    index_file_path = os.path.join(tables_dir, "table_index.json")
    with open(index_file_path, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, indent=2)

def format_table(table_data, is_continued=False):
    """
    Format a table as ASCII text with borders, handling long cell content properly.
    
    Args:
        table_data (list): Table data as a list of lists
        is_continued (bool): Whether this table is a continuation from previous page
        
    Returns:
        str: Formatted table as ASCII text
    """
    if not table_data or not any(table_data):
        return ""
    
    cleaned_table = []
    for row in table_data:
        cleaned_row = []
        for cell in row:
            cell_str = str(cell).strip() if cell is not None else ""
            cell_str = cell_str.replace('\n', ' ').strip()
            cleaned_row.append(cell_str)
        cleaned_table.append(cleaned_row)
    
    if cleaned_table:
        header_row = cleaned_table[0]
        max_col_width = 40  # Maximum width for any column
        min_col_width = 10  # Minimum width for any column
        
        col_widths = []
        for cell in header_row:
            width = min(max(len(cell), min_col_width), max_col_width)
            col_widths.append(width)
    else:
        return ""
    
    output = []
    
    border = '+' + '+'.join('-' * (width + 2) for width in col_widths) + '+'
    
    if is_continued:
        output.append(border)
        continued_note = "| " + "TABLE CONTINUED FROM PREVIOUS PAGE".center(sum(col_widths) + len(col_widths)*3 - 4) + " |"
        output.append(continued_note)
    
    output.append(border)
    
    for row_idx, row in enumerate(cleaned_table):
        wrapped_cells = []
        
        for i, cell in enumerate(row):
            if i < len(col_widths):
                if len(cell) > col_widths[i]:
                    wrapped_text = textwrap.wrap(cell, col_widths[i])
                    wrapped_cells.append(wrapped_text)
                else:
                    wrapped_cells.append([cell])
        
        max_lines = max(len(cell_lines) for cell_lines in wrapped_cells) if wrapped_cells else 1
        
        for line_idx in range(max_lines):
            line_cells = []
            for i, cell_lines in enumerate(wrapped_cells):
                if line_idx < len(cell_lines):
                    text = cell_lines[line_idx]
                else:
                    text = ""
                
                line_cells.append(f" {text:<{col_widths[i]}} ")
            
            output.append('|' + '|'.join(line_cells) + '|')
        
        output.append(border)
    
    table_text = '\n'.join(output)
    
    return table_text

def update_page_wise_content_after_merge(page_wise_content, extracted_tables, tables_dir):
    """
    Update the page_wise_content dictionary to reflect the merged tables.
    This replaces placeholders with the final merged table content from saved files.
    
    Args:
        page_wise_content (dict): Dictionary mapping page numbers to formatted content with placeholders
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory containing table files
        
    Returns:
        Updated page_wise_content dictionary with tables filled in
    """
    import re
    
    updated_content = {}
    
    for page_num, content in page_wise_content.items():
        print(f"  Updating page {page_num} content with final merged tables...")
        updated_page_content = content
        
        # Find all table placeholders in this page's content
        placeholder_pattern = r'Table Content: \{([^}]+)\}'
        matches = list(re.finditer(placeholder_pattern, content))
        
        for match in matches:
            placeholder_string = match.group(1)
            
            # Check if this placeholder exists in our final extracted_tables
            if placeholder_string in extracted_tables:
                table_info = extracted_tables[placeholder_string]
                table_id = table_info['id']
                
                # Load the actual merged table content from the saved file
                table_file = os.path.join(tables_dir, f"{table_id}.txt")
                if os.path.exists(table_file):
                    with open(table_file, 'r', encoding='utf-8') as f:
                        table_content = f.read()
                    
                    # Replace the placeholder with the actual table content
                    old_placeholder = match.group(0)  # Full match including "Table Content: {...}"
                    updated_page_content = updated_page_content.replace(old_placeholder, table_content)
                    
                    # Check if this is a merged or spanning table
                    pages_info = table_info.get('pages', [])
                    is_merged = 'merged_from' in table_info
                    
                    if len(pages_info) > 1:
                        print(f"    Replaced {table_id} on page {page_num} (spans pages {pages_info})")
                    elif is_merged:
                        print(f"    Replaced {table_id} on page {page_num} (merged table)")
                    else:
                        print(f"    Replaced {table_id} on page {page_num}")
                else:
                    print(f"    Warning: Table file not found for {table_id}")
            else:
                print(f"    Warning: Placeholder {placeholder_string[:20]}... not found in final extracted_tables")
        
        updated_content[page_num] = updated_page_content
    
    print(f"  Updated page-wise content for {len(updated_content)} pages with final merged tables")
    return updated_content

# Main execution code - only run when script is executed directly
if __name__ == "__main__":
    # Get the absolute path to the workspace root
    workspace_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))

    # Construct absolute paths for input and output files
    pdf_path = "/Users/<USER>/Desktop/TheAgentic/MedNova-PreIND/backend/gap_analysis_pipeline/Pre-IND-Sample.pdf"
    output_path = "/Users/<USER>/Desktop/TheAgentic/MedNova-PreIND/backend/gap_analysis_pipeline/Pre-IND-Sample.txt"

    # Process the PDF with table extraction
    extracted_tables, table_analysis, page_wise_content = process_pdf_to_txt(pdf_path, output_path)