# %%
import re
import json
import os
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, Tuple
import numpy as np
import asyncio
from transformers import AutoTokenizer
import traceback  # Added import for stack trace information
import time  # Added import for timestamp logging
import inspect    # For frame inspection
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

CRITIQUE_MODEL_NAME = os.getenv("CRITIQUE_MODEL_NAME")
CRITIQUE_MODEL_BASE_URL = os.getenv("CRITIQUE_MODEL_BASE_URL")
CRITIQUE_MODEL_API_KEY = os.getenv("CRITIQUE_MODEL_API_KEY")

INCLUDE_JSON_OUTPUT_TAGS = False

TOPMOST_SUMMARY_THRESHOLD = 3.0
MIDLEVEL_SUMMARY_THRESHOLD = 5.0
CHUNK_SUMMARY_THRESHOLD = 7.0

MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000
MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000

critique_client = AsyncOpenAI(
    base_url=CRITIQUE_MODEL_BASE_URL, 
    api_key=CRITIQUE_MODEL_API_KEY,     
    max_retries=3,
    timeout=10000000
)
generator_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL, 
    api_key=GENERATOR_MODEL_API_KEY,     
    max_retries=3,
    timeout=10000000
)

critique_model = OpenAIModel(
    model_name=CRITIQUE_MODEL_NAME,
    provider=OpenAIProvider(
        base_url=CRITIQUE_MODEL_BASE_URL,
        api_key=CRITIQUE_MODEL_API_KEY
    )
)

generator_model = OpenAIModel(
    model_name=GENERATOR_MODEL_NAME,
    provider=OpenAIProvider(
        base_url=GENERATOR_MODEL_BASE_URL,
        api_key=GENERATOR_MODEL_API_KEY
    )
)

print("GENERATOR_MODEL_NAME", GENERATOR_MODEL_NAME)
print("GENERATOR_MODEL_BASE_URL", GENERATOR_MODEL_BASE_URL)
print("GENERATOR_MODEL_API_KEY", GENERATOR_MODEL_API_KEY)

print("CRITIQUE_MODEL_NAME", CRITIQUE_MODEL_NAME)
print("CRITIQUE_MODEL_BASE_URL", CRITIQUE_MODEL_BASE_URL)
print("CRITIQUE_MODEL_API_KEY", CRITIQUE_MODEL_API_KEY)

# Pydantic schema for structured output
class CheckpointList(BaseModel):
    """Schema for checkpoint list generation"""
    checkpoints: List[str] = Field(
        description="List of specific checkpoint questions that should be answered in summaries for this section",
        min_items=2
    )

class RelevanceEvaluation(BaseModel):
    """Schema for summary relevance evaluation"""
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well the summary answers the checkpoint questions",
        ge=0.0,
        le=10.0
    )

class CritiqueEvaluation(BaseModel):
    """Schema for NDA critique evaluation"""
    overall_rating: float = Field(
        description="Overall rating from 1-10 for the NDA section quality",
        ge=1.0,
        le=10.0
    )
    key_strengths: List[str] = Field(
        description="List of main strengths identified in the NDA section",
        default=[]
    )
    critical_issues: List[str] = Field(
        description="List of critical issues that need to be addressed",
        default=[]
    )
    required_improvements: List[str] = Field(
        description="List of specific improvements required",
        default=[]
    )
    additional_recommendations: List[str] = Field(
        description="List of additional recommendations for enhancement",
        default=[]
    )

def get_json_output_tags():
    if INCLUDE_JSON_OUTPUT_TAGS:
        return "Give your json in <text_output> json </text_output> tags."
    else:
        return ""

async def check_if_llm_is_available(client: AsyncOpenAI, model: str):
    try:
        response = await client.chat.completions.create(model=model, messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, world!"}
        ])
        print(f"LLM {model} is available, response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"Error in checking if LLM {model} is available: {e}")
        return False

def get_relevance_threshold(summary_level):
    if summary_level == 1:
        return TOPMOST_SUMMARY_THRESHOLD
    elif summary_level == 2:
        return MIDLEVEL_SUMMARY_THRESHOLD
    else:
        return CHUNK_SUMMARY_THRESHOLD

def write_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"JSON saved to {filename}")

def read_json(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def get_mongodb_client():
    """Get MongoDB client connection."""
    return MongoClient(os.getenv("MONGO_DB_URL"))

def parse_json_response(response_text: str) -> Any:
    """
    Parse a JSON response that may be wrapped in backticks.
    
    Args:
        response_text: The response text to parse
        
    Returns:
        Any: The parsed JSON object
    """
    # Remove any markdown code block syntax
    response_text = re.sub(r'```json\n?', '', response_text)
    response_text = re.sub(r'```\n?', '', response_text)
    response_text = response_text.strip()
    
    try:
        # print(f"parse_json_response, response_text: {json.loads(response_text)}")
        return json.loads(response_text)
    except json.JSONDecodeError:
        # If the response is not valid JSON, try to extract a list from the text
        # Look for lines that start with numbers, bullets, or dashes
        lines = re.findall(r'^[\d\-\*\.]+\.?\s*(.+)$', response_text, re.MULTILINE)
        if lines:
            return lines
        # If no lines found, split by newlines and clean up
        # print(f"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\n') if line.strip()]}")
        return [line.strip() for line in response_text.split('\n') if line.strip()]
    
def calculate_number_of_tokens(text):
    # Load tokenizer for Mistral model
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    return token_count

async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):
    """
    Wrapper function that retries an async LLM API call when the response is empty.
    
    Args:
        async_func: The async function to call (usually an LLM API call)
        *args: Positional arguments to pass to async_func
        max_retries: Maximum number of retry attempts (default: 3)
        **kwargs: Keyword arguments to pass to async_func
        
    Returns:
        The result of the async_func call, ensuring it's not empty
        
    Raises:
        Exception: If max_retries is reached and the response is still empty
    """
    # Create logs directory if it doesn't exist
    log_dir = "error_logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Extract the function name for logging purposes
    func_name = async_func.__name__ if hasattr(async_func, "__name__") else "unknown_function"
    
    # Try to get the caller's name from the stack
    try:
        caller_frame = inspect.currentframe().f_back
        caller_name = caller_frame.f_code.co_name if caller_frame else "unknown_caller"
    except Exception:
        caller_name = "unknown_caller"
    
    for attempt in range(max_retries):
        try:
            result = await async_func(*args, **kwargs)
            
            # Check if result is None, empty string, just whitespace, or contains tool calls
            is_empty = result is None or (isinstance(result, str) and result.strip() == "")
            contains_tool_call = False
            
            if isinstance(result, str):
                # Check for tool_call patterns
                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)
                contains_tool_call_text = 'tool_call' in result.lower()
                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text
            
            if is_empty or contains_tool_call:
                if is_empty:
                    reason = "empty response"
                    error_type = 'empty_response'
                elif contains_tool_call:
                    reason = "response contains tool calls"
                    error_type = 'tool_call_response'
                
                print(f"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...")
                
                # Get debug information to log
                debug_info = {
                    'error_type': error_type,
                    'function': func_name,
                    'caller': caller_name,
                    'attempt': attempt + 1,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]
                }
                
                # Extract prompt information based on different API patterns
                # For the direct messages pattern in kwargs
                if 'messages' in kwargs:
                    debug_info['messages'] = kwargs['messages']
                    
                # For the pattern where the func is a closure with local make_api_call
                # Try to get source code of the async_func to check for patterns
                try:
                    source = inspect.getsource(async_func)
                    if "chat.completions.create" in source:
                        debug_info['api_pattern'] = "chat_completions_closure"
                except Exception:
                    pass
                
                # Try to extract system_prompt and user_prompt from the caller's frame if available
                try:
                    if caller_frame:
                        caller_locals = caller_frame.f_locals
                        # Capture common patterns in this codebase
                        if 'system_prompt' in caller_locals:
                            debug_info['system_prompt'] = caller_locals['system_prompt']
                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                        # If this is using the OpenAI client pattern, get the model too
                        if 'model' in caller_locals:
                            debug_info['model'] = caller_locals['model']
                        # For the antropic calls
                        if 'CRITIQUE_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                        elif 'GENERATOR_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                        elif 'MODEL' in caller_locals:
                            debug_info['model'] = caller_locals['MODEL']
                except Exception as e:
                    debug_info['frame_inspection_error'] = str(e)
                
                # Save the debug information
                timestamp = int(time.time())
                log_filename = f"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json"
                
                try:
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        json.dump(debug_info, f, indent=2, ensure_ascii=False)
                    print(f"Logged empty response details to {log_filename}")
                except Exception as log_error:
                    print(f"Failed to log empty response details: {str(log_error)}")
                
                # Continue to the next retry attempt
                continue
                
            # If we get here, we have a non-empty response
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            print(f"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}")
            
            # Get debug information to log
            debug_info = {
                'error_type': error_type,
                'error_message': error_msg,
                'function': func_name,
                'caller': caller_name,
                'attempt': attempt + 1,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'stack_trace': traceback.format_exc()
            }
            
            # Extract prompt information based on different API patterns
            # For the direct messages pattern in kwargs
            if 'messages' in kwargs:
                debug_info['messages'] = kwargs['messages']
                
            # For the pattern where the func is a closure with local make_api_call
            # Try to get source code of the async_func to check for patterns
            try:
                source = inspect.getsource(async_func)
                if "chat.completions.create" in source:
                    debug_info['api_pattern'] = "chat_completions_closure"
            except Exception:
                pass
            
            # Try to extract system_prompt and user_prompt from the caller's frame if available
            try:
                if caller_frame:
                    caller_locals = caller_frame.f_locals
                    # Capture common patterns in this codebase
                    if 'system_prompt' in caller_locals:
                        debug_info['system_prompt'] = caller_locals['system_prompt']
                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                    # If this is using the OpenAI client pattern, get the model too
                    if 'model' in caller_locals:
                        debug_info['model'] = caller_locals['model']
                    # For the antropic calls
                    if 'CRITIQUE_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                    elif 'GENERATOR_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                    elif 'MODEL' in caller_locals:
                        debug_info['model'] = caller_locals['MODEL']
            except Exception as frame_error:
                debug_info['frame_inspection_error'] = str(frame_error)
            
            # Save the debug information
            timestamp = int(time.time())
            log_filename = f"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json"
            
            try:
                with open(log_filename, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, indent=2, ensure_ascii=False)
                print(f"Logged error details to {log_filename}")
            except Exception as log_error:
                print(f"Failed to log error details: {str(log_error)}")
            
            if attempt == max_retries - 1:
                # If we've exhausted all retries and still have an error
                print(f"Failed to get non-empty response after {max_retries} attempts")
                return None
            
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** attempt))
    
    # If we've exhausted all retries and still have an empty response
    print(f"Failed to get non-empty response after {max_retries} attempts")
    return None

await check_if_llm_is_available(generator_client, GENERATOR_MODEL_NAME)
await check_if_llm_is_available(critique_client, CRITIQUE_MODEL_NAME)


# %%
ich_ectd_guideline_referenced_with_efficacy_guidelines = read_json("/Users/<USER>/projects/scalegen/MedNova/structured jsons/adverse_event_demo.json")

doc_to_work_on = ich_ectd_guideline_referenced_with_efficacy_guidelines["module2"]["2.7"]["2.7.4"]["2.7.4.2"]


# %%
async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:
    """
    Generate checkpoint questions from a single individual summary.
    
    Args:
        summary_content: The content of the individual summary
        section_info: Dictionary containing section information
        
    Returns:
        List[str]: A list of checkpoint questions generated from the individual summary
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive list of checkpoint questions based on the provided summary."
            
            prompt = f"""
            Create a detailed checklist of specific questions that should be answered in input documents for the following section of ICH eCTD guidelines:
            
            Section: {section_info.get('section', '')}
            Title: {section_info.get('title', '')}

            Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
            
            Individual Summary:
            {summary_content}
            
            The checklist should:
            1. Include all key requirements and recommendations from this summary expressed as specific questions
            2. Cover all aspects mentioned in the summary
            3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.
            4. Be specific enough to clearly determine if an input document addresses each point
            5. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., "Does the document mention if the product is in powder or liquid form?")
            6. Focus on technical and regulatory content needed for NDA documentation
            7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant
            8. Cover stability data, packaging specifications, and compatibility information where appropriate
            9. Include questions about the validation and verification methods used
            """
            
            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)

            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings={
                    "temperature": 0.1,
                },
                retries=3
            )

            response = await checkpoint_list_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            retry_count += 1
            print(f"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Generating basic checkpoints from summary as fallback.")
                # Generate basic checkpoints as fallback
                try:
                    # Extract key sentences from summary as basic checkpoints
                    sentences = [s.strip() + "?" for s in summary_content.split(".") if len(s.strip()) > 20]
                    # Convert statements to questions where possible
                    questions = []
                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload
                        if not sentence.endswith("?"):
                            questions.append(f"Does the document address: {sentence}?")
                        else:
                            questions.append(sentence)
                    return questions
                except Exception:
                    print("Fallback checkpoint generation failed. Returning empty list.")
                    return []
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []

async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:
    """
    Generate checkpoints incrementally from a list of individual summaries.
    
    Args:
        section_info: Dictionary containing section information
        list_of_individual_summary_contents: List of individual summary contents
        
    Returns:
        List[str]: A comprehensive list of checkpoints generated from all individual summaries
    """
    # Handle empty input
    if not list_of_individual_summary_contents:
        return []
        
    # Handle single summary case
    if len(list_of_individual_summary_contents) == 1:
        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)
    
    # Maximum token limit for checkpoint list batches
    
    # Generate initial checkpoints from each summary in parallel
    tasks = [_generate_checkpoints_from_one_summary(summary, section_info) for summary in list_of_individual_summary_contents]
    current_checkpoints = await asyncio.gather(*tasks)
    
    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):
        """Helper function to combine multiple checkpoint lists while preserving all unique checkpoints."""
        total_lists = len(checkpoint_lists)
        print(f"\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints")
        
        # Calculate token count for all lists combined
        combined_text = "\n".join(["\n".join(cp) for cp in checkpoint_lists])
        total_tokens = calculate_number_of_tokens(combined_text)
        print(f"Total input tokens for checkpoint merging: {total_tokens}")
        
        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive merged list of checkpoint questions."
        
        user_prompt = f"""
        Merge the following {total_lists} lists of checkpoint questions into a single comprehensive list for the following section of ICH eCTD guidelines:
        
        Section: {section_info.get('section', '')}
        Title: {section_info.get('title', '')}

        Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
        
        """
        
        # Add each checkpoint list
        for i, checkpoints in enumerate(checkpoint_lists, 1):
            user_prompt += f"""
        Checkpoint List {i}:
        {json.dumps(checkpoints, indent=2)}
        
        """
        
        user_prompt += f"""
        Requirements for Merging:
        1. Preserve ALL unique questions from all {total_lists} lists
        2. Remove any duplicate or redundant questions
        3. Ensure the merged list is comprehensive and covers all aspects
        4. Maintain the specificity and clarity of each question
        5. Keep the question format consistent (e.g., "Does the document mention...")
        6. Ensure each question focuses on a single specific point
        7. Group related questions together when possible
        8. The referenced list of golden instructions and checkpoints must be strictly adhered
        9. Keep all questions focused on technical regulatory content for NDA documentation
        """
        
        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)

        checkpoint_list_agent = Agent(
            model=generator_model,
            system_prompt=system_prompt,
            output_type=CheckpointList,
            model_settings={
                "temperature": 0.1,
            },
            retries=3
        )

        response = await checkpoint_list_agent.run(user_prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        # print(f"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        merged_checkpoints = response.output.checkpoints
        
        # Validate the output
        if len(merged_checkpoints) < 2:
            raise ValueError(f"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}")
        
        print(f"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints")
        
        return merged_checkpoints
    
    # Iteratively merge checkpoint lists in optimal batches
    while len(current_checkpoints) > 1:
        # Create optimal batches based on token count and number of checkpoints
        batches = []
        current_batch = []
        current_token_count = 0
        current_checkpoint_count = 0
        
        for checkpoint_list in current_checkpoints:
            # Calculate tokens for this checkpoint list
            checkpoint_text = "\n".join(checkpoint_list)
            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)
            
            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch
            if (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):
                batches.append(current_batch)
                current_batch = [checkpoint_list]
                current_token_count = checkpoint_tokens
                current_checkpoint_count = len(checkpoint_list)
            else:
                current_batch.append(checkpoint_list)
                current_token_count += checkpoint_tokens
                current_checkpoint_count += len(checkpoint_list)
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for checkpoint list merging")
        for i, batch in enumerate(batches):
            total_checkpoints = sum(len(cp) for cp in batch)
            print(f"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints")
        
        # Process all batches in parallel
        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]
        
        # Wait for all merges to complete
        current_checkpoints = await asyncio.gather(*tasks)
    
    print(f"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}")
    return current_checkpoints[0]

# %%
async def generate_checkpoint_list(section, title, description, referenced_efficacy_guidelines, enhanced_instructions_and_checkpoints):
    """
    Generate a checklist of questions that should be answered by summaries for this section.
    
    Args:
        section: The section identifier (e.g., "3.2.P.2.2.1")
        title: The title of the section
        description: The description of the section
        referenced_efficacy_guidelines: List of efficacy guidelines referenced
        enhanced_instructions_and_checkpoints: A checkpoint list of golden instructions that must be strictly adhered.
        
    Returns:
        List[str]: A list of checkpoint questions that should be answered in summaries
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Create a prompt for the LLM to generate a checklist
            prompt = f"""
            Create a detailed checklist of specific questions that should be answered in summaries for the following section of ICH eCTD guidelines:
            
            Section: {section}
            Title: {title}
            Description: {description}

            Golden Instructions and Checkpoints: {enhanced_instructions_and_checkpoints}
            
            Referenced Efficacy Guidelines: {', '.join(referenced_efficacy_guidelines)}
            
            The checklist should:
            1. Include all key requirements and recommendations for this section expressed as specific questions
            2. Cover all aspects mentioned in the title and description
            3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.
            4. Address all points from the referenced efficacy guidelines
            5. Be specific enough to clearly determine if a summary addresses each point
            6. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., "Does the summary mention if the product is in powder or liquid form?")
            7. Focus on technical and regulatory content needed for NDA documentation
            8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant
            9. Cover stability data, packaging specifications, and compatibility information where appropriate
            10. Include questions about the validation and verification methods used

            Don't include references to sections/guidelines in the questions themselves.
            """

            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive list of checkpoint questions for the given section."
            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)
            
            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings={
                    "temperature": 0.1,
                },
                retries=3
            )

            response = await checkpoint_list_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            print(f"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            traceback.print_exc()
            retry_count += 1
            print(f"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning empty list.")
                return []
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []


# %%
async def evaluate_summary_relevance(summary_content, section_info, checkpoints):
    """
    Use an LLM to evaluate if a summary is relevant to a section by determining if it answers the checkpoint questions.
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoints: List of checkpoint questions that should be answered
        
    Returns:
        float: relevance_score
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Create a prompt for the LLM to evaluate the summary
            prompt = f"""
            Evaluate if the following summary is relevant to the ICH eCTD guideline section described below by determining if it answers any of the checkpoint questions.
            
            SECTION INFORMATION:
            Section: {section_info.get('section', '')}
            Title: {section_info.get('title', '')}
            
            CHECKPOINT QUESTIONS:
            {json.dumps(checkpoints, indent=2)}
            
            SUMMARY TO EVALUATE:
            {summary_content}
            
            Task:
            Carefully analyze if the summary contains information that would answer any of the checkpoint questions listed above. A summary is considered relevant if it provides information to answer at least one checkpoint question, even partially.
            
            Guidelines for evaluation:
            1. For each checkpoint question, determine if the summary provides any information that helps answer it
            2. The summary does not need to explicitly mention the question itself, only provide relevant information
            3. Even partial answers to questions should count toward relevance
            4. Technical content that directly relates to the question topic is particularly important
            5. If multiple questions are answered, the summary should receive a higher relevance score
            6. Consider the depth and detail of the information provided when scoring relevance
            7. If any ONE of the checkpoint questions is answered in the summary, then the summary is relevant

            Please provide your evaluation as:
            How relevant is it on a scale of 0-10? (0 = not relevant, 10 = highly relevant)
            - Score 0-2: Answers no questions or provides only tangential information
            - Score 3-5: Partially answers at least one question with limited detail
            - Score 6-8: Fully answers one question or partially answers multiple questions
            - Score 9-10: Comprehensively answers multiple questions with specific details
            """

            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines with exceptional abilities in evaluating the relevance of content to specific technical questions."
            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)

            relevance_evaluation_agent = Agent(
                model=critique_model,
                system_prompt=system_prompt,
                output_type=RelevanceEvaluation,
                model_settings={
                    "temperature": 0.1,
                },
                retries=3
            )

            response = await relevance_evaluation_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            print(f"evaluate_summary_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            return response.output.relevance_score
            
        except Exception as e:
            retry_count += 1
            print(f"Error in evaluate_summary_relevance (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning default value.")
                return 0.0
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return default value
    return 0.0


# %%
async def search_summaries_by_llm(section_info, efficacy_guidelines, summary_level=1):
    """
    Search for summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        efficacy_guidelines: List of efficacy guidelines to search in (e.g., ["Q6A", "Q6B"])
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['efficacy_docs_summaries']
    
    # Find all summaries matching the criteria
    if len(efficacy_guidelines) > 0:
        summaries = list(collection.find({
            "efficacy_guideline": {"$in": efficacy_guidelines},
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))

    # summaries = list(collection.find({
    #     "summary_level": summary_level
    # }))

    print(f"Found {len(summaries)} summaries for section {section_info.get('section', '')} efficacy guidelines {efficacy_guidelines} at level {summary_level}")
    
    if not summaries:
        print(f"No summaries found for efficacy guidelines {efficacy_guidelines} at level {summary_level}")
        return []
    
    # Get checkpoints for the section
    checkpoints = section_info.get("checkpoint_list", [])
    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        # Generate checkpoints if not already available
        checkpoints = await generate_checkpoint_list(
            section_info.get("section", ""),
            section_info.get("title", ""),
            section_info.get("description", ""),
            section_info.get("referenced_efficacy_guidelines", []),
            section_info.get("enhanced_instructions_and_checkpoints", "")
        )
    
    # Evaluate each summary
    evaluated_summaries = []
    tasks = [evaluate_summary_relevance(summary["content"], section_info, checkpoints) for summary in summaries]
    results = await asyncio.gather(*tasks)
    
    for summary, relevance_score in zip(summaries, results):
        if relevance_score >= get_relevance_threshold(summary_level):
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
            summary["similarity_score"] = relevance_score
            evaluated_summaries.append(summary)
        else:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
    
    # Sort by relevance score
    evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries


# %%
def get_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['efficacy_docs_summaries']
    chunk_collection = db['efficacy_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks


# %%
def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['efficacy_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries


# %%
async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Extract section information
    section = section_info.get("section", "")
    title = section_info.get("title", "")
    description = section_info.get("description", "")
    referenced_efficacy_guidelines = section_info.get("referenced_efficacy_guidelines", [])
    
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_summaries_by_llm(
        section_info=section_info,
        efficacy_guidelines=referenced_efficacy_guidelines,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    for final_summary in final_summaries:
        summaries = get_summaries_for_final_summary(final_summary["_id"])
        
        # Evaluate each individual summary
        evaluated_summaries = []
        tasks = [evaluate_summary_relevance(summary["content"], section_info, section_info.get("checkpoint_list", [])) for summary in summaries]
        results = await asyncio.gather(*tasks)
        
        for summary, relevance_score in zip(summaries, results):
            if relevance_score >= get_relevance_threshold(2):
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
                summary["similarity_score"] = relevance_score
                evaluated_summaries.append(summary)
            else:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
        
        # Sort by relevance score
        evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
        individual_summaries.extend(evaluated_summaries)
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    # BATCH_SIZE = 50
    
    # for i in range(0, len(individual_summaries), BATCH_SIZE):
    #     batch = individual_summaries[i:i + BATCH_SIZE]
    #     batch_tasks = []
        
    #     for individual_summary in batch:
    #         summary_chunks = get_chunks_for_summary(individual_summary["_id"])
    #         # Create tasks for parallel evaluation of chunks
    #         tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("efficacy_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
    #         batch_tasks.extend(zip(summary_chunks, tasks))
        
    #     # Execute all tasks in parallel
    #     results = await asyncio.gather(*[task for _, task in batch_tasks])
        
    #     # Process results
    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):
    #         if relevance_score >= get_relevance_threshold(0):
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    #             chunk["similarity_score"] = relevance_score
    #             chunks.append(chunk)
    #         else:
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks


# %%
async def extract_relevant_chunks(json_obj):
    """
    Recursively iterates through the nested JSON structure, extracts titles and descriptions,
    generates checkpoint lists, and finds relevant summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with added relevant summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs processing
            if (obj and "title" in obj and "description" in obj and 
                "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        # Generate checkpoint list for this section
        section["checkpoint_list"] = await generate_checkpoint_list(
            section["section"], 
            section["title"], 
            section["description"], 
            section.get("referenced_efficacy_guidelines", []),
            section.get("enhanced_instructions_and_checkpoints", "")
        )
        
        # Find relevant summaries and chunks for this section
        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(
            section
        )

        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks")
        
        # Add the results to the section
        section["relevant_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "efficacy_guideline": s["efficacy_guideline"], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            } for s in final_summaries
        ]
        
        section["relevant_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "efficacy_guideline": s["efficacy_guideline"], 
                "similarity_score": s.get("similarity_score", 0), 
                "content": s["content"]
            } for s in individual_summaries
        ]

        # Create a final summary from the relevant individual summaries
        # if len(section["relevant_individual_summaries"]) > 0:
        #     final_summary = await create_combined_summary([summary["content"] for summary in section["relevant_individual_summaries"]])
        #     section["efficacy_guideline_combined_summary"] = final_summary
        # else:
        #     # If no relevant summaries found, create a summary from section information
        #     final_summary = await generate_summary_from_section_info(section)
        #     section["efficacy_guideline_combined_summary"] = final_summary

        # Generate checkpoints incrementally from individual summaries
        if len(section["relevant_individual_summaries"]) > 0:
            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(
                section,
                [summary["content"] for summary in section["relevant_individual_summaries"]]
            )
        else:
            # If no relevant summaries found, generate checkpoints from section information
            checkpoints = await generate_checkpoint_list(
                section["section"],
                section["title"],
                section["description"],
                section.get("referenced_efficacy_guidelines", []),
                section.get("enhanced_instructions_and_checkpoints", "")
            )
        
        print(f"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}")
        section["efficacy_guideline_combined_summaries_checkpoint_list"] = checkpoints
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
processed_doc = await extract_relevant_chunks(doc_to_work_on)

# Save the results to a JSON file
write_to_json(processed_doc, "processed_ectd_guidelines_with_relevant_chunks.json")


# %%
processed_doc = read_json("processed_ectd_guidelines_with_relevant_chunks.json")


# %%
async def search_input_docs_by_llm(section_info, efficacy_input_doc_tag, summary_level=1):
    """
    Search for input document summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        efficacy_input_doc_tag: The input document tag to search for
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['efficacy_input_docs_summaries']
    
    # Find all summaries matching the criteria
    if efficacy_input_doc_tag:
        summaries = list(collection.find({
            "efficacy_input_doc_tag": efficacy_input_doc_tag,
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))
    
    print(f"Found {len(summaries)} input document summaries for section {section_info.get('section', '')} with tag {efficacy_input_doc_tag} at level {summary_level}")
    
    if not summaries:
        print(f"No input document summaries found for tag {efficacy_input_doc_tag} at level {summary_level}")
        return []
    
    # Get checkpoints for the section - prefer using checkpoints from efficacy_guideline_combined_summary
    print(f"Using {len(section_info.get('efficacy_guideline_combined_summaries_checkpoint_list', []))} efficacy_guideline_combined_summaries_checkpoint_list from efficacy_guideline_combined_summary for section {section_info.get('section', '')}")
    checkpoints = section_info.get("efficacy_guideline_combined_summaries_checkpoint_list", [])

    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        return []
    
    # Evaluate each summary
    evaluated_summaries = []
    tasks = [evaluate_summary_relevance(summary["content"], section_info, checkpoints) for summary in summaries]
    results = await asyncio.gather(*tasks)
    
    for summary, relevance_score in zip(summaries, results):
        if relevance_score >= get_relevance_threshold(summary_level):
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
            summary["similarity_score"] = relevance_score
            evaluated_summaries.append(summary)
        else:
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
    
    # Sort by relevance score
    evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries


# %%
def get_input_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the input document chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['efficacy_input_docs_summaries']
    chunk_collection = db['efficacy_input_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks


# %%
def get_input_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual input document summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['efficacy_input_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries


# %%
async def filter_input_docs_by_section(section_info: Dict[str, Any], efficacy_input_doc_tag: str = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter input document summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        efficacy_input_doc_tag: The input document tag to search for
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_input_docs_by_llm(
        section_info=section_info,
        efficacy_input_doc_tag=efficacy_input_doc_tag,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    BATCH_SIZE = 50
    
    for i in range(0, len(final_summaries), BATCH_SIZE):
        batch = final_summaries[i:i + BATCH_SIZE]
        batch_tasks = []
        
        for final_summary in batch:
            summaries = get_input_summaries_for_final_summary(final_summary["_id"])
            # Create tasks for parallel evaluation of individual summaries
            tasks = [evaluate_summary_relevance(summary["content"], section_info, section_info.get("efficacy_guideline_combined_summaries_checkpoint_list", [])) for summary in summaries]
            batch_tasks.extend(zip(summaries, tasks))
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*[task for _, task in batch_tasks])
        
        # Process results
        for (summary, _), relevance_score in zip(batch_tasks, results):
            if relevance_score >= get_relevance_threshold(2):
                print(f"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
                summary["similarity_score"] = relevance_score
                individual_summaries.append(summary)
            else:
                print(f"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    
    for i in range(0, len(individual_summaries), BATCH_SIZE):
        batch = individual_summaries[i:i + BATCH_SIZE]
        batch_tasks = []
        
        for individual_summary in batch:
            summary_chunks = get_input_chunks_for_summary(individual_summary["_id"])
            # Create tasks for parallel evaluation of chunks
            tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("efficacy_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
            batch_tasks.extend(zip(summary_chunks, tasks))
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*[task for _, task in batch_tasks])
        
        # Process results
        for (chunk, _), relevance_score in zip(batch_tasks, results):
            if relevance_score >= get_relevance_threshold(0):
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
                chunk["similarity_score"] = relevance_score
                chunks.append(chunk)
            else:
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks


# %%
async def extract_relevant_input_docs(json_obj, efficacy_input_doc_tag: str = None):
    """
    Recursively iterates through the nested JSON structure, finds relevant efficacy guideline summaries,
    creates a final summary, and then finds relevant input document summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        efficacy_input_doc_tag: The input document tag to search for
        
    Returns:
        Dict: The processed JSON object with added relevant input document summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that has relevant efficacy guideline summaries
            if (obj and "relevant_final_summaries" in obj and 
                obj["relevant_final_summaries"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Processing section {section.get('section', 'unknown')} for input documents")

        # Find relevant input document summaries and chunks
        input_final_summaries, input_individual_summaries, input_chunks = await filter_input_docs_by_section(
            section,
            efficacy_input_doc_tag
        )
        
        # Add the results to the section
        section["relevant_input_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "efficacy_input_doc_tag": s["efficacy_input_doc_tag"], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            } for s in input_final_summaries
        ]
        
        section["relevant_input_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "efficacy_input_doc_tag": s["efficacy_input_doc_tag"], 
                "similarity_score": s.get("similarity_score", 0),
                "content": s["content"]
            } for s in input_individual_summaries
        ]
        
        section["relevant_input_chunks"] = [
            {
                "_id": str(c["_id"]), 
                "efficacy_input_doc_tag": c["efficacy_input_doc_tag"], 
                "chunk_filename": c.get("chunk_filename", ""), 
                "similarity_score": c.get("similarity_score", 0), 
                "content": c.get("content", "")
            } for c in input_chunks
        ]
        
        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(input_final_summaries)} relevant input final summaries, {len(input_individual_summaries)} relevant input individual summaries, and {len(input_chunks)} relevant input chunks")
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
# Process the document to find relevant input documents
processed_doc_with_input_docs = await extract_relevant_input_docs(processed_doc, efficacy_input_doc_tag="efficacy_input_docs")

# Save the results to a JSON file
write_to_json(processed_doc_with_input_docs, "processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")


# %%
processed_doc_with_input_docs = read_json("processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")

# %%
def extract_evaluation_into_json(evaluation_text):
    """
    Extracts the numerical rating and structured feedback from the evaluation text into a JSON format.
    
    Args:
        evaluation_text: The evaluation text to parse
        
    Returns:
        Dict: JSON object containing the evaluation results
    """
    # Initialize the result dictionary
    result = {
        "overall_rating": None,
        "key_strengths": [],
        "critical_issues": [],
        "required_improvements": [],
        "additional_recommendations": []
    }
    
    # Split the text into sections
    sections = evaluation_text.split('\n\n')
    
    for section in sections:
        section = section.strip()
        if not section:
            continue
            
        # Extract overall rating
        if section.startswith("1. Overall Rating"):
            try:
                # Extract the number from the rating line
                rating_text = section.split(":")[1].strip()
                result["overall_rating"] = float(rating_text.split('/')[0])
            except:
                result["overall_rating"] = None
                
        # Extract key strengths
        elif section.startswith("2. Key Strengths"):
            points = section.split('\n')[1:]
            result["key_strengths"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract critical issues
        elif section.startswith("3. Critical Issues"):
            points = section.split('\n')[1:]
            result["critical_issues"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract required improvements
        elif section.startswith("4. Required Improvements"):
            points = section.split('\n')[1:]
            result["required_improvements"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract additional recommendations
        elif section.startswith("5. Additional Recommendations"):
            points = section.split('\n')[1:]
            result["additional_recommendations"] = [point.strip('- ').strip() for point in points if point.strip()]
    
    return result

# %%
async def create_incremental_plan(section_info, individual_summaries):
    """
    Create a comprehensive NDA section plan by incrementally combining plans generated from individual efficacy guideline summaries.
    Uses batch processing to efficiently process multiple summaries.
    
    Args:
        section_info: Dictionary containing section information
        individual_summaries: List of individual relevant efficacy guideline summaries
        
    Returns:
        str: Comprehensive plan for NDA section content
    """
    
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def combine_plans(plans_to_combine):
        """Helper function to combine multiple plans while preserving all requirements."""
        total_plans = len(plans_to_combine)
        print(f"\nCombining {total_plans} plans:")
        
        total_tokens = sum(calculate_number_of_tokens(plan) for plan in plans_to_combine)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple section plans into one comprehensive unified plan that will guide the generation of a complete, regulatory-compliant NDA section.

CRITICAL REQUIREMENTS:
1. Plan Comprehensiveness (HIGHEST PRIORITY):
   - Preserve EVERY requirement, element, and consideration from all plans
   - Ensure NO regulatory requirement is lost or diluted
   - Maintain ALL technical specifications from all plans
   - Include ALL validation and verification details
   - Preserve ALL cross-references and dependencies
   - Keep ALL acceptance criteria and thresholds
   - Maintain ALL risk assessments and control strategies
   - Include ALL testing requirements and methodologies
   - Preserve ALL regulatory compliance elements

2. ICH eCTD Compliance:
   - Ensure the plan follows ICH eCTD v3.2.2 structure requirements
   - Maintain proper section organization and hierarchy
   - Include all required eCTD elements and subsections
   - Preserve regulatory guideline references
   - Ensure plan addresses all ICH requirements

3. Integration Strategy:
   - Create a logical, cohesive document structure
   - Group related requirements together
   - Eliminate redundancies without losing information
   - Organize requirements in order of regulatory importance
   - Ensure technical specifications are properly contextualized
   - Maintain relationships between different requirements
   - Create clear section and subsection organization
   - Establish information hierarchy and flow

4. Technical Depth:
   - Ensure the plan specifies all technical parameters to include
   - Maintain detailed specifications for tests and analyses
   - Preserve methodological details
   - Include precise acceptance criteria
   - Specify validation requirements
   - Maintain detailed documentation standards
   - Include specific technical cross-references

5. Documentation Requirements:
   - Specify all tables, figures, and data presentations needed
   - Detail formatting requirements for technical information
   - Include citations and reference standards
   - Specify any appendices or supplementary materials
   - Maintain documentation conventions from all plans
   - Specify any required attachments or supporting documents

6. Quality Assurance Elements:
   - Include all quality control procedures
   - Maintain quality assurance checkpoints
   - Preserve change control processes
   - Include batch release criteria
   - Specify stability and shelf-life considerations
   - Maintain monitoring and surveillance requirements

7. Risk Management:
   - Include all risk assessment requirements
   - Maintain mitigation strategies
   - Preserve safety considerations
   - Include contingency requirements
   - Specify monitoring and review processes
   - Maintain risk-benefit analyses

OUTPUT FORMAT:
- Structured, hierarchical plan with numbered sections and subsections
- Clear delineation of requirements, specifications, and expectations
- Specific notation of required tables, figures, and data presentations
- Precise listing of technical parameters and acceptance criteria
- Clear indication of required cross-references and dependencies
- Logical organization following ICH eCTD structure

The plan should be detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, ensuring all regulatory requirements are addressed and no technical details are omitted."""

        user_prompt = f"""Please create a comprehensive unified plan that combines the following {total_plans} section plans into one cohesive blueprint for generating a complete, regulatory-compliant NDA section.

"""
        
        # Add each plan with a header
        for i, plan in enumerate(plans_to_combine, 1):
            user_prompt += f"""
Plan {i}:
{plan}

"""

        user_prompt += f"""
VERIFICATION CHECKLIST:
1. Does the combined plan include ALL requirements from all {total_plans} plans?
2. Are ALL technical specifications preserved?
3. Are ALL validation and verification details maintained?
4. Are ALL cross-references and dependencies included?
5. Are ALL regulatory compliance elements preserved?
6. Does the plan follow ICH eCTD structure?
7. Is the plan logically organized with clear sections and subsections?
8. Are ALL required tables, figures, and data presentations specified?
9. Are ALL acceptance criteria and thresholds preserved?
10. Are ALL risk assessment and management requirements included?

IMPORTANT CONSIDERATIONS:
1. The plan must preserve EVERY requirement, element, and consideration from all {total_plans} plans
2. No regulatory requirement should be lost or diluted
3. Technical specifications must be maintained in full detail
4. The plan should follow ICH eCTD v3.2.2 structure requirements
5. The plan should eliminate redundancies while preserving all unique elements
6. The plan should specify all required tables, figures, and data presentations
7. Technical parameters and acceptance criteria should be precisely specified
8. Cross-references and dependencies should be clearly indicated
9. The plan should be organized in a logical, hierarchical structure
10. The plan must be detailed enough to serve as a comprehensive blueprint

After creating the combined plan, verify it against the verification checklist to ensure all elements have been properly incorporated."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"combine_plans token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)
    
    async def create_plan_from_one_summary(summary_content, section_info):
        """Generate a plan from a single summary content."""
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to create a detailed, structured plan for an NDA section based on the provided efficacy guideline summary and section information.

Your plan will serve as a comprehensive blueprint for generating complete, regulatory-compliant content for this NDA section.

CRITICAL REQUIREMENTS:
1. Regulatory Compliance (HIGHEST PRIORITY):
   - Ensure the plan addresses ALL requirements from ICH eCTD v3.2.2
   - Include ALL elements specified in the section description and reference text
   - Follow ALL golden instructions and checkpoints provided
   - Address ALL requirements from the efficacy guideline summary
   - Ensure compliance with all formatting instructions

2. Plan Structure:
   - Create a hierarchical, logical document structure
   - Include clearly numbered sections and subsections
   - Organize requirements in order of regulatory importance
   - Group related requirements together
   - Establish clear information flow and progression
   - Follow standard eCTD section organization

3. Technical Specifications:
   - Specify ALL technical parameters to be included
   - Detail ALL required tests, analyses, and methodologies
   - Include precise acceptance criteria and thresholds
   - Specify validation and verification requirements
   - Detail required technical documentation
   - Include specific cross-references to other sections

4. Documentation Elements:
   - Specify ALL required tables, figures, and data presentations
   - Detail formatting requirements for technical information
   - Include required citations and reference standards
   - Specify any appendices or supplementary materials
   - Detail any required attachments or supporting documents

5. Comprehensive Coverage:
   - Ensure the plan addresses EVERY point in the efficacy guideline summary
   - Include ALL technical specifications mentioned
   - Incorporate ALL validation and verification details
   - Address ALL risk assessment and management requirements
   - Include ALL safety and efficacy considerations
   - Preserve ALL cross-references and dependencies

OUTPUT FORMAT:
- Structured, hierarchical plan with numbered sections and subsections
- Clear delineation of requirements, specifications, and expectations
- Specific notation of required tables, figures, and data presentations
- Precise listing of technical parameters and acceptance criteria
- Clear indication of required cross-references and dependencies
- Logical organization following ICH eCTD structure

The plan should be detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, ensuring all regulatory requirements are addressed and no technical details are omitted."""

        user_prompt = f"""Please create a detailed, structured plan for generating content for the following NDA section based on the efficacy guideline summary and section information provided.

SECTION INFORMATION:
- Section: {section_info.get('section', '')}
- Title: {section_info.get('title', '')}
- Description: {section_info.get('description', '')}
- Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
- Formatting Instructions: {section_info.get("formatting_instructions", "")}

EFFICACY GUIDELINE SUMMARY:
{summary_content}

IMPORTANT CONSIDERATIONS:
1. The plan must address EVERY requirement from the section information and efficacy guideline summary
2. The plan must follow ICH eCTD v3.2.2 structure requirements
3. The plan should specify all required tables, figures, and data presentations
4. Technical parameters and acceptance criteria should be precisely specified
5. The plan should include all validation and verification requirements
6. Cross-references and dependencies should be clearly indicated
7. The plan should be organized in a logical, hierarchical structure
8. The plan must be detailed enough to serve as a comprehensive blueprint

Create a structured plan that would guide the generation of complete, regulatory-compliant content for this NDA section."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"create_plan_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    # Handle empty input
    if not individual_summaries:
        return ""
        
    # Handle single summary case
    if len(individual_summaries) == 1:
        return await create_plan_from_one_summary(individual_summaries[0], section_info)
    
    # First generate initial plans from each summary
    tasks = [create_plan_from_one_summary(summary, section_info) for summary in individual_summaries]
    current_plans = await asyncio.gather(*tasks)
    
    # Process plans iteratively, combining batches in parallel
    while len(current_plans) > 1:
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for plan in current_plans:
            plan_tokens = calculate_number_of_tokens(plan)
            
            # If adding this plan would exceed the token limit, start a new batch
            if current_batch and current_token_count + plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                batches.append(current_batch)
                current_batch = [plan]
                current_token_count = plan_tokens
            else:
                current_batch.append(plan)
                current_token_count += plan_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for plan processing")
        for i, batch in enumerate(batches):
            print(f"Batch {i+1} contains {len(batch)} plans with {sum(calculate_number_of_tokens(p) for p in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_plans(batch) for batch in batches]
        
        # Wait for all combinations to complete
        current_plans = await asyncio.gather(*tasks)

    print("create_incremental_plan tokens: ", calculate_number_of_tokens(current_plans[0]))
    
    return current_plans[0]

async def create_incremental_content(plan, section_info, input_chunks, critique_feedback=None):
    """
    Generate content incrementally using the plan created in Stage 1 and input document chunks.
    Uses batch processing to efficiently process input chunks and combine content.
    
    Args:
        plan: The comprehensive plan for the NDA section
        section_info: Dictionary containing section information
        input_chunks: List of input document chunks to use for content generation
        critique_feedback: Optional critique feedback from previous iterations to address missing content
        
    Returns:
        str: Generated NDA section content
    """
    
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def generate_content_from_chunks(plan, chunks, section_info, critique_feedback=None):
        """Generate content from a batch of input chunks using the plan."""
        total_chunks = len(chunks)
        print(f"\nGenerating content from {total_chunks} chunks:")
        
        total_tokens = sum(calculate_number_of_tokens(chunk.get('content', '')) for chunk in chunks)
        print(f"Total input chunk tokens: {total_tokens}")
        
        system_prompt = """You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to generate high-quality, detailed, regulatory-compliant content for an NDA section based on the provided plan and input document chunks.

CRITICAL REQUIREMENTS:
1. Plan-Guided Content Generation (HIGHEST PRIORITY):
   - Use the provided plan as your PRIMARY FILTER and GUIDE for content selection
   - ONLY include content from input chunks that directly addresses elements in the plan
   - DISCARD any chunk content that does not align with plan requirements
   - Prioritize chunks that contain specific technical data, validation information, and regulatory details mentioned in the plan
   - If a chunk contains both relevant and irrelevant information, extract ONLY the plan-relevant portions
   - Ensure every piece of content serves a specific purpose outlined in the plan

2. Regulatory Compliance (HIGHEST PRIORITY):
   - Follow the provided plan EXACTLY as it has been designed to meet all regulatory requirements
   - Ensure compliance with ICH eCTD v3.2.2 standards
   - Address ALL requirements specified in the plan
   - Follow ALL formatting instructions
   - Adhere to ALL golden instructions and checkpoints

3. Table Tag Preservation (HIGHEST PRIORITY):
   - PRESERVE ALL table tags EXACTLY as they appear in the input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`, etc.)
   - DO NOT modify, rename, or change table tags in ANY way
   - DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
   - MAINTAIN the EXACT position and order of table tags as they appear in the input chunks
   - Include table tags in the exact location where the table should appear in your content
   - Use the table summary information to write brief contextual statements about the table if needed
   - DO NOT omit any table tags from the input chunks that are relevant to the plan
   - Treat table tags as placeholders that must be preserved exactly for later replacement

4. Figure Preservation (HIGHEST PRIORITY):
   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents
   - DO NOT rename any figures (e.g., if a figure is "Figure 1: XYZ" do NOT change it to "Figure 5.7.3: ABC")
   - INCLUDE ALL figures that appear in the input source documents
   - DO NOT modify the content of any figures
   - DO NOT omit any figures from the source documents
   - Include references to all figures in your text

5. NO META-COMMENTARY (HIGHEST PRIORITY):
   - DO NOT include any commentary about how you addressed critique feedback
   - DO NOT include verification checklists or statements about compliance with plans
   - DO NOT include meta-statements like "This addresses the critique feedback" or "As required by the plan"
   - DO NOT include process-related commentary like "Verification of Compliance" sections
   - DO NOT include formatting instructions or comments about document structure in the output
   - Generate ONLY the actual NDA content that would appear in the final submission
   - DO NOT include any self-referential statements about the generation process

6. Content Quality:
   - Generate comprehensive, technically precise content
   - Include all required technical details, parameters, and specifications ONLY as specified in the plan
   - Provide detailed explanations and justifications for plan-required elements
   - Use formal, professional regulatory language
   - Ensure content is scientifically accurate and technically sound
   - Draw information ONLY from the provided input chunks that align with the plan
   - DO NOT fabricate or invent any data not found in the input chunks
   - Focus on depth rather than breadth - cover plan requirements thoroughly

7. Documentation Standards:
   - Use clear, precise, and professional language
   - Format content according to regulatory standards
   - Include all tables (via preserved tags), figures, and references specified in the plan
   - Provide detailed captions and explanations for figures
   - Use consistent terminology throughout
   - Follow professional technical writing standards
   - Maintain logical flow and organization

8. Technical Depth:
   - Provide detailed technical explanations and justifications for plan-specified requirements
   - Include specific parameters, measurements, and criteria as outlined in the plan
   - Explain methodologies and approaches thoroughly where required by the plan
   - Discuss any deviations or special considerations mentioned in the plan
   - Include validation and verification details as specified in the plan
   - Provide comprehensive data interpretation for plan-relevant data
   - Explain technical significance and implications of plan-required elements

9. Narrative Style:
   - Write in a cohesive, flowing narrative style
   - Connect technical information with clear transitions
   - Provide context and background for technical details as required by the plan
   - Explain the significance of findings and data relevant to plan requirements
   - Use professional, formal language throughout
   - Ensure information is presented in a logical sequence following the plan structure
   - Create content that reads as a unified document, not disconnected fragments

OUTPUT FORMAT:
- Professional, regulatory-compliant content following the provided plan
- Well-structured paragraphs with logical flow
- Proper formatting for technical information
- No section headings (as specified in the original requirements)
- Markdown format without backticks or code blocks
- No other formatting (XML, HTML, plaintext) should be included
- Preserved table tags exactly as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`)
- NO meta-commentary about the generation process or compliance verification

The content should be detailed, comprehensive, and ready for inclusion in an NDA submission. Remember that in regulatory documentation, providing thorough information is better than being concise, but ONLY for information that serves the plan requirements. Generate ONLY the actual content that would appear in the final NDA submission - no process commentary."""

        # Prepare input chunks text
        input_chunks_text = "\n\n".join([f"Input Chunk {i+1}:\n{chunk.get('content', '')}" for i, chunk in enumerate(chunks)])

        # Prepare critique feedback section if available
        critique_section = ""
        if critique_feedback:
            critique_section = f"""
PREVIOUS CRITIQUE FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):
- Overall Rating: {critique_feedback.get('overall_rating', 'N/A')}/10

Critical Issues to Address:
{chr(10).join(f"- {issue}" for issue in critique_feedback.get('critical_issues', []))}

Required Improvements to Implement:
{chr(10).join(f"- {improvement}" for improvement in critique_feedback.get('required_improvements', []))}

Additional Recommendations to Consider:
{chr(10).join(f"- {recommendation}" for recommendation in critique_feedback.get('additional_recommendations', []))}

IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.

"""

        user_prompt = f"""Please generate detailed, regulatory-compliant content for an NDA section based on the following plan and input document chunks.

SECTION INFORMATION:
- Section: {section_info.get('section', '')}
- Title: {section_info.get('title', '')}
- Description: {section_info.get('description', '')}
- Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
- Formatting Instructions: {section_info.get("formatting_instructions", "")}

DETAILED PLAN (USE AS PRIMARY FILTER AND GUIDE):
{plan}

{critique_section}INPUT DOCUMENT CHUNKS:
{input_chunks_text}

CRITICAL INSTRUCTIONS FOR PLAN-GUIDED CONTENT GENERATION:
1. Use the DETAILED PLAN as your PRIMARY FILTER - only include content from chunks that directly serves plan requirements
2. EVALUATE each input chunk against the plan requirements before using any information from it
3. DISCARD any chunk content that does not align with specific plan elements
4. For chunks with mixed content, extract ONLY the portions that address plan requirements
5. Prioritize chunks containing: technical specifications, validation data, regulatory compliance information, test results, and analytical methods mentioned in the plan
6. Ignore general background information, marketing content, or tangential data not specified in the plan

TABLE TAG HANDLING INSTRUCTIONS (CRITICAL):
7. PRESERVE ALL table tags EXACTLY as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`)
8. DO NOT modify, rename, or change table tags in ANY way - maintain exact spelling, capitalization, and format
9. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
10. MAINTAIN the EXACT position and order of table tags as they appear in the input chunks
11. Include table tags in the exact location where the table should appear in your content
12. Use table summary information to write brief contextual statements about tables if needed, but always preserve the tags
13. Table format in input chunks will be: "Table X: heading", "detailed table summary", "`<TABLE_TAG_table_x>`"
14. DO NOT omit any table tags from input chunks that are relevant to the plan

NO META-COMMENTARY INSTRUCTIONS (CRITICAL):
15. DO NOT include any statements about addressing critique feedback
16. DO NOT include verification checklists or compliance statements
17. DO NOT include comments about preserving figures or tables "as per source documents"
18. DO NOT include sections like "Verification of Compliance" with checkmarks
19. DO NOT reference plans, critiques, or the generation process in your output
20. Generate ONLY the actual NDA content that would appear in the final regulatory submission

IMPORTANT CONSIDERATIONS:
1. Follow the plan EXACTLY - it has been designed to meet all regulatory requirements
2. Draw information ONLY from input chunks that align with plan requirements - DO NOT invent or fabricate data
3. Generate comprehensive, technically precise content that meets ICH eCTD v3.2.2 standards
4. Use formal, professional regulatory language appropriate for an NDA submission
5. Do not include section headings in your output as these will be added separately
6. Format your output in markdown without backticks or code blocks
7. Include technical details, parameters, and specifications ONLY as specified in the plan
8. Focus on creating high-quality, detailed content that addresses plan requirements thoroughly
9. PRESERVE ALL TABLE TAGS EXACTLY as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way
10. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
11. MAINTAIN EXACT figure numbers and captions as they appear in source documents (e.g., if "Figure 1: XYZ", do not change to "Figure 5.7.3: ABC")
12. INCLUDE ALL figures from the input source documents that are relevant to the plan
13. DO NOT modify the content of any figures - preserve their original format and content
14. If previous critique feedback is provided, ensure your content addresses all critical issues and required improvements WITHOUT referencing the feedback process
15. Generate ONLY content that would appear in the final NDA submission - NO process commentary or meta-statements

Generate detailed content for this section of the NDA based on the provided plan and plan-relevant input chunks. Your output should be indistinguishable from content written directly for an NDA submission."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"generate_content_from_chunks token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    async def combine_content_sections(content_sections):
        """Combine multiple content sections while ensuring proper flow and consistency."""
        total_sections = len(content_sections)
        print(f"\nCombining {total_sections} content sections:")
        
        total_tokens = sum(calculate_number_of_tokens(content) for content in content_sections)
        print(f"Total content section tokens: {total_tokens}")
        
        system_prompt = """You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple sections of NDA content into a cohesive, unified document that maintains regulatory compliance, technical accuracy, and narrative flow.

CRITICAL REQUIREMENTS:
1. Content Preservation (HIGHEST PRIORITY):
   - Preserve ALL technical information from all content sections
   - Ensure NO technical details, data, or regulatory elements are lost
   - Maintain ALL parameters, specifications, and measurements
   - Include ALL methodologies and approaches
   - Preserve ALL validation and verification details
   - Keep ALL risk assessments and control strategies
   - Maintain ALL compliance justifications

2. NO META-COMMENTARY (HIGHEST PRIORITY):
   - DO NOT include any commentary about the combination process
   - DO NOT include verification checklists or statements about compliance
   - DO NOT include meta-statements about preserving content or following plans
   - DO NOT include process-related commentary like "Verification of Compliance" sections with checkmarks
   - DO NOT include formatting instructions or comments about document structure in the output
   - DO NOT reference the combination process or source sections in your output
   - Generate ONLY the actual NDA content that would appear in the final submission
   - DO NOT include any self-referential statements about the integration process

3. Table Tag Preservation (HIGHEST PRIORITY):
   - PRESERVE ALL table tags EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`, etc.)
   - DO NOT modify, rename, or change table tags in ANY way
   - DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
   - MAINTAIN the EXACT position and order of table tags as they appear in the content sections
   - Include table tags in the exact location where they appear in the original content
   - DO NOT omit any table tags from the content sections
   - Treat table tags as placeholders that must be preserved exactly for later replacement

4. Figure Preservation (HIGHEST PRIORITY):
   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents
   - DO NOT rename any figures (e.g., if a figure is "Figure 1: XYZ" do NOT change it to "Figure 5.7.3: ABC")
   - INCLUDE ALL figures that appear in the content sections
   - DO NOT modify the content of any figures
   - DO NOT omit any figures from the content sections
   - Include references to all figures in your text

5. Integration Quality:
   - Create seamless transitions between combined content
   - Eliminate redundancies without losing information
   - Harmonize terminology and phrasing
   - Ensure consistent technical language throughout
   - Maintain a unified voice and writing style
   - Create logical flow between previously separate sections
   - Resolve any contradictions or inconsistencies

6. Narrative Coherence:
   - Ensure the combined document reads as a single, cohesive narrative
   - Create smooth transitions between previously separate sections
   - Maintain logical progression of ideas and information
   - Connect related concepts across the combined content
   - Ensure consistent level of detail throughout
   - Balance technical depth across sections
   - Create a document that reads as if written by a single author

7. Regulatory Standards:
   - Maintain ICH eCTD v3.2.2 compliance throughout
   - Preserve all regulatory justifications and explanations
   - Ensure consistent approach to compliance documentation
   - Maintain proper documentation standards
   - Preserve all technical requirements and specifications
   - Ensure consistent approach to risk management
   - Maintain proper cross-referencing

8. Technical Accuracy:
   - Ensure all combined technical information is accurate and consistent
   - Resolve any contradictions in technical specifications
   - Harmonize methodologies and approaches
   - Ensure consistent presentation of data and results
   - Maintain precise technical language throughout
   - Preserve scientific accuracy in all explanations
   - Ensure calculations and numerical data are consistent

OUTPUT FORMAT:
- Professional, regulatory-compliant content with seamless integration
- Well-structured paragraphs with logical flow
- Consistent formatting throughout
- No section headings (as specified in the original requirements)
- Markdown format without backticks or code blocks
- No other formatting (XML, HTML, plaintext) should be included
- NO meta-commentary about the combination process or compliance verification

The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach. Generate ONLY content that would appear in the final NDA submission."""

        user_prompt = f"""Please combine the following {total_sections} sections of NDA content into a cohesive, unified document while preserving all technical information and ensuring smooth narrative flow.

"""
        
        # Add each content section with a header
        for i, content in enumerate(content_sections, 1):
            user_prompt += f"""
CONTENT SECTION {i}:
{content}

"""
        
        user_prompt += f"""
CRITICAL INSTRUCTIONS:
1. Preserve ALL technical information from all {total_sections} content sections
2. Create seamless transitions between combined content
3. Eliminate redundancies without losing information
4. Harmonize terminology and phrasing
5. Ensure the combined document reads as a single, cohesive narrative
6. Maintain ICH eCTD v3.2.2 compliance throughout
7. Ensure all combined technical information is accurate and consistent
8. Preserve the detailed, comprehensive nature of the content
9. Do not include section headings in your output as these will be added separately
10. Format your output in markdown without backticks or code blocks
11. PRESERVE ALL TABLE TAGS EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way
12. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
13. MAINTAIN EXACT figure numbers and captions as they appear in content sections (e.g., if "Figure 1: XYZ", do not change to "Figure 5.7.3: ABC")
14. INCLUDE ALL figures from all content sections - these are critical and must not be omitted
15. DO NOT modify the content of any figures - preserve their original format and content

NO META-COMMENTARY INSTRUCTIONS (CRITICAL):
11. DO NOT include any statements about combining content or preserving information
12. DO NOT include verification checklists or compliance statements
13. DO NOT include comments about preserving figures or tables "as per source documents"
14. DO NOT include sections like "Verification of Compliance" with checkmarks
15. DO NOT reference the combination process, plans, or source sections in your output
16. Generate ONLY the actual NDA content that would appear in the final regulatory submission

TABLE AND FIGURE HANDLING INSTRUCTIONS (CRITICAL):
17. PRESERVE ALL TABLE TAGS EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way
18. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing
19. MAINTAIN EXACT figure numbers and captions as they appear in content sections (e.g., if "Figure 1: XYZ", do not change to "Figure 5.7.3: ABC")
20. INCLUDE ALL figures from all content sections - these are critical and must not be omitted
21. DO NOT modify the content of any figures - preserve their original format and content

The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach. Your output should be indistinguishable from content written directly for an NDA submission - no process commentary."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"combine_content_sections token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    # Handle empty input
    if not input_chunks:
        return ""

    # Process input chunks in batches based on token count
    batch_contents = []
    
    # Create optimal batches based on token count
    batches = []
    current_batch = []
    current_token_count = 0
    
    for chunk in input_chunks:
        chunk_tokens = calculate_number_of_tokens(chunk.get('content', ''))
        
        # If adding this chunk would exceed the token limit, start a new batch
        if current_batch and current_token_count + chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
            batches.append(current_batch)
            current_batch = [chunk]
            current_token_count = chunk_tokens
        else:
            current_batch.append(chunk)
            current_token_count += chunk_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    print(f"Created {len(batches)} batches for chunk processing")
    for i, batch in enumerate(batches):
        print(f"Batch {i+1} contains {len(batch)} chunks with {sum(calculate_number_of_tokens(c.get('content', '')) for c in batch)} tokens")
    
        # Process all batches in parallel
    tasks = [generate_content_from_chunks(plan, batch, section_info, critique_feedback) for batch in batches]
    batch_contents = await asyncio.gather(*tasks)
    
    # If only one batch, return it directly
    if len(batch_contents) == 1:
        return batch_contents[0]
    
    # Combine batch contents iteratively
    while len(batch_contents) > 1:
        # Create optimal batches based on token count
        content_batches = []
        current_batch = []
        current_token_count = 0
        
        for content in batch_contents:
            content_tokens = calculate_number_of_tokens(content)
            
            # If adding this content would exceed the token limit, start a new batch
            if current_batch and current_token_count + content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                content_batches.append(current_batch)
                current_batch = [content]
                current_token_count = content_tokens
            else:
                current_batch.append(content)
                current_token_count += content_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            content_batches.append(current_batch)
        
        print(f"Created {len(content_batches)} batches for content combining")
        for i, batch in enumerate(content_batches):
            print(f"Batch {i+1} contains {len(batch)} content sections with {sum(calculate_number_of_tokens(c) for c in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_content_sections(batch) for batch in content_batches]
        
        # Wait for all combinations to complete
        batch_contents = await asyncio.gather(*tasks)

    print("create_incremental_content tokens: ", calculate_number_of_tokens(batch_contents[0]))
    
    return batch_contents[0]


# %%
async def refine_content(draft_content, section_info, evaluation_feedback=None):
    """
    Refine the draft content based on evaluation feedback and ensure it meets all requirements.
    Can use both the original draft content and previous output (if available) to make improvements.
    
    Args:
        draft_content: The draft content generated in Stage 2
        section_info: Dictionary containing section information
        evaluation_feedback: Evaluation feedback JSON (optional)
        
    Returns:
        str: Refined NDA section content
    """
    
    system_prompt = """You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to refine and perfect the provided draft NDA section content based on evaluation feedback and ensure it fully meets all regulatory requirements.

CRITICAL REQUIREMENTS:
1. Feedback Implementation (HIGHEST PRIORITY):
   - Address ALL issues identified in the evaluation feedback
   - Implement ALL required improvements
   - Resolve ALL critical issues
   - Follow ALL additional recommendations where applicable
   - Maintain and enhance the identified strengths
   - Ensure the refined content addresses all evaluation concerns

2. NO META-COMMENTARY (HIGHEST PRIORITY):
   - DO NOT include any commentary about how you addressed the feedback
   - DO NOT include verification checklists or statements about compliance
   - DO NOT include meta-statements like "This addresses the previous critique" or "Based on feedback"
   - DO NOT include process-related commentary like "Verification of Compliance" sections with checkmarks
   - DO NOT include formatting instructions or comments about document structure in the output
   - DO NOT reference the feedback, plans, or improvement process in your output
   - Generate ONLY the actual NDA content that would appear in the final submission
   - DO NOT include any self-referential statements about the refinement process

3. Table and Figure Handling (HIGHEST PRIORITY):
   - MAINTAIN EXACT table references in the format "Table {X}: {table heading}" 
   - DO NOT modify or remove any `<TABLE_TAG_table_x>` tags - these are critical placeholders that will be replaced in post-processing
   - KEEP ALL table tags in their original position in the text - do NOT move these tags
   - DO NOT attempt to construct or format the tables yourself - the tables will be inserted during post-processing
   - Use the provided table summaries to inform your writing, but maintain the table placeholders exactly as they appear
   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents
   - DO NOT rename any tables or figures (e.g., if a table is "Table 1: XYZ" do NOT change it to "Table 5.7.3: ABC")
   - INCLUDE ALL tables and figures that appear in the input source documents or draft content
   - DO NOT modify the content of any figures
   - DO NOT omit any tables or figures from the source documents
   - Include references to all tables and figures in your text

4. Regulatory Compliance:
   - Ensure strict compliance with ICH eCTD v3.2.2 standards
   - Address ALL requirements from the section description
   - Follow ALL reference text specifications
   - Adhere to ALL golden instructions and checkpoints
   - Comply with ALL formatting instructions
   - Ensure all technical content meets regulatory standards
   - Verify all cross-references and dependencies are correct

5. Content Quality Enhancement:
   - Improve technical precision and clarity
   - Enhance explanations and justifications
   - Strengthen scientific rationale
   - Add missing technical details identified in feedback
   - Improve data presentation and interpretation
   - Enhance narrative flow and cohesion
   - Ensure comprehensive treatment of all required topics

6. Documentation Standards:
   - Perfect professional language and terminology
   - Improve formatting and structure
   - Enhance references to tables and figures
   - Strengthen justifications for critical decisions
   - Improve documentation of assumptions and limitations
   - Enhance technical documentation quality
   - Ensure consistent terminology throughout

7. Comprehensive Coverage:
   - Verify ALL section description points are addressed
   - Ensure ALL efficacy guideline requirements are covered
   - Confirm ALL technical specifications are included
   - Verify ALL validation and verification details are present
   - Ensure ALL cross-references are accurate
   - Confirm ALL justifications and rationales are provided
   - Verify ALL risk assessments are included

8. Iteration Improvement:
   - Compare previous output with feedback to understand exact issues
   - Ensure the new output improves upon previous versions
   - Preserve comprehensive information while fixing identified issues
   - Use the original draft as a source of complete information
   - Avoid introducing new issues while fixing identified ones

OUTPUT FORMAT:
- Professional, regulatory-compliant content with all issues resolved
- Well-structured paragraphs with improved logical flow
- Enhanced technical precision and detail
- No section headings (as specified in the original requirements)
- Markdown format without backticks or code blocks
- No other formatting (XML, HTML, plaintext) should be included
- Preserve all `<TABLE_TAG_table_x>` tags exactly as they appear
- NO meta-commentary about the refinement process or compliance verification

The refined content should represent a significant improvement over previous versions, addressing all feedback points while maintaining the strengths identified in the evaluation. Generate ONLY content that would appear in the final NDA submission."""

    # Prepare evaluation feedback text
    feedback_text = ""
    previous_output_text = ""
    
    if evaluation_feedback:
        # Check if we have previous output in the enhanced feedback
        if "previous_output" in evaluation_feedback:
            previous_output_text = f"""
PREVIOUS OUTPUT (that feedback applies to):
{evaluation_feedback.get('previous_output', '')}
"""
            
        # First try to use the structured feedback if we have a valid rating and sufficient feedback
        has_valid_structured_feedback = (
            evaluation_feedback.get("overall_rating") is not None and
            (evaluation_feedback.get("key_strengths") or
             evaluation_feedback.get("critical_issues") or
             evaluation_feedback.get("required_improvements"))
        )
        
        if has_valid_structured_feedback:
            # Use structured feedback
            feedback_text = f"""
EVALUATION FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):
- Overall Rating: {evaluation_feedback.get('overall_rating', 'N/A')}/10

Key Strengths:
{chr(10).join(f"- {strength}" for strength in evaluation_feedback.get('key_strengths', []))}

Critical Issues to Address:
{chr(10).join(f"- {issue}" for issue in evaluation_feedback.get('critical_issues', []))}

Required Improvements:
{chr(10).join(f"- {improvement}" for improvement in evaluation_feedback.get('required_improvements', []))}

Additional Recommendations:
{chr(10).join(f"- {recommendation}" for recommendation in evaluation_feedback.get('additional_recommendations', []))}

IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.
"""
        # Fall back to raw evaluation text if available and structured feedback is insufficient
        elif "evaluation_text" in evaluation_feedback:
            feedback_text = f"""
EVALUATION FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):
{evaluation_feedback.get('evaluation_text', '')}

IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.
"""
    
    user_prompt = f"""Please refine and perfect the following draft NDA section content based on the provided evaluation feedback and section information.

SECTION INFORMATION:
- Section: {section_info.get('section', '')}
- Title: {section_info.get('title', '')}
- Description: {section_info.get('description', '')}
- Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
- Formatting Instructions: {section_info.get("formatting_instructions", "")}

{feedback_text}
{previous_output_text}

DRAFT CONTENT TO REFINE:
{draft_content}

CRITICAL INSTRUCTIONS:
1. Address ALL issues identified in the evaluation feedback
2. Implement ALL required improvements
3. Resolve ALL critical issues
4. Follow ALL additional recommendations where applicable
5. Maintain and enhance the identified strengths
6. Ensure strict compliance with ICH eCTD v3.2.2 standards
7. Perfect professional language and technical precision
8. Enhance explanations, justifications, and scientific rationale
9. Do not include section headings in your output as these will be added separately
10. Format your output in markdown without backticks or code blocks
11. Compare previous output with feedback to understand exactly what needs to be improved

NO META-COMMENTARY INSTRUCTIONS (CRITICAL):
12. DO NOT include any statements about addressing feedback or making improvements
13. DO NOT include verification checklists or compliance statements
14. DO NOT include comments about preserving figures or tables "as per source documents"
15. DO NOT include sections like "Verification of Compliance" with checkmarks
16. DO NOT reference feedback, plans, critiques, or the refinement process in your output
17. Generate ONLY the actual NDA content that would appear in the final regulatory submission

TABLE HANDLING INSTRUCTIONS (CRITICAL):
18. DO NOT modify or remove any `<TABLE_TAG_table_x>` tags - these are placeholders for tables that will be inserted during post-processing
19. MAINTAIN the exact position of all `<TABLE_TAG_table_x>` tags in the text
20. DO NOT attempt to construct or format the tables yourself
21. Use the provided table summaries to inform your writing, but keep the table references and tags intact
22. PRESERVE the format "Table X: table heading" exactly as it appears
23. NEVER modify table numbers or headings
24. DO NOT convert table placeholders into actual tables - they will be replaced in post-processing

FIGURE HANDLING INSTRUCTIONS:
25. MAINTAIN EXACT figure numbers and captions as they appear in the source documents
26. DO NOT rename any figures
27. INCLUDE ALL figures from the input source documents and draft content
28. DO NOT modify the content of any figures

Focus on creating a refined, polished document that fully meets all regulatory requirements, addresses all feedback, and represents a significant improvement over previous versions. Your output should be indistinguishable from content written directly for an NDA submission - no process commentary."""

    # Wrap the API call with retry_on_empty_response
    async def make_api_call():
        response = await generator_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3
        )

        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)
        output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
        total_tokens = system_tokens + user_tokens + output_tokens
        print(f"refine_content token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        
        return response.choices[0].message.content

    # Use the retry wrapper to handle empty responses
    return await retry_on_empty_response(make_api_call)


# %%
async def optimized_nda_output_generation(section_info, critique_feedback=None, reuse_plan=None):
    """
    Generate NDA output using a multi-stage, batch processing approach to reduce context size and optimize performance.
    
    Args:
        section_info: Dictionary containing section information
        critique_feedback: Optional critique feedback from previous iterations to address missing content
        reuse_plan: Optional pre-generated plan to reuse (skips Stage 1 for performance)
        
    Returns:
        tuple: (Generated NDA section content, plan used) if reuse_plan is None, else just content
    """
    print(f"Starting optimized NDA output generation for section {section_info.get('section', 'unknown')}")
    
    # Stage 1: Create plan from efficacy guideline summaries (skip if reusing plan)
    if reuse_plan is not None:
        print("Stage 1: Reusing existing plan (performance optimization)")
        plan = reuse_plan
    else:
        individual_summaries = []
        if "relevant_individual_summaries" in section_info:
            individual_summaries = [summary.get("content", "") for summary in section_info.get("relevant_individual_summaries", [])]
        
        # Fall back to combined summary if individual summaries aren't available
        if not individual_summaries and "efficacy_guideline_combined_summary" in section_info:
            individual_summaries = [section_info["efficacy_guideline_combined_summary"]]
        
        print(f"Stage 1: Creating incremental plan from {len(individual_summaries)} individual summaries")
        plan = await create_incremental_plan(section_info, individual_summaries)

        print("plan: ", plan)
    
    # Stage 2: Generate content from input chunks
    input_chunks = section_info.get("relevant_input_chunks", [])
    print(f"Stage 2: Creating incremental content from {len(input_chunks)} input chunks using the plan")
    draft_content = await create_incremental_content(plan, section_info, input_chunks, critique_feedback)

    print("draft_content: ", draft_content)
    
    # Stage 3: Refine content based on evaluation feedback
    evaluation_feedback = section_info.get("nda_output_evaluation_json", None)
    
    if evaluation_feedback:
        print(f"Stage 3: Refining content based on evaluation feedback")
        refined_content = await refine_content(draft_content, section_info, evaluation_feedback)

        print("refined_content: ", refined_content)

        # Return content and plan if this is the first iteration, else just content
        if reuse_plan is None:
            return refined_content, plan
        else:
            return refined_content
    else:
        print(f"No evaluation feedback available, returning draft content")
        # Return content and plan if this is the first iteration, else just content
        if reuse_plan is None:
            return draft_content, plan
        else:
            return draft_content
    
async def optimized_nda_critique(section_info, nda_output):
    """
    Generate a comprehensive NDA critique using a batch processing approach with individual efficacy guideline summaries.
    Uses parallel processing to generate critiques from multiple summaries simultaneously.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    print(f"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}")
    
    # Extract individual summaries
    individual_summaries = []
    if "relevant_individual_summaries" in section_info:
        individual_summaries = [summary.get("content", "") for summary in section_info.get("relevant_individual_summaries", [])]
    
    # Fall back to combined summary if individual summaries aren't available
    if not individual_summaries and "efficacy_guideline_combined_summary" in section_info:
        individual_summaries = [section_info["efficacy_guideline_combined_summary"]]
    
    # If no summaries available, generate critique based only on section info
    if not individual_summaries:
        print("No efficacy guideline summaries available, generating critique based only on section info")
        return await generate_critique_without_guidelines(section_info, nda_output)
    
    print(f"Generating individual critiques from {len(individual_summaries)} efficacy guideline summaries in parallel")
    
    # Generate individual critiques in parallel using asyncio.gather
    tasks = [generate_critique_from_one_summary(summary, section_info, nda_output) 
             for summary in individual_summaries]
    individual_critiques = await asyncio.gather(*tasks)
    
    # If only one critique, return it directly
    if len(individual_critiques) == 1:
        return individual_critiques[0]
    
    # Combine critiques using batch processing
    print("Combining individual critiques using batch processing")
    return await combine_critiques_incrementally(individual_critiques, section_info)


async def generate_critique_from_one_summary(summary_content, section_info, nda_output):
    """
    Generate a critique of NDA output based on a single efficacy guideline summary.
    
    Args:
        summary_content: Content of a single efficacy guideline summary
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    system_prompt = """You are a strict regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate the provided NDA section output against the efficacy guidelines summary and ICH eCTD v3.2.2 requirements.

CRITICAL REQUIREMENTS:
1. Golden Instructions and Formatting Compliance (HIGHEST PRIORITY):
   - STRICTLY evaluate adherence to Golden Instructions and Checkpoints
   - STRICTLY verify compliance with Formatting Instructions
   - These evaluations take precedence over all other assessments
   - Check if every point in Golden Instructions is addressed
   - Verify exact compliance with Formatting Instructions
   - Consider non-compliance with these instructions as critical issues
   - Prioritize these evaluations over all other checks

2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):
   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text
   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing
   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags
   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
   - Verify that ALL tables and figures from source documents are included in the output
   - Consider any table tag alteration, removal, or position change as a CRITICAL issue
   - Check that all tables and figures are properly referenced in the text

3. Content Appropriateness and Relevance Assessment (HIGHEST PRIORITY):
   - VERIFY that ALL content in the output directly relates to and supports the efficacy guideline summary requirements
   - CHECK for any inappropriate tables, figures, or content that should NOT be in this section
   - IDENTIFY any content that appears to be from wrong sections or unrelated sources
   - VERIFY that all tables and figures are appropriate for this specific section and efficacy guidelines
   - CHECK if any content contradicts or conflicts with the efficacy guideline summary
   - ASSESS if content focus aligns with the specific section requirements
   - FLAG any content that seems out of scope or inappropriate for the section

4. ICH eCTD v3.2.2 Compliance Assessment (HIGH PRIORITY):
   - Evaluate adherence to eCTD structure and format
   - Check for proper section organization
   - Verify required elements are present
   - Assess document hierarchy
   - Review cross-referencing accuracy
   - STRICTLY verify compliance with section description
   - STRICTLY verify compliance with reference text
   - Ensure all requirements from the ICH eCTD guidelines are met

5. Efficacy Guidelines Summary Coverage and Alignment:
   - SYSTEMATICALLY verify all key requirements from the efficacy guideline summary are addressed
   - CHECK if critical considerations are covered completely
   - ASSESS documentation completeness against efficacy guideline requirements
   - EVALUATE compliance with EVERY recommendation in the efficacy guideline summary
   - VERIFY implementation of all efficacy guideline recommendations
   - CHECK if any efficacy guideline requirements are missing or inadequately addressed
   - ASSESS if the depth of coverage matches efficacy guideline expectations

6. Golden Checkpoint Systematic Verification:
   - METHODICALLY verify each golden checkpoint is addressed in the output
   - CHECK if the output provides specific answers to each golden checkpoint question
   - IDENTIFY any golden checkpoints that are completely missing from the output
   - ASSESS if the level of detail for each checkpoint meets expectations
   - VERIFY that checkpoint responses are accurate and complete
   - FLAG any superficial or inadequate responses to critical checkpoints

7. Technical Accuracy and Completeness:
   - Verify technical specifications align with efficacy guideline requirements
   - Check parameter accuracy against efficacy guideline standards
   - Assess validation details match efficacy guideline expectations
   - Review analytical methods comply with efficacy guideline recommendations
   - Evaluate data presentation follows efficacy guideline formats

8. Content Quality Assessment:
   - Check for clarity and precision in addressing efficacy guideline requirements
   - Evaluate professional language appropriate for regulatory submission
   - Assess logical flow follows efficacy guideline structure
   - Review completeness of explanations for all efficacy guideline elements
   - Verify data consistency with efficacy guideline standards

9. Hallucination and Fabrication Detection:
   - IDENTIFY any content not supported by the efficacy guideline summary
   - CHECK for fabricated data or claims not found in efficacy guidelines
   - VERIFY all technical references align with efficacy guideline sources
   - ASSESS if any content appears to be invented or unsupported
   - REVIEW consistency between output content and efficacy guideline summary

10. Regulatory Compliance and Efficacy Guideline Adherence:
    - EVALUATE strict adherence to efficacy guideline recommendations
    - CHECK for all regulatory requirements specified in efficacy guidelines
    - ASSESS justification quality against efficacy guideline standards
    - REVIEW risk management approaches match efficacy guideline expectations
    - VERIFY all efficacy guideline compliance elements are addressed

11. Documentation Standards:
    - Check formatting consistency. Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. The output should not include any other formatting like xml, html, plaintext etc.
    - Check if there are section heading present, we dont want markdown headings in the output as we'll be adding them later manually
    - Evaluate table and figure quality meets efficacy guideline standards
    - Assess reference accuracy to efficacy guidelines
    - Review appendix completeness for efficacy guideline requirements
    - Verify document structure follows efficacy guideline organization

12. Efficacy Guideline Gap Analysis:
    - IDENTIFY any efficacy guideline requirements completely missing from output
    - NOTE sections where efficacy guideline coverage is insufficient
    - FLAG incomplete implementation of efficacy guideline recommendations
    - HIGHLIGHT any contradictions with efficacy guideline requirements
    - SUGGEST specific efficacy guideline elements that need enhancement

13. Inappropriate Content Detection:
    - CHECK for any tables, figures, or content that doesn't belong in this section
    - IDENTIFY content that seems copied from wrong sections or sources
    - VERIFY all included material is appropriate for the specific efficacy guidelines
    - FLAG any content that conflicts with section scope or requirements
    - ASSESS if content focus drifts away from efficacy guideline objectives

14. Decision Tree and Process Flow Evaluation (if applicable):
    - If decision trees are present in the efficacy guidelines, assess how well they are addressed
    - Verify that decision points and outcomes are properly documented
    - Check if the rationale for chosen paths clearly aligns with efficacy guidelines
    - Note any missing elements related to efficacy guideline decision processes

Make sure that all table references are maintained in the exact format "Table X: table heading" and all `<TABLE_TAG_table_x>` tags are preserved exactly as they appear in the input. These tags should not be modified, moved, or removed as they will be replaced with actual tables during post-processing.

Rating Criteria (1-10):
1-3: Major deficiencies, significant non-compliance
4-6: Moderate issues, partial compliance
7-8: Minor issues, mostly compliant
9-10: Excellent compliance, minimal improvements needed

Provide a thorough and critical evaluation with specific, actionable feedback for improvement."""

    user_prompt = f"""Please evaluate the following NDA section output against the provided efficacy guideline summary and ICH eCTD requirements.

SECTION INFORMATION:
- Section: {section_info.get('section', '')}
- Title: {section_info.get('title', '')}
- Description: {section_info.get('description', '')}
- Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
- Formatting Instructions: {section_info.get("formatting_instructions", "")}

EFFICACY GUIDELINE SUMMARY TO EVALUATE AGAINST:
{summary_content}

NDA OUTPUT TO EVALUATE:
{nda_output}

CRITICAL EVALUATION FRAMEWORK:
1. SYSTEMATICALLY evaluate the NDA output against EVERY requirement in the efficacy guideline summary
2. METHODICALLY check each Golden Instruction and Checkpoint for complete adherence
3. IDENTIFY any content that appears inappropriate or doesn't belong in this section
4. VERIFY all tables and figures are appropriate for this specific section and efficacy guidelines
5. CHECK for any missing efficacy guideline requirements or inadequate coverage
6. ASSESS if content focus aligns with efficacy guideline objectives
7. FLAG any content that contradicts or conflicts with the efficacy guideline summary

SPECIFIC VALIDATION CHECKS:
□ Does the output address ALL key requirements from the efficacy guideline summary?
□ Are ALL Golden Instructions and Checkpoints adequately addressed?
□ Are there any inappropriate tables, figures, or content that shouldn't be in this section?
□ Does any content appear to be from wrong sections or unrelated sources?
□ Are all included tables and figures appropriate for the specific efficacy guidelines?
□ Does any content contradict or conflict with the efficacy guideline summary?
□ Are there any efficacy guideline requirements completely missing from the output?
□ Does the content focus drift away from efficacy guideline objectives?
□ Are there any fabricated claims not supported by the efficacy guideline summary?
□ Does the depth of coverage match efficacy guideline expectations?

IMPORTANT CONSIDERATIONS:
1. Focus on evaluating the NDA output against the efficacy guideline summary provided
2. Check for strict compliance with Golden Instructions and Checkpoints
3. Verify adherence to ICH eCTD v3.2.2 requirements
4. STRICTLY verify that ALL table references and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
5. VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text
6. CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing
7. STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
8. Assess technical accuracy and completeness against efficacy guideline standards
9. Evaluate documentation standards and quality per efficacy guideline requirements
10. Identify any hallucinations or content not supported by efficacy
12. Identify any inappropriate content that doesn't belong in this section
13. Provide a numerical rating (1-10) and detailed feedback in all required categories

Evaluate thoroughly and provide specific, actionable feedback for improvement with focus on efficacy guideline alignment."""

    system_tokens = calculate_number_of_tokens(system_prompt)
    user_tokens = calculate_number_of_tokens(user_prompt)

    critique_evaluation_agent = Agent(
        model=critique_model,
        system_prompt=system_prompt,
        output_type=CritiqueEvaluation,
        model_settings={
            "temperature": 0.2,
        },
        retries=3
    )

    response = await critique_evaluation_agent.run(user_prompt)

    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
    total_tokens = system_tokens + user_tokens + output_tokens

    print(f"generate_critique_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return response.output


async def generate_critique_without_guidelines(section_info, nda_output):
    """
    Generate a critique of NDA output based only on section information without efficacy guidelines.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    system_prompt = """You are a strict regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate the provided NDA section output against ICH eCTD v3.2.2 requirements and the provided section information.

CRITICAL REQUIREMENTS:
1. Golden Instructions and Formatting Compliance (HIGHEST PRIORITY):
   - STRICTLY evaluate adherence to Golden Instructions and Checkpoints
   - STRICTLY verify compliance with Formatting Instructions
   - These evaluations take precedence over all other assessments
   - Check if every point in Golden Instructions is addressed
   - Verify exact compliance with Formatting Instructions
   - Consider non-compliance with these instructions as critical issues
   - Prioritize these evaluations over all other checks

2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):
   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text
   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing
   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags
   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
   - Verify that ALL tables and figures from source documents are included in the output
   - Consider any table tag alteration, removal, or position change as a CRITICAL issue
   - Check that all tables and figures are properly referenced in the text

3. ICH eCTD v3.2.2 Compliance Assessment (HIGH PRIORITY):
   - Evaluate adherence to eCTD structure and format
   - Check for proper section organization
   - Verify required elements are present
   - Assess document hierarchy
   - Review cross-referencing accuracy
   - STRICTLY verify compliance with section description
   - STRICTLY verify compliance with reference text
   - Ensure all requirements from the ICH eCTD guidelines are met

4. Technical Accuracy and Completeness:
   - Verify technical specifications
   - Check parameter accuracy
   - Assess validation details
   - Review analytical methods
   - Evaluate data presentation

5. Content Quality Assessment:
   - Check for clarity and precision
   - Evaluate professional language
   - Assess logical flow
   - Review completeness of explanations
   - Verify data consistency

6. Hallucination Detection:
   - Identify any unsupported claims
   - Check for fabricated data
   - Verify guideline references
   - Assess technical accuracy
   - Review consistency with source material

7. Regulatory Compliance:
   - Evaluate adherence to guidelines
   - Check for regulatory requirements
   - Assess justification quality
   - Review risk management
   - Verify post-approval considerations

8. Documentation Standards:
   - Check formatting consistency. Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. The output should not include any other formatting like xml, html, plaintext etc.
   - Check if there are section heading present, we dont want markdown headings in the output as we'll be adding them later manually
   - Evaluate table and figure quality
   - Assess reference accuracy
   - Review appendix completeness
   - Verify document structure

9. Improvement Areas:
   - Identify missing elements
   - Note unclear sections
   - Flag incomplete information
   - Highlight inconsistencies
   - Suggest specific enhancements

10. Table and Section Handling Evaluation:
    - For tables:
      * VERIFY that all table tags (`<TABLE_TAG_table_x>`) are preserved EXACTLY as they appear in the input
      * CHECK that the position and order of table tags is maintained
      * CONFIRM that no attempt has been made to construct or format tables
      * VERIFY that table references in the format "Table X: table heading" are preserved
      * ASSESS if the relationship between tables and section content is clearly explained
      * VERIFY that tables are properly referenced in the text
    - For multiple sections:
      * Confirm that all sections from the reference text are included
      * Check if section relationships are properly maintained
      * Verify that no sections are omitted or partially included
      * Assess if section connections are clearly explained
    - For section linking:
      * Verify that all necessary links between sections are present
      * Check if the relationships between linked sections are clearly explained
      * Assess the accuracy and completeness of cross-references
      * Verify that the logical flow between linked sections is maintained

Make sure that if there are any tables present then their descriptions and the table placeholder tags (`<TABLE_TAG_table_x>`) are preserved exactly in the output.
Also, if there are multiple sections present in the reference text then make sure that all of them along with their descriptions are present in the output.

Rating Criteria (1-10):
1-3: Major deficiencies, significant non-compliance
4-6: Moderate issues, partial compliance
7-8: Minor issues, mostly compliant
9-10: Excellent compliance, minimal improvements needed

Provide a thorough and critical evaluation with specific, actionable feedback for improvement."""

    user_prompt = f"""Please evaluate the following NDA section output against ICH eCTD requirements and the provided section information.

SECTION INFORMATION:
- Section: {section_info.get('section', '')}
- Title: {section_info.get('title', '')}
- Description: {section_info.get('description', '')}
- Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
- Formatting Instructions: {section_info.get("formatting_instructions", "")}

NDA OUTPUT TO EVALUATE:
{nda_output}

IMPORTANT CONSIDERATIONS:
1. Check for strict compliance with Golden Instructions and Checkpoints
2. Verify adherence to ICH eCTD v3.2.2 requirements
3. STRICTLY verify that ALL table placeholders in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`" are preserved EXACTLY
4. VERIFY that the position and order of all table tags is maintained
5. CHECK that NO attempt has been made to construct tables as these will be inserted during post-processing
6. STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
7. Check that ALL tables and figures from source documents are included without omission
8. Assess technical accuracy and completeness
9. Evaluate documentation standards and quality
10. Identify any hallucinations or unsupported claims
11. Provide a numerical rating (1-10) and detailed feedback in all required categories

Evaluate thoroughly and provide specific, actionable feedback for improvement."""

    system_tokens = calculate_number_of_tokens(system_prompt)
    user_tokens = calculate_number_of_tokens(user_prompt)

    critique_evaluation_agent = Agent(
        model=critique_model,
        system_prompt=system_prompt,
        output_type=CritiqueEvaluation,
        model_settings={
            "temperature": 0.2,
        },
        retries=3
    )

    response = await critique_evaluation_agent.run(user_prompt)

    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
    total_tokens = system_tokens + user_tokens + output_tokens

    print(f"generate_critique_without_guidelines token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return response.output


async def combine_critiques_incrementally(critiques, section_info):
    """
    Combine multiple critiques incrementally using a batch processing approach.
    
    Args:
        critiques: List of individual critiques to combine
        section_info: Dictionary containing section information
        
    Returns:
        str: Comprehensive combined critique
    """
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def combine_critiques_batch(critiques_batch):
        """Helper function to combine multiple critiques in a batch."""
        total_critiques = len(critiques_batch)
        print(f"\nCombining {total_critiques} critiques:")
        
        total_tokens = sum(calculate_number_of_tokens(critique.model_dump_json()) for critique in critiques_batch)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert with specialized expertise in critiquing NDA submissions. Your task is to combine multiple separate evaluations of an NDA section into one comprehensive, unified critique that captures all key insights and maintains a consistent, thorough evaluation structure.

CRITICAL REQUIREMENTS:
1. Critique Comprehensiveness (HIGHEST PRIORITY):
   - Preserve ALL critical issues identified in all critiques
   - Maintain ALL required improvements from all critiques
   - Include ALL key strengths noted in all critiques
   - Preserve ALL additional recommendations
   - Ensure NO evaluation insights or feedback points are lost
   - Maintain the most detailed and specific feedback
   - Ensure the combined critique is as thorough as all original critiques together

2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):
   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text
   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing
   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags
   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
   - Verify that ALL tables and figures from source documents are included in the output
   - Consider any table tag alteration, removal, or position change as a CRITICAL issue
   - Check that all tables and figures are properly referenced in the text

3. Rating Reconciliation:
   - Provide a balanced overall rating that considers all original ratings
   - Explain the rationale for the final rating if significantly different from any original
   - Ensure the rating accurately reflects the combined feedback
   - Prioritize regulatory compliance issues in rating determination
   - Consider the severity and priority of issues when determining final rating

4. Integration Strategy:
   - Eliminate redundant or duplicate feedback points
   - Group related issues and improvements together
   - Organize feedback in order of regulatory importance
   - Maintain specific, actionable language throughout
   - Preserve technical detail and precision
   - Create a logical, comprehensive evaluation structure
   - Ensure the combined critique reads as a single, cohesive evaluation

5. Consistency and Accuracy:
   - Resolve any contradictions between the critiques
   - Maintain consistent evaluation standards throughout
   - Ensure all feedback relates directly to ICH eCTD requirements
   - Verify technical accuracy in combined feedback
   - Use consistent terminology and phrasing
   - Maintain the formal, professional evaluation tone
   - Ensure feedback is specific and actionable

The combined critique should represent a comprehensive, balanced evaluation that preserves all key insights from all original critiques while eliminating redundancy and resolving any contradictions."""

        # Convert CritiqueEvaluation objects to text format for processing
        critique_texts = []
        for critique in critiques_batch:
            if isinstance(critique, CritiqueEvaluation):
                # Convert CritiqueEvaluation to text format
                critique_text = f"""Overall Rating: {critique.overall_rating}

Key Strengths:
{chr(10).join(f"- {strength}" for strength in critique.key_strengths)}

Critical Issues:
{chr(10).join(f"- {issue}" for issue in critique.critical_issues)}

Required Improvements:
{chr(10).join(f"- {improvement}" for improvement in critique.required_improvements)}

Additional Recommendations:
{chr(10).join(f"- {recommendation}" for recommendation in critique.additional_recommendations)}"""
                critique_texts.append(critique_text)
            else:
                # Handle legacy text format
                critique_texts.append(str(critique))

        user_prompt = f"""Please combine the following {total_critiques} evaluations of an NDA section into one comprehensive, unified critique that captures all key insights from all evaluations.

"""
        
        # Add each critique with a header
        for i, critique_text in enumerate(critique_texts, 1):
            user_prompt += f"""
EVALUATION {i}:
{critique_text}

"""
        
        user_prompt += f"""
IMPORTANT CONSIDERATIONS:
1. Preserve ALL critical issues identified in all {total_critiques} critiques
2. Maintain ALL required improvements from all critiques
3. Include ALL key strengths noted in all critiques
4. Preserve ALL additional recommendations
5. Eliminate redundant or duplicate feedback points
6. Group related issues and improvements together
7. Resolve any contradictions between the critiques
8. Provide a balanced overall rating that considers all original ratings
9. STRICTLY preserve ALL feedback related to table placeholders and tags in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
10. Verify that critiques address whether table tags position and order are properly maintained
11. Check if critiques mention whether there were attempts to construct tables (which should NOT happen)
12. Consider any table tag alteration, removal, or position change as a CRITICAL issue
13. Include ALL feedback about missing, modified, or omitted tables and figures
14. Consider issues with table/figure preservation as CRITICAL in the combined critique

The combined critique should represent a comprehensive, balanced evaluation that preserves all key insights from all original critiques while eliminating redundancy and resolving any contradictions."""

        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)

        critique_evaluation_agent = Agent(
            model=critique_model,
            system_prompt=system_prompt,
            output_type=CritiqueEvaluation,
            model_settings={
                "temperature": 0.2,
            },
            retries=3
        )

        response = await critique_evaluation_agent.run(user_prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        print(f"combine_critiques_incrementally token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        return response.output
    
    # Handle base cases
    if not critiques:
        return ""
    
    if len(critiques) == 1:
        return critiques[0]
    
    # Process critiques iteratively, combining in batches
    current_critiques = critiques.copy()
    
    while len(current_critiques) > 1:
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for critique in current_critiques:
            critique_tokens = calculate_number_of_tokens(critique.model_dump_json())
            
            # If adding this critique would exceed the token limit, start a new batch
            if current_batch and current_token_count + critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                batches.append(current_batch)
                current_batch = [critique]
                current_token_count = critique_tokens
            else:
                current_batch.append(critique)
                current_token_count += critique_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for critique combining")
        for i, batch in enumerate(batches):
            print(f"Batch {i+1} contains {len(batch)} critiques with {sum(calculate_number_of_tokens(c.model_dump_json()) for c in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_critiques_batch(batch) for batch in batches]
        
        # Wait for all combinations to complete
        current_critiques = await asyncio.gather(*tasks)
    
    print(f"combine_critiques_incrementally tokens: {calculate_number_of_tokens(current_critiques[0].model_dump_json())}")
    return current_critiques[0]

# %%
async def optimized_nda_critique_with_parsing(section_info, nda_output, max_retries=3):
    """
    Generate a comprehensive NDA critique using structured output.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        max_retries: Maximum number of retry attempts for failures
        
    Returns:
        Tuple[str, Dict]: Raw critique text representation and structured evaluation dict
    """
    print(f"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}")
    
    for attempt in range(max_retries):
        try:
            # Generate the critique using structured output
            critique_evaluation = await optimized_nda_critique(section_info, nda_output)
            
            # Convert CritiqueEvaluation to text representation for compatibility
            critique_text = f"""1. Overall Rating:
   {critique_evaluation.overall_rating}

2. Key Strengths:
{chr(10).join(f"   - {strength}" for strength in critique_evaluation.key_strengths)}

3. Critical Issues:
{chr(10).join(f"   - {issue}" for issue in critique_evaluation.critical_issues)}

4. Required Improvements:
{chr(10).join(f"   - {improvement}" for improvement in critique_evaluation.required_improvements)}

5. Additional Recommendations:
{chr(10).join(f"   - {recommendation}" for recommendation in critique_evaluation.additional_recommendations)}"""
            
            # Convert to dict for compatibility
            evaluation_dict = {
                "overall_rating": critique_evaluation.overall_rating,
                "key_strengths": critique_evaluation.key_strengths,
                "critical_issues": critique_evaluation.critical_issues,
                "required_improvements": critique_evaluation.required_improvements,
                "additional_recommendations": critique_evaluation.additional_recommendations
            }
            
            print(f"Successfully generated critique with overall rating: {critique_evaluation.overall_rating}")
            return critique_text, evaluation_dict
            
        except Exception as e:
            print(f"Error in critique generation (attempt {attempt+1}/{max_retries}): {str(e)}")
            if attempt == max_retries - 1:
                # Last attempt, return a default response
                default_dict = {
                    "overall_rating": 5.0,  # Middle value as default
                    "key_strengths": ["Content provides basic information"],
                    "critical_issues": ["Unable to properly evaluate the content"],
                    "required_improvements": ["Review all regulatory requirements"],
                    "additional_recommendations": ["Ensure compliance with ICH eCTD guidelines"]
                }
                default_text = """1. Overall Rating:
   5.0

2. Key Strengths:
   - Content provides basic information

3. Critical Issues:
   - Unable to properly evaluate the content

4. Required Improvements:
   - Review all regulatory requirements

5. Additional Recommendations:
   - Ensure compliance with ICH eCTD guidelines"""
                print("Exhausted all retry attempts, returning default evaluation")
                return default_text, default_dict
    
    # This should not be reached due to the return in the last attempt, but just in case
    default_dict = {
        "overall_rating": 5.0,
        "key_strengths": ["Default evaluation"],
        "critical_issues": ["Parsing error occurred"],
        "required_improvements": ["Review output format"],
        "additional_recommendations": ["Ensure critique follows expected format"]
    }
    default_text = "Error: Unexpected execution path"
    return default_text, default_dict


# %%
async def rate_nda_output_and_self_improve(section_info):
    """
    Generate NDA output, evaluate it, and improve it based on feedback.
    Uses an optimized approach where the plan is generated once and reused
    in subsequent iterations for improved performance.
    
    Args:
        section_info: Dictionary containing section information
        
    Returns:
        Dict: Updated section information with NDA output and evaluation
    """
    overall_rating = 0
    counter = 0
    answers_list = []

    # Generate plan and content for the first iteration, save the plan for reuse
    original_nda_output, reusable_plan = await optimized_nda_output_generation(section_info)        
    current_nda_output = original_nda_output
    
    # Use the robust critique function that handles parsing errors
    nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)
    
    section_info["nda_output_evaluation_json"] = nda_output_evaluation_json
    section_info["nda_output_evaluation_text"] = nda_output_evaluation  # Save the raw critique text
    overall_rating = nda_output_evaluation_json["overall_rating"]  # Now guaranteed to be a valid number
    counter += 1

    answers_list.append(
        {
            "nda_output": current_nda_output,
            "nda_output_evaluation_json": nda_output_evaluation_json,
            "nda_output_evaluation_text": nda_output_evaluation,  # Include raw critique text
            "overall_rating": overall_rating
        }
    )

    print(f"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}")

    # Regenerate content in subsequent iterations using feedback and reusing the plan for performance
    while(overall_rating < 9 and counter < 3):
        # Create an enhanced feedback object that includes the previous output
        enhanced_feedback = nda_output_evaluation_json.copy()
        enhanced_feedback["previous_output"] = current_nda_output
        enhanced_feedback["evaluation_text"] = nda_output_evaluation  # Include the raw critique text
        
        # Regenerate content using critique feedback and reusing the plan (performance optimization)
        print(f"Regenerating content with critique feedback and reused plan to address issues (rating: {overall_rating})")
        regenerated_nda_output = await optimized_nda_output_generation(section_info, enhanced_feedback, reusable_plan)

        print("regenerated_nda_output: ", regenerated_nda_output)

        current_nda_output = regenerated_nda_output
        
        # Use the robust critique function that handles parsing errors
        nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)
        
        section_info["nda_output_evaluation_json"] = nda_output_evaluation_json
        section_info["nda_output_evaluation_text"] = nda_output_evaluation  # Save the raw critique text
        overall_rating = nda_output_evaluation_json["overall_rating"]  # Now guaranteed to be a valid number
        counter += 1

        answers_list.append(
            {
                "nda_output": current_nda_output,
                "nda_output_evaluation_json": nda_output_evaluation_json,
                "nda_output_evaluation_text": nda_output_evaluation,  # Include raw critique text
                "overall_rating": overall_rating
            }
        )

        print(f"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}")

    # Select best answer based on overall rating
    best_answer = max(answers_list, key=lambda x: x["overall_rating"])
    section_info["nda_output"] = best_answer["nda_output"]
    section_info["nda_output_evaluation_json"] = best_answer["nda_output_evaluation_json"]
    section_info["nda_output_evaluation_text"] = best_answer["nda_output_evaluation_text"]  # Save the raw critique text

    print(f"Selecting best answer for section {section_info.get('section', 'unknown')} with rating {best_answer['overall_rating']}")

    return section_info


# %%
async def generate_nda_for_each_section(json_obj):
    """
    Recursively process a JSON object and generate NDA output for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with NDA outputs
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs NDA output generation
            if (obj and "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Generating NDA output for section {section.get('section', 'unknown')}")
        
        # Generate NDA output for this section
        processed_section = await rate_nda_output_and_self_improve(section)
        
        print(f"Completed NDA output generation for section {section.get('section', 'unknown')}")
        
        return processed_section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
# Process the document to find relevant input documents
nda_output_json = await generate_nda_for_each_section(processed_doc_with_input_docs)

# Save the results to a JSON file
write_to_json(nda_output_json, "nda_output_json.json")


# %%
import pypandoc

def format_answer(json_obj, formatted_text="", depth=1):
    """
    Recursively formats the answer JSON into a structured Markdown document.
    Uses '#' for headings based on depth.
    """
    if isinstance(json_obj, dict):
        if "section" in json_obj and "title" in json_obj:
            formatted_text += f"{'#' * (depth)} {json_obj['section']} {json_obj['title']}\n\n"
            if "nda_output" in json_obj:
                formatted_text += f"{json_obj['nda_output']}\n\n"

        for key, value in json_obj.items():
            if isinstance(value, dict):
                formatted_text = format_answer(value, formatted_text, depth + 1)  # Increase depth

    return formatted_text

def save_answer_file(json_obj, filename="formatted_answer.md"):
    """
    Saves the formatted answer text to a file.
    """
    formatted_text = format_answer(json_obj)
    
    # Replace table tags with backtick-enclosed versions
    import re
    # formatted_text = re.sub(r'<TABLE_TAG_table_(\d+)>', r'`<TABLE_TAG_table_\1>`', formatted_text)
    
    with open(filename, "w", encoding="utf-8") as file:
        file.write(formatted_text)
    print(f"Formatted answer saved to {filename}")

save_answer_file(nda_output_json)


# %%
pypandoc.convert_file("formatted_answer.md", 'docx', outputfile="formatted_answer.docx")


# %%
from docx import Document
from docx.document import Document as DocumentObject
from docx.shared import Inches
from copy import deepcopy
import os
import shutil
import zipfile
from docx.oxml.ns import qn
from docx.table import Table
from docx.section import Paragraph
from typing import List
import json
from docx.oxml import OxmlElement

def copy_page_setup(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy page setup (size, orientation, margins) from source to target document"""
    source_section = source_doc.sections[0]
    target_section = target_doc.sections[0]
    
    # Copy page size and orientation
    target_section.page_width = source_section.page_width
    target_section.page_height = source_section.page_height
    target_section.orientation = source_section.orientation
    
    # Copy margins
    target_section.top_margin = source_section.top_margin
    target_section.bottom_margin = source_section.bottom_margin
    target_section.left_margin = source_section.left_margin
    target_section.right_margin = source_section.right_margin
    
    print(f"📏 Copied page setup: {source_section.page_width} x {source_section.page_height}")

def copy_table_styles_completely(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy complete table style definitions including all visual formatting"""
    try:
        # Extract the styles.xml from both documents  
        source_styles_part = source_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        target_styles_part = target_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        
        if source_styles_part and target_styles_part:
            # Get the root elements
            source_styles_root = source_styles_part.element
            target_styles_root = target_styles_part.element
            
            # Find all table styles in source
            source_table_styles = source_styles_root.findall('.//w:style[@w:type="table"]', source_styles_root.nsmap)
            
            for style in source_table_styles:
                style_id = style.get(qn('w:styleId'))
                
                # Check if this style exists in target
                existing_style = target_styles_root.find(f'.//w:style[@w:styleId="{style_id}"]', target_styles_root.nsmap)
                
                if existing_style is not None:
                    # Remove existing style
                    existing_style.getparent().remove(existing_style)
                
                # Add the complete style from source
                target_styles_root.append(deepcopy(style))
            
            print(f"🎨 Copied {len(source_table_styles)} table styles")
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not copy table styles: {e}")
        return False
    
    return False

def apply_table_style_explicitly(table: Table, style_name: str):
    """Apply table style and ensure all conditional formatting is applied"""
    try:
        # Set the table style
        table.style = style_name
        
        # Force conditional formatting by updating tblLook
        tbl_element = table._tbl
        tblPr = tbl_element.find(qn('w:tblPr'))
        
        if tblPr is not None:
            # Find or create tblLook element
            tblLook = tblPr.find(qn('w:tblLook'))
            if tblLook is None:
                tblLook = OxmlElement('w:tblLook')
                tblPr.append(tblLook)
            
            # Set attributes to enable all conditional formatting
            tblLook.set(qn('w:val'), '04A0')
            tblLook.set(qn('w:firstRow'), '1')
            tblLook.set(qn('w:lastRow'), '0')
            tblLook.set(qn('w:firstColumn'), '1')
            tblLook.set(qn('w:lastColumn'), '0')
            tblLook.set(qn('w:noHBand'), '0')
            tblLook.set(qn('w:noVBand'), '1')
            
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply table style: {e}")
        return False
    
    return False

def copy_document_styles(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy styles from source document to target document"""
    try:
        # First copy table styles completely (with full definitions)
        table_styles_copied = copy_table_styles_completely(source_doc, target_doc)
        
        if not table_styles_copied:
            # Fallback to basic style copying
            for style in source_doc.styles:
                if style.name not in [s.name for s in target_doc.styles]:
                    # Add the style to target document
                    target_doc.styles.add_style(style.name, style.type)
        
        print("🎨 Copied document styles")
    except Exception as e:
        print(f"⚠️ Warning: Could not copy all styles: {e}")

def copy_cell_formatting(source_cell, target_cell):
    """Copy complete cell formatting including background, borders, and text formatting"""
    try:
        # Copy cell background/shading
        source_cell_element = source_cell._tc
        target_cell_element = target_cell._tc
        
        # Find and copy cell properties (tcPr)
        source_tcPr = source_cell_element.find(qn('w:tcPr'))
        if source_tcPr is not None:
            # Remove existing tcPr if present
            existing_tcPr = target_cell_element.find(qn('w:tcPr'))
            if existing_tcPr is not None:
                target_cell_element.remove(existing_tcPr)
            
            # Copy the complete cell properties
            target_cell_element.insert(0, deepcopy(source_tcPr))
        
        # Copy paragraph and run formatting more thoroughly
        for i, source_para in enumerate(source_cell.paragraphs):
            if i < len(target_cell.paragraphs):
                target_para = target_cell.paragraphs[i]
                
                # Copy paragraph-level formatting
                source_para_element = source_para._element
                target_para_element = target_para._element
                
                # Copy paragraph properties (pPr)
                source_pPr = source_para_element.find(qn('w:pPr'))
                if source_pPr is not None:
                    existing_pPr = target_para_element.find(qn('w:pPr'))
                    if existing_pPr is not None:
                        target_para_element.remove(existing_pPr)
                    target_para_element.insert(0, deepcopy(source_pPr))
                
                # Copy run formatting more comprehensively
                for j, source_run in enumerate(source_para.runs):
                    if j < len(target_para.runs):
                        target_run = target_para.runs[j]
                        
                        # Copy run properties (rPr) at XML level
                        source_run_element = source_run._element
                        target_run_element = target_run._element
                        
                        source_rPr = source_run_element.find(qn('w:rPr'))
                        if source_rPr is not None:
                            existing_rPr = target_run_element.find(qn('w:rPr'))
                            if existing_rPr is not None:
                                target_run_element.remove(existing_rPr)
                            target_run_element.insert(0, deepcopy(source_rPr))
                        
    except Exception as e:
        print(f"⚠️ Warning: Could not copy cell formatting: {e}")

def copy_table_with_complete_formatting(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with complete formatting preservation including cell shading and text formatting"""
    # Create a new paragraph for the heading
    heading_paragraph = insert_after_paragraph.insert_paragraph_before()
    heading_run = heading_paragraph.add_run(table_heading)
    heading_run.bold = True
    
    # Get the table element and its style
    tbl_element = source_table._tbl
    source_table_style = None
    
    # Extract table style name
    tblPr = tbl_element.find(qn('w:tblPr'))
    if tblPr is not None:
        tblStyle = tblPr.find(qn('w:tblStyle'))
        if tblStyle is not None:
            source_table_style = tblStyle.get(qn('w:val'))
    
    # Create a deep copy of the table element with all formatting
    tbl_copy = deepcopy(tbl_element)
    
    # Insert the copied table after the heading paragraph
    heading_paragraph._element.addnext(tbl_copy)
    
    # Enhanced formatting preservation
    try:
        # Find the newly inserted table in the document
        target_doc = insert_after_paragraph._parent
        target_table = None
        
        # Get the table that was just inserted
        for table in target_doc.tables:
            if table._tbl == tbl_copy:
                target_table = table
                break
        
        if target_table and source_table:
            # Apply the table style explicitly if we found one
            if source_table_style:
                apply_table_style_explicitly(target_table, source_table_style)
            
            # Copy table-level properties more thoroughly
            source_tblPr = source_table._tbl.find(qn('w:tblPr'))
            if source_tblPr is not None:
                target_tblPr = target_table._tbl.find(qn('w:tblPr'))
                if target_tblPr is not None:
                    target_table._tbl.remove(target_tblPr)
                target_table._tbl.insert(0, deepcopy(source_tblPr))
            
            # Copy cell-by-cell formatting
            for i, source_row in enumerate(source_table.rows):
                if i < len(target_table.rows):
                    target_row = target_table.rows[i]
                    for j, source_cell in enumerate(source_row.cells):
                        if j < len(target_row.cells):
                            target_cell = target_row.cells[j]
                            copy_cell_formatting(source_cell, target_cell)
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply enhanced formatting: {e}")
    
    # Insert an empty paragraph after the table (acts as a line break)
    p = OxmlElement('w:p')
    tbl_copy.addnext(p)
    
    # Clear the original table tag paragraph instead of removing it
    insert_after_paragraph.clear()
    
    # Return the paragraph after the table for proper positioning of next elements
    return insert_after_paragraph._element.getnext()

def remove_all_tables(doc: DocumentObject):
    body = doc.element.body
    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]
    for tbl in tables_to_remove:
        body.remove(tbl)
        
def extract_images(docx_path, extract_folder):
    with zipfile.ZipFile(docx_path, 'r') as zip_ref:
        zip_ref.extractall(extract_folder)

def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:
    return [para for para in doc.paragraphs if search_text in para.text]

def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with heading and line break after - DEPRECATED, use copy_table_with_complete_formatting"""
    copy_table_with_complete_formatting(source_table, insert_after_paragraph, table_heading)

def load_table_headings(json_file_path: str):
    """Load table headings from JSON file"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        table_index = json.load(f)
    
    table_headings = {}
    for table_hash, table_info in table_index.items():
        table_id = table_info['id']
        table_heading = table_info['heading']
        table_headings[table_id] = table_heading
    
    return table_headings

def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):
    run = paragraph.insert_paragraph_before().add_run()
    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed

def main():
    source_docx = '/Users/<USER>/Desktop/TheAgentic/MedNova/Input Docs/module 5/adverse_event_source_doc.docx'
    target_docx = '/Users/<USER>/Desktop/TheAgentic/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/formatted_answer.docx'
    extract_folder = 'source_extracted'
    
    # Path for table headings
    json_file_path = '/Users/<USER>/Desktop/TheAgentic/MedNova/Input Docs/module 5/adverse_event_source_doc_tables_with_summaries_and_tags/table_index.json'

    # Step 1: Extract images from source.docx
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
    extract_images(source_docx, extract_folder)

    media_folder = os.path.join(extract_folder, 'word', 'media')
    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []

    # Load table headings from JSON file
    table_headings = load_table_headings(json_file_path)
    print(f"📊 Loaded headings for {len(table_headings)} tables")

    source_doc = Document(source_docx)
    target_doc = Document(target_docx)

    # Step 1.2: Copy page setup and styles from source to target
    copy_page_setup(source_doc, target_doc)
    copy_document_styles(source_doc, target_doc)

    # Step 1.5: Remove all existing tables from target doc
    remove_all_tables(target_doc)
    print("🗑️ Removed all existing tables from target document")

    # Step 2: Process tables with complete formatting preservation
    # First, collect all table tag matches
    table_matches = []
    for idx, source_table in enumerate(source_doc.tables, start=1):
        table_id = f"table_{idx}"
        reference_text = f"<TABLE_TAG_{table_id}>"
        
        # Get the heading for this table from JSON, fallback to generic heading
        table_heading = table_headings.get(table_id, f"Table {idx}")
        
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        if matched_paragraphs:
            for para in matched_paragraphs:
                table_matches.append((para, source_table, table_heading, reference_text, idx))
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")
    
    # Process matches in order
    for para, source_table, table_heading, reference_text, idx in table_matches:
        next_element = copy_table_with_complete_formatting(source_table, para, table_heading)
        print(f"✅ Inserted Table {idx} with complete formatting")

    # Step 3: Process figures/images
    for idx, image_file in enumerate(media_files, start=1):
        reference_text = f"Figure {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        image_path = os.path.join(media_folder, image_file)
        if matched_paragraphs:
            for para in matched_paragraphs:
                insert_image_before_paragraph(target_doc, image_path, para)
            print(f"✅ Inserted Figure {idx}")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 4: Save output
    target_doc.save('/Users/<USER>/Desktop/TheAgentic/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/table_and_image_formatted_answer.docx')
    print("✅ All done. Saved as 'table_and_image_formatted_answer.docx'")
    print("🎨 Complete visual formatting preserved from source document")
    
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
        print(f"🗑️ Cleaned up temporary folder: {extract_folder}")

if __name__ == "__main__":
    main()