{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import re\n", "import json\n", "import os\n", "from openai import OpenAI, AsyncOpenAI\n", "from dotenv import load_dotenv\n", "from pymongo import MongoClient\n", "from bson.objectid import ObjectId\n", "from typing import List, Dict, Any, Tuple\n", "import numpy as np\n", "import asyncio\n", "from transformers import AutoTokenizer\n", "import traceback  # Added import for stack trace information\n", "import time  # Added import for timestamp logging\n", "import inspect    # For frame inspection\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent\n", "from pydantic_ai.models.openai import OpenAIModel\n", "from pydantic_ai.providers.openai import OpenAIProvider\n", "\n", "load_dotenv()\n", "\n", "GENERATOR_MODEL_NAME = os.getenv(\"GENERATOR_MODEL_NAME\")\n", "GENERATOR_MODEL_BASE_URL = os.getenv(\"GENERATOR_MODEL_BASE_URL\")\n", "GENERATOR_MODEL_API_KEY = os.getenv(\"GENERATOR_MODEL_API_KEY\")\n", "\n", "CRITIQUE_MODEL_NAME = os.getenv(\"CRITIQUE_MODEL_NAME\")\n", "CRITIQUE_MODEL_BASE_URL = os.getenv(\"CRITIQUE_MODEL_BASE_URL\")\n", "CRITIQUE_MODEL_API_KEY = os.getenv(\"CRITIQUE_MODEL_API_KEY\")\n", "\n", "INCLUDE_JSON_OUTPUT_TAGS = False\n", "\n", "TOPMOST_SUMMARY_THRESHOLD = 3.0\n", "MIDLEVEL_SUMMARY_THRESHOLD = 5.0\n", "CHUNK_SUMMARY_THRESHOLD = 7.0\n", "\n", "MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000\n", "MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000\n", "\n", "critique_client = AsyncOpenAI(\n", "    base_url=CRITIQUE_MODEL_BASE_URL, \n", "    api_key=CRITIQUE_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "generator_client = AsyncOpenAI(\n", "    base_url=GENERATOR_MODEL_BASE_URL, \n", "    api_key=GENERATOR_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "\n", "critique_model = OpenAIModel(\n", "    model_name=CRITIQUE_MODEL_NAME,\n", "    provider=OpenAIProvider(\n", "        base_url=CRITIQUE_MODEL_BASE_URL,\n", "        api_key=CRITIQUE_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "generator_model = OpenAIModel(\n", "    model_name=GENERATOR_MODEL_NAME,\n", "    provider=OpenAIProvider(\n", "        base_url=GENERATOR_MODEL_BASE_URL,\n", "        api_key=GENERATOR_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "print(\"GENERATOR_MODEL_NAME\", GENERATOR_MODEL_NAME)\n", "print(\"GENERATOR_MODEL_BASE_URL\", GENERATOR_MODEL_BASE_URL)\n", "print(\"GENERATOR_MODEL_API_KEY\", GENERATOR_MODEL_API_KEY)\n", "\n", "print(\"CRITIQUE_MODEL_NAME\", CRITIQUE_MODEL_NAME)\n", "print(\"CRITIQUE_MODEL_BASE_URL\", CRITIQUE_MODEL_BASE_URL)\n", "print(\"CRITIQUE_MODEL_API_KEY\", CRITIQUE_MODEL_API_KEY)\n", "\n", "# Pydantic schema for structured output\n", "class CheckpointList(BaseModel):\n", "    \"\"\"Schema for checkpoint list generation\"\"\"\n", "    checkpoints: List[str] = Field(\n", "        description=\"List of specific checkpoint questions that should be answered in summaries for this section\",\n", "        min_items=2\n", "    )\n", "\n", "class RelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for summary relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers the checkpoint questions\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "\n", "class CritiqueEvaluation(BaseModel):\n", "    \"\"\"Schema for NDA critique evaluation\"\"\"\n", "    overall_rating: float = Field(\n", "        description=\"Overall rating from 1-10 for the NDA section quality\",\n", "        ge=1.0,\n", "        le=10.0\n", "    )\n", "    key_strengths: List[str] = Field(\n", "        description=\"List of main strengths identified in the NDA section\",\n", "        default=[]\n", "    )\n", "    critical_issues: List[str] = Field(\n", "        description=\"List of critical issues that need to be addressed\",\n", "        default=[]\n", "    )\n", "    required_improvements: List[str] = Field(\n", "        description=\"List of specific improvements required\",\n", "        default=[]\n", "    )\n", "    additional_recommendations: List[str] = Field(\n", "        description=\"List of additional recommendations for enhancement\",\n", "        default=[]\n", "    )\n", "\n", "def get_json_output_tags():\n", "    if INCLUDE_JSON_OUTPUT_TAGS:\n", "        return \"Give your json in <text_output> json </text_output> tags.\"\n", "    else:\n", "        return \"\"\n", "\n", "async def check_if_llm_is_available(client: AsyncOpenAI, model: str):\n", "    try:\n", "        response = await client.chat.completions.create(model=model, messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "            {\"role\": \"user\", \"content\": \"Hello, world!\"}\n", "        ])\n", "        print(f\"LLM {model} is available, response: {response.choices[0].message.content}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error in checking if LLM {model} is available: {e}\")\n", "        return False\n", "\n", "def get_relevance_threshold(summary_level):\n", "    if summary_level == 1:\n", "        return TOPMOST_SUMMARY_THRESHOLD\n", "    elif summary_level == 2:\n", "        return MIDLEVEL_SUMMARY_THRESHOLD\n", "    else:\n", "        return CHUNK_SUMMARY_THRESHOLD\n", "\n", "def write_to_json(data, filename):\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, indent=2, ensure_ascii=False)\n", "    print(f\"JSON saved to {filename}\")\n", "\n", "def read_json(filename):\n", "    with open(filename, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "def get_mongodb_client():\n", "    \"\"\"Get MongoDB client connection.\"\"\"\n", "    return MongoClient(os.getenv(\"MONGO_DB_URL\"))\n", "\n", "def parse_json_response(response_text: str) -> Any:\n", "    \"\"\"\n", "    Parse a JSON response that may be wrapped in backticks.\n", "    \n", "    Args:\n", "        response_text: The response text to parse\n", "        \n", "    Returns:\n", "        Any: The parsed JSON object\n", "    \"\"\"\n", "    # Remove any markdown code block syntax\n", "    response_text = re.sub(r'```json\\n?', '', response_text)\n", "    response_text = re.sub(r'```\\n?', '', response_text)\n", "    response_text = response_text.strip()\n", "    \n", "    try:\n", "        # print(f\"parse_json_response, response_text: {json.loads(response_text)}\")\n", "        return json.loads(response_text)\n", "    except json.JSONDecodeError:\n", "        # If the response is not valid JSON, try to extract a list from the text\n", "        # Look for lines that start with numbers, bullets, or dashes\n", "        lines = re.findall(r'^[\\d\\-\\*\\.]+\\.?\\s*(.+)$', response_text, re.MULTILINE)\n", "        if lines:\n", "            return lines\n", "        # If no lines found, split by newlines and clean up\n", "        # print(f\"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\\n') if line.strip()]}\")\n", "        return [line.strip() for line in response_text.split('\\n') if line.strip()]\n", "    \n", "def calculate_number_of_tokens(text):\n", "    # Load tokenizer for Mistral model\n", "    tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-32B\")\n", "\n", "    def count_tokens(text):\n", "        tokens = tokenizer.encode(text, add_special_tokens=False)\n", "        return len(tokens)\n", "\n", "    token_count = count_tokens(text)\n", "    return token_count\n", "\n", "async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):\n", "    \"\"\"\n", "    Wrapper function that retries an async LLM API call when the response is empty.\n", "    \n", "    Args:\n", "        async_func: The async function to call (usually an LLM API call)\n", "        *args: Positional arguments to pass to async_func\n", "        max_retries: Maximum number of retry attempts (default: 3)\n", "        **kwargs: Keyword arguments to pass to async_func\n", "        \n", "    Returns:\n", "        The result of the async_func call, ensuring it's not empty\n", "        \n", "    Raises:\n", "        Exception: If max_retries is reached and the response is still empty\n", "    \"\"\"\n", "    # Create logs directory if it doesn't exist\n", "    log_dir = \"error_logs\"\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    # Extract the function name for logging purposes\n", "    func_name = async_func.__name__ if hasattr(async_func, \"__name__\") else \"unknown_function\"\n", "    \n", "    # Try to get the caller's name from the stack\n", "    try:\n", "        caller_frame = inspect.currentframe().f_back\n", "        caller_name = caller_frame.f_code.co_name if caller_frame else \"unknown_caller\"\n", "    except Exception:\n", "        caller_name = \"unknown_caller\"\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            result = await async_func(*args, **kwargs)\n", "            \n", "            # Check if result is None or an empty string or just whitespace\n", "            if result is None or (isinstance(result, str) and result.strip() == \"\"):\n", "                print(f\"Warning: Received empty response from LLM (attempt {attempt+1}/{max_retries}), retrying...\")\n", "                \n", "                # Get debug information to log\n", "                debug_info = {\n", "                    'error_type': 'empty_response',\n", "                    'function': func_name,\n", "                    'caller': caller_name,\n", "                    'attempt': attempt + 1,\n", "                    'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "                }\n", "                \n", "                # Extract prompt information based on different API patterns\n", "                # For the direct messages pattern in kwargs\n", "                if 'messages' in kwargs:\n", "                    debug_info['messages'] = kwargs['messages']\n", "                    \n", "                # For the pattern where the func is a closure with local make_api_call\n", "                # Try to get source code of the async_func to check for patterns\n", "                try:\n", "                    source = inspect.getsource(async_func)\n", "                    if \"chat.completions.create\" in source:\n", "                        debug_info['api_pattern'] = \"chat_completions_closure\"\n", "                except Exception:\n", "                    pass\n", "                \n", "                # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "                try:\n", "                    if caller_frame:\n", "                        caller_locals = caller_frame.f_locals\n", "                        # Capture common patterns in this codebase\n", "                        if 'system_prompt' in caller_locals:\n", "                            debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                        # If this is using the OpenAI client pattern, get the model too\n", "                        if 'model' in caller_locals:\n", "                            debug_info['model'] = caller_locals['model']\n", "                        # For the antropic calls\n", "                        if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                        elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                        elif 'M<PERSON>' in caller_locals:\n", "                            debug_info['model'] = caller_locals['MODEL']\n", "                except Exception as e:\n", "                    debug_info['frame_inspection_error'] = str(e)\n", "                \n", "                # Save the debug information\n", "                timestamp = int(time.time())\n", "                log_filename = f\"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "                \n", "                try:\n", "                    with open(log_filename, 'w', encoding='utf-8') as f:\n", "                        json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                    print(f\"Logged empty response details to {log_filename}\")\n", "                except Exception as log_error:\n", "                    print(f\"Failed to log empty response details: {str(log_error)}\")\n", "                \n", "                # Continue to the next retry attempt\n", "                continue\n", "                \n", "            # If we get here, we have a non-empty response\n", "            return result\n", "            \n", "        except Exception as e:\n", "            error_type = type(e).__name__\n", "            error_msg = str(e)\n", "            print(f\"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}\")\n", "            \n", "            # Get debug information to log\n", "            debug_info = {\n", "                'error_type': error_type,\n", "                'error_message': error_msg,\n", "                'function': func_name,\n", "                'caller': caller_name,\n", "                'attempt': attempt + 1,\n", "                'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                'stack_trace': traceback.format_exc()\n", "            }\n", "            \n", "            # Extract prompt information based on different API patterns\n", "            # For the direct messages pattern in kwargs\n", "            if 'messages' in kwargs:\n", "                debug_info['messages'] = kwargs['messages']\n", "                \n", "            # For the pattern where the func is a closure with local make_api_call\n", "            # Try to get source code of the async_func to check for patterns\n", "            try:\n", "                source = inspect.getsource(async_func)\n", "                if \"chat.completions.create\" in source:\n", "                    debug_info['api_pattern'] = \"chat_completions_closure\"\n", "            except Exception:\n", "                pass\n", "            \n", "            # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "            try:\n", "                if caller_frame:\n", "                    caller_locals = caller_frame.f_locals\n", "                    # Capture common patterns in this codebase\n", "                    if 'system_prompt' in caller_locals:\n", "                        debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                    # If this is using the OpenAI client pattern, get the model too\n", "                    if 'model' in caller_locals:\n", "                        debug_info['model'] = caller_locals['model']\n", "                    # For the antropic calls\n", "                    if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                    elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                    elif 'M<PERSON>' in caller_locals:\n", "                        debug_info['model'] = caller_locals['MODEL']\n", "            except Exception as frame_error:\n", "                debug_info['frame_inspection_error'] = str(frame_error)\n", "            \n", "            # Save the debug information\n", "            timestamp = int(time.time())\n", "            log_filename = f\"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "            \n", "            try:\n", "                with open(log_filename, 'w', encoding='utf-8') as f:\n", "                    json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                print(f\"Logged error details to {log_filename}\")\n", "            except Exception as log_error:\n", "                print(f\"Failed to log error details: {str(log_error)}\")\n", "            \n", "            if attempt == max_retries - 1:\n", "                # If we've exhausted all retries and still have an error\n", "                print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "                return None\n", "            \n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** attempt))\n", "    \n", "    # If we've exhausted all retries and still have an empty response\n", "    print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "    return None\n", "\n", "await check_if_llm_is_available(generator_client, GENERATOR_MODEL_NAME)\n", "await check_if_llm_is_available(critique_client, CRITIQUE_MODEL_NAME)\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["ich_ectd_guideline_referenced_with_efficacy_guidelines = read_json(\"/Users/<USER>/projects/scalegen/MedNova/structured jsons/adverse_event_demo.json\")\n", "\n", "doc_to_work_on = ich_ectd_guideline_referenced_with_efficacy_guidelines[\"module2\"][\"2.7\"][\"2.7.4\"][\"2.7.4.2\"]\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoint questions from a single individual summary.\n", "    \n", "    Args:\n", "        summary_content: The content of the individual summary\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions generated from the individual summary\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive list of checkpoint questions based on the provided summary.\"\n", "            \n", "            prompt = f\"\"\"\n", "            Create a detailed checklist of specific questions that should be answered in input documents for the following section of ICH eCTD guidelines:\n", "            \n", "            Section: {section_info.get('section', '')}\n", "            Title: {section_info.get('title', '')}\n", "\n", "            Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "            \n", "            Individual Summary:\n", "            {summary_content}\n", "            \n", "            The checklist should:\n", "            1. Include all key requirements and recommendations from this summary expressed as specific questions\n", "            2. Cover all aspects mentioned in the summary\n", "            3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.\n", "            4. Be specific enough to clearly determine if an input document addresses each point\n", "            5. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., \"Does the document mention if the product is in powder or liquid form?\")\n", "            6. Focus on technical and regulatory content needed for NDA documentation\n", "            7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant\n", "            8. Cover stability data, packaging specifications, and compatibility information where appropriate\n", "            9. Include questions about the validation and verification methods used\n", "            \"\"\"\n", "            \n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await checkpoint_list_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Generating basic checkpoints from summary as fallback.\")\n", "                # Generate basic checkpoints as fallback\n", "                try:\n", "                    # Extract key sentences from summary as basic checkpoints\n", "                    sentences = [s.strip() + \"?\" for s in summary_content.split(\".\") if len(s.strip()) > 20]\n", "                    # Convert statements to questions where possible\n", "                    questions = []\n", "                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload\n", "                        if not sentence.endswith(\"?\"):\n", "                            questions.append(f\"Does the document address: {sentence}?\")\n", "                        else:\n", "                            questions.append(sentence)\n", "                    return questions\n", "                except Exception:\n", "                    print(\"Fallback checkpoint generation failed. Returning empty list.\")\n", "                    return []\n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n", "async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoints incrementally from a list of individual summaries.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        list_of_individual_summary_contents: List of individual summary contents\n", "        \n", "    Returns:\n", "        List[str]: A comprehensive list of checkpoints generated from all individual summaries\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not list_of_individual_summary_contents:\n", "        return []\n", "        \n", "    # Handle single summary case\n", "    if len(list_of_individual_summary_contents) == 1:\n", "        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)\n", "    \n", "    # Maximum token limit for checkpoint list batches\n", "    \n", "    # Generate initial checkpoints from each summary in parallel\n", "    tasks = [_generate_checkpoints_from_one_summary(summary, section_info) for summary in list_of_individual_summary_contents]\n", "    current_checkpoints = await asyncio.gather(*tasks)\n", "    \n", "    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):\n", "        \"\"\"Helper function to combine multiple checkpoint lists while preserving all unique checkpoints.\"\"\"\n", "        total_lists = len(checkpoint_lists)\n", "        print(f\"\\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints\")\n", "        \n", "        # Calculate token count for all lists combined\n", "        combined_text = \"\\n\".join([\"\\n\".join(cp) for cp in checkpoint_lists])\n", "        total_tokens = calculate_number_of_tokens(combined_text)\n", "        print(f\"Total input tokens for checkpoint merging: {total_tokens}\")\n", "        \n", "        system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive merged list of checkpoint questions.\"\n", "        \n", "        user_prompt = f\"\"\"\n", "        Merge the following {total_lists} lists of checkpoint questions into a single comprehensive list for the following section of ICH eCTD guidelines:\n", "        \n", "        Section: {section_info.get('section', '')}\n", "        Title: {section_info.get('title', '')}\n", "\n", "        Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "        \n", "        \"\"\"\n", "        \n", "        # Add each checkpoint list\n", "        for i, checkpoints in enumerate(checkpoint_lists, 1):\n", "            user_prompt += f\"\"\"\n", "        Checkpoint List {i}:\n", "        {json.dumps(checkpoints, indent=2)}\n", "        \n", "        \"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "        Requirements for Merging:\n", "        1. Preserve ALL unique questions from all {total_lists} lists\n", "        2. Remove any duplicate or redundant questions\n", "        3. Ensure the merged list is comprehensive and covers all aspects\n", "        4. Maintain the specificity and clarity of each question\n", "        5. Keep the question format consistent (e.g., \"Does the document mention...\")\n", "        6. Ensure each question focuses on a single specific point\n", "        7. Group related questions together when possible\n", "        8. The referenced list of golden instructions and checkpoints must be strictly adhered\n", "        9. Keep all questions focused on technical regulatory content for NDA documentation\n", "        \"\"\"\n", "        \n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "        checkpoint_list_agent = Agent(\n", "            model=generator_model,\n", "            system_prompt=system_prompt,\n", "            output_type=CheckpointList,\n", "            model_settings={\n", "                \"temperature\": 0.1,\n", "            },\n", "            retries=3\n", "        )\n", "\n", "        response = await checkpoint_list_agent.run(user_prompt)\n", "\n", "        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "        # print(f\"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "        merged_checkpoints = response.output.checkpoints\n", "        \n", "        # Validate the output\n", "        if len(merged_checkpoints) < 2:\n", "            raise ValueError(f\"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}\")\n", "        \n", "        print(f\"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints\")\n", "        \n", "        return merged_checkpoints\n", "    \n", "    # Iteratively merge checkpoint lists in optimal batches\n", "    while len(current_checkpoints) > 1:\n", "        # Create optimal batches based on token count and number of checkpoints\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        current_checkpoint_count = 0\n", "        \n", "        for checkpoint_list in current_checkpoints:\n", "            # Calculate tokens for this checkpoint list\n", "            checkpoint_text = \"\\n\".join(checkpoint_list)\n", "            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)\n", "            \n", "            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch\n", "            if (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):\n", "                batches.append(current_batch)\n", "                current_batch = [checkpoint_list]\n", "                current_token_count = checkpoint_tokens\n", "                current_checkpoint_count = len(checkpoint_list)\n", "            else:\n", "                current_batch.append(checkpoint_list)\n", "                current_token_count += checkpoint_tokens\n", "                current_checkpoint_count += len(checkpoint_list)\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        print(f\"Created {len(batches)} batches for checkpoint list merging\")\n", "        for i, batch in enumerate(batches):\n", "            total_checkpoints = sum(len(cp) for cp in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]\n", "        \n", "        # Wait for all merges to complete\n", "        current_checkpoints = await asyncio.gather(*tasks)\n", "    \n", "    print(f\"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}\")\n", "    return current_checkpoints[0]\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def generate_checkpoint_list(section, title, description, referenced_efficacy_guidelines, enhanced_instructions_and_checkpoints):\n", "    \"\"\"\n", "    Generate a checklist of questions that should be answered by summaries for this section.\n", "    \n", "    Args:\n", "        section: The section identifier (e.g., \"3.2.P.2.2.1\")\n", "        title: The title of the section\n", "        description: The description of the section\n", "        referenced_efficacy_guidelines: List of efficacy guidelines referenced\n", "        enhanced_instructions_and_checkpoints: A checkpoint list of golden instructions that must be strictly adhered.\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions that should be answered in summaries\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Create a prompt for the LLM to generate a checklist\n", "            prompt = f\"\"\"\n", "            Create a detailed checklist of specific questions that should be answered in summaries for the following section of ICH eCTD guidelines:\n", "            \n", "            Section: {section}\n", "            Title: {title}\n", "            Description: {description}\n", "\n", "            Golden Instructions and Checkpoints: {enhanced_instructions_and_checkpoints}\n", "            \n", "            Referenced Efficacy Guidelines: {', '.join(referenced_efficacy_guidelines)}\n", "            \n", "            The checklist should:\n", "            1. Include all key requirements and recommendations for this section expressed as specific questions\n", "            2. Cover all aspects mentioned in the title and description\n", "            3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.\n", "            4. Address all points from the referenced efficacy guidelines\n", "            5. Be specific enough to clearly determine if a summary addresses each point\n", "            6. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., \"Does the summary mention if the product is in powder or liquid form?\")\n", "            7. Focus on technical and regulatory content needed for NDA documentation\n", "            8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant\n", "            9. Cover stability data, packaging specifications, and compatibility information where appropriate\n", "            10. Include questions about the validation and verification methods used\n", "\n", "            Don't include references to sections/guidelines in the questions themselves.\n", "            \"\"\"\n", "\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Generate a comprehensive list of checkpoint questions for the given section.\"\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "            \n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await checkpoint_list_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            print(f\"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            retry_count += 1\n", "            print(f\"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning empty list.\")\n", "                return []\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def evaluate_summary_relevance(summary_content, section_info, checkpoints):\n", "    \"\"\"\n", "    Use an LLM to evaluate if a summary is relevant to a section by determining if it answers the checkpoint questions.\n", "    \n", "    Args:\n", "        summary_content: The content of the summary to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoints: List of checkpoint questions that should be answered\n", "        \n", "    Returns:\n", "        float: relevance_score\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Create a prompt for the LLM to evaluate the summary\n", "            prompt = f\"\"\"\n", "            Evaluate if the following summary is relevant to the ICH eCTD guideline section described below by determining if it answers any of the checkpoint questions.\n", "            \n", "            SECTION INFORMATION:\n", "            Section: {section_info.get('section', '')}\n", "            Title: {section_info.get('title', '')}\n", "            \n", "            CHECKPOINT QUESTIONS:\n", "            {json.dumps(checkpoints, indent=2)}\n", "            \n", "            SUMMARY TO EVALUATE:\n", "            {summary_content}\n", "            \n", "            Task:\n", "            Carefully analyze if the summary contains information that would answer any of the checkpoint questions listed above. A summary is considered relevant if it provides information to answer at least one checkpoint question, even partially.\n", "            \n", "            Guidelines for evaluation:\n", "            1. For each checkpoint question, determine if the summary provides any information that helps answer it\n", "            2. The summary does not need to explicitly mention the question itself, only provide relevant information\n", "            3. Even partial answers to questions should count toward relevance\n", "            4. Technical content that directly relates to the question topic is particularly important\n", "            5. If multiple questions are answered, the summary should receive a higher relevance score\n", "            6. Consider the depth and detail of the information provided when scoring relevance\n", "            7. If any ONE of the checkpoint questions is answered in the summary, then the summary is relevant\n", "\n", "            Please provide your evaluation as:\n", "            How relevant is it on a scale of 0-10? (0 = not relevant, 10 = highly relevant)\n", "            - Score 0-2: Answers no questions or provides only tangential information\n", "            - Score 3-5: <PERSON><PERSON> answers at least one question with limited detail\n", "            - Score 6-8: <PERSON><PERSON> answers one question or partially answers multiple questions\n", "            - Score 9-10: Comprehensively answers multiple questions with specific details\n", "            \"\"\"\n", "\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines with exceptional abilities in evaluating the relevance of content to specific technical questions.\"\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            relevance_evaluation_agent = Agent(\n", "                model=critique_model,\n", "                system_prompt=system_prompt,\n", "                output_type=RelevanceEvaluation,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await relevance_evaluation_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            print(f\"evaluate_summary_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            return response.output.relevance_score\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in evaluate_summary_relevance (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning default value.\")\n", "                return 0.0\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return default value\n", "    return 0.0\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def search_summaries_by_llm(section_info, efficacy_guidelines, summary_level=1):\n", "    \"\"\"\n", "    Search for summaries in MongoDB based on relevance to the section using an LLM.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        efficacy_guidelines: List of efficacy guidelines to search in (e.g., [\"Q6A\", \"Q6B\"])\n", "        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)\n", "        \n", "    Returns:\n", "        List[Dict]: List of relevant summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    collection = db['efficacy_docs_summaries']\n", "    \n", "    # Find all summaries matching the criteria\n", "    if len(efficacy_guidelines) > 0:\n", "        summaries = list(collection.find({\n", "            \"efficacy_guideline\": {\"$in\": efficacy_guidelines},\n", "            \"summary_level\": summary_level\n", "        }))\n", "    else:\n", "        summaries = list(collection.find({\n", "            \"summary_level\": summary_level\n", "        }))\n", "\n", "    # summaries = list(collection.find({\n", "    #     \"summary_level\": summary_level\n", "    # }))\n", "\n", "    print(f\"Found {len(summaries)} summaries for section {section_info.get('section', '')} efficacy guidelines {efficacy_guidelines} at level {summary_level}\")\n", "    \n", "    if not summaries:\n", "        print(f\"No summaries found for efficacy guidelines {efficacy_guidelines} at level {summary_level}\")\n", "        return []\n", "    \n", "    # Get checkpoints for the section\n", "    checkpoints = section_info.get(\"checkpoint_list\", [])\n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section_info.get('section', '')}\")\n", "        # Generate checkpoints if not already available\n", "        checkpoints = await generate_checkpoint_list(\n", "            section_info.get(\"section\", \"\"),\n", "            section_info.get(\"title\", \"\"),\n", "            section_info.get(\"description\", \"\"),\n", "            section_info.get(\"referenced_efficacy_guidelines\", []),\n", "            section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "        )\n", "    \n", "    # Evaluate each summary\n", "    evaluated_summaries = []\n", "    tasks = [evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints) for summary in summaries]\n", "    results = await asyncio.gather(*tasks)\n", "    \n", "    for summary, relevance_score in zip(summaries, results):\n", "        if relevance_score >= get_relevance_threshold(summary_level):\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}\")\n", "            summary[\"similarity_score\"] = relevance_score\n", "            evaluated_summaries.append(summary)\n", "        else:\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}\")\n", "    \n", "    # Sort by relevance score\n", "    evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "    \n", "    client.close()\n", "    return evaluated_summaries\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def get_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the chunks associated with a summary.\n", "    \n", "    Args:\n", "        summary_id: The ID of the summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['efficacy_docs_summaries']\n", "    chunk_collection = db['efficacy_docs_chunks']\n", "    \n", "    # Get the summary\n", "    summary = summary_collection.find_one({\"_id\": ObjectId(summary_id)})\n", "    \n", "    if not summary or \"chunk_reference_ids\" not in summary:\n", "        print(f\"No chunk references found for summary {summary_id}\")\n", "        return []\n", "    \n", "    # Get the chunks\n", "    chunk_ids = summary[\"chunk_reference_ids\"]\n", "    chunks = list(chunk_collection.find({\"_id\": {\"$in\": chunk_ids}}))\n", "    \n", "    client.close()\n", "    return chunks\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the individual summaries associated with a final summary.\n", "    \n", "    Args:\n", "        final_summary_id: The ID of the final summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of individual summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['efficacy_docs_summaries']\n", "    \n", "    # Get the final summary\n", "    final_summary = summary_collection.find_one({\"_id\": ObjectId(final_summary_id)})\n", "    \n", "    if not final_summary or \"summary_reference_ids\" not in final_summary:\n", "        print(f\"No summary references found for final summary {final_summary_id}\")\n", "        return []\n", "    \n", "    # Get the individual summaries\n", "    summary_ids = final_summary[\"summary_reference_ids\"]\n", "    individual_summaries = list(summary_collection.find({\"_id\": {\"$in\": summary_ids}}))\n", "    \n", "    client.close()\n", "    return individual_summaries\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:\n", "    \"\"\"\n", "    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information (section, title, description, etc.)\n", "        \n", "    Returns:\n", "        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks\n", "    \"\"\"\n", "    # Extract section information\n", "    section = section_info.get(\"section\", \"\")\n", "    title = section_info.get(\"title\", \"\")\n", "    description = section_info.get(\"description\", \"\")\n", "    referenced_efficacy_guidelines = section_info.get(\"referenced_efficacy_guidelines\", [])\n", "    \n", "    # Step 1: Search for relevant final summaries (level 1)\n", "    final_summaries = await search_summaries_by_llm(\n", "        section_info=section_info,\n", "        efficacy_guidelines=referenced_efficacy_guidelines,\n", "        summary_level=1\n", "    )\n", "    \n", "    # Step 2: For each final summary, get relevant individual summaries (level 2)\n", "    individual_summaries = []\n", "    for final_summary in final_summaries:\n", "        summaries = get_summaries_for_final_summary(final_summary[\"_id\"])\n", "        \n", "        # Evaluate each individual summary\n", "        evaluated_summaries = []\n", "        tasks = [evaluate_summary_relevance(summary[\"content\"], section_info, section_info.get(\"checkpoint_list\", [])) for summary in summaries]\n", "        results = await asyncio.gather(*tasks)\n", "        \n", "        for summary, relevance_score in zip(summaries, results):\n", "            if relevance_score >= get_relevance_threshold(2):\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}\")\n", "                summary[\"similarity_score\"] = relevance_score\n", "                evaluated_summaries.append(summary)\n", "            else:\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}\")\n", "        \n", "        # Sort by relevance score\n", "        evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "        individual_summaries.extend(evaluated_summaries)\n", "    \n", "    # Step 3: For each individual summary, get relevant chunks\n", "    chunks = []\n", "    # BATCH_SIZE = 50\n", "    \n", "    # for i in range(0, len(individual_summaries), BATCH_SIZE):\n", "    #     batch = individual_summaries[i:i + BATCH_SIZE]\n", "    #     batch_tasks = []\n", "        \n", "    #     for individual_summary in batch:\n", "    #         summary_chunks = get_chunks_for_summary(individual_summary[\"_id\"])\n", "    #         # Create tasks for parallel evaluation of chunks\n", "    #         tasks = [evaluate_summary_relevance(chunk[\"content\"], section_info, section_info.get(\"efficacy_guideline_combined_summaries_checkpoint_list\", [])) for chunk in summary_chunks]\n", "    #         batch_tasks.extend(zip(summary_chunks, tasks))\n", "        \n", "    #     # Execute all tasks in parallel\n", "    #     results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "    #     # Process results\n", "    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):\n", "    #         if relevance_score >= get_relevance_threshold(0):\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    #             chunk[\"similarity_score\"] = relevance_score\n", "    #             chunks.append(chunk)\n", "    #         else:\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    \n", "    return final_summaries, individual_summaries, chunks\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def extract_relevant_chunks(json_obj):\n", "    \"\"\"\n", "    Recursively iterates through the nested JSON structure, extracts titles and descriptions,\n", "    generates checkpoint lists, and finds relevant summaries and chunks for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with added relevant summaries and chunks\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that needs processing\n", "            if (obj and \"title\" in obj and \"description\" in obj and \n", "                \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        # Generate checkpoint list for this section\n", "        section[\"checkpoint_list\"] = await generate_checkpoint_list(\n", "            section[\"section\"], \n", "            section[\"title\"], \n", "            section[\"description\"], \n", "            section.get(\"referenced_efficacy_guidelines\", []),\n", "            section.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "        )\n", "        \n", "        # Find relevant summaries and chunks for this section\n", "        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(\n", "            section\n", "        )\n", "\n", "        # Print summary of results\n", "        print(f\"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks\")\n", "        \n", "        # Add the results to the section\n", "        section[\"relevant_final_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"efficacy_guideline\": s[\"efficacy_guideline\"], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            } for s in final_summaries\n", "        ]\n", "        \n", "        section[\"relevant_individual_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"efficacy_guideline\": s[\"efficacy_guideline\"], \n", "                \"similarity_score\": s.get(\"similarity_score\", 0), \n", "                \"content\": s[\"content\"]\n", "            } for s in individual_summaries\n", "        ]\n", "\n", "        # Create a final summary from the relevant individual summaries\n", "        # if len(section[\"relevant_individual_summaries\"]) > 0:\n", "        #     final_summary = await create_combined_summary([summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]])\n", "        #     section[\"efficacy_guideline_combined_summary\"] = final_summary\n", "        # else:\n", "        #     # If no relevant summaries found, create a summary from section information\n", "        #     final_summary = await generate_summary_from_section_info(section)\n", "        #     section[\"efficacy_guideline_combined_summary\"] = final_summary\n", "\n", "        # Generate checkpoints incrementally from individual summaries\n", "        if len(section[\"relevant_individual_summaries\"]) > 0:\n", "            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(\n", "                section,\n", "                [summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]]\n", "            )\n", "        else:\n", "            # If no relevant summaries found, generate checkpoints from section information\n", "            checkpoints = await generate_checkpoint_list(\n", "                section[\"section\"],\n", "                section[\"title\"],\n", "                section[\"description\"],\n", "                section.get(\"referenced_efficacy_guidelines\", []),\n", "                section.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "            )\n", "        \n", "        print(f\"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}\")\n", "        section[\"efficacy_guideline_combined_summaries_checkpoint_list\"] = checkpoints\n", "        \n", "        return section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["processed_doc = await extract_relevant_chunks(doc_to_work_on)\n", "\n", "# Save the results to a JSON file\n", "write_to_json(processed_doc, \"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["processed_doc = read_json(\"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def search_input_docs_by_llm(section_info, efficacy_input_doc_tag, summary_level=1):\n", "    \"\"\"\n", "    Search for input document summaries in MongoDB based on relevance to the section using an LLM.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        efficacy_input_doc_tag: The input document tag to search for\n", "        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)\n", "        \n", "    Returns:\n", "        List[Dict]: List of relevant summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    collection = db['efficacy_input_docs_summaries']\n", "    \n", "    # Find all summaries matching the criteria\n", "    if efficacy_input_doc_tag:\n", "        summaries = list(collection.find({\n", "            \"efficacy_input_doc_tag\": efficacy_input_doc_tag,\n", "            \"summary_level\": summary_level\n", "        }))\n", "    else:\n", "        summaries = list(collection.find({\n", "            \"summary_level\": summary_level\n", "        }))\n", "    \n", "    print(f\"Found {len(summaries)} input document summaries for section {section_info.get('section', '')} with tag {efficacy_input_doc_tag} at level {summary_level}\")\n", "    \n", "    if not summaries:\n", "        print(f\"No input document summaries found for tag {efficacy_input_doc_tag} at level {summary_level}\")\n", "        return []\n", "    \n", "    # Get checkpoints for the section - prefer using checkpoints from efficacy_guideline_combined_summary\n", "    print(f\"Using {len(section_info.get('efficacy_guideline_combined_summaries_checkpoint_list', []))} efficacy_guideline_combined_summaries_checkpoint_list from efficacy_guideline_combined_summary for section {section_info.get('section', '')}\")\n", "    checkpoints = section_info.get(\"efficacy_guideline_combined_summaries_checkpoint_list\", [])\n", "\n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section_info.get('section', '')}\")\n", "        return []\n", "    \n", "    # Evaluate each summary\n", "    evaluated_summaries = []\n", "    tasks = [evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints) for summary in summaries]\n", "    results = await asyncio.gather(*tasks)\n", "    \n", "    for summary, relevance_score in zip(summaries, results):\n", "        if relevance_score >= get_relevance_threshold(summary_level):\n", "            print(f\"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}\")\n", "            summary[\"similarity_score\"] = relevance_score\n", "            evaluated_summaries.append(summary)\n", "        else:\n", "            print(f\"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}\")\n", "    \n", "    # Sort by relevance score\n", "    evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "    \n", "    client.close()\n", "    return evaluated_summaries\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def get_input_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the input document chunks associated with a summary.\n", "    \n", "    Args:\n", "        summary_id: The ID of the summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['efficacy_input_docs_summaries']\n", "    chunk_collection = db['efficacy_input_docs_chunks']\n", "    \n", "    # Get the summary\n", "    summary = summary_collection.find_one({\"_id\": ObjectId(summary_id)})\n", "    \n", "    if not summary or \"chunk_reference_ids\" not in summary:\n", "        print(f\"No chunk references found for summary {summary_id}\")\n", "        return []\n", "    \n", "    # Get the chunks\n", "    chunk_ids = summary[\"chunk_reference_ids\"]\n", "    chunks = list(chunk_collection.find({\"_id\": {\"$in\": chunk_ids}}))\n", "    \n", "    client.close()\n", "    return chunks\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def get_input_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the individual input document summaries associated with a final summary.\n", "    \n", "    Args:\n", "        final_summary_id: The ID of the final summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of individual summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['efficacy_input_docs_summaries']\n", "    \n", "    # Get the final summary\n", "    final_summary = summary_collection.find_one({\"_id\": ObjectId(final_summary_id)})\n", "    \n", "    if not final_summary or \"summary_reference_ids\" not in final_summary:\n", "        print(f\"No summary references found for final summary {final_summary_id}\")\n", "        return []\n", "    \n", "    # Get the individual summaries\n", "    summary_ids = final_summary[\"summary_reference_ids\"]\n", "    individual_summaries = list(summary_collection.find({\"_id\": {\"$in\": summary_ids}}))\n", "    \n", "    client.close()\n", "    return individual_summaries\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def filter_input_docs_by_section(section_info: Dict[str, Any], efficacy_input_doc_tag: str = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:\n", "    \"\"\"\n", "    Filter input document summaries based on section information, going from summary level 1 to summary level 2 to chunks.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information (section, title, description, etc.)\n", "        efficacy_input_doc_tag: The input document tag to search for\n", "        \n", "    Returns:\n", "        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks\n", "    \"\"\"\n", "    # Step 1: Search for relevant final summaries (level 1)\n", "    final_summaries = await search_input_docs_by_llm(\n", "        section_info=section_info,\n", "        efficacy_input_doc_tag=efficacy_input_doc_tag,\n", "        summary_level=1\n", "    )\n", "    \n", "    # Step 2: For each final summary, get relevant individual summaries (level 2)\n", "    individual_summaries = []\n", "    BATCH_SIZE = 50\n", "    \n", "    for i in range(0, len(final_summaries), BATCH_SIZE):\n", "        batch = final_summaries[i:i + BATCH_SIZE]\n", "        batch_tasks = []\n", "        \n", "        for final_summary in batch:\n", "            summaries = get_input_summaries_for_final_summary(final_summary[\"_id\"])\n", "            # Create tasks for parallel evaluation of individual summaries\n", "            tasks = [evaluate_summary_relevance(summary[\"content\"], section_info, section_info.get(\"efficacy_guideline_combined_summaries_checkpoint_list\", [])) for summary in summaries]\n", "            batch_tasks.extend(zip(summaries, tasks))\n", "        \n", "        # Execute all tasks in parallel\n", "        results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "        # Process results\n", "        for (summary, _), relevance_score in zip(batch_tasks, results):\n", "            if relevance_score >= get_relevance_threshold(2):\n", "                print(f\"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}\")\n", "                summary[\"similarity_score\"] = relevance_score\n", "                individual_summaries.append(summary)\n", "            else:\n", "                print(f\"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}\")\n", "    \n", "    # Step 3: For each individual summary, get relevant chunks\n", "    chunks = []\n", "    \n", "    for i in range(0, len(individual_summaries), BATCH_SIZE):\n", "        batch = individual_summaries[i:i + BATCH_SIZE]\n", "        batch_tasks = []\n", "        \n", "        for individual_summary in batch:\n", "            summary_chunks = get_input_chunks_for_summary(individual_summary[\"_id\"])\n", "            # Create tasks for parallel evaluation of chunks\n", "            tasks = [evaluate_summary_relevance(chunk[\"content\"], section_info, section_info.get(\"efficacy_guideline_combined_summaries_checkpoint_list\", [])) for chunk in summary_chunks]\n", "            batch_tasks.extend(zip(summary_chunks, tasks))\n", "        \n", "        # Execute all tasks in parallel\n", "        results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "        # Process results\n", "        for (chunk, _), relevance_score in zip(batch_tasks, results):\n", "            if relevance_score >= get_relevance_threshold(0):\n", "                print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "                chunk[\"similarity_score\"] = relevance_score\n", "                chunks.append(chunk)\n", "            else:\n", "                print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    \n", "    return final_summaries, individual_summaries, chunks\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def extract_relevant_input_docs(json_obj, efficacy_input_doc_tag: str = None):\n", "    \"\"\"\n", "    Recursively iterates through the nested JSON structure, finds relevant efficacy guideline summaries,\n", "    creates a final summary, and then finds relevant input document summaries and chunks for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        efficacy_input_doc_tag: The input document tag to search for\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with added relevant input document summaries and chunks\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that has relevant efficacy guideline summaries\n", "            if (obj and \"relevant_final_summaries\" in obj and \n", "                obj[\"relevant_final_summaries\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        print(f\"Processing section {section.get('section', 'unknown')} for input documents\")\n", "\n", "        # Find relevant input document summaries and chunks\n", "        input_final_summaries, input_individual_summaries, input_chunks = await filter_input_docs_by_section(\n", "            section,\n", "            efficacy_input_doc_tag\n", "        )\n", "        \n", "        # Add the results to the section\n", "        section[\"relevant_input_final_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"efficacy_input_doc_tag\": s[\"efficacy_input_doc_tag\"], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            } for s in input_final_summaries\n", "        ]\n", "        \n", "        section[\"relevant_input_individual_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"efficacy_input_doc_tag\": s[\"efficacy_input_doc_tag\"], \n", "                \"similarity_score\": s.get(\"similarity_score\", 0),\n", "                \"content\": s[\"content\"]\n", "            } for s in input_individual_summaries\n", "        ]\n", "        \n", "        section[\"relevant_input_chunks\"] = [\n", "            {\n", "                \"_id\": str(c[\"_id\"]), \n", "                \"efficacy_input_doc_tag\": c[\"efficacy_input_doc_tag\"], \n", "                \"chunk_filename\": c.get(\"chunk_filename\", \"\"), \n", "                \"similarity_score\": c.get(\"similarity_score\", 0), \n", "                \"content\": c.get(\"content\", \"\")\n", "            } for c in input_chunks\n", "        ]\n", "        \n", "        # Print summary of results\n", "        print(f\"Section {section.get('section', 'unknown')}: Found {len(input_final_summaries)} relevant input final summaries, {len(input_individual_summaries)} relevant input individual summaries, and {len(input_chunks)} relevant input chunks\")\n", "        \n", "        return section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Process the document to find relevant input documents\n", "processed_doc_with_input_docs = await extract_relevant_input_docs(processed_doc, efficacy_input_doc_tag=\"efficacy_input_docs\")\n", "\n", "# Save the results to a JSON file\n", "write_to_json(processed_doc_with_input_docs, \"processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json\")\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["processed_doc_with_input_docs = read_json(\"processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json\")\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def extract_evaluation_into_json(evaluation_text):\n", "    \"\"\"\n", "    Extracts the numerical rating and structured feedback from the evaluation text into a JSON format.\n", "    \n", "    Args:\n", "        evaluation_text: The evaluation text to parse\n", "        \n", "    Returns:\n", "        Dict: JSON object containing the evaluation results\n", "    \"\"\"\n", "    # Initialize the result dictionary\n", "    result = {\n", "        \"overall_rating\": None,\n", "        \"key_strengths\": [],\n", "        \"critical_issues\": [],\n", "        \"required_improvements\": [],\n", "        \"additional_recommendations\": []\n", "    }\n", "    \n", "    # Split the text into sections\n", "    sections = evaluation_text.split('\\n\\n')\n", "    \n", "    for section in sections:\n", "        section = section.strip()\n", "        if not section:\n", "            continue\n", "            \n", "        # Extract overall rating\n", "        if section.startswith(\"1. Overall Rating\"):\n", "            try:\n", "                # Extract the number from the rating line\n", "                rating_text = section.split(\":\")[1].strip()\n", "                result[\"overall_rating\"] = float(rating_text.split('/')[0])\n", "            except:\n", "                result[\"overall_rating\"] = None\n", "                \n", "        # Extract key strengths\n", "        elif section.startswith(\"2. Key Strengths\"):\n", "            points = section.split('\\n')[1:]\n", "            result[\"key_strengths\"] = [point.strip('- ').strip() for point in points if point.strip()]\n", "            \n", "        # Extract critical issues\n", "        elif section.startswith(\"3. Critical Issues\"):\n", "            points = section.split('\\n')[1:]\n", "            result[\"critical_issues\"] = [point.strip('- ').strip() for point in points if point.strip()]\n", "            \n", "        # Extract required improvements\n", "        elif section.startswith(\"4. Required Improvements\"):\n", "            points = section.split('\\n')[1:]\n", "            result[\"required_improvements\"] = [point.strip('- ').strip() for point in points if point.strip()]\n", "            \n", "        # Extract additional recommendations\n", "        elif section.startswith(\"5. Additional Recommendations\"):\n", "            points = section.split('\\n')[1:]\n", "            result[\"additional_recommendations\"] = [point.strip('- ').strip() for point in points if point.strip()]\n", "    \n", "    return result\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def create_incremental_plan(section_info, individual_summaries):\n", "    \"\"\"\n", "    Create a comprehensive NDA section plan by incrementally combining plans generated from individual efficacy guideline summaries.\n", "    Uses batch processing to efficiently process multiple summaries.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        individual_summaries: List of individual relevant efficacy guideline summaries\n", "        \n", "    Returns:\n", "        str: Comprehensive plan for NDA section content\n", "    \"\"\"\n", "    \n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def combine_plans(plans_to_combine):\n", "        \"\"\"Helper function to combine multiple plans while preserving all requirements.\"\"\"\n", "        total_plans = len(plans_to_combine)\n", "        print(f\"\\nCombining {total_plans} plans:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(plan) for plan in plans_to_combine)\n", "        print(f\"Total input tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple section plans into one comprehensive unified plan that will guide the generation of a complete, regulatory-compliant NDA section.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Plan Comprehensiveness (HIGHEST PRIORITY):\n", "   - Preserve EVERY requirement, element, and consideration from all plans\n", "   - Ensure NO regulatory requirement is lost or diluted\n", "   - Maintain ALL technical specifications from all plans\n", "   - Include ALL validation and verification details\n", "   - Preserve ALL cross-references and dependencies\n", "   - Keep ALL acceptance criteria and thresholds\n", "   - Maintain ALL risk assessments and control strategies\n", "   - Include ALL testing requirements and methodologies\n", "   - Preserve ALL regulatory compliance elements\n", "\n", "2. ICH eCTD Compliance:\n", "   - Ensure the plan follows ICH eCTD v3.2.2 structure requirements\n", "   - Maintain proper section organization and hierarchy\n", "   - Include all required eCTD elements and subsections\n", "   - Preserve regulatory guideline references\n", "   - Ensure plan addresses all ICH requirements\n", "\n", "3. Integration Strategy:\n", "   - Create a logical, cohesive document structure\n", "   - Group related requirements together\n", "   - Eliminate redundancies without losing information\n", "   - Organize requirements in order of regulatory importance\n", "   - Ensure technical specifications are properly contextualized\n", "   - Maintain relationships between different requirements\n", "   - Create clear section and subsection organization\n", "   - Establish information hierarchy and flow\n", "\n", "4. Technical Depth:\n", "   - Ensure the plan specifies all technical parameters to include\n", "   - Maintain detailed specifications for tests and analyses\n", "   - Preserve methodological details\n", "   - Include precise acceptance criteria\n", "   - Specify validation requirements\n", "   - Maintain detailed documentation standards\n", "   - Include specific technical cross-references\n", "\n", "5. Documentation Requirements:\n", "   - Specify all tables, figures, and data presentations needed\n", "   - Detail formatting requirements for technical information\n", "   - Include citations and reference standards\n", "   - Specify any appendices or supplementary materials\n", "   - Maintain documentation conventions from all plans\n", "   - Specify any required attachments or supporting documents\n", "\n", "6. Quality Assurance Elements:\n", "   - Include all quality control procedures\n", "   - Maintain quality assurance checkpoints\n", "   - Preserve change control processes\n", "   - Include batch release criteria\n", "   - Specify stability and shelf-life considerations\n", "   - Maintain monitoring and surveillance requirements\n", "\n", "7. Risk Management:\n", "   - Include all risk assessment requirements\n", "   - Maintain mitigation strategies\n", "   - Preserve safety considerations\n", "   - Include contingency requirements\n", "   - Specify monitoring and review processes\n", "   - Maintain risk-benefit analyses\n", "\n", "OUTPUT FORMAT:\n", "- Structured, hierarchical plan with numbered sections and subsections\n", "- Clear delineation of requirements, specifications, and expectations\n", "- Specific notation of required tables, figures, and data presentations\n", "- Precise listing of technical parameters and acceptance criteria\n", "- Clear indication of required cross-references and dependencies\n", "- Logical organization following ICH eCTD structure\n", "\n", "The plan should be detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, ensuring all regulatory requirements are addressed and no technical details are omitted.\"\"\"\n", "\n", "        user_prompt = f\"\"\"Please create a comprehensive unified plan that combines the following {total_plans} section plans into one cohesive blueprint for generating a complete, regulatory-compliant NDA section.\n", "\n", "\"\"\"\n", "        \n", "        # Add each plan with a header\n", "        for i, plan in enumerate(plans_to_combine, 1):\n", "            user_prompt += f\"\"\"\n", "Plan {i}:\n", "{plan}\n", "\n", "\"\"\"\n", "\n", "        user_prompt += f\"\"\"\n", "VERIFICATION CHECKLIST:\n", "1. Does the combined plan include ALL requirements from all {total_plans} plans?\n", "2. Are ALL technical specifications preserved?\n", "3. Are ALL validation and verification details maintained?\n", "4. Are ALL cross-references and dependencies included?\n", "5. Are ALL regulatory compliance elements preserved?\n", "6. Does the plan follow ICH eCTD structure?\n", "7. Is the plan logically organized with clear sections and subsections?\n", "8. Are ALL required tables, figures, and data presentations specified?\n", "9. Are ALL acceptance criteria and thresholds preserved?\n", "10. Are ALL risk assessment and management requirements included?\n", "\n", "IMPORTANT CONSIDERATIONS:\n", "1. The plan must preserve EVERY requirement, element, and consideration from all {total_plans} plans\n", "2. No regulatory requirement should be lost or diluted\n", "3. Technical specifications must be maintained in full detail\n", "4. The plan should follow ICH eCTD v3.2.2 structure requirements\n", "5. The plan should eliminate redundancies while preserving all unique elements\n", "6. The plan should specify all required tables, figures, and data presentations\n", "7. Technical parameters and acceptance criteria should be precisely specified\n", "8. Cross-references and dependencies should be clearly indicated\n", "9. The plan should be organized in a logical, hierarchical structure\n", "10. The plan must be detailed enough to serve as a comprehensive blueprint\n", "\n", "After creating the combined plan, verify it against the verification checklist to ensure all elements have been properly incorporated.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"combine_plans token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "    \n", "    async def create_plan_from_one_summary(summary_content, section_info):\n", "        \"\"\"Generate a plan from a single summary content.\"\"\"\n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to create a detailed, structured plan for an NDA section based on the provided efficacy guideline summary and section information.\n", "\n", "Your plan will serve as a comprehensive blueprint for generating complete, regulatory-compliant content for this NDA section.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Regulatory Compliance (HIGHEST PRIORITY):\n", "   - Ensure the plan addresses ALL requirements from ICH eCTD v3.2.2\n", "   - Include ALL elements specified in the section description and reference text\n", "   - Follow ALL golden instructions and checkpoints provided\n", "   - Address ALL requirements from the efficacy guideline summary\n", "   - Ensure compliance with all formatting instructions\n", "\n", "2. Plan Structure:\n", "   - Create a hierarchical, logical document structure\n", "   - Include clearly numbered sections and subsections\n", "   - Organize requirements in order of regulatory importance\n", "   - Group related requirements together\n", "   - Establish clear information flow and progression\n", "   - Follow standard eCTD section organization\n", "\n", "3. Technical Specifications:\n", "   - Specify ALL technical parameters to be included\n", "   - Detail ALL required tests, analyses, and methodologies\n", "   - Include precise acceptance criteria and thresholds\n", "   - Specify validation and verification requirements\n", "   - Detail required technical documentation\n", "   - Include specific cross-references to other sections\n", "\n", "4. Documentation Elements:\n", "   - Specify ALL required tables, figures, and data presentations\n", "   - Detail formatting requirements for technical information\n", "   - Include required citations and reference standards\n", "   - Specify any appendices or supplementary materials\n", "   - Detail any required attachments or supporting documents\n", "\n", "5. Comprehensive Coverage:\n", "   - Ensure the plan addresses EVERY point in the efficacy guideline summary\n", "   - Include ALL technical specifications mentioned\n", "   - Incorporate ALL validation and verification details\n", "   - Address ALL risk assessment and management requirements\n", "   - Include ALL safety and efficacy considerations\n", "   - Preserve ALL cross-references and dependencies\n", "\n", "OUTPUT FORMAT:\n", "- Structured, hierarchical plan with numbered sections and subsections\n", "- Clear delineation of requirements, specifications, and expectations\n", "- Specific notation of required tables, figures, and data presentations\n", "- Precise listing of technical parameters and acceptance criteria\n", "- Clear indication of required cross-references and dependencies\n", "- Logical organization following ICH eCTD structure\n", "\n", "The plan should be detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, ensuring all regulatory requirements are addressed and no technical details are omitted.\"\"\"\n", "\n", "        user_prompt = f\"\"\"Please create a detailed, structured plan for generating content for the following NDA section based on the efficacy guideline summary and section information provided.\n", "\n", "SECTION INFORMATION:\n", "- Section: {section_info.get('section', '')}\n", "- Title: {section_info.get('title', '')}\n", "- Description: {section_info.get('description', '')}\n", "- Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "- Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "\n", "EFFICACY GUIDELINE SUMMARY:\n", "{summary_content}\n", "\n", "IMPORTANT CONSIDERATIONS:\n", "1. The plan must address EVERY requirement from the section information and efficacy guideline summary\n", "2. The plan must follow ICH eCTD v3.2.2 structure requirements\n", "3. The plan should specify all required tables, figures, and data presentations\n", "4. Technical parameters and acceptance criteria should be precisely specified\n", "5. The plan should include all validation and verification requirements\n", "6. Cross-references and dependencies should be clearly indicated\n", "7. The plan should be organized in a logical, hierarchical structure\n", "8. The plan must be detailed enough to serve as a comprehensive blueprint\n", "\n", "Create a structured plan that would guide the generation of complete, regulatory-compliant content for this NDA section.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"create_plan_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    # Handle empty input\n", "    if not individual_summaries:\n", "        return \"\"\n", "        \n", "    # Handle single summary case\n", "    if len(individual_summaries) == 1:\n", "        return await create_plan_from_one_summary(individual_summaries[0], section_info)\n", "    \n", "    # First generate initial plans from each summary\n", "    tasks = [create_plan_from_one_summary(summary, section_info) for summary in individual_summaries]\n", "    current_plans = await asyncio.gather(*tasks)\n", "    \n", "    # Process plans iteratively, combining batches in parallel\n", "    while len(current_plans) > 1:\n", "        # Create optimal batches based on token count\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for plan in current_plans:\n", "            plan_tokens = calculate_number_of_tokens(plan)\n", "            \n", "            # If adding this plan would exceed the token limit, start a new batch\n", "            if current_batch and current_token_count + plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                batches.append(current_batch)\n", "                current_batch = [plan]\n", "                current_token_count = plan_tokens\n", "            else:\n", "                current_batch.append(plan)\n", "                current_token_count += plan_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        print(f\"Created {len(batches)} batches for plan processing\")\n", "        for i, batch in enumerate(batches):\n", "            print(f\"Batch {i+1} contains {len(batch)} plans with {sum(calculate_number_of_tokens(p) for p in batch)} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_plans(batch) for batch in batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        current_plans = await asyncio.gather(*tasks)\n", "\n", "    print(\"create_incremental_plan tokens: \", calculate_number_of_tokens(current_plans[0]))\n", "    \n", "    return current_plans[0]\n", "\n", "async def create_incremental_content(plan, section_info, input_chunks, critique_feedback=None):\n", "    \"\"\"\n", "    Generate content incrementally using the plan created in Stage 1 and input document chunks.\n", "    Uses batch processing to efficiently process input chunks and combine content.\n", "    \n", "    Args:\n", "        plan: The comprehensive plan for the NDA section\n", "        section_info: Dictionary containing section information\n", "        input_chunks: List of input document chunks to use for content generation\n", "        critique_feedback: Optional critique feedback from previous iterations to address missing content\n", "        \n", "    Returns:\n", "        str: Generated NDA section content\n", "    \"\"\"\n", "    \n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def generate_content_from_chunks(plan, chunks, section_info, critique_feedback=None):\n", "        \"\"\"Generate content from a batch of input chunks using the plan.\"\"\"\n", "        total_chunks = len(chunks)\n", "        print(f\"\\nGenerating content from {total_chunks} chunks:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(chunk.get('content', '')) for chunk in chunks)\n", "        print(f\"Total input chunk tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to generate high-quality, detailed, regulatory-compliant content for an NDA section based on the provided plan and input document chunks.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Plan-Guided Content Generation (HIGHEST PRIORITY):\n", "   - Use the provided plan as your PRIMARY FILTER and GUIDE for content selection\n", "   - ONLY include content from input chunks that directly addresses elements in the plan\n", "   - DISCARD any chunk content that does not align with plan requirements\n", "   - Prioritize chunks that contain specific technical data, validation information, and regulatory details mentioned in the plan\n", "   - If a chunk contains both relevant and irrelevant information, extract ONLY the plan-relevant portions\n", "   - Ensure every piece of content serves a specific purpose outlined in the plan\n", "\n", "2. Regulatory Compliance (HIGHEST PRIORITY):\n", "   - Follow the provided plan EXACTLY as it has been designed to meet all regulatory requirements\n", "   - Ensure compliance with ICH eCTD v3.2.2 standards\n", "   - Address ALL requirements specified in the plan\n", "   - Follow ALL formatting instructions\n", "   - Adhere to ALL golden instructions and checkpoints\n", "\n", "3. Table Tag Preservation (HIGHEST PRIORITY):\n", "   - PRESERVE ALL table tags EXACTLY as they appear in the input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`, etc.)\n", "   - DO NOT modify, rename, or change table tags in ANY way\n", "   - DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "   - MAINTAIN the EXACT position and order of table tags as they appear in the input chunks\n", "   - Include table tags in the exact location where the table should appear in your content\n", "   - Use the table summary information to write brief contextual statements about the table if needed\n", "   - DO NOT omit any table tags from the input chunks that are relevant to the plan\n", "   - Treat table tags as placeholders that must be preserved exactly for later replacement\n", "\n", "4. Figure Preservation (HIGHEST PRIORITY):\n", "   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents\n", "   - DO NOT rename any figures (e.g., if a figure is \"Figure 1: XYZ\" do NOT change it to \"Figure 5.7.3: ABC\")\n", "   - INCLUDE ALL figures that appear in the input source documents\n", "   - DO NOT modify the content of any figures\n", "   - DO NOT omit any figures from the source documents\n", "   - Include references to all figures in your text\n", "\n", "5. NO META-COMMENTARY (HIGHEST PRIORITY):\n", "   - DO NOT include any commentary about how you addressed critique feedback\n", "   - DO NOT include verification checklists or statements about compliance with plans\n", "   - DO NOT include meta-statements like \"This addresses the critique feedback\" or \"As required by the plan\"\n", "   - DO NOT include process-related commentary like \"Verification of Compliance\" sections\n", "   - DO NOT include formatting instructions or comments about document structure in the output\n", "   - Generate ONLY the actual NDA content that would appear in the final submission\n", "   - DO NOT include any self-referential statements about the generation process\n", "\n", "6. Content Quality:\n", "   - Generate comprehensive, technically precise content\n", "   - Include all required technical details, parameters, and specifications ONLY as specified in the plan\n", "   - Provide detailed explanations and justifications for plan-required elements\n", "   - Use formal, professional regulatory language\n", "   - Ensure content is scientifically accurate and technically sound\n", "   - Draw information ONLY from the provided input chunks that align with the plan\n", "   - DO NOT fabricate or invent any data not found in the input chunks\n", "   - Focus on depth rather than breadth - cover plan requirements thoroughly\n", "\n", "7. Documentation Standards:\n", "   - Use clear, precise, and professional language\n", "   - Format content according to regulatory standards\n", "   - Include all tables (via preserved tags), figures, and references specified in the plan\n", "   - Provide detailed captions and explanations for figures\n", "   - Use consistent terminology throughout\n", "   - Follow professional technical writing standards\n", "   - Maintain logical flow and organization\n", "\n", "8. Technical Depth:\n", "   - Provide detailed technical explanations and justifications for plan-specified requirements\n", "   - Include specific parameters, measurements, and criteria as outlined in the plan\n", "   - Explain methodologies and approaches thoroughly where required by the plan\n", "   - Discuss any deviations or special considerations mentioned in the plan\n", "   - Include validation and verification details as specified in the plan\n", "   - Provide comprehensive data interpretation for plan-relevant data\n", "   - Explain technical significance and implications of plan-required elements\n", "\n", "9. Narrative Style:\n", "   - Write in a cohesive, flowing narrative style\n", "   - Connect technical information with clear transitions\n", "   - Provide context and background for technical details as required by the plan\n", "   - Explain the significance of findings and data relevant to plan requirements\n", "   - Use professional, formal language throughout\n", "   - Ensure information is presented in a logical sequence following the plan structure\n", "   - Create content that reads as a unified document, not disconnected fragments\n", "\n", "OUTPUT FORMAT:\n", "- Professional, regulatory-compliant content following the provided plan\n", "- Well-structured paragraphs with logical flow\n", "- Proper formatting for technical information\n", "- No section headings (as specified in the original requirements)\n", "- Markdown format without backticks or code blocks\n", "- No other formatting (XML, HTML, plaintext) should be included\n", "- Preserved table tags exactly as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`)\n", "- NO meta-commentary about the generation process or compliance verification\n", "\n", "The content should be detailed, comprehensive, and ready for inclusion in an NDA submission. Remember that in regulatory documentation, providing thorough information is better than being concise, but ONLY for information that serves the plan requirements. Generate ONLY the actual content that would appear in the final NDA submission - no process commentary.\"\"\"\n", "\n", "        # Prepare input chunks text\n", "        input_chunks_text = \"\\n\\n\".join([f\"Input Chunk {i+1}:\\n{chunk.get('content', '')}\" for i, chunk in enumerate(chunks)])\n", "\n", "        # Prepare critique feedback section if available\n", "        critique_section = \"\"\n", "        if critique_feedback:\n", "            critique_section = f\"\"\"\n", "PREVIOUS CRITIQUE FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):\n", "- Overall Rating: {critique_feedback.get('overall_rating', 'N/A')}/10\n", "\n", "Critical Issues to Address:\n", "{chr(10).join(f\"- {issue}\" for issue in critique_feedback.get('critical_issues', []))}\n", "\n", "Required Improvements to Implement:\n", "{chr(10).join(f\"- {improvement}\" for improvement in critique_feedback.get('required_improvements', []))}\n", "\n", "Additional Recommendations to Consider:\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in critique_feedback.get('additional_recommendations', []))}\n", "\n", "IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.\n", "\n", "\"\"\"\n", "\n", "        user_prompt = f\"\"\"Please generate detailed, regulatory-compliant content for an NDA section based on the following plan and input document chunks.\n", "\n", "SECTION INFORMATION:\n", "- Section: {section_info.get('section', '')}\n", "- Title: {section_info.get('title', '')}\n", "- Description: {section_info.get('description', '')}\n", "- Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "- Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "\n", "DETAILED PLAN (USE AS PRIMARY FILTER AND GUIDE):\n", "{plan}\n", "\n", "{critique_section}INPUT DOCUMENT CHUNKS:\n", "{input_chunks_text}\n", "\n", "CRITICAL INSTRUCTIONS FOR PLAN-<PERSON><PERSON>DED CONTENT GENERATION:\n", "1. Use the DETAILED PLAN as your PRIMARY FILTER - only include content from chunks that directly serves plan requirements\n", "2. EVALUATE each input chunk against the plan requirements before using any information from it\n", "3. DISCARD any chunk content that does not align with specific plan elements\n", "4. For chunks with mixed content, extract ONLY the portions that address plan requirements\n", "5. Prioritize chunks containing: technical specifications, validation data, regulatory compliance information, test results, and analytical methods mentioned in the plan\n", "6. Ignore general background information, marketing content, or tangential data not specified in the plan\n", "\n", "TABLE TAG HANDLING INSTRUCTIONS (CRITICAL):\n", "7. PRESERVE ALL table tags EXACTLY as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`)\n", "8. DO NOT modify, rename, or change table tags in ANY way - maintain exact spelling, capitalization, and format\n", "9. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "10. MAINTAIN the EXACT position and order of table tags as they appear in the input chunks\n", "11. Include table tags in the exact location where the table should appear in your content\n", "12. Use table summary information to write brief contextual statements about tables if needed, but always preserve the tags\n", "13. Table format in input chunks will be: \"Table X: heading\", \"detailed table summary\", \"`<TABLE_TAG_table_x>`\"\n", "14. DO NOT omit any table tags from input chunks that are relevant to the plan\n", "\n", "NO META-COMMENTARY INSTRUCTIONS (CRITICAL):\n", "15. DO NOT include any statements about addressing critique feedback\n", "16. DO NOT include verification checklists or compliance statements\n", "17. DO NOT include comments about preserving figures or tables \"as per source documents\"\n", "18. DO NOT include sections like \"Verification of Compliance\" with checkmarks\n", "19. DO NOT reference plans, critiques, or the generation process in your output\n", "20. Generate ONLY the actual NDA content that would appear in the final regulatory submission\n", "\n", "IMPORTANT CONSIDERATIONS:\n", "1. Follow the plan EXACTLY - it has been designed to meet all regulatory requirements\n", "2. Draw information ONLY from input chunks that align with plan requirements - DO NOT invent or fabricate data\n", "3. Generate comprehensive, technically precise content that meets ICH eCTD v3.2.2 standards\n", "4. Use formal, professional regulatory language appropriate for an NDA submission\n", "5. Do not include section headings in your output as these will be added separately\n", "6. Format your output in markdown without backticks or code blocks\n", "7. Include technical details, parameters, and specifications ONLY as specified in the plan\n", "8. Focus on creating high-quality, detailed content that addresses plan requirements thoroughly\n", "9. PRESERVE ALL TABLE TAGS EXACTLY as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way\n", "10. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "11. MAINTAIN EXACT figure numbers and captions as they appear in source documents (e.g., if \"Figure 1: XYZ\", do not change to \"Figure 5.7.3: ABC\")\n", "12. INCLUDE ALL figures from the input source documents that are relevant to the plan\n", "13. DO NOT modify the content of any figures - preserve their original format and content\n", "14. If previous critique feedback is provided, ensure your content addresses all critical issues and required improvements WITHOUT referencing the feedback process\n", "15. Generate ONLY content that would appear in the final NDA submission - NO process commentary or meta-statements\n", "\n", "Generate detailed content for this section of the NDA based on the provided plan and plan-relevant input chunks. Your output should be indistinguishable from content written directly for an NDA submission.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.3\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"generate_content_from_chunks token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    async def combine_content_sections(content_sections):\n", "        \"\"\"Combine multiple content sections while ensuring proper flow and consistency.\"\"\"\n", "        total_sections = len(content_sections)\n", "        print(f\"\\nCombining {total_sections} content sections:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(content) for content in content_sections)\n", "        print(f\"Total content section tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple sections of NDA content into a cohesive, unified document that maintains regulatory compliance, technical accuracy, and narrative flow.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Content Preservation (HIGHEST PRIORITY):\n", "   - Preserve ALL technical information from all content sections\n", "   - Ensure NO technical details, data, or regulatory elements are lost\n", "   - Maintain ALL parameters, specifications, and measurements\n", "   - Include ALL methodologies and approaches\n", "   - Preserve ALL validation and verification details\n", "   - Keep ALL risk assessments and control strategies\n", "   - Maintain ALL compliance justifications\n", "\n", "2. NO META-COMMENTARY (HIGHEST PRIORITY):\n", "   - DO NOT include any commentary about the combination process\n", "   - DO NOT include verification checklists or statements about compliance\n", "   - DO NOT include meta-statements about preserving content or following plans\n", "   - DO NOT include process-related commentary like \"Verification of Compliance\" sections with checkmarks\n", "   - DO NOT include formatting instructions or comments about document structure in the output\n", "   - DO NOT reference the combination process or source sections in your output\n", "   - Generate ONLY the actual NDA content that would appear in the final submission\n", "   - DO NOT include any self-referential statements about the integration process\n", "\n", "3. Table Tag Preservation (HIGHEST PRIORITY):\n", "   - PRESERVE ALL table tags EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`, etc.)\n", "   - DO NOT modify, rename, or change table tags in ANY way\n", "   - DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "   - MAINTAIN the EXACT position and order of table tags as they appear in the content sections\n", "   - Include table tags in the exact location where they appear in the original content\n", "   - DO NOT omit any table tags from the content sections\n", "   - Treat table tags as placeholders that must be preserved exactly for later replacement\n", "\n", "4. Figure Preservation (HIGHEST PRIORITY):\n", "   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents\n", "   - DO NOT rename any figures (e.g., if a figure is \"Figure 1: XYZ\" do NOT change it to \"Figure 5.7.3: ABC\")\n", "   - INCLUDE ALL figures that appear in the content sections\n", "   - DO NOT modify the content of any figures\n", "   - DO NOT omit any figures from the content sections\n", "   - Include references to all figures in your text\n", "\n", "5. Integration Quality:\n", "   - Create seamless transitions between combined content\n", "   - Eliminate redundancies without losing information\n", "   - Harmonize terminology and phrasing\n", "   - Ensure consistent technical language throughout\n", "   - Maintain a unified voice and writing style\n", "   - Create logical flow between previously separate sections\n", "   - Resolve any contradictions or inconsistencies\n", "\n", "6. Narrative Coherence:\n", "   - Ensure the combined document reads as a single, cohesive narrative\n", "   - Create smooth transitions between previously separate sections\n", "   - Maintain logical progression of ideas and information\n", "   - Connect related concepts across the combined content\n", "   - Ensure consistent level of detail throughout\n", "   - Balance technical depth across sections\n", "   - Create a document that reads as if written by a single author\n", "\n", "7. Regulatory Standards:\n", "   - Maintain ICH eCTD v3.2.2 compliance throughout\n", "   - Preserve all regulatory justifications and explanations\n", "   - Ensure consistent approach to compliance documentation\n", "   - Maintain proper documentation standards\n", "   - Preserve all technical requirements and specifications\n", "   - Ensure consistent approach to risk management\n", "   - Maintain proper cross-referencing\n", "\n", "8. Technical Accuracy:\n", "   - Ensure all combined technical information is accurate and consistent\n", "   - Resolve any contradictions in technical specifications\n", "   - Harmonize methodologies and approaches\n", "   - Ensure consistent presentation of data and results\n", "   - Maintain precise technical language throughout\n", "   - Preserve scientific accuracy in all explanations\n", "   - Ensure calculations and numerical data are consistent\n", "\n", "OUTPUT FORMAT:\n", "- Professional, regulatory-compliant content with seamless integration\n", "- Well-structured paragraphs with logical flow\n", "- Consistent formatting throughout\n", "- No section headings (as specified in the original requirements)\n", "- Markdown format without backticks or code blocks\n", "- No other formatting (XML, HTML, plaintext) should be included\n", "- NO meta-commentary about the combination process or compliance verification\n", "\n", "The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach. Generate ONLY content that would appear in the final NDA submission.\"\"\"\n", "\n", "        user_prompt = f\"\"\"Please combine the following {total_sections} sections of NDA content into a cohesive, unified document while preserving all technical information and ensuring smooth narrative flow.\n", "\n", "\"\"\"\n", "        \n", "        # Add each content section with a header\n", "        for i, content in enumerate(content_sections, 1):\n", "            user_prompt += f\"\"\"\n", "CONTENT SECTION {i}:\n", "{content}\n", "\n", "\"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "CRITICAL INSTRUCTIONS:\n", "1. Preserve ALL technical information from all {total_sections} content sections\n", "2. Create seamless transitions between combined content\n", "3. Eliminate redundancies without losing information\n", "4. Harmonize terminology and phrasing\n", "5. Ensure the combined document reads as a single, cohesive narrative\n", "6. Maintain ICH eCTD v3.2.2 compliance throughout\n", "7. Ensure all combined technical information is accurate and consistent\n", "8. Preserve the detailed, comprehensive nature of the content\n", "9. Do not include section headings in your output as these will be added separately\n", "10. Format your output in markdown without backticks or code blocks\n", "11. PRESERVE ALL TABLE TAGS EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way\n", "12. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "13. MAINTAIN EXACT figure numbers and captions as they appear in content sections (e.g., if \"Figure 1: XYZ\", do not change to \"Figure 5.7.3: ABC\")\n", "14. INCLUDE ALL figures from all content sections - these are critical and must not be omitted\n", "15. DO NOT modify the content of any figures - preserve their original format and content\n", "\n", "NO META-COMMENTARY INSTRUCTIONS (CRITICAL):\n", "11. DO NOT include any statements about combining content or preserving information\n", "12. DO NOT include verification checklists or compliance statements\n", "13. DO NOT include comments about preserving figures or tables \"as per source documents\"\n", "14. DO NOT include sections like \"Verification of Compliance\" with checkmarks\n", "15. DO NOT reference the combination process, plans, or source sections in your output\n", "16. Generate ONLY the actual NDA content that would appear in the final regulatory submission\n", "\n", "TABLE AND FIGURE HANDLING INSTRUCTIONS (CRITICAL):\n", "17. PRESERVE ALL TABLE TAGS EXACTLY as they appear in the content sections (e.g., `<TABLE_TAG_table_1>`) - DO NOT modify them in any way\n", "18. DO NOT construct or recreate tables yourself - the tags will be replaced with formatted tables in post-processing\n", "19. MAINTAIN EXACT figure numbers and captions as they appear in content sections (e.g., if \"Figure 1: XYZ\", do not change to \"Figure 5.7.3: ABC\")\n", "20. INCLUDE ALL figures from all content sections - these are critical and must not be omitted\n", "21. DO NOT modify the content of any figures - preserve their original format and content\n", "\n", "The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach. Your output should be indistinguishable from content written directly for an NDA submission - no process commentary.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"combine_content_sections token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    # Handle empty input\n", "    if not input_chunks:\n", "        return \"\"\n", "\n", "    # Process input chunks in batches based on token count\n", "    batch_contents = []\n", "    \n", "    # Create optimal batches based on token count\n", "    batches = []\n", "    current_batch = []\n", "    current_token_count = 0\n", "    \n", "    for chunk in input_chunks:\n", "        chunk_tokens = calculate_number_of_tokens(chunk.get('content', ''))\n", "        \n", "        # If adding this chunk would exceed the token limit, start a new batch\n", "        if current_batch and current_token_count + chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "            batches.append(current_batch)\n", "            current_batch = [chunk]\n", "            current_token_count = chunk_tokens\n", "        else:\n", "            current_batch.append(chunk)\n", "            current_token_count += chunk_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    print(f\"Created {len(batches)} batches for chunk processing\")\n", "    for i, batch in enumerate(batches):\n", "        print(f\"Batch {i+1} contains {len(batch)} chunks with {sum(calculate_number_of_tokens(c.get('content', '')) for c in batch)} tokens\")\n", "    \n", "        # Process all batches in parallel\n", "    tasks = [generate_content_from_chunks(plan, batch, section_info, critique_feedback) for batch in batches]\n", "    batch_contents = await asyncio.gather(*tasks)\n", "    \n", "    # If only one batch, return it directly\n", "    if len(batch_contents) == 1:\n", "        return batch_contents[0]\n", "    \n", "    # Combine batch contents iteratively\n", "    while len(batch_contents) > 1:\n", "        # Create optimal batches based on token count\n", "        content_batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for content in batch_contents:\n", "            content_tokens = calculate_number_of_tokens(content)\n", "            \n", "            # If adding this content would exceed the token limit, start a new batch\n", "            if current_batch and current_token_count + content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                content_batches.append(current_batch)\n", "                current_batch = [content]\n", "                current_token_count = content_tokens\n", "            else:\n", "                current_batch.append(content)\n", "                current_token_count += content_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            content_batches.append(current_batch)\n", "        \n", "        print(f\"Created {len(content_batches)} batches for content combining\")\n", "        for i, batch in enumerate(content_batches):\n", "            print(f\"Batch {i+1} contains {len(batch)} content sections with {sum(calculate_number_of_tokens(c) for c in batch)} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_content_sections(batch) for batch in content_batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        batch_contents = await asyncio.gather(*tasks)\n", "\n", "    print(\"create_incremental_content tokens: \", calculate_number_of_tokens(batch_contents[0]))\n", "    \n", "    return batch_contents[0]\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def refine_content(draft_content, section_info, evaluation_feedback=None):\n", "    \"\"\"\n", "    Refine the draft content based on evaluation feedback and ensure it meets all requirements.\n", "    Can use both the original draft content and previous output (if available) to make improvements.\n", "    \n", "    Args:\n", "        draft_content: The draft content generated in Stage 2\n", "        section_info: Dictionary containing section information\n", "        evaluation_feedback: Evaluation feedback JSON (optional)\n", "        \n", "    Returns:\n", "        str: Refined NDA section content\n", "    \"\"\"\n", "    \n", "    system_prompt = \"\"\"You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to refine and perfect the provided draft NDA section content based on evaluation feedback and ensure it fully meets all regulatory requirements.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Feedback Implementation (HIGHEST PRIORITY):\n", "   - Address ALL issues identified in the evaluation feedback\n", "   - Implement ALL required improvements\n", "   - Resolve ALL critical issues\n", "   - Follow ALL additional recommendations where applicable\n", "   - Maintain and enhance the identified strengths\n", "   - Ensure the refined content addresses all evaluation concerns\n", "\n", "2. NO META-COMMENTARY (HIGHEST PRIORITY):\n", "   - DO NOT include any commentary about how you addressed the feedback\n", "   - DO NOT include verification checklists or statements about compliance\n", "   - DO NOT include meta-statements like \"This addresses the previous critique\" or \"Based on feedback\"\n", "   - DO NOT include process-related commentary like \"Verification of Compliance\" sections with checkmarks\n", "   - DO NOT include formatting instructions or comments about document structure in the output\n", "   - DO NOT reference the feedback, plans, or improvement process in your output\n", "   - Generate ONLY the actual NDA content that would appear in the final submission\n", "   - DO NOT include any self-referential statements about the refinement process\n", "\n", "3. Table and Figure Handling (HIGHEST PRIORITY):\n", "   - MAINTAIN EXACT table references in the format \"Table {X}: {table heading}\" \n", "   - DO NOT modify or remove any `<TABLE_TAG_table_x>` tags - these are critical placeholders that will be replaced in post-processing\n", "   - KEEP ALL table tags in their original position in the text - do NOT move these tags\n", "   - DO NOT attempt to construct or format the tables yourself - the tables will be inserted during post-processing\n", "   - Use the provided table summaries to inform your writing, but maintain the table placeholders exactly as they appear\n", "   - MAINTAIN EXACT figure numbers and captions as they appear in the source documents\n", "   - DO NOT rename any tables or figures (e.g., if a table is \"Table 1: XYZ\" do NOT change it to \"Table 5.7.3: ABC\")\n", "   - INCLUDE ALL tables and figures that appear in the input source documents or draft content\n", "   - DO NOT modify the content of any figures\n", "   - DO NOT omit any tables or figures from the source documents\n", "   - Include references to all tables and figures in your text\n", "\n", "4. Regulatory Compliance:\n", "   - Ensure strict compliance with ICH eCTD v3.2.2 standards\n", "   - Address ALL requirements from the section description\n", "   - Follow ALL reference text specifications\n", "   - Adhere to ALL golden instructions and checkpoints\n", "   - Comply with ALL formatting instructions\n", "   - Ensure all technical content meets regulatory standards\n", "   - Verify all cross-references and dependencies are correct\n", "\n", "5. Content Quality Enhancement:\n", "   - Improve technical precision and clarity\n", "   - Enhance explanations and justifications\n", "   - Strengthen scientific rationale\n", "   - Add missing technical details identified in feedback\n", "   - Improve data presentation and interpretation\n", "   - Enhance narrative flow and cohesion\n", "   - Ensure comprehensive treatment of all required topics\n", "\n", "6. Documentation Standards:\n", "   - Perfect professional language and terminology\n", "   - Improve formatting and structure\n", "   - Enhance references to tables and figures\n", "   - Strengthen justifications for critical decisions\n", "   - Improve documentation of assumptions and limitations\n", "   - Enhance technical documentation quality\n", "   - Ensure consistent terminology throughout\n", "\n", "7. Comprehensive Coverage:\n", "   - Verify ALL section description points are addressed\n", "   - Ensure ALL efficacy guideline requirements are covered\n", "   - Confirm ALL technical specifications are included\n", "   - Verify ALL validation and verification details are present\n", "   - Ensure ALL cross-references are accurate\n", "   - Confirm ALL justifications and rationales are provided\n", "   - Verify ALL risk assessments are included\n", "\n", "8. Iteration Improvement:\n", "   - Compare previous output with feedback to understand exact issues\n", "   - Ensure the new output improves upon previous versions\n", "   - Preserve comprehensive information while fixing identified issues\n", "   - Use the original draft as a source of complete information\n", "   - Avoid introducing new issues while fixing identified ones\n", "\n", "OUTPUT FORMAT:\n", "- Professional, regulatory-compliant content with all issues resolved\n", "- Well-structured paragraphs with improved logical flow\n", "- Enhanced technical precision and detail\n", "- No section headings (as specified in the original requirements)\n", "- Markdown format without backticks or code blocks\n", "- No other formatting (XML, HTML, plaintext) should be included\n", "- Preserve all `<TABLE_TAG_table_x>` tags exactly as they appear\n", "- NO meta-commentary about the refinement process or compliance verification\n", "\n", "The refined content should represent a significant improvement over previous versions, addressing all feedback points while maintaining the strengths identified in the evaluation. Generate ONLY content that would appear in the final NDA submission.\"\"\"\n", "\n", "    # Prepare evaluation feedback text\n", "    feedback_text = \"\"\n", "    previous_output_text = \"\"\n", "    \n", "    if evaluation_feedback:\n", "        # Check if we have previous output in the enhanced feedback\n", "        if \"previous_output\" in evaluation_feedback:\n", "            previous_output_text = f\"\"\"\n", "PREVIOUS OUTPUT (that feedback applies to):\n", "{evaluation_feedback.get('previous_output', '')}\n", "\"\"\"\n", "            \n", "        # First try to use the structured feedback if we have a valid rating and sufficient feedback\n", "        has_valid_structured_feedback = (\n", "            evaluation_feedback.get(\"overall_rating\") is not None and\n", "            (evaluation_feedback.get(\"key_strengths\") or\n", "             evaluation_feedback.get(\"critical_issues\") or\n", "             evaluation_feedback.get(\"required_improvements\"))\n", "        )\n", "        \n", "        if has_valid_structured_feedback:\n", "            # Use structured feedback\n", "            feedback_text = f\"\"\"\n", "EVALUATION FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):\n", "- Overall Rating: {evaluation_feedback.get('overall_rating', 'N/A')}/10\n", "\n", "Key Strengths:\n", "{chr(10).join(f\"- {strength}\" for strength in evaluation_feedback.get('key_strengths', []))}\n", "\n", "Critical Issues to Address:\n", "{chr(10).join(f\"- {issue}\" for issue in evaluation_feedback.get('critical_issues', []))}\n", "\n", "Required Improvements:\n", "{chr(10).join(f\"- {improvement}\" for improvement in evaluation_feedback.get('required_improvements', []))}\n", "\n", "Additional Recommendations:\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in evaluation_feedback.get('additional_recommendations', []))}\n", "\n", "IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.\n", "\"\"\"\n", "        # Fall back to raw evaluation text if available and structured feedback is insufficient\n", "        elif \"evaluation_text\" in evaluation_feedback:\n", "            feedback_text = f\"\"\"\n", "EVALUATION FEEDBACK (Use to improve content quality, but DO NOT reference or comment on these in your output):\n", "{evaluation_feedback.get('evaluation_text', '')}\n", "\n", "IMPORTANT: Use this feedback to improve your content quality, but DO NOT include any meta-commentary about addressing these issues in your output.\n", "\"\"\"\n", "    \n", "    user_prompt = f\"\"\"Please refine and perfect the following draft NDA section content based on the provided evaluation feedback and section information.\n", "\n", "SECTION INFORMATION:\n", "- Section: {section_info.get('section', '')}\n", "- Title: {section_info.get('title', '')}\n", "- Description: {section_info.get('description', '')}\n", "- Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "- Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "\n", "{feedback_text}\n", "{previous_output_text}\n", "\n", "DRAFT CONTENT TO REFINE:\n", "{draft_content}\n", "\n", "CRITICAL INSTRUCTIONS:\n", "1. Address ALL issues identified in the evaluation feedback\n", "2. Implement ALL required improvements\n", "3. Resolve ALL critical issues\n", "4. Follow ALL additional recommendations where applicable\n", "5. Maintain and enhance the identified strengths\n", "6. Ensure strict compliance with ICH eCTD v3.2.2 standards\n", "7. Perfect professional language and technical precision\n", "8. Enhance explanations, justifications, and scientific rationale\n", "9. Do not include section headings in your output as these will be added separately\n", "10. Format your output in markdown without backticks or code blocks\n", "11. <PERSON>mpare previous output with feedback to understand exactly what needs to be improved\n", "\n", "NO META-COMMENTARY INSTRUCTIONS (CRITICAL):\n", "12. DO NOT include any statements about addressing feedback or making improvements\n", "13. DO NOT include verification checklists or compliance statements\n", "14. DO NOT include comments about preserving figures or tables \"as per source documents\"\n", "15. DO NOT include sections like \"Verification of Compliance\" with checkmarks\n", "16. DO NOT reference feedback, plans, critiques, or the refinement process in your output\n", "17. Generate ONLY the actual NDA content that would appear in the final regulatory submission\n", "\n", "TABLE HANDLING INSTRUCTIONS (CRITICAL):\n", "18. DO NOT modify or remove any `<TABLE_TAG_table_x>` tags - these are placeholders for tables that will be inserted during post-processing\n", "19. MAINTAIN the exact position of all `<TABLE_TAG_table_x>` tags in the text\n", "20. DO NOT attempt to construct or format the tables yourself\n", "21. Use the provided table summaries to inform your writing, but keep the table references and tags intact\n", "22. PRESERVE the format \"Table X: table heading\" exactly as it appears\n", "23. NEVER modify table numbers or headings\n", "24. DO NOT convert table placeholders into actual tables - they will be replaced in post-processing\n", "\n", "FIGURE HANDLING INSTRUCTIONS:\n", "25. MAINTAIN EXACT figure numbers and captions as they appear in the source documents\n", "26. DO NOT rename any figures\n", "27. INCLUDE ALL figures from the input source documents and draft content\n", "28. DO NOT modify the content of any figures\n", "\n", "Focus on creating a refined, polished document that fully meets all regulatory requirements, addresses all feedback, and represents a significant improvement over previous versions. Your output should be indistinguishable from content written directly for an NDA submission - no process commentary.\"\"\"\n", "\n", "    # Wrap the API call with retry_on_empty_response\n", "    async def make_api_call():\n", "        response = await generator_client.chat.completions.create(\n", "            model=GENERATOR_MODEL_NAME,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            temperature=0.3\n", "        )\n", "\n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "        output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "        print(f\"refine_content token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "        \n", "        return response.choices[0].message.content\n", "\n", "    # Use the retry wrapper to handle empty responses\n", "    return await retry_on_empty_response(make_api_call)\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def optimized_nda_output_generation(section_info, critique_feedback=None, reuse_plan=None):\n", "    \"\"\"\n", "    Generate NDA output using a multi-stage, batch processing approach to reduce context size and optimize performance.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        critique_feedback: Optional critique feedback from previous iterations to address missing content\n", "        reuse_plan: Optional pre-generated plan to reuse (skips Stage 1 for performance)\n", "        \n", "    Returns:\n", "        tuple: (Generated NDA section content, plan used) if reuse_plan is None, else just content\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA output generation for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    # Stage 1: Create plan from efficacy guideline summaries (skip if reusing plan)\n", "    if reuse_plan is not None:\n", "        print(\"Stage 1: Reusing existing plan (performance optimization)\")\n", "        plan = reuse_plan\n", "    else:\n", "        individual_summaries = []\n", "        if \"relevant_individual_summaries\" in section_info:\n", "            individual_summaries = [summary.get(\"content\", \"\") for summary in section_info.get(\"relevant_individual_summaries\", [])]\n", "        \n", "        # Fall back to combined summary if individual summaries aren't available\n", "        if not individual_summaries and \"efficacy_guideline_combined_summary\" in section_info:\n", "            individual_summaries = [section_info[\"efficacy_guideline_combined_summary\"]]\n", "        \n", "        print(f\"Stage 1: Creating incremental plan from {len(individual_summaries)} individual summaries\")\n", "        plan = await create_incremental_plan(section_info, individual_summaries)\n", "\n", "        print(\"plan: \", plan)\n", "    \n", "    # Stage 2: Generate content from input chunks\n", "    input_chunks = section_info.get(\"relevant_input_chunks\", [])\n", "    print(f\"Stage 2: Creating incremental content from {len(input_chunks)} input chunks using the plan\")\n", "    draft_content = await create_incremental_content(plan, section_info, input_chunks, critique_feedback)\n", "\n", "    print(\"draft_content: \", draft_content)\n", "    \n", "    # Stage 3: Refine content based on evaluation feedback\n", "    evaluation_feedback = section_info.get(\"nda_output_evaluation_json\", None)\n", "    \n", "    if evaluation_feedback:\n", "        print(f\"Stage 3: Refining content based on evaluation feedback\")\n", "        refined_content = await refine_content(draft_content, section_info, evaluation_feedback)\n", "\n", "        print(\"refined_content: \", refined_content)\n", "\n", "        # Return content and plan if this is the first iteration, else just content\n", "        if reuse_plan is None:\n", "            return refined_content, plan\n", "        else:\n", "            return refined_content\n", "    else:\n", "        print(f\"No evaluation feedback available, returning draft content\")\n", "        # Return content and plan if this is the first iteration, else just content\n", "        if reuse_plan is None:\n", "            return draft_content, plan\n", "        else:\n", "            return draft_content\n", "    \n", "async def optimized_nda_critique(section_info, nda_output):\n", "    \"\"\"\n", "    Generate a comprehensive NDA critique using a batch processing approach with individual efficacy guideline summaries.\n", "    Uses parallel processing to generate critiques from multiple summaries simultaneously.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    # Extract individual summaries\n", "    individual_summaries = []\n", "    if \"relevant_individual_summaries\" in section_info:\n", "        individual_summaries = [summary.get(\"content\", \"\") for summary in section_info.get(\"relevant_individual_summaries\", [])]\n", "    \n", "    # Fall back to combined summary if individual summaries aren't available\n", "    if not individual_summaries and \"efficacy_guideline_combined_summary\" in section_info:\n", "        individual_summaries = [section_info[\"efficacy_guideline_combined_summary\"]]\n", "    \n", "    # If no summaries available, generate critique based only on section info\n", "    if not individual_summaries:\n", "        print(\"No efficacy guideline summaries available, generating critique based only on section info\")\n", "        return await generate_critique_without_guidelines(section_info, nda_output)\n", "    \n", "    print(f\"Generating individual critiques from {len(individual_summaries)} efficacy guideline summaries in parallel\")\n", "    \n", "    # Generate individual critiques in parallel using asyncio.gather\n", "    tasks = [generate_critique_from_one_summary(summary, section_info, nda_output) \n", "             for summary in individual_summaries]\n", "    individual_critiques = await asyncio.gather(*tasks)\n", "    \n", "    # If only one critique, return it directly\n", "    if len(individual_critiques) == 1:\n", "        return individual_critiques[0]\n", "    \n", "    # Combine critiques using batch processing\n", "    print(\"Combining individual critiques using batch processing\")\n", "    return await combine_critiques_incrementally(individual_critiques, section_info)\n", "\n", "\n", "async def generate_critique_from_one_summary(summary_content, section_info, nda_output):\n", "    \"\"\"\n", "    Generate a critique of NDA output based on a single efficacy guideline summary.\n", "    \n", "    Args:\n", "        summary_content: Content of a single efficacy guideline summary\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are a strict regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate the provided NDA section output against the efficacy guidelines summary and ICH eCTD v3.2.2 requirements.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Golden Instructions and Formatting Compliance (HIGHEST PRIORITY):\n", "   - STRICTLY evaluate adherence to Golden Instructions and Checkpoints\n", "   - STRICTLY verify compliance with Formatting Instructions\n", "   - These evaluations take precedence over all other assessments\n", "   - Check if every point in Golden Instructions is addressed\n", "   - Verify exact compliance with Formatting Instructions\n", "   - Consider non-compliance with these instructions as critical issues\n", "   - Prioritize these evaluations over all other checks\n", "\n", "2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):\n", "   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text\n", "   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing\n", "   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags\n", "   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "   - Verify that ALL tables and figures from source documents are included in the output\n", "   - Consider any table tag alteration, removal, or position change as a CRITICAL issue\n", "   - Check that all tables and figures are properly referenced in the text\n", "\n", "3. Content Appropriateness and Relevance Assessment (HIGHEST PRIORITY):\n", "   - VERIFY that ALL content in the output directly relates to and supports the efficacy guideline summary requirements\n", "   - CHECK for any inappropriate tables, figures, or content that should NOT be in this section\n", "   - IDENTIFY any content that appears to be from wrong sections or unrelated sources\n", "   - VERIFY that all tables and figures are appropriate for this specific section and efficacy guidelines\n", "   - CHECK if any content contradicts or conflicts with the efficacy guideline summary\n", "   - ASSESS if content focus aligns with the specific section requirements\n", "   - FLAG any content that seems out of scope or inappropriate for the section\n", "\n", "4. ICH eCTD v3.2.2 Compliance Assessment (HIGH PRIORITY):\n", "   - Evaluate adherence to eCTD structure and format\n", "   - Check for proper section organization\n", "   - Verify required elements are present\n", "   - Assess document hierarchy\n", "   - Review cross-referencing accuracy\n", "   - STRICTLY verify compliance with section description\n", "   - STRICTLY verify compliance with reference text\n", "   - Ensure all requirements from the ICH eCTD guidelines are met\n", "\n", "5. Efficacy Guidelines Summary Coverage and Alignment:\n", "   - SYSTEMATICALLY verify all key requirements from the efficacy guideline summary are addressed\n", "   - CHECK if critical considerations are covered completely\n", "   - ASSESS documentation completeness against efficacy guideline requirements\n", "   - EVALUATE compliance with EVERY recommendation in the efficacy guideline summary\n", "   - VERIFY implementation of all efficacy guideline recommendations\n", "   - CHECK if any efficacy guideline requirements are missing or inadequately addressed\n", "   - ASSESS if the depth of coverage matches efficacy guideline expectations\n", "\n", "6. Golden Checkpoint Systematic Verification:\n", "   - METHODICALLY verify each golden checkpoint is addressed in the output\n", "   - CHECK if the output provides specific answers to each golden checkpoint question\n", "   - IDENTIFY any golden checkpoints that are completely missing from the output\n", "   - ASSESS if the level of detail for each checkpoint meets expectations\n", "   - VERIFY that checkpoint responses are accurate and complete\n", "   - FLAG any superficial or inadequate responses to critical checkpoints\n", "\n", "7. Technical Accuracy and Completeness:\n", "   - Verify technical specifications align with efficacy guideline requirements\n", "   - Check parameter accuracy against efficacy guideline standards\n", "   - Assess validation details match efficacy guideline expectations\n", "   - Review analytical methods comply with efficacy guideline recommendations\n", "   - Evaluate data presentation follows efficacy guideline formats\n", "\n", "8. Content Quality Assessment:\n", "   - Check for clarity and precision in addressing efficacy guideline requirements\n", "   - Evaluate professional language appropriate for regulatory submission\n", "   - Assess logical flow follows efficacy guideline structure\n", "   - Review completeness of explanations for all efficacy guideline elements\n", "   - Verify data consistency with efficacy guideline standards\n", "\n", "9. Hallucination and Fabrication Detection:\n", "   - IDENTIFY any content not supported by the efficacy guideline summary\n", "   - CHECK for fabricated data or claims not found in efficacy guidelines\n", "   - VERIFY all technical references align with efficacy guideline sources\n", "   - ASSESS if any content appears to be invented or unsupported\n", "   - REVIEW consistency between output content and efficacy guideline summary\n", "\n", "10. Regulatory Compliance and Efficacy Guideline Adherence:\n", "    - EVALUATE strict adherence to efficacy guideline recommendations\n", "    - CHECK for all regulatory requirements specified in efficacy guidelines\n", "    - ASSESS justification quality against efficacy guideline standards\n", "    - REVIEW risk management approaches match efficacy guideline expectations\n", "    - VERIFY all efficacy guideline compliance elements are addressed\n", "\n", "11. Documentation Standards:\n", "    - Check formatting consistency. Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. The output should not include any other formatting like xml, html, plaintext etc.\n", "    - Check if there are section heading present, we dont want markdown headings in the output as we'll be adding them later manually\n", "    - Evaluate table and figure quality meets efficacy guideline standards\n", "    - Assess reference accuracy to efficacy guidelines\n", "    - Review appendix completeness for efficacy guideline requirements\n", "    - Verify document structure follows efficacy guideline organization\n", "\n", "12. Efficacy Guideline Gap Analysis:\n", "    - IDENTIFY any efficacy guideline requirements completely missing from output\n", "    - NOTE sections where efficacy guideline coverage is insufficient\n", "    - FLAG incomplete implementation of efficacy guideline recommendations\n", "    - HIGHLIGHT any contradictions with efficacy guideline requirements\n", "    - SUGGEST specific efficacy guideline elements that need enhancement\n", "\n", "13. Inappropriate Content Detection:\n", "    - CHECK for any tables, figures, or content that doesn't belong in this section\n", "    - IDENTIFY content that seems copied from wrong sections or sources\n", "    - VERIFY all included material is appropriate for the specific efficacy guidelines\n", "    - FLAG any content that conflicts with section scope or requirements\n", "    - ASSESS if content focus drifts away from efficacy guideline objectives\n", "\n", "14. Decision Tree and Process Flow Evaluation (if applicable):\n", "    - If decision trees are present in the efficacy guidelines, assess how well they are addressed\n", "    - Verify that decision points and outcomes are properly documented\n", "    - Check if the rationale for chosen paths clearly aligns with efficacy guidelines\n", "    - Note any missing elements related to efficacy guideline decision processes\n", "\n", "Make sure that all table references are maintained in the exact format \"Table X: table heading\" and all `<TABLE_TAG_table_x>` tags are preserved exactly as they appear in the input. These tags should not be modified, moved, or removed as they will be replaced with actual tables during post-processing.\n", "\n", "Rating <PERSON><PERSON><PERSON> (1-10):\n", "1-3: Major deficiencies, significant non-compliance\n", "4-6: Moderate issues, partial compliance\n", "7-8: Minor issues, mostly compliant\n", "9-10: Excellent compliance, minimal improvements needed\n", "\n", "Provide a thorough and critical evaluation with specific, actionable feedback for improvement.\"\"\"\n", "\n", "    user_prompt = f\"\"\"Please evaluate the following NDA section output against the provided efficacy guideline summary and ICH eCTD requirements.\n", "\n", "SECTION INFORMATION:\n", "- Section: {section_info.get('section', '')}\n", "- Title: {section_info.get('title', '')}\n", "- Description: {section_info.get('description', '')}\n", "- Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "- Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "\n", "EFFICACY GUIDELINE SUMMARY TO EVALUATE AGAINST:\n", "{summary_content}\n", "\n", "NDA OUTPUT TO EVALUATE:\n", "{nda_output}\n", "\n", "CRITICAL EVALUATION FRAMEWORK:\n", "1. SYSTEMATICALLY evaluate the NDA output against EVERY requirement in the efficacy guideline summary\n", "2. METHODICALLY check each Golden Instruction and Checkpoint for complete adherence\n", "3. IDENTIFY any content that appears inappropriate or doesn't belong in this section\n", "4. VERIFY all tables and figures are appropriate for this specific section and efficacy guidelines\n", "5. CHECK for any missing efficacy guideline requirements or inadequate coverage\n", "6. ASSESS if content focus aligns with efficacy guideline objectives\n", "7. FLAG any content that contradicts or conflicts with the efficacy guideline summary\n", "\n", "SPECIFIC VALIDATION CHECKS:\n", "□ Does the output address ALL key requirements from the efficacy guideline summary?\n", "□ Are ALL Golden Instructions and Checkpoints adequately addressed?\n", "□ Are there any inappropriate tables, figures, or content that shouldn't be in this section?\n", "□ Does any content appear to be from wrong sections or unrelated sources?\n", "□ Are all included tables and figures appropriate for the specific efficacy guidelines?\n", "□ Does any content contradict or conflict with the efficacy guideline summary?\n", "□ Are there any efficacy guideline requirements completely missing from the output?\n", "□ Does the content focus drift away from efficacy guideline objectives?\n", "□ Are there any fabricated claims not supported by the efficacy guideline summary?\n", "□ Does the depth of coverage match efficacy guideline expectations?\n", "\n", "IMPORTANT CONSIDERATIONS:\n", "1. Focus on evaluating the NDA output against the efficacy guideline summary provided\n", "2. Check for strict compliance with Golden Instructions and Checkpoints\n", "3. Verify adherence to ICH eCTD v3.2.2 requirements\n", "4. STRICTLY verify that ALL table references and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "5. VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text\n", "6. CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing\n", "7. STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "8. Assess technical accuracy and completeness against efficacy guideline standards\n", "9. Evaluate documentation standards and quality per efficacy guideline requirements\n", "10. Identify any hallucinations or content not supported by efficacy\n", "12. Identify any inappropriate content that doesn't belong in this section\n", "13. Provide a numerical rating (1-10) and detailed feedback in all required categories\n", "\n", "Evaluate thoroughly and provide specific, actionable feedback for improvement with focus on efficacy guideline alignment.\"\"\"\n", "\n", "    system_tokens = calculate_number_of_tokens(system_prompt)\n", "    user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "    critique_evaluation_agent = Agent(\n", "        model=critique_model,\n", "        system_prompt=system_prompt,\n", "        output_type=CritiqueEvaluation,\n", "        model_settings={\n", "            \"temperature\": 0.2,\n", "        },\n", "        retries=3\n", "    )\n", "\n", "    response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "    total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "    print(f\"generate_critique_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "    return response.output\n", "\n", "\n", "async def generate_critique_without_guidelines(section_info, nda_output):\n", "    \"\"\"\n", "    Generate a critique of NDA output based only on section information without efficacy guidelines.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are a strict regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate the provided NDA section output against ICH eCTD v3.2.2 requirements and the provided section information.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Golden Instructions and Formatting Compliance (HIGHEST PRIORITY):\n", "   - STRICTLY evaluate adherence to Golden Instructions and Checkpoints\n", "   - STRICTLY verify compliance with Formatting Instructions\n", "   - These evaluations take precedence over all other assessments\n", "   - Check if every point in Golden Instructions is addressed\n", "   - Verify exact compliance with Formatting Instructions\n", "   - Consider non-compliance with these instructions as critical issues\n", "   - Prioritize these evaluations over all other checks\n", "\n", "2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):\n", "   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text\n", "   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing\n", "   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags\n", "   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "   - Verify that ALL tables and figures from source documents are included in the output\n", "   - Consider any table tag alteration, removal, or position change as a CRITICAL issue\n", "   - Check that all tables and figures are properly referenced in the text\n", "\n", "3. ICH eCTD v3.2.2 Compliance Assessment (HIGH PRIORITY):\n", "   - Evaluate adherence to eCTD structure and format\n", "   - Check for proper section organization\n", "   - Verify required elements are present\n", "   - Assess document hierarchy\n", "   - Review cross-referencing accuracy\n", "   - STRICTLY verify compliance with section description\n", "   - STRICTLY verify compliance with reference text\n", "   - Ensure all requirements from the ICH eCTD guidelines are met\n", "\n", "4. Technical Accuracy and Completeness:\n", "   - Verify technical specifications\n", "   - Check parameter accuracy\n", "   - Assess validation details\n", "   - Review analytical methods\n", "   - Evaluate data presentation\n", "\n", "5. Content Quality Assessment:\n", "   - Check for clarity and precision\n", "   - Evaluate professional language\n", "   - Assess logical flow\n", "   - Review completeness of explanations\n", "   - Verify data consistency\n", "\n", "6. Hallucination Detection:\n", "   - Identify any unsupported claims\n", "   - Check for fabricated data\n", "   - Verify guideline references\n", "   - Assess technical accuracy\n", "   - Review consistency with source material\n", "\n", "7. Regulatory Compliance:\n", "   - Evaluate adherence to guidelines\n", "   - Check for regulatory requirements\n", "   - Assess justification quality\n", "   - Review risk management\n", "   - Verify post-approval considerations\n", "\n", "8. Documentation Standards:\n", "   - Check formatting consistency. Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. The output should not include any other formatting like xml, html, plaintext etc.\n", "   - Check if there are section heading present, we dont want markdown headings in the output as we'll be adding them later manually\n", "   - Evaluate table and figure quality\n", "   - Assess reference accuracy\n", "   - Review appendix completeness\n", "   - Verify document structure\n", "\n", "9. Improvement Areas:\n", "   - Identify missing elements\n", "   - Note unclear sections\n", "   - Flag incomplete information\n", "   - Highlight inconsistencies\n", "   - Suggest specific enhancements\n", "\n", "10. Table and Section Handling Evaluation:\n", "    - For tables:\n", "      * VERIFY that all table tags (`<TABLE_TAG_table_x>`) are preserved EXACTLY as they appear in the input\n", "      * CHECK that the position and order of table tags is maintained\n", "      * CONFIRM that no attempt has been made to construct or format tables\n", "      * VERIFY that table references in the format \"Table X: table heading\" are preserved\n", "      * ASSESS if the relationship between tables and section content is clearly explained\n", "      * VERIFY that tables are properly referenced in the text\n", "    - For multiple sections:\n", "      * Confirm that all sections from the reference text are included\n", "      * Check if section relationships are properly maintained\n", "      * Verify that no sections are omitted or partially included\n", "      * Assess if section connections are clearly explained\n", "    - For section linking:\n", "      * Verify that all necessary links between sections are present\n", "      * Check if the relationships between linked sections are clearly explained\n", "      * Assess the accuracy and completeness of cross-references\n", "      * Verify that the logical flow between linked sections is maintained\n", "\n", "Make sure that if there are any tables present then their descriptions and the table placeholder tags (`<TABLE_TAG_table_x>`) are preserved exactly in the output.\n", "Also, if there are multiple sections present in the reference text then make sure that all of them along with their descriptions are present in the output.\n", "\n", "Rating <PERSON><PERSON><PERSON> (1-10):\n", "1-3: Major deficiencies, significant non-compliance\n", "4-6: Moderate issues, partial compliance\n", "7-8: Minor issues, mostly compliant\n", "9-10: Excellent compliance, minimal improvements needed\n", "\n", "Provide a thorough and critical evaluation with specific, actionable feedback for improvement.\"\"\"\n", "\n", "    user_prompt = f\"\"\"Please evaluate the following NDA section output against ICH eCTD requirements and the provided section information.\n", "\n", "SECTION INFORMATION:\n", "- Section: {section_info.get('section', '')}\n", "- Title: {section_info.get('title', '')}\n", "- Description: {section_info.get('description', '')}\n", "- Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "- Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "\n", "NDA OUTPUT TO EVALUATE:\n", "{nda_output}\n", "\n", "IMPORTANT CONSIDERATIONS:\n", "1. Check for strict compliance with Golden Instructions and Checkpoints\n", "2. Verify adherence to ICH eCTD v3.2.2 requirements\n", "3. STRICTLY verify that ALL table placeholders in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\" are preserved EXACTLY\n", "4. VERIFY that the position and order of all table tags is maintained\n", "5. CHECK that NO attempt has been made to construct tables as these will be inserted during post-processing\n", "6. STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "7. Check that ALL tables and figures from source documents are included without omission\n", "8. <PERSON><PERSON>s technical accuracy and completeness\n", "9. Evaluate documentation standards and quality\n", "10. Identify any hallucinations or unsupported claims\n", "11. Provide a numerical rating (1-10) and detailed feedback in all required categories\n", "\n", "Evaluate thoroughly and provide specific, actionable feedback for improvement.\"\"\"\n", "\n", "    system_tokens = calculate_number_of_tokens(system_prompt)\n", "    user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "    critique_evaluation_agent = Agent(\n", "        model=critique_model,\n", "        system_prompt=system_prompt,\n", "        output_type=CritiqueEvaluation,\n", "        model_settings={\n", "            \"temperature\": 0.2,\n", "        },\n", "        retries=3\n", "    )\n", "\n", "    response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "    total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "    print(f\"generate_critique_without_guidelines token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "    return response.output\n", "\n", "\n", "async def combine_critiques_incrementally(critiques, section_info):\n", "    \"\"\"\n", "    Combine multiple critiques incrementally using a batch processing approach.\n", "    \n", "    Args:\n", "        critiques: List of individual critiques to combine\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        str: Comprehensive combined critique\n", "    \"\"\"\n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def combine_critiques_batch(critiques_batch):\n", "        \"\"\"Helper function to combine multiple critiques in a batch.\"\"\"\n", "        total_critiques = len(critiques_batch)\n", "        print(f\"\\nCombining {total_critiques} critiques:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(critique.model_dump_json()) for critique in critiques_batch)\n", "        print(f\"Total input tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert with specialized expertise in critiquing NDA submissions. Your task is to combine multiple separate evaluations of an NDA section into one comprehensive, unified critique that captures all key insights and maintains a consistent, thorough evaluation structure.\n", "\n", "CRITICAL REQUIREMENTS:\n", "1. Critique Comprehensiveness (HIGHEST PRIORITY):\n", "   - Preserve ALL critical issues identified in all critiques\n", "   - Maintain ALL required improvements from all critiques\n", "   - Include ALL key strengths noted in all critiques\n", "   - Preserve ALL additional recommendations\n", "   - Ensure NO evaluation insights or feedback points are lost\n", "   - Maintain the most detailed and specific feedback\n", "   - Ensure the combined critique is as thorough as all original critiques together\n", "\n", "2. Table and Figure Preservation Assessment (HIGHEST PRIORITY):\n", "   - STRICTLY verify that ALL table placeholders and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "   - VERIFY that the exact position and order of all `<TABLE_TAG_table_x>` tags is maintained in the text\n", "   - CHECK that NO attempt has been made to construct or format the tables as these will be inserted during post-processing\n", "   - CONFIRM that the table summary information is properly referenced and used in the text without replacing the table tags\n", "   - STRICTLY verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "   - Verify that ALL tables and figures from source documents are included in the output\n", "   - Consider any table tag alteration, removal, or position change as a CRITICAL issue\n", "   - Check that all tables and figures are properly referenced in the text\n", "\n", "3. Rating Reconciliation:\n", "   - Provide a balanced overall rating that considers all original ratings\n", "   - Explain the rationale for the final rating if significantly different from any original\n", "   - Ensure the rating accurately reflects the combined feedback\n", "   - Prioritize regulatory compliance issues in rating determination\n", "   - Consider the severity and priority of issues when determining final rating\n", "\n", "4. Integration Strategy:\n", "   - Eliminate redundant or duplicate feedback points\n", "   - Group related issues and improvements together\n", "   - Organize feedback in order of regulatory importance\n", "   - Maintain specific, actionable language throughout\n", "   - Preserve technical detail and precision\n", "   - Create a logical, comprehensive evaluation structure\n", "   - Ensure the combined critique reads as a single, cohesive evaluation\n", "\n", "5. Consistency and Accuracy:\n", "   - Resolve any contradictions between the critiques\n", "   - Maintain consistent evaluation standards throughout\n", "   - Ensure all feedback relates directly to ICH eCTD requirements\n", "   - Verify technical accuracy in combined feedback\n", "   - Use consistent terminology and phrasing\n", "   - Maintain the formal, professional evaluation tone\n", "   - Ensure feedback is specific and actionable\n", "\n", "The combined critique should represent a comprehensive, balanced evaluation that preserves all key insights from all original critiques while eliminating redundancy and resolving any contradictions.\"\"\"\n", "\n", "        # Convert CritiqueEvaluation objects to text format for processing\n", "        critique_texts = []\n", "        for critique in critiques_batch:\n", "            if isinstance(critique, CritiqueEvaluation):\n", "                # Convert CritiqueEvaluation to text format\n", "                critique_text = f\"\"\"Overall Rating: {critique.overall_rating}\n", "\n", "Key Strengths:\n", "{chr(10).join(f\"- {strength}\" for strength in critique.key_strengths)}\n", "\n", "Critical Issues:\n", "{chr(10).join(f\"- {issue}\" for issue in critique.critical_issues)}\n", "\n", "Required Improvements:\n", "{chr(10).join(f\"- {improvement}\" for improvement in critique.required_improvements)}\n", "\n", "Additional Recommendations:\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in critique.additional_recommendations)}\"\"\"\n", "                critique_texts.append(critique_text)\n", "            else:\n", "                # Handle legacy text format\n", "                critique_texts.append(str(critique))\n", "\n", "        user_prompt = f\"\"\"Please combine the following {total_critiques} evaluations of an NDA section into one comprehensive, unified critique that captures all key insights from all evaluations.\n", "\n", "\"\"\"\n", "        \n", "        # Add each critique with a header\n", "        for i, critique_text in enumerate(critique_texts, 1):\n", "            user_prompt += f\"\"\"\n", "EVALUATION {i}:\n", "{critique_text}\n", "\n", "\"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "IMPORTANT CONSIDERATIONS:\n", "1. Preserve ALL critical issues identified in all {total_critiques} critiques\n", "2. Maintain ALL required improvements from all critiques\n", "3. Include ALL key strengths noted in all critiques\n", "4. Preserve ALL additional recommendations\n", "5. Eliminate redundant or duplicate feedback points\n", "6. Group related issues and improvements together\n", "7. Resolve any contradictions between the critiques\n", "8. Provide a balanced overall rating that considers all original ratings\n", "9. STRICTLY preserve ALL feedback related to table placeholders and tags in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "10. Verify that critiques address whether table tags position and order are properly maintained\n", "11. Check if critiques mention whether there were attempts to construct tables (which should NOT happen)\n", "12. Consider any table tag alteration, removal, or position change as a CRITICAL issue\n", "13. Include ALL feedback about missing, modified, or omitted tables and figures\n", "14. Consider issues with table/figure preservation as CRITICAL in the combined critique\n", "\n", "The combined critique should represent a comprehensive, balanced evaluation that preserves all key insights from all original critiques while eliminating redundancy and resolving any contradictions.\"\"\"\n", "\n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "        critique_evaluation_agent = Agent(\n", "            model=critique_model,\n", "            system_prompt=system_prompt,\n", "            output_type=CritiqueEvaluation,\n", "            model_settings={\n", "                \"temperature\": 0.2,\n", "            },\n", "            retries=3\n", "        )\n", "\n", "        response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "        print(f\"combine_critiques_incrementally token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "        return response.output\n", "    \n", "    # Handle base cases\n", "    if not critiques:\n", "        return \"\"\n", "    \n", "    if len(critiques) == 1:\n", "        return critiques[0]\n", "    \n", "    # Process critiques iteratively, combining in batches\n", "    current_critiques = critiques.copy()\n", "    \n", "    while len(current_critiques) > 1:\n", "        # Create optimal batches based on token count\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for critique in current_critiques:\n", "            critique_tokens = calculate_number_of_tokens(critique.model_dump_json())\n", "            \n", "            # If adding this critique would exceed the token limit, start a new batch\n", "            if current_batch and current_token_count + critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                batches.append(current_batch)\n", "                current_batch = [critique]\n", "                current_token_count = critique_tokens\n", "            else:\n", "                current_batch.append(critique)\n", "                current_token_count += critique_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        print(f\"Created {len(batches)} batches for critique combining\")\n", "        for i, batch in enumerate(batches):\n", "            print(f\"Batch {i+1} contains {len(batch)} critiques with {sum(calculate_number_of_tokens(c.model_dump_json()) for c in batch)} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_critiques_batch(batch) for batch in batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        current_critiques = await asyncio.gather(*tasks)\n", "    \n", "    print(f\"combine_critiques_incrementally tokens: {calculate_number_of_tokens(current_critiques[0].model_dump_json())}\")\n", "    return current_critiques[0]\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def optimized_nda_critique_with_parsing(section_info, nda_output, max_retries=3):\n", "    \"\"\"\n", "    Generate a comprehensive NDA critique using structured output.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        max_retries: Maximum number of retry attempts for failures\n", "        \n", "    Returns:\n", "        Tuple[str, Dict]: Raw critique text representation and structured evaluation dict\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            # Generate the critique using structured output\n", "            critique_evaluation = await optimized_nda_critique(section_info, nda_output)\n", "            \n", "            # Convert CritiqueEvaluation to text representation for compatibility\n", "            critique_text = f\"\"\"1. Overall Rating:\n", "   {critique_evaluation.overall_rating}\n", "\n", "2. Key Strengths:\n", "{chr(10).join(f\"   - {strength}\" for strength in critique_evaluation.key_strengths)}\n", "\n", "3. Critical Issues:\n", "{chr(10).join(f\"   - {issue}\" for issue in critique_evaluation.critical_issues)}\n", "\n", "4. Required Improvements:\n", "{chr(10).join(f\"   - {improvement}\" for improvement in critique_evaluation.required_improvements)}\n", "\n", "5. Additional Recommendations:\n", "{chr(10).join(f\"   - {recommendation}\" for recommendation in critique_evaluation.additional_recommendations)}\"\"\"\n", "            \n", "            # Convert to dict for compatibility\n", "            evaluation_dict = {\n", "                \"overall_rating\": critique_evaluation.overall_rating,\n", "                \"key_strengths\": critique_evaluation.key_strengths,\n", "                \"critical_issues\": critique_evaluation.critical_issues,\n", "                \"required_improvements\": critique_evaluation.required_improvements,\n", "                \"additional_recommendations\": critique_evaluation.additional_recommendations\n", "            }\n", "            \n", "            print(f\"Successfully generated critique with overall rating: {critique_evaluation.overall_rating}\")\n", "            return critique_text, evaluation_dict\n", "            \n", "        except Exception as e:\n", "            print(f\"Error in critique generation (attempt {attempt+1}/{max_retries}): {str(e)}\")\n", "            if attempt == max_retries - 1:\n", "                # Last attempt, return a default response\n", "                default_dict = {\n", "                    \"overall_rating\": 5.0,  # Middle value as default\n", "                    \"key_strengths\": [\"Content provides basic information\"],\n", "                    \"critical_issues\": [\"Unable to properly evaluate the content\"],\n", "                    \"required_improvements\": [\"Review all regulatory requirements\"],\n", "                    \"additional_recommendations\": [\"Ensure compliance with ICH eCTD guidelines\"]\n", "                }\n", "                default_text = \"\"\"1. Overall Rating:\n", "   5.0\n", "\n", "2. Key Strengths:\n", "   - Content provides basic information\n", "\n", "3. Critical Issues:\n", "   - Unable to properly evaluate the content\n", "\n", "4. Required Improvements:\n", "   - Review all regulatory requirements\n", "\n", "5. Additional Recommendations:\n", "   - Ensure compliance with ICH eCTD guidelines\"\"\"\n", "                print(\"Exhausted all retry attempts, returning default evaluation\")\n", "                return default_text, default_dict\n", "    \n", "    # This should not be reached due to the return in the last attempt, but just in case\n", "    default_dict = {\n", "        \"overall_rating\": 5.0,\n", "        \"key_strengths\": [\"Default evaluation\"],\n", "        \"critical_issues\": [\"Parsing error occurred\"],\n", "        \"required_improvements\": [\"Review output format\"],\n", "        \"additional_recommendations\": [\"Ensure critique follows expected format\"]\n", "    }\n", "    default_text = \"Error: Unexpected execution path\"\n", "    return default_text, default_dict\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def rate_nda_output_and_self_improve(section_info):\n", "    \"\"\"\n", "    Generate NDA output, evaluate it, and improve it based on feedback.\n", "    Uses an optimized approach where the plan is generated once and reused\n", "    in subsequent iterations for improved performance.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Dict: Updated section information with NDA output and evaluation\n", "    \"\"\"\n", "    overall_rating = 0\n", "    counter = 0\n", "    answers_list = []\n", "\n", "    # Generate plan and content for the first iteration, save the plan for reuse\n", "    original_nda_output, reusable_plan = await optimized_nda_output_generation(section_info)        \n", "    current_nda_output = original_nda_output\n", "    \n", "    # Use the robust critique function that handles parsing errors\n", "    nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)\n", "    \n", "    section_info[\"nda_output_evaluation_json\"] = nda_output_evaluation_json\n", "    section_info[\"nda_output_evaluation_text\"] = nda_output_evaluation  # Save the raw critique text\n", "    overall_rating = nda_output_evaluation_json[\"overall_rating\"]  # Now guaranteed to be a valid number\n", "    counter += 1\n", "\n", "    answers_list.append(\n", "        {\n", "            \"nda_output\": current_nda_output,\n", "            \"nda_output_evaluation_json\": nda_output_evaluation_json,\n", "            \"nda_output_evaluation_text\": nda_output_evaluation,  # Include raw critique text\n", "            \"overall_rating\": overall_rating\n", "        }\n", "    )\n", "\n", "    print(f\"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}\")\n", "\n", "    # Regenerate content in subsequent iterations using feedback and reusing the plan for performance\n", "    while(overall_rating < 9 and counter < 3):\n", "        # Create an enhanced feedback object that includes the previous output\n", "        enhanced_feedback = nda_output_evaluation_json.copy()\n", "        enhanced_feedback[\"previous_output\"] = current_nda_output\n", "        enhanced_feedback[\"evaluation_text\"] = nda_output_evaluation  # Include the raw critique text\n", "        \n", "        # Regenerate content using critique feedback and reusing the plan (performance optimization)\n", "        print(f\"Regenerating content with critique feedback and reused plan to address issues (rating: {overall_rating})\")\n", "        regenerated_nda_output = await optimized_nda_output_generation(section_info, enhanced_feedback, reusable_plan)\n", "\n", "        print(\"regenerated_nda_output: \", regenerated_nda_output)\n", "\n", "        current_nda_output = regenerated_nda_output\n", "        \n", "        # Use the robust critique function that handles parsing errors\n", "        nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)\n", "        \n", "        section_info[\"nda_output_evaluation_json\"] = nda_output_evaluation_json\n", "        section_info[\"nda_output_evaluation_text\"] = nda_output_evaluation  # Save the raw critique text\n", "        overall_rating = nda_output_evaluation_json[\"overall_rating\"]  # Now guaranteed to be a valid number\n", "        counter += 1\n", "\n", "        answers_list.append(\n", "            {\n", "                \"nda_output\": current_nda_output,\n", "                \"nda_output_evaluation_json\": nda_output_evaluation_json,\n", "                \"nda_output_evaluation_text\": nda_output_evaluation,  # Include raw critique text\n", "                \"overall_rating\": overall_rating\n", "            }\n", "        )\n", "\n", "        print(f\"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}\")\n", "\n", "    # Select best answer based on overall rating\n", "    best_answer = max(answers_list, key=lambda x: x[\"overall_rating\"])\n", "    section_info[\"nda_output\"] = best_answer[\"nda_output\"]\n", "    section_info[\"nda_output_evaluation_json\"] = best_answer[\"nda_output_evaluation_json\"]\n", "    section_info[\"nda_output_evaluation_text\"] = best_answer[\"nda_output_evaluation_text\"]  # Save the raw critique text\n", "\n", "    print(f\"Selecting best answer for section {section_info.get('section', 'unknown')} with rating {best_answer['overall_rating']}\")\n", "\n", "    return section_info\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["async def generate_nda_for_each_section(json_obj):\n", "    \"\"\"\n", "    Recursively process a JSON object and generate NDA output for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with NDA outputs\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that needs NDA output generation\n", "            if (obj and \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        print(f\"Generating NDA output for section {section.get('section', 'unknown')}\")\n", "        \n", "        # Generate NDA output for this section\n", "        processed_section = await rate_nda_output_and_self_improve(section)\n", "        \n", "        print(f\"Completed NDA output generation for section {section.get('section', 'unknown')}\")\n", "        \n", "        return processed_section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Process the document to find relevant input documents\n", "nda_output_json = await generate_nda_for_each_section(processed_doc_with_input_docs)\n", "\n", "# Save the results to a JSON file\n", "write_to_json(nda_output_json, \"nda_output_json.json\")\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pypandoc\n", "\n", "def format_answer(json_obj, formatted_text=\"\", depth=1):\n", "    \"\"\"\n", "    Recursively formats the answer JSON into a structured Markdown document.\n", "    Uses '#' for headings based on depth.\n", "    \"\"\"\n", "    if isinstance(json_obj, dict):\n", "        if \"section\" in json_obj and \"title\" in json_obj:\n", "            formatted_text += f\"{'#' * (depth)} {json_obj['section']} {json_obj['title']}\\n\\n\"\n", "            if \"nda_output\" in json_obj:\n", "                formatted_text += f\"{json_obj['nda_output']}\\n\\n\"\n", "\n", "        for key, value in json_obj.items():\n", "            if isinstance(value, dict):\n", "                formatted_text = format_answer(value, formatted_text, depth + 1)  # Increase depth\n", "\n", "    return formatted_text\n", "\n", "def save_answer_file(json_obj, filename=\"formatted_answer.md\"):\n", "    \"\"\"\n", "    Saves the formatted answer text to a file.\n", "    \"\"\"\n", "    formatted_text = format_answer(json_obj)\n", "    \n", "    # Replace table tags with backtick-enclosed versions\n", "    import re\n", "    # formatted_text = re.sub(r'<TABLE_TAG_table_(\\d+)>', r'`<TABLE_TAG_table_\\1>`', formatted_text)\n", "    \n", "    with open(filename, \"w\", encoding=\"utf-8\") as file:\n", "        file.write(formatted_text)\n", "    print(f\"Formatted answer saved to {filename}\")\n", "\n", "save_answer_file(nda_output_json)\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["pypandoc.convert_file(\"formatted_answer.md\", 'docx', outputfile=\"formatted_answer.docx\")\n", "\n"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["from docx import Document\n", "from docx.document import Document as DocumentObject\n", "from docx.shared import Inches\n", "from copy import deepcopy\n", "import os\n", "import shutil\n", "import zipfile\n", "from docx.oxml.ns import qn\n", "from docx.table import Table\n", "from docx.section import Paragraph\n", "from typing import List\n", "import json\n", "from docx.oxml import OxmlElement\n", "\n", "def copy_page_setup(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy page setup (size, orientation, margins) from source to target document\"\"\"\n", "    source_section = source_doc.sections[0]\n", "    target_section = target_doc.sections[0]\n", "    \n", "    # Copy page size and orientation\n", "    target_section.page_width = source_section.page_width\n", "    target_section.page_height = source_section.page_height\n", "    target_section.orientation = source_section.orientation\n", "    \n", "    # Copy margins\n", "    target_section.top_margin = source_section.top_margin\n", "    target_section.bottom_margin = source_section.bottom_margin\n", "    target_section.left_margin = source_section.left_margin\n", "    target_section.right_margin = source_section.right_margin\n", "    \n", "    print(f\"📏 Copied page setup: {source_section.page_width} x {source_section.page_height}\")\n", "\n", "def copy_table_styles_completely(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy complete table style definitions including all visual formatting\"\"\"\n", "    try:\n", "        # Extract the styles.xml from both documents  \n", "        source_styles_part = source_doc.part.part_related_by(\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\")\n", "        target_styles_part = target_doc.part.part_related_by(\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\")\n", "        \n", "        if source_styles_part and target_styles_part:\n", "            # Get the root elements\n", "            source_styles_root = source_styles_part.element\n", "            target_styles_root = target_styles_part.element\n", "            \n", "            # Find all table styles in source\n", "            source_table_styles = source_styles_root.findall('.//w:style[@w:type=\"table\"]', source_styles_root.nsmap)\n", "            \n", "            for style in source_table_styles:\n", "                style_id = style.get(qn('w:styleId'))\n", "                \n", "                # Check if this style exists in target\n", "                existing_style = target_styles_root.find(f'.//w:style[@w:styleId=\"{style_id}\"]', target_styles_root.nsmap)\n", "                \n", "                if existing_style is not None:\n", "                    # Remove existing style\n", "                    existing_style.getparent().remove(existing_style)\n", "                \n", "                # Add the complete style from source\n", "                target_styles_root.append(deepcopy(style))\n", "            \n", "            print(f\"🎨 Copied {len(source_table_styles)} table styles\")\n", "            return True\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy table styles: {e}\")\n", "        return False\n", "    \n", "    return False\n", "\n", "def apply_table_style_explicitly(table: Table, style_name: str):\n", "    \"\"\"Apply table style and ensure all conditional formatting is applied\"\"\"\n", "    try:\n", "        # Set the table style\n", "        table.style = style_name\n", "        \n", "        # Force conditional formatting by updating tblLook\n", "        tbl_element = table._tbl\n", "        tblPr = tbl_element.find(qn('w:tblPr'))\n", "        \n", "        if tblPr is not None:\n", "            # Find or create tblLook element\n", "            tblLook = tblPr.find(qn('w:tblLook'))\n", "            if tbl<PERSON>ook is None:\n", "                tblLook = OxmlElement('w:tblLook')\n", "                tblPr.append(tblLook)\n", "            \n", "            # Set attributes to enable all conditional formatting\n", "            tblLook.set(qn('w:val'), '04A0')\n", "            tblLook.set(qn('w:firstRow'), '1')\n", "            tblLook.set(qn('w:lastRow'), '0')\n", "            tblLook.set(qn('w:firstColumn'), '1')\n", "            tblLook.set(qn('w:lastColumn'), '0')\n", "            tblLook.set(qn('w:noHBand'), '0')\n", "            tblLook.set(qn('w:noVBand'), '1')\n", "            \n", "            return True\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not apply table style: {e}\")\n", "        return False\n", "    \n", "    return False\n", "\n", "def copy_document_styles(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy styles from source document to target document\"\"\"\n", "    try:\n", "        # First copy table styles completely (with full definitions)\n", "        table_styles_copied = copy_table_styles_completely(source_doc, target_doc)\n", "        \n", "        if not table_styles_copied:\n", "            # Fallback to basic style copying\n", "            for style in source_doc.styles:\n", "                if style.name not in [s.name for s in target_doc.styles]:\n", "                    # Add the style to target document\n", "                    target_doc.styles.add_style(style.name, style.type)\n", "        \n", "        print(\"🎨 Copied document styles\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy all styles: {e}\")\n", "\n", "def copy_cell_formatting(source_cell, target_cell):\n", "    \"\"\"Copy complete cell formatting including background, borders, and text formatting\"\"\"\n", "    try:\n", "        # Copy cell background/shading\n", "        source_cell_element = source_cell._tc\n", "        target_cell_element = target_cell._tc\n", "        \n", "        # Find and copy cell properties (tcPr)\n", "        source_tcPr = source_cell_element.find(qn('w:tcPr'))\n", "        if source_tcPr is not None:\n", "            # Remove existing tcPr if present\n", "            existing_tcPr = target_cell_element.find(qn('w:tcPr'))\n", "            if existing_tcPr is not None:\n", "                target_cell_element.remove(existing_tcPr)\n", "            \n", "            # Copy the complete cell properties\n", "            target_cell_element.insert(0, deepcopy(source_tcPr))\n", "        \n", "        # Copy paragraph and run formatting more thoroughly\n", "        for i, source_para in enumerate(source_cell.paragraphs):\n", "            if i < len(target_cell.paragraphs):\n", "                target_para = target_cell.paragraphs[i]\n", "                \n", "                # Copy paragraph-level formatting\n", "                source_para_element = source_para._element\n", "                target_para_element = target_para._element\n", "                \n", "                # Copy paragraph properties (pPr)\n", "                source_pPr = source_para_element.find(qn('w:pPr'))\n", "                if source_pPr is not None:\n", "                    existing_pPr = target_para_element.find(qn('w:pPr'))\n", "                    if existing_pPr is not None:\n", "                        target_para_element.remove(existing_pPr)\n", "                    target_para_element.insert(0, deepcopy(source_pPr))\n", "                \n", "                # Copy run formatting more comprehensively\n", "                for j, source_run in enumerate(source_para.runs):\n", "                    if j < len(target_para.runs):\n", "                        target_run = target_para.runs[j]\n", "                        \n", "                        # Copy run properties (rPr) at XML level\n", "                        source_run_element = source_run._element\n", "                        target_run_element = target_run._element\n", "                        \n", "                        source_rPr = source_run_element.find(qn('w:rPr'))\n", "                        if source_rPr is not None:\n", "                            existing_rPr = target_run_element.find(qn('w:rPr'))\n", "                            if existing_rPr is not None:\n", "                                target_run_element.remove(existing_rPr)\n", "                            target_run_element.insert(0, deepcopy(source_rPr))\n", "                        \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy cell formatting: {e}\")\n", "\n", "def copy_table_with_complete_formatting(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):\n", "    \"\"\"Copy table with complete formatting preservation including cell shading and text formatting\"\"\"\n", "    # Create a new paragraph for the heading before the table tag paragraph\n", "    heading_paragraph = insert_after_paragraph.insert_paragraph_before()\n", "    heading_run = heading_paragraph.add_run(table_heading)\n", "    heading_run.bold = True\n", "    \n", "    # Get the table element and its style\n", "    tbl_element = source_table._tbl\n", "    source_table_style = None\n", "    \n", "    # Extract table style name\n", "    tblPr = tbl_element.find(qn('w:tblPr'))\n", "    if tblPr is not None:\n", "        tblStyle = tblPr.find(qn('w:tblStyle'))\n", "        if tblStyle is not None:\n", "            source_table_style = tblStyle.get(qn('w:val'))\n", "    \n", "    # Create a deep copy of the table element with all formatting\n", "    tbl_copy = deepcopy(tbl_element)\n", "    \n", "    # Insert the copied table after the table tag paragraph\n", "    insert_after_paragraph._element.addnext(tbl_copy)\n", "    \n", "    # Enhanced formatting preservation\n", "    try:\n", "        # Find the newly inserted table in the document\n", "        target_doc = insert_after_paragraph._parent\n", "        target_table = None\n", "        \n", "        # Get the table that was just inserted\n", "        for table in target_doc.tables:\n", "            if table._tbl == tbl_copy:\n", "                target_table = table\n", "                break\n", "        \n", "        if target_table and source_table:\n", "            # Apply the table style explicitly if we found one\n", "            if source_table_style:\n", "                apply_table_style_explicitly(target_table, source_table_style)\n", "            \n", "            # Copy table-level properties more thoroughly\n", "            source_tblPr = source_table._tbl.find(qn('w:tblPr'))\n", "            if source_tblPr is not None:\n", "                target_tblPr = target_table._tbl.find(qn('w:tblPr'))\n", "                if target_tblPr is not None:\n", "                    target_table._tbl.remove(target_tblPr)\n", "                target_table._tbl.insert(0, deepcopy(source_tblPr))\n", "            \n", "            # Copy cell-by-cell formatting\n", "            for i, source_row in enumerate(source_table.rows):\n", "                if i < len(target_table.rows):\n", "                    target_row = target_table.rows[i]\n", "                    for j, source_cell in enumerate(source_row.cells):\n", "                        if j < len(target_row.cells):\n", "                            target_cell = target_row.cells[j]\n", "                            copy_cell_formatting(source_cell, target_cell)\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not apply enhanced formatting: {e}\")\n", "    \n", "    # Insert an empty paragraph after the table (acts as a line break)\n", "    p = OxmlElement('w:p')\n", "    tbl_copy.addnext(p)\n", "    \n", "    # Clear the table tag paragraph content instead of removing it\n", "    insert_after_paragraph.clear()\n", "\n", "def remove_all_tables(doc: DocumentObject):\n", "    body = doc.element.body\n", "    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]\n", "    for tbl in tables_to_remove:\n", "        body.remove(tbl)\n", "        \n", "def extract_images(docx_path, extract_folder):\n", "    with zipfile.ZipFile(docx_path, 'r') as zip_ref:\n", "        zip_ref.extractall(extract_folder)\n", "\n", "def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:\n", "    return [para for para in doc.paragraphs if search_text in para.text]\n", "\n", "def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):\n", "    \"\"\"Copy table with heading and line break after - DEPRECATED, use copy_table_with_complete_formatting\"\"\"\n", "    copy_table_with_complete_formatting(source_table, insert_after_paragraph, table_heading)\n", "\n", "def load_table_headings(json_file_path: str):\n", "    \"\"\"Load table headings from JSON file\"\"\"\n", "    with open(json_file_path, 'r', encoding='utf-8') as f:\n", "        table_index = json.load(f)\n", "    \n", "    table_headings = {}\n", "    for table_hash, table_info in table_index.items():\n", "        table_id = table_info['id']\n", "        table_heading = table_info['heading']\n", "        table_headings[table_id] = table_heading\n", "    \n", "    return table_headings\n", "\n", "def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):\n", "    run = paragraph.insert_paragraph_before().add_run()\n", "    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed\n", "\n", "def main():\n", "    source_docx = '/Users/<USER>/projects/scalegen/MedNova/Input Docs/module 5/adverse_event_source_doc.docx'\n", "    target_docx = '/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/formatted_answer.docx'\n", "    extract_folder = 'source_extracted'\n", "    \n", "    # Path for table headings\n", "    json_file_path = '/Users/<USER>/projects/scalegen/MedNova/Input Docs/module 5/adverse_event_source_doc_tables_with_summaries_and_tags/table_index.json'\n", "\n", "    # Step 1: Extract images from source.docx\n", "    if os.path.exists(extract_folder):\n", "        shutil.rmtree(extract_folder)\n", "    extract_images(source_docx, extract_folder)\n", "\n", "    media_folder = os.path.join(extract_folder, 'word', 'media')\n", "    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []\n", "\n", "    # Load table headings from JSON file\n", "    table_headings = load_table_headings(json_file_path)\n", "    print(f\"📊 Loaded headings for {len(table_headings)} tables\")\n", "\n", "    source_doc = Document(source_docx)\n", "    target_doc = Document(target_docx)\n", "\n", "    # Step 1.2: Copy page setup and styles from source to target\n", "    copy_page_setup(source_doc, target_doc)\n", "    copy_document_styles(source_doc, target_doc)\n", "\n", "    # Step 1.5: Remove all existing tables from target doc\n", "    remove_all_tables(target_doc)\n", "    print(\"🗑️ Removed all existing tables from target document\")\n", "\n", "    # Step 2: Process tables with complete formatting preservation\n", "    # First, collect all table tag matches to avoid displacement issues\n", "    table_matches = []\n", "    for idx, source_table in enumerate(source_doc.tables, start=1):\n", "        table_id = f\"table_{idx}\"\n", "        reference_text = f\"<TABLE_TAG_{table_id}>\"\n", "        \n", "        # Get the heading for this table from JSON, fallback to generic heading\n", "        table_heading = table_headings.get(table_id, f\"Table {idx}\")\n", "        \n", "        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)\n", "        if matched_paragraphs:\n", "            for para in matched_paragraphs:\n", "                table_matches.append((para, source_table, table_heading, reference_text, idx))\n", "        else:\n", "            print(f\"⚠️ Reference '{reference_text}' not found in target document\")\n", "    \n", "    # Process matches in reverse order to avoid displacement issues\n", "    for para, source_table, table_heading, reference_text, idx in reversed(table_matches):\n", "        copy_table_with_complete_formatting(source_table, para, table_heading)\n", "        print(f\"✅ Inserted Table {idx} with complete formatting\")\n", "\n", "    # Step 3: Process figures/images\n", "    for idx, image_file in enumerate(media_files, start=1):\n", "        reference_text = f\"Figure {idx}:\"\n", "        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)\n", "        image_path = os.path.join(media_folder, image_file)\n", "        if matched_paragraphs:\n", "            for para in matched_paragraphs:\n", "                insert_image_before_paragraph(target_doc, image_path, para)\n", "            print(f\"✅ Inserted Figure {idx}\")\n", "        else:\n", "            print(f\"⚠️ Reference '{reference_text}' not found in target document\")\n", "\n", "    # Step 4: Save output\n", "    target_doc.save('/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/table_and_image_formatted_answer.docx')\n", "    print(\"✅ All done. Saved as 'table_and_image_formatted_answer.docx'\")\n", "    print(\"🎨 Complete visual formatting preserved from source document\")\n", "    \n", "    if os.path.exists(extract_folder):\n", "        shutil.rmtree(extract_folder)\n", "        print(f\"🗑️ Cleaned up temporary folder: {extract_folder}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}