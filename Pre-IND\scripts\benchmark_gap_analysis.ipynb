{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/projects/scalegen/virtualenvs/NDA/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GENERATOR_MODEL_NAME agentic-large\n", "GENERATOR_MODEL_BASE_URL http://**************:9000/v1\n", "GENERATOR_MODEL_API_KEY d07a13795b2b470d90699fd67716e29a\n", "CRITIQUE_MODEL_NAME agentic-large\n", "CRITIQUE_MODEL_BASE_URL http://**************:9000/v1\n", "CRITIQUE_MODEL_API_KEY d07a13795b2b470d90699fd67716e29a\n", "LLM agentic-large is available, response: Hello! How can I assist you today?\n", "LLM agentic-large is available, response: Hello! How can I assist you today?\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "import re\n", "import json\n", "import os\n", "from openai import OpenAI, AsyncOpenAI\n", "from dotenv import load_dotenv\n", "from pymongo import MongoClient, AsyncMongoClient\n", "from bson.objectid import ObjectId\n", "from typing import List, Dict, Any, Tuple\n", "import numpy as np\n", "import asyncio\n", "from transformers import AutoTokenizer\n", "import traceback  # Added import for stack trace information\n", "import time  # Added import for timestamp logging\n", "import inspect    # For frame inspection\n", "import hashlib  # For content hashing in caching\n", "from functools import lru_cache\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent\n", "from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings\n", "from pydantic_ai.providers.anthropic import AnthropicProvider\n", "from pydantic_ai.models.openai import OpenAIModel, OpenAIResponsesModelSettings, ReasoningEffort\n", "from pydantic_ai.providers.openai import OpenAIProvider\n", "\n", "load_dotenv()\n", "\n", "# Global semaphore for rate limiting LLM API calls\n", "MAX_CONCURRENT_REQUESTS_FOR_GENERATOR = 160  # Configurable limit for concurrent API requests\n", "api_semaphore_for_generator = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS_FOR_GENERATOR)\n", "MAX_CONCURRENT_REQUESTS_FOR_CRITIQUE = 160  # Configurable limit for concurrent API requests\n", "api_semaphore_for_critique = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS_FOR_CRITIQUE)\n", "\n", "GENERATOR_MODEL_NAME = os.getenv(\"GENERATOR_MODEL_NAME\")\n", "GENERATOR_MODEL_BASE_URL = os.getenv(\"GENERATOR_MODEL_BASE_URL\")\n", "GENERATOR_MODEL_API_KEY = os.getenv(\"GENERATOR_MODEL_API_KEY\")\n", "\n", "CRITIQUE_MODEL_NAME = os.getenv(\"CRITIQUE_MODEL_NAME\")\n", "CRITIQUE_MODEL_BASE_URL = os.getenv(\"CRITIQUE_MODEL_BASE_URL\")\n", "CRITIQUE_MODEL_API_KEY = os.getenv(\"CRITIQUE_MODEL_API_KEY\")\n", "\n", "MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 20000\n", "MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 20000\n", "\n", "RELEVANCE_THRESHOLD = 8.0\n", "\n", "# Configuration for batched evaluation\n", "USE_BATCHED_EVALUATION = True  # Set to False to use individual evaluation (for testing/comparison)\n", "BATCH_SIZE_MAX_TOKENS = 20000\n", "BATCH_SIZE_MAX_SUMMARIES = 10\n", "\n", "# Configuration for batched chunk evaluation in gap analysis\n", "USE_BATCHED_CHUNK_EVALUATION = True  # Set to False to use individual chunk evaluation (for testing/comparison)\n", "CHUNK_BATCH_SIZE_MAX_TOKENS = 20000\n", "CHUNK_BATCH_SIZE_MAX_CHUNKS = 10\n", "\n", "DRUG_TYPES_JSON_PATH=\"/Users/<USER>/projects/scalegen/MedNova/structured jsons/drug_types.json\"\n", "\n", "# Load tokenizer for Mistral model (this could also be cached globally)\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-32B\")\n", "\n", "async def rate_limited_agent_call(agent, prompt, semaphore):\n", "    \"\"\"\n", "    Wrapper function that applies rate limiting to pydantic-ai agent calls using a semaphore.\n", "    \n", "    Args:\n", "        agent: The pydantic-ai Agent instance\n", "        prompt: The prompt to send to the agent\n", "        \n", "    Returns:\n", "        The result of the agent call\n", "    \"\"\"\n", "    async with semaphore:\n", "        return await agent.run(prompt)\n", "\n", "def get_generator_and_critique_model(generator_base_url, critique_base_url):\n", "    generator_model = None\n", "    critique_model = None\n", "\n", "    # GENERATOR MODEL\n", "\n", "    if \"openai\" in generator_base_url:\n", "        generator_model = OpenAIModel(\n", "            model_name=GENERATOR_MODEL_NAME,\n", "            provider=OpenAIProvider(\n", "                base_url=GENERATOR_MODEL_BASE_URL,\n", "                api_key=GENERATOR_MODEL_API_KEY\n", "            )\n", "        )\n", "    elif \"anthropic\" in generator_base_url:\n", "        generator_model = AnthropicModel(\n", "            model_name=GENERATOR_MODEL_NAME,\n", "            provider=AnthropicProvider(\n", "                api_key=GENERATOR_MODEL_API_KEY\n", "            )\n", "        )\n", "    elif \"http://\" in generator_base_url or \"theagentic\" in generator_base_url:\n", "        generator_model = OpenAIModel(\n", "            model_name=GENERATOR_MODEL_NAME,\n", "            provider=OpenAIProvider(\n", "                base_url=GENERATOR_MODEL_BASE_URL,\n", "                api_key=GENERATOR_MODEL_API_KEY\n", "            )\n", "        )\n", "\n", "    # CRITIQUE MODEL\n", "\n", "    if \"openai\" in critique_base_url:\n", "        critique_model = OpenAIModel(\n", "            model_name=CRITIQUE_MODEL_NAME,\n", "            provider=OpenAIProvider(\n", "                base_url=CRITIQUE_MODEL_BASE_URL,\n", "                api_key=CRITIQUE_MODEL_API_KEY\n", "            )\n", "        )\n", "    elif \"anthropic\" in critique_base_url:\n", "        critique_model = AnthropicModel(\n", "            model_name=CRITIQUE_MODEL_NAME,\n", "            provider=AnthropicProvider(\n", "                api_key=CRITIQUE_MODEL_API_KEY\n", "            )\n", "        )\n", "    elif \"http://\" in critique_base_url or \"theagentic\" in critique_base_url:\n", "        critique_model = OpenAIModel(\n", "            model_name=CRITIQUE_MODEL_NAME,\n", "            provider=OpenAIProvider(\n", "                base_url=CRITIQUE_MODEL_BASE_URL,\n", "                api_key=CRITIQUE_MODEL_API_KEY\n", "            )\n", "        )\n", "\n", "    return generator_model, critique_model\n", "\n", "def get_model_settings(base_url, temperature):\n", "    if \"openai\" in base_url:\n", "        return OpenAIResponsesModelSettings({\n", "            \"openai_reasoning_effort\": \"high\",\n", "            \"openai_reasoning_generate_summary\": \"detailed\"\n", "        })\n", "    elif \"anthropic\" in base_url:\n", "        return AnthropicModelSettings({\n", "            \"max_tokens\": 64000,\n", "            \"extra_body\": {\n", "                \"stream\": True\n", "            },\n", "            \"anthropic_thinking\": {\n", "                \"type\": \"enabled\",\n", "                \"budget_tokens\": 63999\n", "            }\n", "        })\n", "    elif \"http://\" in base_url or \"theagentic\" in base_url:\n", "        return {\n", "            \"temperature\": temperature,\n", "            \"extra_body\": {\n", "                # \"return_reasoning\": True\n", "            }\n", "        }\n", "    \n", "def get_structured_output_using_pydantic(base_url):\n", "    if \"openai\" in base_url:\n", "        return True\n", "    elif \"anthropic\" in base_url:\n", "        return False\n", "    elif \"http://\" in base_url or \"theagentic\" in base_url:\n", "        return True\n", "\n", "generator_model, critique_model = get_generator_and_critique_model(GENERATOR_MODEL_BASE_URL, CRITIQUE_MODEL_BASE_URL)\n", "\n", "print(\"GENERATOR_MODEL_NAME\", GENERATOR_MODEL_NAME)\n", "print(\"GENERATOR_MODEL_BASE_URL\", GENERATOR_MODEL_BASE_URL)\n", "print(\"GENERATOR_MODEL_API_KEY\", GENERATOR_MODEL_API_KEY)\n", "\n", "print(\"CRITIQUE_MODEL_NAME\", CRITIQUE_MODEL_NAME)\n", "print(\"CRITIQUE_MODEL_BASE_URL\", CRITIQUE_MODEL_BASE_URL)\n", "print(\"CRITIQUE_MODEL_API_KEY\", CRITIQUE_MODEL_API_KEY)\n", "\n", "# Pydantic schema for structured output\n", "class CheckpointList(BaseModel):\n", "    \"\"\"Schema for checkpoint list generation\"\"\"\n", "    checkpoints: List[str] = Field(\n", "        description=\"List of specific checkpoint questions that should be answered in summaries for this section\",\n", "        min_items=2\n", "    )\n", "\n", "class RelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for summary relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers the checkpoint questions\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of your score\",\n", "        default=\"\"\n", "    )\n", "\n", "class CombinedJustifications(BaseModel):\n", "    combined_supporting_evidence: str = Field(description=\"Markdown-formatted category-level explanation of what's present and working well\", default=\"No relevant information available\")\n", "    combined_gap_analysis: str = Field(description=\"Markdown-formatted category-level explanation of what's missing or insufficient\", default=\"No gaps identified\")\n", "\n", "class StrategicRecommendations(BaseModel):\n", "    recommendations: List[str]\n", "\n", "class CheckpointRelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for individual checkpoint relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers this specific checkpoint question.\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of your score for this specific checkpoint\",\n", "        default=\"\"\n", "    )\n", "\n", "class SummaryEvaluationResult(BaseModel):\n", "    \"\"\"Schema for individual summary evaluation result in a batch\"\"\"\n", "    summary_id: str = Field(description=\"Unique identifier for the summary\")\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well this summary answers the checkpoint question\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of the relevance score for this summary\",\n", "        default=\"\"\n", "    )\n", "\n", "class BatchedSummaryEvaluation(BaseModel):\n", "    \"\"\"Schema for batched summary evaluation against a single checkpoint\"\"\"\n", "    checkpoint_question: str = Field(description=\"The checkpoint question being evaluated\")\n", "    evaluations: List[SummaryEvaluationResult] = Field(\n", "        description=\"List of evaluation results for each summary in the batch\",\n", "        min_items=1\n", "    )\n", "\n", "class CritiqueEvaluation(BaseModel):\n", "    \"\"\"Schema for Pre-IND critique evaluation\"\"\"\n", "    overall_rating: float = Field(\n", "        description=\"Overall rating from 1-10 for the Pre-IND section quality\",\n", "        ge=1.0,\n", "        le=10.0\n", "    )\n", "    key_strengths: List[str] = Field(\n", "        description=\"List of main strengths identified in the Pre-IND section\",\n", "        default=[]\n", "    )\n", "    critical_issues: List[str] = Field(\n", "        description=\"List of critical issues that need to be addressed\",\n", "        default=[]\n", "    )\n", "    required_improvements: List[str] = Field(\n", "        description=\"List of specific improvements required\",\n", "        default=[]\n", "    )\n", "    additional_recommendations: List[str] = Field(\n", "        description=\"List of additional recommendations for enhancement\",\n", "        default=[]\n", "    )\n", "\n", "class ChunkEvaluationResult(BaseModel):\n", "    \"\"\"Schema for individual chunk evaluation result in a batch\"\"\"\n", "    chunk_id: str = Field(description=\"Unique identifier for the chunk\")\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well this chunk answers the checkpoint question\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of the relevance score for this chunk\",\n", "        default=\"\"\n", "    )\n", "\n", "class BatchedChunkEvaluation(BaseModel):\n", "    \"\"\"Schema for batched chunk evaluation against a single checkpoint\"\"\"\n", "    checkpoint_question: str = Field(description=\"The checkpoint question being evaluated\")\n", "    evaluations: List[ChunkEvaluationResult] = Field(\n", "        description=\"List of evaluation results for each chunk in the batch\",\n", "        min_items=1\n", "    )\n", "\n", "class DrugTypeScore(BaseModel):\n", "    \"\"\"Schema for individual drug type score with justification\"\"\"\n", "    drug_type_name: str = Field(description=\"Name of the drug type (e.g., 'Small-Molecule Drugs')\")\n", "    score: float = Field(\n", "        description=\"Confidence score from 0-10 for this drug type classification\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Detailed reasoning for this score based on evidence from the input documents\",\n", "        default=\"\"\n", "    )\n", "    key_evidence: List[str] = Field(\n", "        description=\"Specific text excerpts from documents that support this classification\",\n", "        default=[]\n", "    )\n", "\n", "class DrugTypeClassification(BaseModel):\n", "    \"\"\"Schema for comprehensive drug type classification result\"\"\"\n", "    drug_type_scores: List[DrugTypeScore] = Field(\n", "        description=\"Scores and justifications for all 6 drug types\",\n", "        min_items=6,\n", "        max_items=6\n", "    )\n", "    primary_classification: str = Field(\n", "        description=\"The most likely drug type based on highest confidence score\"\n", "    )\n", "    confidence_level: str = Field(\n", "        description=\"Overall confidence in the primary classification: 'high' (score ≥8), 'medium' (score 6-7.9), 'low' (score 4-5.9), or 'uncertain' (score <4)\",\n", "        default=\"uncertain\"\n", "    )\n", "    overall_justification: str = Field(\n", "        description=\"Summary explanation of the primary classification decision\",\n", "        default=\"\"\n", "    )\n", "\n", "class AggregatedDrugTypeClassification(BaseModel):\n", "    \"\"\"Schema for final aggregated drug type classification from multiple batches\"\"\"\n", "    final_classification: str = Field(description=\"Final determined drug type\")\n", "    aggregated_scores: List[DrugTypeScore] = Field(\n", "        description=\"Aggregated scores across all batches for each drug type\",\n", "        min_items=6,\n", "        max_items=6\n", "    )\n", "    confidence_level: str = Field(description=\"Final confidence level\")\n", "    batch_count: int = Field(description=\"Number of batches processed\")\n", "    consensus_strength: str = Field(\n", "        description=\"Strength of consensus across batches: 'strong', 'moderate', 'weak'\",\n", "        default=\"weak\"\n", "    )\n", "    final_justification: str = Field(\n", "        description=\"Final reasoning for the classification decision\",\n", "        default=\"\"\n", "    )\n", "\n", "def get_mongodb_collection_from_referenced_guidelines(ich_guidelines: List[str]):\n", "    checklist = {\n", "        \"Q\": <PERSON><PERSON><PERSON>,\n", "        \"E\": <PERSON><PERSON><PERSON>,\n", "        \"S\": <PERSON><PERSON><PERSON>,\n", "        \"M\": <PERSON><PERSON><PERSON>\n", "    }\n", "\n", "    collection_mapping_for_summaries = {\n", "        \"Q\": \"quality_docs_summaries\",\n", "        \"E\": \"efficacy_docs_summaries\",\n", "        \"S\": \"safety_docs_summaries\",\n", "        \"M\": \"multidisciplinary_docs_summaries\"\n", "    }\n", "\n", "    collection_mapping_for_chunks = {\n", "        \"Q\": \"quality_docs_chunks\",\n", "        \"E\": \"efficacy_docs_chunks\",\n", "        \"S\": \"safety_docs_chunks\",\n", "        \"M\": \"multidisciplinary_docs_chunks\"\n", "    }\n", "\n", "    key_name_mapping = {\n", "        \"Q\": \"quality_guideline\",\n", "        \"E\": \"efficacy_guideline\",\n", "        \"S\": \"safety_guideline\",\n", "        \"M\": \"multidisciplinary_guideline\"\n", "    }\n", "\n", "    for ich_guideline in ich_guidelines:\n", "        if ich_guideline.startswith(\"Q\"):\n", "            checklist[\"Q\"] = True\n", "        elif ich_guideline.startswith(\"E\"):\n", "            checklist[\"E\"] = True\n", "        elif ich_guideline.startswith(\"S\"):\n", "            checklist[\"S\"] = True\n", "        elif ich_guideline.startswith(\"M\"):\n", "            checklist[\"M\"] = True\n", "\n", "    mapping_to_return = {\n", "        \"summaries\": [],\n", "        \"chunks\": [],\n", "        \"key_name\": []\n", "    }\n", "\n", "    for key, value in checklist.items():\n", "        if value:\n", "            mapping_to_return[\"summaries\"].append(collection_mapping_for_summaries[key])\n", "            mapping_to_return[\"chunks\"].append(collection_mapping_for_chunks[key])\n", "            mapping_to_return[\"key_name\"].append(key_name_mapping[key])\n", "    \n", "    return mapping_to_return\n", "\n", "def get_mongodb_collection_from_summary(summary: dict[str, Any]):\n", "    all_keys = summary.keys()\n", "    ich_ref_list = []\n", "    for key in all_keys:\n", "        if key.startswith(\"quality_guideline\"):\n", "            ich_ref_list.append(key[0].upper())\n", "        elif key.startswith(\"efficacy_guideline\"):\n", "            ich_ref_list.append(key[0].upper())\n", "        elif key.startswith(\"safety_guideline\"):\n", "            ich_ref_list.append(key[0].upper())\n", "        elif key.startswith(\"multidisciplinary_guideline\"):\n", "            ich_ref_list.append(key[0].upper())\n", "    collection_mapping = get_mongodb_collection_from_referenced_guidelines(ich_ref_list)\n", "    summary_name = collection_mapping[\"summaries\"][0]\n", "    chunk_name = collection_mapping[\"chunks\"][0]\n", "    key_name = collection_mapping[\"key_name\"][0]\n", "    return summary_name, chunk_name, key_name\n", "\n", "def format_list_as_markdown(items: List[str], title: str = None) -> str:\n", "    \"\"\"\n", "    Format a list of items as clean markdown bullet points.\n", "    \n", "    Args:\n", "        items: List of items to format\n", "        title: Optional title for the list\n", "        \n", "    Returns:\n", "        Markdown-formatted string\n", "    \"\"\"\n", "    if not items:\n", "        return \"None available\"\n", "    \n", "    formatted = \"\"\n", "    if title:\n", "        formatted += f\"**{title}:**\\n\"\n", "    \n", "    for item in items:\n", "        formatted += f\"- {item}\\n\"\n", "    \n", "    return formatted.strip()\n", "\n", "def format_dict_as_markdown(data: Dict, title: str = None) -> str:\n", "    \"\"\"\n", "    Format a dictionary as clean markdown.\n", "    \n", "    Args:\n", "        data: Dictionary to format\n", "        title: Optional title for the section\n", "        \n", "    Returns:\n", "        Markdown-formatted string\n", "    \"\"\"\n", "    if not data:\n", "        return \"None available\"\n", "    \n", "    formatted = \"\"\n", "    if title:\n", "        formatted += f\"**{title}:**\\n\"\n", "    \n", "    for key, value in data.items():\n", "        if isinstance(value, list):\n", "            formatted += f\"- **{key}:** {', '.join(str(v) for v in value)}\\n\"\n", "        elif isinstance(value, dict):\n", "            formatted += f\"- **{key}:**\\n\"\n", "            for sub_key, sub_value in value.items():\n", "                formatted += f\"  - {sub_key}: {sub_value}\\n\"\n", "        else:\n", "            formatted += f\"- **{key}:** {value}\\n\"\n", "\n", "    return formatted.strip()\n", "\n", "def format_evidence_batches_as_markdown(batches: List[str]) -> str:\n", "    \"\"\"\n", "    Format evidence batches as clean markdown sections.\n", "    \n", "    Args:\n", "        batches: List of evidence batch strings\n", "        \n", "    Returns:\n", "        Markdown-formatted string with clear batch separation\n", "    \"\"\"\n", "    if not batches:\n", "        return \"No batches available\"\n", "    \n", "    formatted = \"\"\n", "    for i, batch in enumerate(batches, 1):\n", "        formatted += f\"### Batch {i}\\n\\n{batch}\\n\\n\"\n", "    \n", "    return formatted.strip()\n", "\n", "def format_category_summaries_as_markdown(categories: List[Dict]) -> str:\n", "    \"\"\"\n", "    Format category summaries as clean markdown sections.\n", "    \n", "    Args:\n", "        categories: List of category summary dictionaries\n", "        \n", "    Returns:\n", "        Markdown-formatted string\n", "    \"\"\"\n", "    if not categories:\n", "        return \"No categories available\"\n", "    \n", "    formatted = \"\"\n", "    for category in categories:\n", "        category_name = category.get('category', 'Unknown Category')\n", "        score_range = category.get('score_range', [0, 0])\n", "        checkpoint_count = category.get('checkpoint_count', 0)\n", "        supporting_evidence = category.get('supporting_evidence', 'None available')\n", "        gap_analysis = category.get('gap_analysis', 'None available')\n", "        \n", "        formatted += f\"### {category_name.replace('_', ' ').title()}\\n\"\n", "        formatted += f\"- **Score Range:** {score_range[0]}-{score_range[1]}\\n\"\n", "        formatted += f\"- **Number of Checkpoints:** {checkpoint_count}\\n\\n\"\n", "        \n", "        formatted += f\"**Supporting Evidence:**\\n{supporting_evidence}\\n\\n\"\n", "        formatted += f\"**Gap Analysis:**\\n{gap_analysis}\\n\\n\"\n", "        formatted += \"---\\n\\n\"\n", "    \n", "    return formatted.strip()\n", "\n", "async def check_if_llm_is_available(model, base_url, semaphore):\n", "    try:\n", "        agent = Agent(\n", "            model=model,\n", "            system_prompt=\"You are a helpful assistant.\",\n", "            model_settings=get_model_settings(base_url, 1),\n", "            retries=3\n", "        )\n", "        prompt = \"Hello World!\"\n", "\n", "        result = None\n", "        if \"anthropic\" in base_url:\n", "            # Use the semaphore properly for streaming calls\n", "            async with semaphore:\n", "                async with agent.run_stream(prompt) as response:\n", "                    result = await response.get_output()\n", "        else:    \n", "            result = await rate_limited_agent_call(agent, prompt, semaphore)\n", "            result = result.output\n", "\n", "        print(f\"LLM {model.model_name} is available, response: {result}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error in checking if LLM {model} is available: {e}\")\n", "        return False\n", "\n", "def write_to_json(data, filename):\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, indent=2, ensure_ascii=False)\n", "    print(f\"JSON saved to {filename}\")\n", "\n", "def read_json(filename):\n", "    with open(filename, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "async def get_mongodb_client():\n", "    \"\"\"Get MongoDB client connection.\"\"\"\n", "    return AsyncMongoClient(os.getenv(\"MONGO_DB_URL\"))\n", "\n", "def extract_content_from_tags(content: str, tag_name: str) -> str:\n", "    \"\"\"\n", "    Extract content from specific XML-style tags if present, otherwise return content as is.\n", "    \n", "    Args:\n", "        content: The raw content from the LLM response\n", "        tag_name: The tag name to look for (without < > brackets)\n", "        \n", "    Returns:\n", "        Extracted content or original content if tags not found\n", "    \"\"\"\n", "    if not content:\n", "        return content\n", "        \n", "    # Look for specified tags (case-insensitive)\n", "    import re\n", "    tag_match = re.search(fr'<{tag_name}>(.*?)</{tag_name}>', content, re.DOTALL | re.IGNORECASE)\n", "    \n", "    if tag_match:\n", "        # Extract and clean the content\n", "        extracted = tag_match.group(1).strip()\n", "        return extracted\n", "    else:\n", "        # Return original content if no tags found\n", "        return content\n", "\n", "def extract_and_parse_response(content: str, expected_output_type: str, tag_name: str = None) -> Any:\n", "    \"\"\"\n", "    Universal function to extract content from tags and parse based on expected output type.\n", "    \n", "    Args:\n", "        content: Raw LLM response content\n", "        expected_output_type: Type of expected output ('json', 'text', 'markdown', 'structured')\n", "        tag_name: Optional tag name to extract from (e.g., 'final_checkpoints', 'final_recommendations')\n", "        \n", "    Returns:\n", "        Processed content based on expected output type\n", "    \"\"\"\n", "    if not content:\n", "        return content\n", "    \n", "    # Step 1: Extract from tags if tag_name is provided\n", "    if tag_name:\n", "        content = extract_content_from_tags(content, tag_name)\n", "    \n", "    # Step 2: Process based on expected output type\n", "    if expected_output_type == 'json':\n", "        return parse_json_response(content)\n", "    elif expected_output_type in ['text', 'markdown']:\n", "        return content.strip()\n", "    elif expected_output_type == 'structured':\n", "        # For structured output, return as-is (already processed by pydantic)\n", "        return content\n", "    else:\n", "        # Default: return as-is\n", "        return content\n", "\n", "def parse_json_response(response_text: str) -> Any:\n", "    \"\"\"\n", "    Parse a JSON response that may be wrapped in backticks.\n", "    \n", "    Args:\n", "        response_text: The response text to parse\n", "        \n", "    Returns:\n", "        Any: The parsed JSON object\n", "    \"\"\"\n", "    # Remove any markdown code block syntax\n", "    response_text = re.sub(r'```json\\n?', '', response_text)\n", "    response_text = re.sub(r'```\\n?', '', response_text)\n", "    response_text = response_text.strip()\n", "    \n", "    try:\n", "        # print(f\"parse_json_response, response_text: {json.loads(response_text)}\")\n", "        return json.loads(response_text)\n", "    except json.JSONDecodeError:\n", "        # If the response is not valid JSON, try to extract a list from the text\n", "        # Look for lines that start with numbers, bullets, or dashes\n", "        lines = re.findall(r'^[\\d\\-\\*\\.]+\\.?\\s*(.+)$', response_text, re.MULTILINE)\n", "        if lines:\n", "            return lines\n", "        # If no lines found, split by newlines and clean up\n", "        # print(f\"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\\n') if line.strip()]}\")\n", "        return [line.strip() for line in response_text.split('\\n') if line.strip()]\n", "    \n", "# Global cache for token calculations\n", "_token_cache = {}\n", "\n", "# Global cache for enhanced section context\n", "_enhanced_context_cache = {}\n", "\n", "def calculate_number_of_tokens(text: str) -> int:\n", "    \"\"\"\n", "    Calculate number of tokens for given text with caching for performance.\n", "    Uses content hash as cache key to avoid memory bloat.\n", "    \n", "    Args:\n", "        text: The text to tokenize\n", "        \n", "    Returns:\n", "        int: Number of tokens\n", "    \"\"\"\n", "    if not text:\n", "        return 0\n", "    \n", "    # Create a hash of the content for cache key (more memory efficient than storing full text)\n", "    content_hash = hashlib.md5(text.encode('utf-8')).hexdigest()\n", "    \n", "    # Check cache first\n", "    if content_hash in _token_cache:\n", "        return _token_cache[content_hash]\n", "    \n", "    global tokenizer\n", "    if tokenizer is None:\n", "        # Load tokenizer for Mistral model (this could also be cached globally)\n", "        tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-32B\")\n", "\n", "    def count_tokens(text):\n", "        tokens = tokenizer.encode(text, add_special_tokens=False)\n", "        return len(tokens)\n", "\n", "    token_count = count_tokens(text)\n", "    \n", "    # Cache the result\n", "    _token_cache[content_hash] = token_count\n", "    \n", "    # Prevent cache from growing too large (keep last 10000 calculations)\n", "    if len(_token_cache) > 10000:\n", "        # Remove oldest 2000 entries to prevent frequent cleanup\n", "        oldest_keys = list(_token_cache.keys())[:2000]\n", "        for key in oldest_keys:\n", "            del _token_cache[key]\n", "    \n", "    return token_count\n", "\n", "def create_summary_batches(summaries: List[Dict], checkpoint_question: str, max_batch_tokens: int = BATCH_SIZE_MAX_TOKENS, max_summaries_per_batch: int = BATCH_SIZE_MAX_SUMMARIES) -> List[List[Dict]]:\n", "    \"\"\"\n", "    Create batches of summaries for efficient evaluation against a single checkpoint.\n", "    \n", "    Args:\n", "        summaries: List of summary dictionaries with content and metadata\n", "        checkpoint_question: The checkpoint question (used for token calculation)\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_summaries_per_batch: Maximum number of summaries per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of summaries\n", "    \"\"\"\n", "    if not summaries:\n", "        return []\n", "    \n", "    # Calculate base tokens for checkpoint and system prompt (estimated)\n", "    base_tokens = calculate_number_of_tokens(checkpoint_question) + 1000  # Add buffer for system prompt\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for summary in summaries:\n", "        # Calculate tokens for this summary including XML tags\n", "        summary_content = summary.get('content', '')\n", "        summary_id = str(summary.get('_id', 'unknown'))\n", "        \n", "        # Estimate tokens including XML formatting\n", "        summary_tokens = calculate_number_of_tokens(f\"<summary_{len(current_batch) + 1}>\\n{summary_id}\\n{summary_content}\\n</summary_{len(current_batch) + 1}>\")\n", "        \n", "        # Check if adding this summary would exceed limits\n", "        if (current_tokens + summary_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_summaries_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add summary to current batch\n", "        current_batch.append(summary)\n", "        current_tokens += summary_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n", "\n", "def create_chunk_batches(chunks: List[Dict], checkpoint_question: str, max_batch_tokens: int = CHUNK_BATCH_SIZE_MAX_TOKENS, max_chunks_per_batch: int = CHUNK_BATCH_SIZE_MAX_CHUNKS) -> List[List[Dict]]:\n", "    \"\"\"\n", "    Create batches of chunks for efficient evaluation against a single checkpoint.\n", "    \n", "    Args:\n", "        chunks: List of chunk dictionaries with content and metadata\n", "        checkpoint_question: The checkpoint question (used for token calculation)\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_chunks_per_batch: Maximum number of chunks per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of chunks\n", "    \"\"\"\n", "    if not chunks:\n", "        return []\n", "    \n", "    # Calculate base tokens for checkpoint and system prompt (estimated)\n", "    base_tokens = calculate_number_of_tokens(checkpoint_question) + 1000  # Add buffer for system prompt\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for chunk in chunks:\n", "        # Calculate tokens for this chunk including XML tags\n", "        chunk_content = chunk.get('content', '')\n", "        chunk_id = str(chunk.get('_id', 'unknown'))\n", "        \n", "        # Estimate tokens including XML formatting\n", "        chunk_tokens = calculate_number_of_tokens(f\"<chunk_{len(current_batch) + 1}>\\n{chunk_id}\\n{chunk_content}\\n</chunk_{len(current_batch) + 1}>\")\n", "        \n", "        # Check if adding this chunk would exceed limits\n", "        if (current_tokens + chunk_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_chunks_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add chunk to current batch\n", "        current_batch.append(chunk)\n", "        current_tokens += chunk_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n", "\n", "async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):\n", "    \"\"\"\n", "    Wrapper function that retries an async LLM API call when the response is empty.\n", "    \n", "    Args:\n", "        async_func: The async function to call (usually an LLM API call)\n", "        *args: Positional arguments to pass to async_func\n", "        max_retries: Maximum number of retry attempts (default: 3)\n", "        **kwargs: Keyword arguments to pass to async_func\n", "        \n", "    Returns:\n", "        The result of the async_func call, ensuring it's not empty\n", "        \n", "    Raises:\n", "        Exception: If max_retries is reached and the response is still empty\n", "    \"\"\"\n", "    # Create logs directory if it doesn't exist\n", "    log_dir = \"error_logs\"\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    # Extract the function name for logging purposes\n", "    func_name = async_func.__name__ if hasattr(async_func, \"__name__\") else \"unknown_function\"\n", "    \n", "    # Try to get the caller's name from the stack\n", "    try:\n", "        caller_frame = inspect.currentframe().f_back\n", "        caller_name = caller_frame.f_code.co_name if caller_frame else \"unknown_caller\"\n", "    except Exception:\n", "        caller_name = \"unknown_caller\"\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            result = await async_func(*args, **kwargs)\n", "            \n", "            # Check if result is None, empty string, just whitespace, or contains tool calls\n", "            is_empty = result is None or (isinstance(result, str) and result.strip() == \"\")\n", "            contains_tool_call = False\n", "            \n", "            if isinstance(result, str):\n", "                # Check for tool_call patterns\n", "                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)\n", "                contains_tool_call_text = 'tool_call' in result.lower()\n", "                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text\n", "            \n", "            if is_empty or contains_tool_call:\n", "                if is_empty:\n", "                    reason = \"empty response\"\n", "                    error_type = 'empty_response'\n", "                elif contains_tool_call:\n", "                    reason = \"response contains tool calls\"\n", "                    error_type = 'tool_call_response'\n", "                \n", "                print(f\"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...\")\n", "                \n", "                # Get debug information to log\n", "                debug_info = {\n", "                    'error_type': error_type,\n", "                    'function': func_name,\n", "                    'caller': caller_name,\n", "                    'attempt': attempt + 1,\n", "                    'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]\n", "                }\n", "                \n", "                # Extract prompt information based on different API patterns\n", "                # For the direct messages pattern in kwargs\n", "                if 'messages' in kwargs:\n", "                    debug_info['messages'] = kwargs['messages']\n", "                    \n", "                # For the pattern where the func is a closure with local make_api_call\n", "                # Try to get source code of the async_func to check for patterns\n", "                try:\n", "                    source = inspect.getsource(async_func)\n", "                    if \"chat.completions.create\" in source:\n", "                        debug_info['api_pattern'] = \"chat_completions_closure\"\n", "                except Exception:\n", "                    pass\n", "                \n", "                # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "                try:\n", "                    if caller_frame:\n", "                        caller_locals = caller_frame.f_locals\n", "                        # Capture common patterns in this codebase\n", "                        if 'system_prompt' in caller_locals:\n", "                            debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                        # If this is using the OpenAI client pattern, get the model too\n", "                        if 'model' in caller_locals:\n", "                            debug_info['model'] = caller_locals['model']\n", "                        # For the antropic calls\n", "                        if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                        elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                        elif 'M<PERSON>' in caller_locals:\n", "                            debug_info['model'] = caller_locals['MODEL']\n", "                except Exception as e:\n", "                    debug_info['frame_inspection_error'] = str(e)\n", "                \n", "                # Save the debug information\n", "                timestamp = int(time.time())\n", "                log_filename = f\"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "                \n", "                try:\n", "                    with open(log_filename, 'w', encoding='utf-8') as f:\n", "                        json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                    print(f\"Logged empty response details to {log_filename}\")\n", "                except Exception as log_error:\n", "                    print(f\"Failed to log empty response details: {str(log_error)}\")\n", "                \n", "                # Continue to the next retry attempt\n", "                continue\n", "                \n", "            # If we get here, we have a non-empty response\n", "            return result\n", "            \n", "        except Exception as e:\n", "            error_type = type(e).__name__\n", "            error_msg = str(e)\n", "            print(f\"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}\")\n", "            \n", "            # Get debug information to log\n", "            debug_info = {\n", "                'error_type': error_type,\n", "                'error_message': error_msg,\n", "                'function': func_name,\n", "                'caller': caller_name,\n", "                'attempt': attempt + 1,\n", "                'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                'stack_trace': traceback.format_exc()\n", "            }\n", "            \n", "            # Extract prompt information based on different API patterns\n", "            # For the direct messages pattern in kwargs\n", "            if 'messages' in kwargs:\n", "                debug_info['messages'] = kwargs['messages']\n", "                \n", "            # For the pattern where the func is a closure with local make_api_call\n", "            # Try to get source code of the async_func to check for patterns\n", "            try:\n", "                source = inspect.getsource(async_func)\n", "                if \"chat.completions.create\" in source:\n", "                    debug_info['api_pattern'] = \"chat_completions_closure\"\n", "            except Exception:\n", "                pass\n", "            \n", "            # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "            try:\n", "                if caller_frame:\n", "                    caller_locals = caller_frame.f_locals\n", "                    # Capture common patterns in this codebase\n", "                    if 'system_prompt' in caller_locals:\n", "                        debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                    # If this is using the OpenAI client pattern, get the model too\n", "                    if 'model' in caller_locals:\n", "                        debug_info['model'] = caller_locals['model']\n", "                    # For the antropic calls\n", "                    if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                    elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                    elif 'M<PERSON>' in caller_locals:\n", "                        debug_info['model'] = caller_locals['MODEL']\n", "            except Exception as frame_error:\n", "                debug_info['frame_inspection_error'] = str(frame_error)\n", "            \n", "            # Save the debug information\n", "            timestamp = int(time.time())\n", "            log_filename = f\"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "            \n", "            try:\n", "                with open(log_filename, 'w', encoding='utf-8') as f:\n", "                    json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                print(f\"Logged error details to {log_filename}\")\n", "            except Exception as log_error:\n", "                print(f\"Failed to log error details: {str(log_error)}\")\n", "            \n", "            if attempt == max_retries - 1:\n", "                # If we've exhausted all retries and still have an error\n", "                print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "                return None\n", "            \n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** attempt))\n", "    \n", "    # If we've exhausted all retries and still have an empty response\n", "    print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "    return None\n", "\n", "# MongoDB client and LLM availability checks will be handled in async functions when needed\n", "mongodb_client = await get_mongodb_client()\n", "await check_if_llm_is_available(generator_model, GENERATOR_MODEL_BASE_URL, api_semaphore_for_generator)\n", "await check_if_llm_is_available(critique_model, CRITIQUE_MODEL_BASE_URL, api_semaphore_for_critique)\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["pre_ind_briefing_template = read_json(\"/Users/<USER>/projects/scalegen/MedNova/structured jsons/pre_ind_briefing_template.json\")\n", "\n", "doc_to_work_on = pre_ind_briefing_template[\"1\"][\"1.3\"]\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_outer_section_info(section_number: str) -> Dict:\n", "    \"\"\"\n", "    Extract outer section information given a section number from the briefing template.\n", "    \n", "    Args:\n", "        section_number: Section like \"5.3\" or \"1.2.3\" or \"5\"\n", "        \n", "    Returns:\n", "        Dict containing outer section info or None if no outer section or section not found\n", "    \"\"\"\n", "    if not section_number:\n", "        return None\n", "    \n", "    # Handle sections that are already top-level (no dots)\n", "    if '.' not in section_number:\n", "        return None  # No outer section for top-level sections like \"5\"\n", "    \n", "    # Parse section number to get outer section\n", "    # \"5.3\" -> \"5\", \"1.2.3\" -> \"1\", \"3.2.1.4\" -> \"3\"\n", "    outer_section_number = section_number.split('.')[0]\n", "    \n", "    def find_section_recursively(obj, target_section):\n", "        \"\"\"Recursively search for a section in the nested JSON structure.\"\"\"\n", "        if isinstance(obj, dict):\n", "            # Check if current object has the target section as a key\n", "            if target_section in obj:\n", "                section_obj = obj[target_section]\n", "                if isinstance(section_obj, dict) and 'section' in section_obj:\n", "                    return {\n", "                        'section': section_obj.get('section', ''),\n", "                        'title': section_obj.get('title', ''),\n", "                        'description': section_obj.get('description', ''),\n", "                        'referenced_ich_guidelines': section_obj.get('referenced_ich_guidelines', [])\n", "                    }\n", "            \n", "            # Recursively search in all nested dictionaries\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    result = find_section_recursively(value, target_section)\n", "                    if result:\n", "                        return result\n", "        \n", "        return None\n", "    \n", "    # Navigate JSON structure to find the outer section\n", "    try:\n", "        return find_section_recursively(pre_ind_briefing_template, outer_section_number)\n", "    except Exception as e:\n", "        print(f\"Warning: Could not extract outer section info for {section_number}: {e}\")\n", "    \n", "    return None\n", "\n", "def get_enhanced_section_context(section_number: str, title: str, description: str, \n", "                               referenced_ich_guidelines: List[str]) -> Dict:\n", "    \"\"\"\n", "    Get enhanced section context including outer section information if available.\n", "    Uses caching to avoid rebuilding the same context repeatedly.\n", "    \n", "    Args:\n", "        section_number: Current section number\n", "        title: Current section title\n", "        description: Current section description\n", "        referenced_ich_guidelines: Current section ICH guidelines\n", "        \n", "    Returns:\n", "        Dict containing both current and outer section context\n", "    \"\"\"\n", "    # Create cache key from all parameters\n", "    cache_key = f\"{section_number}|{title}|{description}|{','.join(sorted(referenced_ich_guidelines))}\"\n", "    \n", "    # Check cache first\n", "    if cache_key in _enhanced_context_cache:\n", "        return _enhanced_context_cache[cache_key]\n", "    \n", "    # Build context if not cached\n", "    outer_section_info = extract_outer_section_info(section_number)\n", "    \n", "    context = {\n", "        'current_section': {\n", "            'section': section_number,\n", "            'title': title,\n", "            'description': description,\n", "            'referenced_ich_guidelines': referenced_ich_guidelines\n", "        },\n", "        'outer_section': outer_section_info,\n", "        'has_outer_section': outer_section_info is not None\n", "    }\n", "    \n", "    # Cache the result\n", "    _enhanced_context_cache[cache_key] = context\n", "    \n", "    # Prevent cache from growing too large (keep last 1000 contexts)\n", "    if len(_enhanced_context_cache) > 1000:\n", "        # Remove oldest 200 entries to prevent frequent cleanup\n", "        oldest_keys = list(_enhanced_context_cache.keys())[:200]\n", "        for key in oldest_keys:\n", "            del _enhanced_context_cache[key]\n", "    \n", "    return context\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoint questions from a single individual summary.\n", "    \n", "    Args:\n", "        summary_content: The content of the individual summary\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions generated from the individual summary\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive checklist of specific questions based on the provided summary for a section of a Pre-IND document.\"\n", "            \n", "            prompt = f\"\"\"\n", "First, review the following information:\n", "\n", "Summary Content:\n", "<summary_content>\n", "{summary_content}\n", "</summary_content>\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            prompt += f\"\"\"\n", "Current Section:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                prompt += f\"\"\"\n", "Context: You are generating checkpoints for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            prompt += \"\"\"Before creating the checklist, analyze the provided information and formulate your approach inside your thinking block using <checklist_preparation> tags:\n", "\n", "1. Summarize the key points from the summary content.\n", "2. Quote relevant phrases from the summary content.\n", "3. Identify the main topics and requirements mentioned in the instructions.\n", "4. List any specific technical or regulatory aspects that need to be addressed.\n", "5. Note any particular areas of focus (e.g., physical characteristics, chemical properties, manufacturing processes, quality control measures, stability data, packaging specifications, compatibility information).\n", "6. Brainstorm potential questions based on the quoted phrases and identified topics.\n", "7. Ensure alignment with ICH guidelines.\n", "8. Consider how to phrase each point as a specific, answerable question.\n", "\n", "Now, create a detailed checklist of specific questions based on your analysis. The checklist should:\n", "\n", "1. Include all key requirements and recommendations from the summary expressed as specific questions.\n", "2. Cover all aspects mentioned in the summary.\n", "3. <PERSON><PERSON>tly adhere to the instructions.\n", "4. Be specific enough to clearly determine if an input document addresses each point.\n", "5. Format EACH checkpoint as a question that can be answered with yes/no or specific information.\n", "6. Focus on technical and regulatory content needed for Pre-IND documentation.\n", "7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.\n", "8. Cover stability data, packaging specifications, and compatibility information where appropriate.\n", "9. Include questions about the validation and verification methods used.\n", "10. Use the material prepared in the thinking block to ensure comprehensive coverage.\n", "\n", "After creating the checklist, review it to ensure:\n", "- All points from the summary are covered\n", "- Questions align with the instructions\n", "- Each question is specific and answerable\n", "- The checklist is comprehensive and accurate\n", "\n", "Present your final checklist after this review. Your output should consist only of the checklist and should not duplicate or rehash any of the work you did in the thinking block.\n", "\"\"\"\n", "            \n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            response = await rate_limited_agent_call(checkpoint_list_agent, prompt, api_semaphore_for_generator)\n", "\n", "            # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Generating basic checkpoints from summary as fallback.\")\n", "                # Generate basic checkpoints as fallback\n", "                try:\n", "                    # Extract key sentences from summary as basic checkpoints\n", "                    sentences = [s.strip() + \"?\" for s in summary_content.split(\".\") if len(s.strip()) > 20]\n", "                    # Convert statements to questions where possible\n", "                    questions = []\n", "                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload\n", "                        if not sentence.endswith(\"?\"):\n", "                            questions.append(f\"Does the document address: {sentence}?\")\n", "                        else:\n", "                            questions.append(sentence)\n", "                    return questions\n", "                except Exception:\n", "                    print(\"Fallback checkpoint generation failed. Returning empty list.\")\n", "                    return []\n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n", "async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoints incrementally from a list of individual summaries.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        list_of_individual_summary_contents: List of individual summary contents\n", "        \n", "    Returns:\n", "        List[str]: A comprehensive list of checkpoints generated from all individual summaries\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not list_of_individual_summary_contents:\n", "        return []\n", "        \n", "    # Handle single summary case\n", "    if len(list_of_individual_summary_contents) == 1:\n", "        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)\n", "    \n", "    # Maximum token limit for checkpoint list batches\n", "    \n", "    # Generate initial checkpoints from each summary in parallel using asyncio.gather\n", "    current_checkpoints = await asyncio.gather(*[\n", "        _generate_checkpoints_from_one_summary(summary, section_info) \n", "        for summary in list_of_individual_summary_contents\n", "    ])\n", "    \n", "    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):\n", "        \"\"\"Helper function to combine multiple checkpoint lists while preserving all unique checkpoints.\"\"\"\n", "        total_lists = len(checkpoint_lists)\n", "        print(f\"\\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints\")\n", "        \n", "        # Calculate token count for all lists combined\n", "        combined_text = \"\\n\".join([\"\\n\".join(cp) for cp in checkpoint_lists])\n", "        # total_tokens = calculate_number_of_tokens(combined_text)\n", "        # print(f\"Total input tokens for checkpoint merging: {total_tokens}\")\n", "        \n", "        # Get enhanced section context including outer section info\n", "        enhanced_context = get_enhanced_section_context(\n", "            section_info.get('section', ''),\n", "            section_info.get('title', ''),\n", "            section_info.get('description', ''),\n", "            section_info.get('referenced_ich_guidelines', [])\n", "        )\n", "        \n", "        system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive merged list of checkpoint questions for a specific section of a Pre-IND document.\"\n", "        \n", "        user_prompt = f\"\"\"\n", "First, review the following information:\n", "\n", "\"\"\"\n", "        \n", "        # Add outer section context if available\n", "        if enhanced_context['has_outer_section']:\n", "            outer = enhanced_context['outer_section']\n", "            user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "        \n", "        # Add current section information\n", "        current = enhanced_context['current_section']\n", "        user_prompt += f\"\"\"\n", "Current Section:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "        \n", "        # Add context explanation if there's an outer section\n", "        if enhanced_context['has_outer_section']:\n", "            user_prompt += f\"\"\"\n", "Context: You are merging checkpoint lists for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "You will be processing {total_lists} lists of checkpoint questions. \n", "\n", "\"\"\"\n", "        \n", "        # Add each checkpoint list\n", "        for i, checkpoints in enumerate(checkpoint_lists, 1):\n", "            user_prompt += f\"\"\"\n", "Checkpoint List {i}:\n", "<checkpoints_{i}>\n", "{format_list_as_markdown(checkpoints)}\n", "</checkpoints_{i}>\n", "\n", "\"\"\"\n", "\n", "        user_prompt += f\"\"\"\n", "Your goal is to merge these lists into a single comprehensive list while adhering to the following requirements:\n", "\n", "1. Preserve ALL unique questions from all lists\n", "2. Remove any duplicate or redundant questions\n", "3. Ensure the merged list is comprehensive and covers all aspects\n", "4. Maintain the specificity and clarity of each question\n", "5. Keep the question format consistent (e.g., \"Does the document mention...\")\n", "6. Ensure each question focuses on a single specific point\n", "7. Group related questions together when possible\n", "8. Strictly adhere to the referenced list of instructions\n", "9. Keep all questions focused on technical regulatory content for Pre-IND documentation\n", "\n", "Before producing the final merged list, wrap your analysis inside <checkpoint_analysis> tags in your thinking block. In this analysis:\n", "1. Summarize the key points from the section_info, section_title.\n", "2. Analyze the first list of checkpoints, noting any patterns or themes.\n", "3. Plan how you will approach merging subsequent lists (even though we only have the first list now).\n", "4. Ensure all requirements are met and pay special attention to accuracy and best practices in regulatory documentation.\n", "\n", "After your analysis, provide the merged list of checkpoint questions. \n", "\n", "Your final output should consist only of the merged list of checkpoint questions and should not duplicate or rehash any of the work you did in the checkpoint analysis section.\n", "        \"\"\"\n", "        \n", "        # system_tokens = calculate_number_of_tokens(system_prompt)\n", "        # user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "        checkpoint_list_agent = Agent(\n", "            model=generator_model,\n", "            system_prompt=system_prompt,\n", "            output_type=CheckpointList,\n", "            model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "            retries=3\n", "        )\n", "\n", "        response = await rate_limited_agent_call(checkpoint_list_agent, user_prompt, api_semaphore_for_generator)\n", "\n", "        # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "        # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "        # print(f\"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "        merged_checkpoints = response.output.checkpoints\n", "        \n", "        # Validate the output\n", "        if len(merged_checkpoints) < 2:\n", "            raise ValueError(f\"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}\")\n", "        \n", "        print(f\"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints\")\n", "        \n", "        return merged_checkpoints\n", "    \n", "    # Iteratively merge checkpoint lists in optimal batches\n", "    while len(current_checkpoints) > 1:\n", "        # Create optimal batches based on token count and number of checkpoints\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        current_checkpoint_count = 0\n", "        \n", "        for checkpoint_list in current_checkpoints:\n", "            # Calculate tokens for this checkpoint list\n", "            checkpoint_text = \"\\n\".join(checkpoint_list)\n", "            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)\n", "            \n", "            # Check if this single checkpoint list exceeds the token limit\n", "            if checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT:\n", "                print(f\"Warning: Single checkpoint list has {checkpoint_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT}\")\n", "                # Force this large list to be merged with the current batch to avoid infinite loop\n", "                if current_batch:\n", "                    # Add to current batch despite exceeding limit\n", "                    current_batch.append(checkpoint_list)\n", "                    current_token_count += checkpoint_tokens\n", "                    current_checkpoint_count += len(checkpoint_list)\n", "                    print(f\"Forcing merge of oversized checkpoint list with current batch (total tokens: {current_token_count})\")\n", "                    # Finalize this batch\n", "                    batches.append(current_batch)\n", "                    current_batch = []\n", "                    current_token_count = 0\n", "                    current_checkpoint_count = 0\n", "                else:\n", "                    # If current_batch is empty, we need to pair this with the next checkpoint list\n", "                    # to avoid it being processed alone repeatedly\n", "                    current_batch = [checkpoint_list]\n", "                    current_token_count = checkpoint_tokens\n", "                    current_checkpoint_count = len(checkpoint_list)\n", "                    print(f\"Starting new batch with oversized checkpoint list ({checkpoint_tokens} tokens)\")\n", "            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch\n", "            elif (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):\n", "                batches.append(current_batch)\n", "                current_batch = [checkpoint_list]\n", "                current_token_count = checkpoint_tokens\n", "                current_checkpoint_count = len(checkpoint_list)\n", "            else:\n", "                current_batch.append(checkpoint_list)\n", "                current_token_count += checkpoint_tokens\n", "                current_checkpoint_count += len(checkpoint_list)\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        # Ensure no batch has only one checkpoint list to avoid infinite loop\n", "        # If we have a single-item batch, try to merge it with another batch\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, we need to handle it\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                # Merge with the last batch\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                # This is the only batch, which means we have only one checkpoint list left\n", "                # This should not happen in the while loop condition, but just in case\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        print(f\"Created {len(batches)} batches for checkpoint list merging\")\n", "        for i, batch in enumerate(batches):\n", "            total_checkpoints = sum(len(cp) for cp in batch)\n", "            # total_tokens = sum(calculate_number_of_tokens(\"\\n\".join(cp)) for cp in batch)\n", "            # print(f\"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints and {total_tokens} total tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]\n", "        \n", "        # Wait for all merges to complete\n", "        current_checkpoints = await asyncio.gather(*tasks)\n", "    \n", "    # print(f\"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}\")\n", "    return current_checkpoints[0]\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["async def generate_checkpoint_list(section, title, description, referenced_ich_guidelines):\n", "    \"\"\"\n", "    Generate a checklist of questions that should be answered by summaries for this section.\n", "    \n", "    Args:\n", "        section: The section identifier (e.g., \"3.2.P.2.2.1\")\n", "        title: The title of the section\n", "        description: The description of the section\n", "        referenced_ich_guidelines: List of ICH guidelines referenced\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions that should be answered in summaries\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section, title, description, referenced_ich_guidelines\n", "            )\n", "            \n", "            # Create a prompt for the LLM to generate a checklist\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive list of checkpoint questions for a specific section of of a Pre-IND document.\"\n", "\n", "            # Build the prompt with outer section context if available\n", "            prompt = f\"\"\"\n", "First, carefully review the following information:\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            prompt += f\"\"\"\n", "Current Section:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                prompt += f\"\"\"\n", "Context: You are generating checkpoints for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            prompt += \"\"\"Your goal is to create a detailed checklist of specific questions that should be answered in summaries for this section. Follow these guidelines:\n", "\n", "1. Include all key requirements and recommendations, expressed as specific questions.\n", "2. Cover all aspects mentioned in the title and description.\n", "3. <PERSON><PERSON>tly adhere to the instructions.\n", "4. Address all points from the referenced ICH guidelines.\n", "5. Ensure each question is specific enough to clearly determine if a summary addresses the point.\n", "6. Format each checkpoint as a question that can be answered with yes/no or specific information.\n", "7. Focus on technical and regulatory content needed for Pre-IND documentation.\n", "8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.\n", "9. Cover stability data, packaging specifications, and compatibility information where appropriate.\n", "10. Include questions about the validation and verification methods used.\n", "11. Do not include references to sections/guidelines in the questions themselves.\n", "\n", "Before formulating the final list, conduct your analysis within <checkpoint_planning> tags inside your thinking block:\n", "a) Quote key phrases from each input section\n", "b) Break down main themes and requirements\n", "c) List and number potential areas for checkpoint questions\n", "\n", "After your analysis, present the final list of checkpoint questions\n", "\n", "Each checkpoint should be a string in the list of checkpoints.\n", "\n", "Your final output should consist only of the checkpoint list and should not duplicate or rehash any of the work you did in the thinking block.\n", "\n", "Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.\n", "\"\"\"\n", "\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(prompt)\n", "            \n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            response = await rate_limited_agent_call(checkpoint_list_agent, prompt, api_semaphore_for_generator)\n", "\n", "            # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            retry_count += 1\n", "            print(f\"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning empty list.\")\n", "                return []\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def evaluate_batch_checkpoint_relevance(summary_batch: List[Dict], section_info: Dict, checkpoint_question: str) -> List[CheckpointRelevanceEvaluation]:\n", "    \"\"\"\n", "    Evaluate multiple summaries against a single checkpoint question in a batch.\n", "    \n", "    Args:\n", "        summary_batch: List of summary dictionaries to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoint_question: Single checkpoint question to evaluate against\n", "        \n", "    Returns:\n", "        List[CheckpointRelevanceEvaluation]: List of evaluation results for each summary\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate multiple summaries against a single checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details.\"\"\"\n", "\n", "            # Build batched summaries content\n", "            batched_summaries_content = \"<batched_summaries>\\n\"\n", "            for i, summary in enumerate(summary_batch, 1):\n", "                summary_id = str(summary.get('_id', f'summary_{i}'))\n", "                summary_content = summary.get('content', '')\n", "                batched_summaries_content += f\"<summary_{i}>\\nID: {summary_id}\\nContent: {summary_content}\\n</summary_{i}>\\n\\n\"\n", "            batched_summaries_content += \"</batched_summaries>\"\n", "\n", "            prompt = f\"\"\"\n", "Evaluate the relevance of the provided summaries to a specific checkpoint question for the provided Pre-IND section:\n", "\n", "{batched_summaries_content}\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                prompt += f\"\"\"\n", "Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how each summary contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            prompt += f\"\"\"\n", "Please evaluate the relevance of each summary to this specific checkpoint question:\n", "<checkpoint_question>\n", "{checkpoint_question}\n", "</checkpoint_question>\n", "\n", "IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.\n", "\n", "**Evaluation Instructions:**\n", "1. Evaluate each summary independently against the checkpoint question\n", "2. Apply consistent evaluation criteria across all summaries\n", "3. Use the same scoring standards for all summaries in this batch\n", "4. Consider both direct and indirect relevance to the checkpoint\n", "\n", "**Scoring Criteria (0-10 scale):**\n", "- 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information valuable for regulatory submission\n", "- 6-7: <PERSON><PERSON> addresses the question with some relevant information contributing to understanding\n", "- 4-5: Provides limited but potentially useful information related to the question\n", "- 1-3: Provides minimal information with some connection to the question\n", "- 0: Does not address the question at all\n", "\n", "For each summary, provide:\n", "- The summary ID (as provided in the input)\n", "- A relevance score from 0-10\n", "- A brief justification for your score\n", "\n", "Your response should evaluate all {len(summary_batch)} summaries in the batch.\n", "\"\"\"\n", "\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            output_type = str\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                output_type = BatchedSummaryEvaluation\n", "\n", "            critique_evaluation_agent = Agent(\n", "                model=critique_model,\n", "                system_prompt=system_prompt,\n", "                output_type=output_type,\n", "                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            result = None\n", "            if \"anthropic\" in CRITIQUE_MODEL_BASE_URL:\n", "                # Use the semaphore properly for streaming calls\n", "                async with api_semaphore_for_critique:\n", "                    async with critique_evaluation_agent.run_stream(prompt) as response:\n", "                        result = await response.get_output()\n", "            else:\n", "                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)\n", "                result = result.output\n", "\n", "            # Process the result\n", "            evaluation_results = []\n", "            \n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                # Extract evaluations from structured output\n", "                for eval_result in result.evaluations:\n", "                    evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                        relevance_score=eval_result.relevance_score,\n", "                        justification=eval_result.justification\n", "                    ))\n", "            else:\n", "                # Parse JSON response for non-structured output\n", "                try:\n", "                    # Try to extract JSON from the response\n", "                    json_match = re.search(r'\\{.*\\}', result, re.DOTALL)\n", "                    if json_match:\n", "                        json_str = json_match.group(0)\n", "                        evaluation_data = json.loads(json_str)\n", "                        \n", "                        # Extract evaluations\n", "                        evaluations = evaluation_data.get(\"evaluations\", [])\n", "                        for eval_dict in evaluations:\n", "                            evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                                relevance_score=eval_dict.get(\"relevance_score\", 0.0),\n", "                                justification=eval_dict.get(\"justification\", \"Error occurred during evaluation\")\n", "                            ))\n", "                    else:\n", "                        # Fallback: create default evaluations\n", "                        print(\"Warning: Could not extract JSON from batched response\")\n", "                        for summary in summary_batch:\n", "                            evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                                relevance_score=0.0,\n", "                                justification=\"Error occurred during evaluation\"\n", "                            ))\n", "                except Exception as e:\n", "                    print(f\"Error parsing JSON response: {e}\")\n", "                    # Fallback evaluations\n", "                    for summary in summary_batch:\n", "                        evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                            relevance_score=0.0,\n", "                            justification=\"Error occurred during evaluation\"\n", "                        ))\n", "\n", "            # output_tokens = calculate_number_of_tokens(str([e.model_dump() for e in evaluation_results]))\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"evaluate_batch_checkpoint_relevance: Evaluated {len(summary_batch)} summaries in batch - Total tokens: {total_tokens}\")\n", "\n", "            return evaluation_results\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in evaluate_batch_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning default evaluations.\")\n", "                # Return default evaluations for all summaries in batch\n", "                return [CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Error occurred during evaluation\"\n", "                ) for _ in summary_batch]\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return [CheckpointRelevanceEvaluation(\n", "        relevance_score=0.0,\n", "        justification=\"Unexpected error\"\n", "    ) for _ in summary_batch]\n", "\n", "async def evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint_question):\n", "    \"\"\"\n", "    Evaluate if a summary is relevant to a single checkpoint question.\n", "    \n", "    Args:\n", "        summary_content: The content of the summary to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoint_question: Single checkpoint question to evaluate against\n", "        \n", "    Returns:\n", "        CheckpointRelevanceEvaluation: Structured evaluation result\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate if a given summary addresses a specific checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details.\"\"\"\n", "\n", "            prompt = f\"\"\"\n", "Evaluate the relevance of the provided summary to a specific checkpoint question for the provided Pre-IND section:\n", "\n", "Summary Content to Evaluate:\n", "<summary_content>\n", "{summary_content}\n", "</summary_content>\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                prompt += f\"\"\"\n", "Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this checkpoint contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            prompt += f\"\"\"\n", "Please evaluate the relevance of the summary to this specific checkpoint question:\n", "<checkpoint_question>\n", "{checkpoint_question}\n", "</checkpoint_question>\n", "\n", "IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.\n", "\n", "Evaluation Process:\n", "1. Carefully read the checkpoint question to understand the specific information it's requesting.\n", "2. Analyze the summary content to identify information that directly, indirectly, or partially addresses the checkpoint question.\n", "3. Consider the depth, specificity, and quality of the information provided.\n", "4. Remember that the summary doesn't need to explicitly mention the question itself, only provide relevant information that would help answer it.\n", "5. Give credit to partial relevance - information that addresses part of the question or provides context is valuable.\n", "\n", "Conduct your evaluation in the relevance_evaluation thinking block:\n", "\n", "1. Quote Extraction and Relevance Assessment:\n", "   - List specific parts of the summary that relate to the checkpoint question.\n", "   - For each quote, explain how well it addresses the question (completely, partially, or provides relevant context).\n", "   - Include information that might be indirectly relevant but still useful for regulatory purposes.\n", "\n", "2. Partial Relevance Analysis:\n", "   - Identify content that doesn't fully answer the question but provides relevant background, context, or related information.\n", "   - Consider whether this partial information would be valuable for regulatory professionals working on this section.\n", "\n", "3. Depth and Specificity Assessment:\n", "   - Evaluate the level of detail and precision in the relevant information.\n", "   - Consider if the information provides useful context even if not perfectly specific to the checkpoint question.\n", "\n", "4. Regulatory Perspective:\n", "   - Elaborate on how a regulatory professional could use this information to address the checkpoint.\n", "   - Consider the value of having comprehensive information for Pre-IND submissions.\n", "   - Assess whether the information contributes to the overall understanding of the regulatory requirements.\n", "\n", "5. Scoring Justification:\n", "   - Based on your analysis, assign a preliminary score from 0 to 10 using the following criteria:\n", "     - 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information that would be valuable for regulatory submission\n", "     - 6-7: <PERSON><PERSON> addresses the question with some relevant information that contributes to understanding the requirements\n", "     - 4-5: Provides limited but potentially useful information related to the question or relevant context\n", "     - 1-3: Provides minimal information that has some connection to the question but is mostly tangential\n", "     - 0: Does not address the question at all or only contains completely irrelevant information\n", "   - Provide a detailed justification for your score, referencing specific elements of your analysis.\n", "\n", "IMPORTANT: Be inclusive in your evaluation while maintaining quality standards. For Pre-IND submissions, information that partially addresses a checkpoint or provides valuable context should be scored favorably (8+ range) as comprehensive information gathering is critical for regulatory success. However, still exclude content that is merely tangential or only contains very indirect mentions without substantial relevance.\n", "\n", "After your analysis, provide your final evaluation in the following JSON format:\n", "\n", "{{\n", "  \"relevance_score\": <number from 0-10>,\n", "  \"justification\": \"<detailed explanation of your score>\"\n", "}}\n", "\n", "Your final evaluation should be concise and should not duplicate the detailed work from your relevance evaluation thinking block.\"\"\"\n", "\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            output_type = str\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                output_type = RelevanceEvaluation\n", "\n", "            critique_evaluation_agent = Agent(\n", "                model=critique_model,\n", "                system_prompt=system_prompt,\n", "                output_type=output_type,\n", "                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            result = None\n", "            if \"anthropic\" in CRITIQUE_MODEL_BASE_URL:\n", "                # Use the semaphore properly for streaming calls\n", "                async with api_semaphore_for_critique:\n", "                    async with critique_evaluation_agent.run_stream(prompt) as response:\n", "                        result = await response.get_output()\n", "            else:\n", "                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)\n", "                result = result.output\n", "\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                checkpoint_evaluation = result\n", "            else:\n", "                # Parse the JSON response\n", "                try:\n", "                    # Extract JSON from the response\n", "                    json_match = re.search(r'\\{.*\\}', result, re.DOTALL)\n", "                    if json_match:\n", "                        json_str = json_match.group(0)\n", "                        evaluation_data = json.loads(json_str)\n", "                        \n", "                        # Create CheckpointRelevanceEvaluation object\n", "                        checkpoint_evaluation = CheckpointRelevanceEvaluation(\n", "                            relevance_score=evaluation_data.get(\"relevance_score\", 0.0),\n", "                            justification=evaluation_data.get(\"justification\", \"Error occurred during evaluation\")\n", "                        )\n", "                    else:\n", "                        # Fallback if JSON parsing fails\n", "                        print(\"Warning: Could not extract JSON from response, using default evaluation\")\n", "                        checkpoint_evaluation = CheckpointRelevanceEvaluation(\n", "                            relevance_score=0.0,\n", "                            justification=\"Error occurred during evaluation\"\n", "                        )\n", "                except Exception as e:\n", "                    print(f\"Error parsing JSON response: {e}\")\n", "                    # Fallback evaluation\n", "                    checkpoint_evaluation = CheckpointRelevanceEvaluation(\n", "                        relevance_score=0.0,\n", "                        justification=\"Error occurred during evaluation\"\n", "                    )\n", "\n", "            # output_tokens = calculate_number_of_tokens(checkpoint_evaluation.model_dump_json())\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # Uncomment for detailed token tracking\n", "            # print(f\"evaluate_individual_checkpoint_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            return checkpoint_evaluation\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in evaluate_individual_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning default evaluation.\")\n", "                # Return default evaluation\n", "                return CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Error occurred during evaluation\"\n", "                )\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return CheckpointRelevanceEvaluation(\n", "        relevance_score=0.0,\n", "        justification=\"Unexpected error\"\n", "    )\n", "\n", "async def evaluate_batch_chunks_checkpoint_relevance(chunk_batch: List[Dict], section_info: Dict, checkpoint_question: str) -> List[CheckpointRelevanceEvaluation]:\n", "    \"\"\"\n", "    Evaluate multiple chunks against a single checkpoint question in a batch.\n", "    \n", "    Args:\n", "        chunk_batch: List of chunk dictionaries to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoint_question: Single checkpoint question to evaluate against\n", "        \n", "    Returns:\n", "        List[CheckpointRelevanceEvaluation]: List of evaluation results for each chunk\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate multiple input document chunks against a single checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details.\"\"\"\n", "\n", "            # Build batched chunks content\n", "            batched_chunks_content = \"<batched_chunks>\\n\"\n", "            for i, chunk in enumerate(chunk_batch, 1):\n", "                chunk_id = str(chunk.get('_id', f'chunk_{i}'))\n", "                chunk_content = chunk.get('content', '')\n", "                batched_chunks_content += f\"<chunk_{i}>\\nID: {chunk_id}\\nContent: {chunk_content}\\n</chunk_{i}>\\n\\n\"\n", "            batched_chunks_content += \"</batched_chunks>\"\n", "\n", "            prompt = f\"\"\"\n", "Evaluate the relevance of the provided input document chunks to a specific checkpoint question for the provided Pre-IND section:\n", "\n", "{batched_chunks_content}\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                prompt += f\"\"\"\n", "Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how each chunk contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            prompt += f\"\"\"\n", "Please evaluate the relevance of each chunk to this specific checkpoint question:\n", "<checkpoint_question>\n", "{checkpoint_question}\n", "</checkpoint_question>\n", "\n", "IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.\n", "\n", "**Evaluation Instructions:**\n", "1. Evaluate each chunk independently against the checkpoint question\n", "2. Apply consistent evaluation criteria across all chunks\n", "3. Use the same scoring standards for all chunks in this batch\n", "4. Consider both direct and indirect relevance to the checkpoint\n", "\n", "**Scoring Criteria (0-10 scale):**\n", "- 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information valuable for regulatory submission\n", "- 6-7: <PERSON><PERSON> addresses the question with some relevant information contributing to understanding\n", "- 4-5: Provides limited but potentially useful information related to the question\n", "- 1-3: Provides minimal information with some connection to the question\n", "- 0: Does not address the question at all\n", "\n", "For each chunk, provide:\n", "- The chunk ID (as provided in the input)\n", "- A relevance score from 0-10\n", "- A brief justification for your score\n", "\n", "Your response should evaluate all {len(chunk_batch)} chunks in the batch.\n", "\"\"\"\n", "\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            output_type = str\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                output_type = BatchedChunkEvaluation\n", "\n", "            critique_evaluation_agent = Agent(\n", "                model=critique_model,\n", "                system_prompt=system_prompt,\n", "                output_type=output_type,\n", "                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            result = None\n", "            if \"anthropic\" in CRITIQUE_MODEL_BASE_URL:\n", "                # Use the semaphore properly for streaming calls\n", "                async with api_semaphore_for_critique:\n", "                    async with critique_evaluation_agent.run_stream(prompt) as response:\n", "                        result = await response.get_output()\n", "            else:\n", "                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)\n", "                result = result.output\n", "\n", "            # Process the result\n", "            evaluation_results = []\n", "            \n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                # Extract evaluations from structured output\n", "                for eval_result in result.evaluations:\n", "                    evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                        relevance_score=eval_result.relevance_score,\n", "                        justification=eval_result.justification\n", "                    ))\n", "            else:\n", "                # Parse JSON response for non-structured output\n", "                try:\n", "                    # Try to extract JSON from the response\n", "                    json_match = re.search(r'\\{.*\\}', result, re.DOTALL)\n", "                    if json_match:\n", "                        json_str = json_match.group(0)\n", "                        evaluation_data = json.loads(json_str)\n", "                        \n", "                        # Extract evaluations\n", "                        evaluations = evaluation_data.get(\"evaluations\", [])\n", "                        for eval_dict in evaluations:\n", "                            evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                                relevance_score=eval_dict.get(\"relevance_score\", 0.0),\n", "                                justification=eval_dict.get(\"justification\", \"Error occurred during evaluation\")\n", "                            ))\n", "                    else:\n", "                        # Fallback: create default evaluations\n", "                        print(\"Warning: Could not extract JSON from batched chunk response\")\n", "                        for chunk in chunk_batch:\n", "                            evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                                relevance_score=0.0,\n", "                                justification=\"Error occurred during evaluation\"\n", "                            ))\n", "                except Exception as e:\n", "                    print(f\"Error parsing JSON response: {e}\")\n", "                    # Fallback evaluations\n", "                    for chunk in chunk_batch:\n", "                        evaluation_results.append(CheckpointRelevanceEvaluation(\n", "                            relevance_score=0.0,\n", "                            justification=\"Error occurred during evaluation\"\n", "                        ))\n", "\n", "            # output_tokens = calculate_number_of_tokens(str([e.model_dump() for e in evaluation_results]))\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"evaluate_batch_chunks_checkpoint_relevance: Evaluated {len(chunk_batch)} chunks in batch - Total tokens: {total_tokens}\")\n", "\n", "            return evaluation_results\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in evaluate_batch_chunks_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning default evaluations.\")\n", "                # Return default evaluations for all chunks in batch\n", "                return [CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Error occurred during evaluation\"\n", "                ) for _ in chunk_batch]\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return [CheckpointRelevanceEvaluation(\n", "        relevance_score=0.0,\n", "        justification=\"Unexpected error\"\n", "    ) for _ in chunk_batch]\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["async def evaluate_summary_relevance(summary_content, section_info, checkpoints):\n", "    \"\"\"\n", "    Use an LLM to evaluate if a summary is relevant to a section by evaluating each checkpoint individually.\n", "    This approach provides more accurate relevance determination by:\n", "    1. Evaluating each checkpoint question individually in parallel\n", "    2. Using a clear 8+ threshold for relevance determination\n", "    3. Aggregating individual scores and applying threshold to final average\n", "    \n", "    Args:\n", "        summary_content: The content of the summary to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoints: List of checkpoint questions that should be answered\n", "        \n", "    Returns:\n", "        Tuple[bool, float, int]: Tuple containing boolean relevance, average score, and number of relevant checkpoints\n", "    \"\"\"\n", "    # Handle empty checkpoint list\n", "    if not checkpoints:\n", "        print(\"No checkpoints provided for evaluation\")\n", "        return False, 0.0, 0\n", "    \n", "    try:\n", "        print(f\"Evaluating summary relevance using {len(checkpoints)} individual checkpoints\")\n", "        \n", "        # Create tasks for parallel evaluation of each checkpoint\n", "        tasks = [\n", "            evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint)\n", "            for checkpoint in checkpoints\n", "        ]\n", "        \n", "        # Execute all evaluations in parallel\n", "        checkpoint_evaluations = await asyncio.gather(*tasks, return_exceptions=True)\n", "        \n", "        # Process results and handle any exceptions\n", "        valid_evaluations = []\n", "        for i, evaluation in enumerate(checkpoint_evaluations):\n", "            if isinstance(evaluation, Exception):\n", "                print(f\"Error evaluating checkpoint {i+1}: {evaluation}\")\n", "                # Create default evaluation for failed checkpoint\n", "                valid_evaluations.append(CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Evaluation failed\"\n", "                ))\n", "            else:\n", "                valid_evaluations.append(evaluation)\n", "        \n", "        # Calculate simple average of all individual scores\n", "        total_checkpoints = len(valid_evaluations)\n", "        total_score = sum(eval.relevance_score for eval in valid_evaluations)\n", "        average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0\n", "        number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)\n", "        \n", "        # Determine if summary is relevant based on average score\n", "        is_relevant = number_of_relevant_checkpoints >= 1\n", "\n", "        if not is_relevant:\n", "            print(\"Summary is not relevant, evaluations:\", \"\\n\".join([f\"Relevance Score: {eval.relevance_score:.2f}, Justification: {eval.justification}\" for eval in valid_evaluations]))\n", "\n", "        return is_relevant, average_score, number_of_relevant_checkpoints\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in evaluate_summary_relevance: {e}\")\n", "        traceback.print_exc()\n", "        return False, 0.0, 0\n", "\n", "async def evaluate_multiple_summaries_relevance(summaries: List[Dict], section_info: Dict, checkpoints: List[str]) -> List[Tuple[bool, float, int]]:\n", "    \"\"\"\n", "    Efficiently evaluate multiple summaries against multiple checkpoints using parallel batched evaluation.\n", "    \n", "    PERFORMANCE OPTIMIZATION:\n", "    - Batches summaries together for each checkpoint (reduces API calls by 5-10x)\n", "    - Processes ALL checkpoints in parallel (instead of sequentially)\n", "    - Uses semaphore-controlled concurrency for optimal resource usage\n", "    \n", "    Example: 10 summaries × 15 checkpoints = 150 individual calls\n", "    → Becomes ~8 parallel batch calls (18x faster!)\n", "    \n", "    Args:\n", "        summaries: List of summary dictionaries to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoints: List of checkpoint questions that should be answered\n", "        \n", "    Returns:\n", "        List of tuples for each summary containing (is_relevant, average_score, number_of_relevant_checkpoints)\n", "    \"\"\"\n", "    # Handle empty inputs\n", "    if not summaries or not checkpoints:\n", "        print(\"No summaries or checkpoints provided for evaluation\")\n", "        return [(False, 0.0, 0) for _ in summaries]\n", "    \n", "    try:        \n", "        print(f\"Evaluating {len(summaries)} summaries against {len(checkpoints)} checkpoints using batched approach\")\n", "        \n", "        # Initialize results matrix: [summary_index][checkpoint_index] = evaluation\n", "        summary_evaluations = [[None for _ in checkpoints] for _ in summaries]\n", "        \n", "        # Prepare all checkpoint processing tasks for parallel execution\n", "        print(f\"Preparing parallel processing for {len(checkpoints)} checkpoints...\")\n", "        \n", "        # Create all batch tasks for all checkpoints\n", "        checkpoint_tasks = []\n", "        checkpoint_batch_info = []  # Store info needed for result processing\n", "        \n", "        for checkpoint_idx, checkpoint in enumerate(checkpoints):\n", "            print(f\"Preparing checkpoint {checkpoint_idx + 1}/{len(checkpoints)}: {checkpoint[:100]}...\")\n", "            \n", "            # Create batches for this checkpoint\n", "            batches = create_summary_batches(summaries, checkpoint)\n", "            print(f\"Created {len(batches)} batches for checkpoint {checkpoint_idx + 1}\")\n", "            \n", "            # Create tasks for all batches of this checkpoint\n", "            for batch_idx, batch in enumerate(batches):\n", "                task = evaluate_batch_checkpoint_relevance(batch, section_info, checkpoint)\n", "                checkpoint_tasks.append(task)\n", "                \n", "                # Store metadata for result processing\n", "                checkpoint_batch_info.append({\n", "                    'checkpoint_idx': checkpoint_idx,\n", "                    'batch_idx': batch_idx,\n", "                    'batch_size': len(batch),\n", "                    'batch_start_summary_idx': sum(len(b) for b in batches[:batch_idx])  # Calculate starting summary index for this batch\n", "                })\n", "        \n", "        print(f\"🚀 Executing {len(checkpoint_tasks)} batch evaluation tasks in parallel across {len(checkpoints)} checkpoints...\")\n", "        \n", "        # Execute ALL batch tasks across ALL checkpoints in parallel\n", "        all_batch_results = await asyncio.gather(*checkpoint_tasks, return_exceptions=True)\n", "        \n", "        print(f\"✅ Completed parallel execution of {len(checkpoint_tasks)} batch evaluation tasks\")\n", "        \n", "        # Process results and assign to summary_evaluations matrix\n", "        for task_idx, (batch_result, batch_info) in enumerate(zip(all_batch_results, checkpoint_batch_info)):\n", "            checkpoint_idx = batch_info['checkpoint_idx']\n", "            batch_idx = batch_info['batch_idx']\n", "            batch_size = batch_info['batch_size']\n", "            batch_start_idx = batch_info['batch_start_summary_idx']\n", "            \n", "            if isinstance(batch_result, Exception):\n", "                print(f\"Error evaluating batch {batch_idx + 1} for checkpoint {checkpoint_idx + 1}: {batch_result}\")\n", "                # Create default evaluations for failed batch\n", "                for i in range(batch_size):\n", "                    summary_idx = batch_start_idx + i\n", "                    summary_evaluations[summary_idx][checkpoint_idx] = CheckpointRelevanceEvaluation(\n", "                        relevance_score=0.0,\n", "                        justification=\"Batch evaluation failed\"\n", "                    )\n", "            else:\n", "                # Assign evaluations to corresponding summaries\n", "                for i, evaluation in enumerate(batch_result):\n", "                    summary_idx = batch_start_idx + i\n", "                    summary_evaluations[summary_idx][checkpoint_idx] = evaluation\n", "        \n", "        # Calculate final results for each summary\n", "        final_results = []\n", "        for summary_idx, summary in enumerate(summaries):\n", "            evaluations = summary_evaluations[summary_idx]\n", "            \n", "            # Filter out None evaluations (shouldn't happen but safety check)\n", "            valid_evaluations = [eval for eval in evaluations if eval is not None]\n", "            \n", "            if not valid_evaluations:\n", "                final_results.append((False, 0.0, 0))\n", "                continue\n", "            \n", "            # Calculate metrics\n", "            total_checkpoints = len(valid_evaluations)\n", "            total_score = sum(eval.relevance_score for eval in valid_evaluations)\n", "            average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0\n", "            number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)\n", "            \n", "            # Determine if summary is relevant\n", "            is_relevant = number_of_relevant_checkpoints >= 1\n", "            \n", "            if not is_relevant:\n", "                print(f\"Summary {summary.get('_id', summary_idx)} is not relevant, avg score: {average_score:.2f}\")\n", "            else:\n", "                print(f\"Summary {summary.get('_id', summary_idx)} is relevant, avg score: {average_score:.2f}, relevant checkpoints: {number_of_relevant_checkpoints}\")\n", "            \n", "            final_results.append((is_relevant, average_score, number_of_relevant_checkpoints))\n", "        \n", "        return final_results\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in evaluate_multiple_summaries_relevance: {e}\")\n", "        traceback.print_exc()\n", "        return [(False, 0.0, 0) for _ in summaries]\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["async def search_summaries_by_llm(section_info, ich_guidelines, summary_level=1):\n", "    \"\"\"\n", "    Search for summaries in MongoDB based on relevance to the section using an LLM.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        ich_guidelines: List of ICH guidelines to search in (e.g., [\"Q6A\", \"Q6B\"])\n", "        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)\n", "        \n", "    Returns:\n", "        List[Dict]: List of relevant summaries with their content and metadata\n", "    \"\"\"\n", "    db = mongodb_client['mednova']\n", "    summaries = []\n", "\n", "    collection_mapping = get_mongodb_collection_from_referenced_guidelines(ich_guidelines)\n", "    \n", "    # Create tasks for parallel collection queries\n", "    async def fetch_from_collection(collection_name, key_name):\n", "        collection = db[collection_name]\n", "        return await collection.find({\n", "            key_name: {\"$in\": ich_guidelines},\n", "            \"summary_level\": summary_level\n", "        }).to_list()\n", "    \n", "    # Execute all collection queries in parallel\n", "    tasks = [\n", "        fetch_from_collection(collection_name, collection_mapping[\"key_name\"][index])\n", "        for index, collection_name in enumerate(collection_mapping[\"summaries\"])\n", "    ]\n", "    \n", "    results = await asyncio.gather(*tasks)\n", "    \n", "    # Combine all results\n", "    for summaries_for_collection in results:\n", "        summaries.extend(summaries_for_collection)\n", "\n", "    print(f\"Found {len(summaries)} summaries for section {section_info.get('section', '')} ICH guidelines {ich_guidelines} at level {summary_level}\")\n", "    \n", "    if not summaries:\n", "        print(f\"No summaries found for ICH guidelines {ich_guidelines} at level {summary_level}\")\n", "        return []\n", "    \n", "    # Get checkpoints for the section\n", "    checkpoints = section_info.get(\"checkpoint_list\", [])\n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section_info.get('section', '')}\")\n", "        # Generate checkpoints if not already available\n", "        checkpoints = await generate_checkpoint_list(\n", "            section_info.get(\"section\", \"\"),\n", "            section_info.get(\"title\", \"\"),\n", "            section_info.get(\"description\", \"\"),\n", "            section_info.get(\"referenced_ich_guidelines\", [])\n", "        )\n", "    \n", "    # Choose evaluation method based on configuration\n", "    if USE_BATCHED_EVALUATION and len(summaries) > 1:\n", "        # Use batched approach for better performance with fallback to individual evaluation\n", "        try:\n", "            results = await evaluate_multiple_summaries_relevance(summaries, section_info, checkpoints)\n", "        except Exception as e:\n", "            print(f\"Batched evaluation failed: {e}, falling back to individual evaluation\")\n", "            # Fallback to individual evaluation\n", "            evaluation_tasks = [\n", "                evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints)\n", "                for summary in summaries\n", "            ]\n", "            results = await asyncio.gather(*evaluation_tasks)\n", "    else:\n", "        # Use individual evaluation (for testing/comparison or single summary)\n", "        print(f\"Using individual evaluation for {len(summaries)} summaries\")\n", "        evaluation_tasks = [\n", "            evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints)\n", "            for summary in summaries\n", "        ]\n", "        results = await asyncio.gather(*evaluation_tasks)\n", "    \n", "    # Process results and print evaluation outcomes\n", "    evaluated_summaries = []\n", "    for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(summaries, results):\n", "        print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "        \n", "        if is_relevant:\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "            summary[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "            evaluated_summaries.append(summary)\n", "        else:\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "    \n", "    # No need to sort by relevance score since all relevant summaries have same score\n", "    # evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "    \n", "    return evaluated_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["async def get_summaries_for_final_summary(final_summary_id: str, summary_name: str, chunk_name: str, key_name: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the individual summaries associated with a final summary.\n", "    \n", "    Args:\n", "        final_summary_id: The ID of the final summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of individual summaries with their content and metadata\n", "    \"\"\"\n", "    db = mongodb_client['mednova']\n", "    summary_collection = db[summary_name]\n", "    \n", "    # Get the final summary\n", "    final_summary = await summary_collection.find_one({\"_id\": ObjectId(final_summary_id)})\n", "    \n", "    if not final_summary or \"summary_reference_ids\" not in final_summary:\n", "        print(f\"No summary references found for final summary {final_summary_id}\")\n", "        return []\n", "    \n", "    # Get the individual summaries\n", "    summary_ids = final_summary[\"summary_reference_ids\"]\n", "    individual_summaries = list(await summary_collection.find({\"_id\": {\"$in\": summary_ids}}).to_list())\n", "    \n", "    return individual_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:\n", "    \"\"\"\n", "    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information (section, title, description, etc.)\n", "        \n", "    Returns:\n", "        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks\n", "    \"\"\"\n", "    # Extract section information\n", "    section = section_info.get(\"section\", \"\")\n", "    title = section_info.get(\"title\", \"\")\n", "    description = section_info.get(\"description\", \"\")\n", "    referenced_ich_guidelines = section_info.get(\"referenced_ich_guidelines\", [])\n", "    \n", "    # Step 1: Search for relevant final summaries (level 1)\n", "    final_summaries = await search_summaries_by_llm(\n", "        section_info=section_info,\n", "        ich_guidelines=referenced_ich_guidelines,\n", "        summary_level=1\n", "    )\n", "    \n", "    # Step 2: For each final summary, get relevant individual summaries (level 2) - parallel processing\n", "    individual_summaries = []\n", "    \n", "    # Create tasks for parallel processing of final summaries\n", "    async def process_final_summary(final_summary):\n", "        summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(final_summary)\n", "        summaries = await get_summaries_for_final_summary(final_summary[\"_id\"], summary_name, chunk_name, key_name)\n", "        \n", "        # Evaluate all individual summaries using batched approach for better performance\n", "        evaluated_summaries = []\n", "        \n", "        # Choose evaluation method based on configuration\n", "        if USE_BATCHED_EVALUATION and len(summaries) > 1:\n", "            # Use batched evaluation for better performance with fallback to individual evaluation\n", "            try:\n", "                results = await evaluate_multiple_summaries_relevance(summaries, section_info, section_info.get(\"checkpoint_list\", []))\n", "            except Exception as e:\n", "                print(f\"Batched evaluation failed: {e}, falling back to individual evaluation\")\n", "                # Fallback to individual evaluation\n", "                evaluation_tasks = [\n", "                    evaluate_summary_relevance(summary[\"content\"], section_info, section_info.get(\"checkpoint_list\", []))\n", "                    for summary in summaries\n", "                ]\n", "                results = await asyncio.gather(*evaluation_tasks)\n", "        else:\n", "            # Use individual evaluation (for testing/comparison or single summary)\n", "            print(f\"Using individual evaluation for {len(summaries)} summaries\")\n", "            evaluation_tasks = [\n", "                evaluate_summary_relevance(summary[\"content\"], section_info, section_info.get(\"checkpoint_list\", []))\n", "                for summary in summaries\n", "            ]\n", "            results = await asyncio.gather(*evaluation_tasks)\n", "        \n", "        # Process results and print evaluation outcomes\n", "        for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(summaries, results):\n", "            print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "            \n", "            if is_relevant:\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "                summary[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "                evaluated_summaries.append(summary)\n", "            else:\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "        \n", "        return evaluated_summaries\n", "    \n", "    # Execute all final summary processing tasks in parallel\n", "    final_summary_tasks = [process_final_summary(final_summary) for final_summary in final_summaries]\n", "    final_summary_results = await asyncio.gather(*final_summary_tasks)\n", "    \n", "    # Flatten the results\n", "    for evaluated_summaries in final_summary_results:\n", "        individual_summaries.extend(evaluated_summaries)\n", "    \n", "    # Step 3: For each individual summary, get relevant chunks\n", "    chunks = []\n", "    # BATCH_SIZE = 50\n", "    \n", "    # for i in range(0, len(individual_summaries), BATCH_SIZE):\n", "    #     batch = individual_summaries[i:i + BATCH_SIZE]\n", "    #     batch_tasks = []\n", "        \n", "    #     for individual_summary in batch:\n", "    #         summary_chunks = get_chunks_for_summary(individual_summary[\"_id\"])\n", "    #         # Create tasks for parallel evaluation of chunks\n", "    #         tasks = [evaluate_summary_relevance(chunk[\"content\"], section_info, section_info.get(\"quality_guideline_combined_summaries_checkpoint_list\", [])) for chunk in summary_chunks]\n", "    #         batch_tasks.extend(zip(summary_chunks, tasks))\n", "        \n", "    #     # Execute all tasks in parallel\n", "    #     results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "    #     # Process results\n", "    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):\n", "    #         if relevance_score >= get_relevance_threshold(0):\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    #             chunk[\"similarity_score\"] = relevance_score\n", "    #             chunks.append(chunk)\n", "    #         else:\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    \n", "    return final_summaries, individual_summaries, chunks\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["async def extract_relevant_chunks(json_obj):\n", "    \"\"\"\n", "    Recursively iterates through the nested JSON structure, extracts titles and descriptions,\n", "    generates checkpoint lists, and finds relevant summaries and chunks for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with added relevant summaries and chunks\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that needs processing\n", "            if (obj and \"title\" in obj and \"description\" in obj and \n", "                \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        # Generate checkpoint list for this section\n", "        section[\"checkpoint_list\"] = await generate_checkpoint_list(\n", "            section[\"section\"], \n", "            section[\"title\"], \n", "            section[\"description\"], \n", "            section.get(\"referenced_ich_guidelines\", [])\n", "        )\n", "        \n", "        # Find relevant summaries and chunks for this section\n", "        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(\n", "            section\n", "        )\n", "\n", "        # Print summary of results\n", "        print(f\"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks\")\n", "\n", "        section[\"relevant_final_summaries\"] = []\n", "        section[\"relevant_individual_summaries\"] = []\n", "\n", "        for s in final_summaries:\n", "            summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(s)\n", "            section[\"relevant_final_summaries\"].append({\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"ich_guideline\": s[key_name], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            })\n", "        \n", "        for s in individual_summaries:\n", "            summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(s)\n", "            section[\"relevant_individual_summaries\"].append({\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"ich_guideline\": s[key_name], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            })\n", "\n", "        # Create a final summary from the relevant individual summaries\n", "        # if len(section[\"relevant_individual_summaries\"]) > 0:\n", "        #     final_summary = await create_combined_summary([summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]])\n", "        #     section[\"quality_guideline_combined_summary\"] = final_summary\n", "        # else:\n", "        #     # If no relevant summaries found, create a summary from section information\n", "        #     final_summary = await generate_summary_from_section_info(section)\n", "        #     section[\"quality_guideline_combined_summary\"] = final_summary\n", "\n", "        # Generate checkpoints incrementally from individual summaries\n", "        if len(section[\"relevant_individual_summaries\"]) > 0:\n", "            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(\n", "                section,\n", "                [summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]]\n", "            )\n", "        else:\n", "            # If no relevant summaries found, generate checkpoints from section information\n", "            checkpoints = await generate_checkpoint_list(\n", "                section[\"section\"],\n", "                section[\"title\"],\n", "                section[\"description\"],\n", "                section.get(\"referenced_ich_guidelines\", [])\n", "            )\n", "        \n", "        print(f\"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}\")\n", "        section[\"ich_guideline_combined_summaries_checkpoint_list\"] = checkpoints\n", "        \n", "        return section\n", "    \n", "    # Process all sections sequentially with intermediate saves\n", "    if sections_to_process:\n", "        # processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        # for i, section in enumerate(sections_to_process):\n", "        #     section.update(processed_sections[i])\n", "\n", "        for i, section in enumerate(sections_to_process):\n", "            print(f\"Processing section {i+1}/{len(sections_to_process)}: {section.get('section', 'unknown')}\")\n", "            processed_section = await process_section(section)\n", "            section.update(processed_section)\n", "            \n", "            # Save intermediate progress after each section\n", "            write_to_json(json_obj, \"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "            \n", "            print(f\"✅ Completed section {i+1}/{len(sections_to_process)}: {section.get('section', 'unknown')}\")\n", "    \n", "    return json_obj\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# processed_doc = await extract_relevant_chunks(doc_to_work_on)\n", "\n", "# # Save the results to a JSON file\n", "# write_to_json(processed_doc, \"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["\n", "def load_drug_type_definitions(drug_types_json_path: str = \"structured jsons/drug_types.json\") -> Dict:\n", "    \"\"\"\n", "    Load and format drug type definitions for classification prompts.\n", "    \n", "    Args:\n", "        drug_types_json_path: Path to the drug types JSON file\n", "        \n", "    Returns:\n", "        Dict containing formatted drug type definitions\n", "    \"\"\"\n", "    try:\n", "        drug_types_data = read_json(drug_types_json_path)\n", "        \n", "        # Extract the 6 main drug types (sections 1-6)\n", "        formatted_definitions = {}\n", "        \n", "        for section_num in [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\"]:\n", "            if section_num in drug_types_data:\n", "                section_data = drug_types_data[section_num]\n", "                formatted_definitions[section_data[\"title\"]] = {\n", "                    \"title\": section_data[\"title\"],\n", "                    \"description\": section_data[\"description\"],\n", "                    \"referenced_ich_guidelines\": section_data.get(\"referenced_ich_guidelines\", []),\n", "                    \"referenced_fda_guidelines\": section_data.get(\"referenced_fda_guidelines\", [])\n", "                }\n", "        \n", "        return formatted_definitions\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading drug type definitions: {e}\")\n", "        # Return basic fallback definitions\n", "        return {\n", "            \"Small-Molecule Drugs\": {\"title\": \"Small-Molecule Drugs\", \"description\": \"Chemical drugs with low molecular weight\"},\n", "            \"Biologic Therapeutics: Therapeutic Proteins (including Monoclonal Antibodies - mAbs)\": {\"title\": \"Therapeutic Proteins\", \"description\": \"Protein-based therapeutics\"},\n", "            \"Biologic Therapeutics: Vaccines\": {\"title\": \"Vaccines\", \"description\": \"Biological preparations for immunity\"},\n", "            \"Cell Therapy Products\": {\"title\": \"Cell Therapy Products\", \"description\": \"Live cell therapeutics\"},\n", "            \"Gene Therapy Products\": {\"title\": \"Gene Therapy Products\", \"description\": \"Genetic modification therapeutics\"},\n", "            \"Combination Products\": {\"title\": \"Combination Products\", \"description\": \"Multi-component products\"}\n", "        }\n", "\n", "async def generate_drug_type_checkpoints(drug_type_name: str, drug_type_definition: Dict) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoint questions for a specific drug type for Pre-IND/IND/NDA submissions.\n", "    \n", "    Args:\n", "        drug_type_name: Name of the drug type (e.g., 'Small-Molecule Drugs')\n", "        drug_type_definition: Dictionary containing drug type definition and guidelines\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions relevant to the drug type\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"\"\"You are an expert pharmaceutical regulatory scientist specializing in drug development and regulatory submissions (Pre-IND, IND, NDA). Your task is to generate comprehensive checkpoint questions for a specific drug type that will be used to identify the most relevant regulatory guidelines and requirements.\"\"\"\n", "\n", "            user_prompt = f\"\"\"\n", "You are creating checkpoint questions for drug type classification and regulatory guideline identification.\n", "\n", "<drug_type_information>\n", "<drug_type_name>{drug_type_name}</drug_type_name>\n", "<description>{drug_type_definition.get('description', '')}</description>\n", "<referenced_ich_guidelines>{', '.join(drug_type_definition.get('referenced_ich_guidelines', []))}</referenced_ich_guidelines>\n", "<referenced_fda_guidelines>{', '.join(drug_type_definition.get('referenced_fda_guidelines', []))}</referenced_fda_guidelines>\n", "</drug_type_information>\n", "\n", "Your goal is to create a comprehensive list of checkpoint questions that will help identify the most relevant regulatory guidelines and requirements for this specific drug type. These questions will be used to:\n", "\n", "1. Filter relevant ICH and FDA guideline summaries\n", "2. Identify drug-type-specific regulatory requirements\n", "3. Guide Pre-IND, IND, and NDA preparation for this drug type\n", "\n", "**CRITICAL REQUIREMENTS:**\n", "\n", "1. **Regulatory Context**: Focus on Pre-IND, IND, and NDA submission requirements specific to this drug type\n", "2. **Technical Specificity**: Include drug-type-specific technical and scientific considerations\n", "3. **Manufacturing & Quality**: Address drug-type-specific manufacturing, quality control, and characterization requirements\n", "4. **Safety & Efficacy**: Include drug-type-specific safety evaluation and efficacy assessment considerations\n", "5. **Regulatory Pathway**: Consider unique regulatory pathways and requirements for this drug type\n", "\n", "**Guidelines for Checkpoint Creation:**\n", "\n", "1. Each checkpoint should be a specific question that can help identify relevant regulatory guidance\n", "2. Focus on what makes this drug type unique from a regulatory perspective\n", "3. Include questions about drug-type-specific manufacturing processes and controls\n", "4. Address unique safety and efficacy considerations for this drug type\n", "5. Include questions about drug-type-specific regulatory pathways and requirements\n", "6. Cover CMC (Chemistry, Manufacturing, and Controls) aspects specific to this drug type\n", "7. Include questions about drug-type-specific preclinical and clinical considerations\n", "8. Format each checkpoint as a clear, specific question\n", "9. Do not include references to specific guidelines in the questions themselves\n", "10. Ensure questions are applicable across Pre-IND, IND, and NDA contexts\n", "\n", "Before generating the final list, conduct your analysis in <checkpoint_planning> tags inside your thinking block:\n", "a) Identify key regulatory characteristics of this drug type\n", "b) List unique manufacturing and quality considerations\n", "c) Identify specific safety and efficacy assessment needs\n", "d) Consider unique regulatory pathway requirements\n", "e) Plan checkpoint questions to cover all critical areas\n", "\n", "After your analysis, provide the final list of checkpoint questions.\n", "\n", "Each checkpoint should be a specific question that helps identify relevant regulatory guidance for this drug type.\n", "\n", "Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.\n", "\"\"\"\n", "\n", "            checkpoint_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            response = await rate_limited_agent_call(checkpoint_agent, user_prompt, api_semaphore_for_generator)\n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 3:\n", "                raise ValueError(f\"Expected at least 3 checkpoints for drug type {drug_type_name}, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in generate_drug_type_checkpoints for {drug_type_name} (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached for {drug_type_name}. Returning empty list.\")\n", "                return []\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    return []\n", "\n", "async def search_ich_guidelines_for_drug_type(drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:\n", "    \"\"\"\n", "    Search for relevant ICH guideline summaries for a specific drug type.\n", "    \n", "    Args:\n", "        drug_type_info: Dictionary containing drug type information including referenced ICH guidelines\n", "        checkpoints: List of checkpoint questions for filtering\n", "        \n", "    Returns:\n", "        List[Dict]: Relevant ICH guideline summaries\n", "    \"\"\"\n", "    referenced_ich_guidelines = drug_type_info.get('referenced_ich_guidelines', [])\n", "    \n", "    if not referenced_ich_guidelines:\n", "        print(f\"No ICH guidelines referenced for drug type: {drug_type_info.get('title', 'Unknown')}\")\n", "        return []\n", "    \n", "    # Create a section_info-like structure for compatibility with existing functions\n", "    drug_type_section_info = {\n", "        'section': f\"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}\",\n", "        'title': drug_type_info.get('title', ''),\n", "        'description': drug_type_info.get('description', ''),\n", "        'referenced_ich_guidelines': referenced_ich_guidelines,\n", "        'checkpoint_list': checkpoints\n", "    }\n", "    \n", "    # Use the existing search function\n", "    relevant_summaries = await search_summaries_by_llm(\n", "        section_info=drug_type_section_info,\n", "        ich_guidelines=referenced_ich_guidelines,\n", "        summary_level=1\n", "    )\n", "    \n", "    return relevant_summaries\n", "\n", "async def search_fda_guidelines_for_drug_type(drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:\n", "    \"\"\"\n", "    Search for relevant FDA guideline summaries for a specific drug type using batched evaluation.\n", "    \n", "    Args:\n", "        drug_type_info: Dictionary containing drug type information including referenced FDA guidelines\n", "        checkpoints: List of checkpoint questions for filtering\n", "        \n", "    Returns:\n", "        List[Dict]: Relevant FDA guideline summaries\n", "    \"\"\"\n", "    referenced_fda_guidelines = drug_type_info.get('referenced_fda_guidelines', [])\n", "    \n", "    if not referenced_fda_guidelines:\n", "        print(f\"No FDA guidelines referenced for drug type: {drug_type_info.get('title', 'Unknown')}\")\n", "        return []\n", "    \n", "    print(f\"Searching FDA guidelines for drug type {drug_type_info.get('title', '')}: {referenced_fda_guidelines}\")\n", "    \n", "    # Create a section_info-like structure for compatibility\n", "    drug_type_section_info = {\n", "        'section': f\"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}\",\n", "        'title': drug_type_info.get('title', ''),\n", "        'description': drug_type_info.get('description', ''),\n", "        'referenced_fda_guidelines': referenced_fda_guidelines,\n", "        'checkpoint_list': checkpoints\n", "    }\n", "    \n", "    # Fetch from FDA collections using parallel queries (same pattern as search_summaries_by_llm)\n", "    db = mongodb_client['mednova']\n", "    fda_summaries_collection = db['fda_docs_summaries']\n", "    \n", "    # Create tasks for parallel collection queries\n", "    async def fetch_fda_summaries_for_guideline(fda_guideline):\n", "        return await fda_summaries_collection.find({\n", "            \"fda_guideline\": fda_guideline,\n", "            \"summary_level\": 1  # Get top-level summaries first\n", "        }).to_list()\n", "    \n", "    # Execute all FDA guideline queries in parallel\n", "    tasks = [\n", "        fetch_fda_summaries_for_guideline(fda_guideline)\n", "        for fda_guideline in referenced_fda_guidelines\n", "    ]\n", "    \n", "    results = await asyncio.gather(*tasks)\n", "    \n", "    # Combine all results and add source metadata\n", "    all_fda_summaries = []\n", "    for fda_guideline, fda_summaries_for_guideline in zip(referenced_fda_guidelines, results):\n", "        print(f\"Found {len(fda_summaries_for_guideline)} FDA summaries for guideline: {fda_guideline}\")\n", "        for summary in fda_summaries_for_guideline:\n", "            # Add source metadata to each summary\n", "            summary[\"source_type\"] = \"FDA\"\n", "            summary[\"source_guideline\"] = fda_guideline\n", "            all_fda_summaries.append(summary)\n", "\n", "    print(f\"Found {len(all_fda_summaries)} total FDA summaries for drug type {drug_type_info.get('title', '')} across {len(referenced_fda_guidelines)} guidelines\")\n", "    \n", "    if not all_fda_summaries:\n", "        print(f\"No FDA summaries found for guidelines {referenced_fda_guidelines}\")\n", "        return []\n", "    \n", "    # Use batched evaluation (same pattern as search_summaries_by_llm)\n", "    if USE_BATCHED_EVALUATION and len(all_fda_summaries) > 1:\n", "        # Use batched approach for better performance with fallback to individual evaluation\n", "        try:\n", "            results = await evaluate_multiple_summaries_relevance(all_fda_summaries, drug_type_section_info, checkpoints)\n", "        except Exception as e:\n", "            print(f\"Batched evaluation failed: {e}, falling back to individual evaluation\")\n", "            # Fallback to individual evaluation\n", "            evaluation_tasks = [\n", "                evaluate_summary_relevance(summary[\"content\"], drug_type_section_info, checkpoints)\n", "                for summary in all_fda_summaries\n", "            ]\n", "            results = await asyncio.gather(*evaluation_tasks)\n", "    else:\n", "        # Use individual evaluation (for testing/comparison or single summary)\n", "        print(f\"Using individual evaluation for {len(all_fda_summaries)} FDA summaries\")\n", "        evaluation_tasks = [\n", "            evaluate_summary_relevance(summary[\"content\"], drug_type_section_info, checkpoints)\n", "            for summary in all_fda_summaries\n", "        ]\n", "        results = await asyncio.gather(*evaluation_tasks)\n", "    \n", "    # Process results and collect relevant summaries (same pattern as search_summaries_by_llm)\n", "    relevant_fda_summaries = []\n", "    for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(all_fda_summaries, results):\n", "        print(f\"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "        \n", "        if is_relevant:\n", "            print(f\"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is RELEVANT\")\n", "            summary[\"similarity_score\"] = average_score\n", "            relevant_fda_summaries.append(summary)\n", "        else:\n", "            print(f\"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is NOT RELEVANT\")\n", "    \n", "    print(f\"Found {len(relevant_fda_summaries)} relevant FDA summaries for drug type\")\n", "    return relevant_fda_summaries\n", "\n", "async def get_individual_summaries_for_drug_type_guidelines(relevant_summaries: List[Dict], drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:\n", "    \"\"\"\n", "    Get individual summaries for the relevant drug type guidelines.\n", "    \n", "    Args:\n", "        relevant_summaries: List of relevant top-level summaries (ICH and FDA)\n", "        drug_type_info: Drug type information\n", "        checkpoints: Checkpoint questions for filtering\n", "        \n", "    Returns:\n", "        List[Dict]: Relevant individual summaries\n", "    \"\"\"\n", "    individual_summaries = []\n", "    \n", "    # Create section_info for compatibility\n", "    drug_type_section_info = {\n", "        'section': f\"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}\",\n", "        'title': drug_type_info.get('title', ''),\n", "        'description': drug_type_info.get('description', ''),\n", "        'checkpoint_list': checkpoints\n", "    }\n", "    \n", "    async def process_guideline_summary(summary):\n", "        try:\n", "            source_type = summary.get('source_type', 'ICH')  # Default to ICH if not specified\n", "            \n", "            if source_type == 'FDA':\n", "                # Handle FDA summaries\n", "                summary_id = summary[\"_id\"]\n", "                db = mongodb_client['mednova']\n", "                fda_summaries_collection = db['fda_docs_summaries']\n", "                \n", "                # Get individual summaries for this FDA guideline\n", "                individual_fda_summaries = await fda_summaries_collection.find({\n", "                    \"fda_guideline\": summary.get('source_guideline', ''),\n", "                    \"summary_level\": 2  # Individual level summaries\n", "                }).to_list()\n", "                \n", "            else:\n", "                # Handle ICH summaries (existing logic)\n", "                summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(summary)\n", "                individual_fda_summaries = await get_summaries_for_final_summary(\n", "                    summary[\"_id\"], summary_name, chunk_name, key_name\n", "                )\n", "            \n", "            # Evaluate individual summaries\n", "            evaluated_summaries = []\n", "            \n", "            if individual_fda_summaries:\n", "                try:\n", "                    results = await evaluate_multiple_summaries_relevance(\n", "                        individual_fda_summaries, drug_type_section_info, checkpoints\n", "                    )\n", "                    \n", "                    for summary_item, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(individual_fda_summaries, results):\n", "                        if is_relevant:\n", "                            print(f\"Individual summary {summary_item['_id']} for drug type is RELEVANT with score {average_score}\")\n", "                            summary_item[\"similarity_score\"] = average_score\n", "                            summary_item[\"source_type\"] = source_type\n", "                            evaluated_summaries.append(summary_item)\n", "                        else:\n", "                            print(f\"Individual summary {summary_item['_id']} for drug type is NOT RELEVANT\")\n", "                            \n", "                except Exception as e:\n", "                    print(f\"Error evaluating individual summaries: {e}\")\n", "            \n", "            return evaluated_summaries\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing guideline summary {summary.get('_id', 'unknown')}: {e}\")\n", "            return []\n", "    \n", "    # Process all summaries in parallel\n", "    summary_tasks = [process_guideline_summary(summary) for summary in relevant_summaries]\n", "    summary_results = await asyncio.gather(*summary_tasks)\n", "    \n", "    # Flatten results\n", "    for evaluated_summaries in summary_results:\n", "        individual_summaries.extend(evaluated_summaries)\n", "    \n", "    print(f\"Found {len(individual_summaries)} relevant individual summaries for drug type\")\n", "    return individual_summaries\n", "\n", "async def combine_drug_type_summaries(individual_summaries: List[Dict], drug_type_info: Dict) -> str:\n", "    \"\"\"\n", "    Combine individual drug type guideline summaries into a comprehensive summary.\n", "    \n", "    Args:\n", "        individual_summaries: List of relevant individual summaries\n", "        drug_type_info: Drug type information\n", "        \n", "    Returns:\n", "        str: Combined comprehensive summary for the drug type\n", "    \"\"\"\n", "    if not individual_summaries:\n", "        return f\"No relevant guideline summaries found for {drug_type_info.get('title', 'this drug type')}.\"\n", "    \n", "    # Extract content from summaries\n", "    summary_contents = [summary.get('content', '') for summary in individual_summaries if summary.get('content', '').strip()]\n", "    \n", "    if not summary_contents:\n", "        return f\"No valid content found in summaries for {drug_type_info.get('title', 'this drug type')}.\"\n", "    \n", "    # Use divide and conquer approach similar to checkpoint merging\n", "    return await combine_summaries_incrementally(summary_contents, drug_type_info)\n", "\n", "def create_summary_content_batches(summary_contents: List[str], drug_type_info: Dict, max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_summaries_per_batch: int = 3) -> List[List[str]]:\n", "    \"\"\"\n", "    Create batches of summary contents for efficient combination using token-based logic.\n", "    \n", "    Args:\n", "        summary_contents: List of summary content strings\n", "        drug_type_info: Drug type information (used for token calculation context)\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_summaries_per_batch: Maximum number of summaries per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of summary content strings\n", "    \"\"\"\n", "    if not summary_contents:\n", "        return []\n", "    \n", "    # Calculate base tokens for system prompt and drug type context (estimated)\n", "    drug_type_context = f\"Drug Type: {drug_type_info.get('title', '')}\\nDescription: {drug_type_info.get('description', '')}\"\n", "    base_tokens = calculate_number_of_tokens(drug_type_context) + 2000  # Buffer for system prompt and formatting\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for summary_content in summary_contents:\n", "        # Calculate tokens for this summary content including XML formatting\n", "        summary_tokens = calculate_number_of_tokens(f\"<summary_{len(current_batch) + 1}>\\n{summary_content}\\n</summary_{len(current_batch) + 1}>\")\n", "        \n", "        # Check if adding this summary would exceed limits\n", "        if (current_tokens + summary_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_summaries_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add summary to current batch\n", "        current_batch.append(summary_content)\n", "        current_tokens += summary_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n", "\n", "async def combine_summaries_incrementally(summary_contents: List[str], drug_type_info: Dict) -> str:\n", "    \"\"\"\n", "    Combine summary contents incrementally using divide and conquer approach with token-based batching.\n", "    \n", "    Args:\n", "        summary_contents: List of summary content strings\n", "        drug_type_info: Drug type information\n", "        \n", "    Returns:\n", "        str: Combined comprehensive summary\n", "    \"\"\"\n", "    if not summary_contents:\n", "        return \"\"\n", "    \n", "    if len(summary_contents) == 1:\n", "        return await _combine_single_summary_for_drug_type(summary_contents[0], drug_type_info)\n", "    \n", "    print(f\"Combining {len(summary_contents)} summary contents incrementally with token-based batching\")\n", "    \n", "    # Iteratively merge summaries using token-based batching\n", "    current_summaries = summary_contents.copy()\n", "    \n", "    while len(current_summaries) > 1:\n", "        # Create optimal batches based on token count and number of summaries\n", "        batches = create_summary_content_batches(current_summaries, drug_type_info)\n", "        \n", "        print(f\"Created {len(batches)} batches for summary combination\")\n", "        for i, batch in enumerate(batches):\n", "            total_tokens = sum(calculate_number_of_tokens(summary) for summary in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} summaries with {total_tokens} total tokens\")\n", "        \n", "        # Ensure no batch has only one item to avoid infinite loop\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, merge it with the last batch\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        # Process all batches in parallel\n", "        batch_tasks = [\n", "            _combine_summary_batch_for_drug_type(batch, drug_type_info)\n", "            for batch in batches\n", "        ]\n", "        \n", "        # Wait for all batches to complete\n", "        current_summaries = await asyncio.gather(*batch_tasks)\n", "        \n", "        # Filter out any empty results\n", "        current_summaries = [summary for summary in current_summaries if summary and summary.strip()]\n", "        \n", "        print(f\"Combined {len(batches)} batches into {len(current_summaries)} intermediate results\")\n", "        \n", "        # Safety check to prevent infinite loops\n", "        if len(current_summaries) == len(batches) and all(len(batch) == 1 for batch in batches):\n", "            # If we're not making progress and all batches are single items, just concatenate\n", "            print(\"Warning: No progress in incremental combination, concatenating results\")\n", "            return \"\\n\\n---\\n\\n\".join(current_summaries)\n", "    \n", "    print(f\"Final incremental combination complete\")\n", "    return current_summaries[0] if current_summaries else \"\"\n", "\n", "async def _combine_single_summary_for_drug_type(summary_content: str, drug_type_info: Dict) -> str:\n", "    \"\"\"\n", "    Process a single summary for drug type context.\n", "    \n", "    Args:\n", "        summary_content: The summary content\n", "        drug_type_info: Drug type information\n", "        \n", "    Returns:\n", "        str: Processed summary with drug type context\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"\"\"You are an expert pharmaceutical regulatory scientist. Your task is to process and contextualize regulatory guideline summaries specifically for a given drug type in the context of Pre-IND, IND, and NDA submissions.\"\"\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are processing a regulatory guideline summary for the drug type: **{drug_type_info.get('title', '')}**\n", "\n", "Drug Type Description: {drug_type_info.get('description', '')}\n", "\n", "Regulatory Summary to Process:\n", "<summary_content>\n", "{summary_content}\n", "</summary_content>\n", "\n", "Your task is to process this summary and present it in a way that is specifically relevant to {drug_type_info.get('title', 'this drug type')} for regulatory submissions (Pre-IND, IND, NDA).\n", "\n", "Before processing the summary, conduct a thorough analysis in your thinking block using <summary_processing_analysis> tags:\n", "\n", "1. **Content Analysis**: \n", "   - Read through the regulatory summary and identify key themes\n", "   - Note specific regulatory requirements, guidelines, and recommendations\n", "   - Identify technical terminology and regulatory language used\n", "\n", "2. **Drug Type Relevance Assessment**:\n", "   - Analyze which parts of the summary are most relevant to {drug_type_info.get('title', 'this drug type')}\n", "   - Identify content that may not be applicable to this specific drug type\n", "   - Consider unique characteristics of {drug_type_info.get('title', 'this drug type')} from the description\n", "\n", "3. **Regulatory Areas Mapping**:\n", "   - Categorize relevant content by regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)\n", "   - Identify which regulatory submission stages (Pre-IND, IND, NDA) each requirement applies to\n", "   - Note any drug-type-specific regulatory pathways or considerations\n", "\n", "4. **Content Organization Strategy**:\n", "   - Plan how to structure the processed summary for maximum clarity\n", "   - Determine the logical flow of information for regulatory scientists\n", "   - Consider how to emphasize the most critical requirements for this drug type\n", "\n", "5. **Processing Approach**:\n", "   - Decide what information to emphasize, de-emphasize, or exclude\n", "   - Plan how to maintain technical accuracy while improving drug-type relevance\n", "   - Consider how to make the content actionable for regulatory submissions\n", "\n", "Now, based on your analysis, process the summary following these guidelines:\n", "\n", "**Processing Instructions:**\n", "1. Extract and emphasize information that is specifically relevant to {drug_type_info.get('title', 'this drug type')}\n", "2. Organize the content by key regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)\n", "3. Maintain the technical accuracy and regulatory specificity\n", "4. Focus on requirements that would be applicable to Pre-IND, IND, and NDA submissions\n", "5. Present the information in a clear, structured format with proper headings\n", "6. Remove or de-emphasize information that is not relevant to this drug type\n", "7. Use markdown formatting for better readability and structure\n", "8. Highlight critical requirements and drug-type-specific considerations\n", "\n", "Present your final processed summary in <final_processed_summary> tags. The content within these tags should be a comprehensive, well-organized summary that regulatory scientists can use to understand the specific requirements for {drug_type_info.get('title', 'this drug type')}. Your final output should not duplicate or rehash any of the work you did in the thinking block.\n", "\"\"\"\n", "\n", "            agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=str,\n", "                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "            \n", "            # Extract content from final_processed_summary tags\n", "            extracted_content = extract_content_from_tags(response.output, 'final_processed_summary')\n", "            return extracted_content\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in _combine_single_summary_for_drug_type (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                return summary_content  # Return original content if all retries fail\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    return summary_content\n", "\n", "async def _combine_summary_batch_for_drug_type(summary_batch: List[str], drug_type_info: Dict) -> str:\n", "    \"\"\"\n", "    Combine a batch of summaries for drug type context.\n", "    \n", "    Args:\n", "        summary_batch: List of summary contents to combine\n", "        drug_type_info: Drug type information\n", "        \n", "    Returns:\n", "        str: Combined summary\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"\"\"You are an expert pharmaceutical regulatory scientist. Your task is to combine multiple regulatory guideline summaries into a comprehensive, coherent summary specifically for a given drug type in the context of Pre-IND, IND, and NDA submissions.\"\"\"\n", "            \n", "            # Prepare summary content\n", "            summaries_text = \"\"\n", "            for i, summary in enumerate(summary_batch, 1):\n", "                summaries_text += f\"<summary_{i}>\\n{summary}\\n</summary_{i}>\\n\\n\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are combining multiple regulatory guideline summaries for the drug type: **{drug_type_info.get('title', '')}**\n", "\n", "Drug Type Description: {drug_type_info.get('description', '')}\n", "\n", "Regulatory Summaries to Combine:\n", "{summaries_text}\n", "\n", "Your task is to create a comprehensive, unified summary that combines all the relevant information from these summaries specifically for {drug_type_info.get('title', 'this drug type')}.\n", "\n", "Before combining the summaries, conduct a thorough analysis in your thinking block using <summary_combination_analysis> tags:\n", "\n", "1. **Individual Summary Review**:\n", "   - Read through each regulatory summary and identify their key contributions\n", "   - Note unique information and overlapping content across summaries\n", "   - Identify the strongest regulatory guidance from each source\n", "\n", "2. **Content Categorization**:\n", "   - Group similar content from different summaries by regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)\n", "   - Identify which summaries provide the most comprehensive guidance for each area\n", "   - Note any conflicting or contradictory information between summaries\n", "\n", "3. **Drug Type Relevance Assessment**:\n", "   - Analyze which combined content is most critical for {drug_type_info.get('title', 'this drug type')}\n", "   - Consider the unique regulatory challenges and requirements for this drug type\n", "   - Prioritize information based on Pre-IND, IND, and NDA submission needs\n", "\n", "4. **Integration Strategy**:\n", "   - Plan how to merge overlapping information without redundancy\n", "   - Determine how to resolve any contradictions (prioritize more comprehensive/recent guidance)\n", "   - Design the structure and flow for the combined summary\n", "\n", "5. **Quality Enhancement Approach**:\n", "   - Identify opportunities to strengthen the combined guidance\n", "   - Plan how to create logical connections between different regulatory areas\n", "   - Consider how to make the final summary more actionable for regulatory scientists\n", "\n", "Now, based on your analysis, combine the summaries following these guidelines:\n", "\n", "**Combination Instructions:**\n", "1. Combine all relevant information while avoiding redundancy\n", "2. Organize by key regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)\n", "3. Emphasize information most critical to {drug_type_info.get('title', 'this drug type')}\n", "4. Ensure the combined summary is coherent and well-structured with clear headings\n", "5. Maintain technical accuracy and regulatory specificity\n", "6. Focus on Pre-IND, IND, and NDA submission requirements\n", "7. Resolve any contradictions by providing the most comprehensive guidance\n", "8. Create a unified narrative that flows logically\n", "9. Use markdown formatting for better readability and organization\n", "10. Highlight the most critical requirements and drug-type-specific considerations\n", "\n", "Present your final combined summary in <final_combined_summary> tags. The content within these tags should be a comprehensive, well-organized combined summary that regulatory scientists can use as a single reference for {drug_type_info.get('title', 'this drug type')} requirements. Your final output should not duplicate or rehash any of the work you did in the thinking block.\n", "\"\"\"\n", "\n", "            agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=str,\n", "                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                retries=3\n", "            )\n", "\n", "            response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "            \n", "            # Extract content from final_combined_summary tags\n", "            extracted_content = extract_content_from_tags(response.output, 'final_combined_summary')\n", "            return extracted_content\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in _combine_summary_batch_for_drug_type (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                # Fallback: concatenate summaries with basic formatting\n", "                return \"\\n\\n---\\n\\n\".join(summary_batch)\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    return \"\\n\\n---\\n\\n\".join(summary_batch)\n", "\n", "async def process_drug_type_guidelines(drug_type_name: str, drug_type_definition: Dict) -> Dict:\n", "    \"\"\"\n", "    Process guidelines for a single drug type.\n", "    \n", "    Args:\n", "        drug_type_name: Name of the drug type\n", "        drug_type_definition: Drug type definition from JSON\n", "        \n", "    Returns:\n", "        Dict: Processed drug type guideline information\n", "    \"\"\"\n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"PROCESSING DRUG TYPE: {drug_type_name}\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    # Step 1: Generate checkpoints for this drug type\n", "    print(f\"Step 1: Generating checkpoints for {drug_type_name}...\")\n", "    checkpoints = await generate_drug_type_checkpoints(drug_type_name, drug_type_definition)\n", "    print(f\"Generated {len(checkpoints)} checkpoints for {drug_type_name}\")\n", "    \n", "    if not checkpoints:\n", "        print(f\"No checkpoints generated for {drug_type_name}, skipping...\")\n", "        return {\n", "            \"drug_type_name\": drug_type_name,\n", "            \"drug_type_definition\": drug_type_definition,\n", "            \"checkpoints\": [],\n", "            \"relevant_ich_summaries\": [],\n", "            \"relevant_fda_summaries\": [],\n", "            \"individual_summaries\": [],\n", "            \"combined_summary\": f\"No guidelines found for {drug_type_name}.\",\n", "            \"processing_status\": \"failed\"\n", "        }\n", "    \n", "    # Step 2: Search for relevant ICH guidelines\n", "    print(f\"Step 2: Searching relevant ICH guidelines for {drug_type_name}...\")\n", "    relevant_ich_summaries = await search_ich_guidelines_for_drug_type(drug_type_definition, checkpoints)\n", "    print(f\"Found {len(relevant_ich_summaries)} relevant ICH summaries for {drug_type_name}\")\n", "    \n", "    # Step 3: Search for relevant FDA guidelines\n", "    print(f\"Step 3: Searching relevant FDA guidelines for {drug_type_name}...\")\n", "    relevant_fda_summaries = await search_fda_guidelines_for_drug_type(drug_type_definition, checkpoints)\n", "    print(f\"Found {len(relevant_fda_summaries)} relevant FDA summaries for {drug_type_name}\")\n", "    \n", "    # Step 4: Get individual summaries\n", "    print(f\"Step 4: Getting individual summaries for {drug_type_name}...\")\n", "    all_relevant_summaries = relevant_ich_summaries + relevant_fda_summaries\n", "    individual_summaries = await get_individual_summaries_for_drug_type_guidelines(\n", "        all_relevant_summaries, drug_type_definition, checkpoints\n", "    )\n", "    print(f\"Found {len(individual_summaries)} relevant individual summaries for {drug_type_name}\")\n", "    \n", "    # Step 5: <PERSON><PERSON><PERSON> summaries into final comprehensive summary\n", "    print(f\"Step 5: Combining summaries for {drug_type_name}...\")\n", "    combined_summary = await combine_drug_type_summaries(individual_summaries, drug_type_definition)\n", "    print(f\"Generated combined summary for {drug_type_name} ({len(combined_summary)} characters)\")\n", "    \n", "    result = {\n", "        \"drug_type_name\": drug_type_name,\n", "        \"drug_type_definition\": drug_type_definition,\n", "        \"checkpoints\": checkpoints,\n", "        \"relevant_ich_summaries\": [\n", "            {\n", "                \"_id\": str(summary[\"_id\"]),\n", "                \"content\": summary.get(\"content\", \"\")[:500] + \"...\" if len(summary.get(\"content\", \"\")) > 500 else summary.get(\"content\", \"\"),\n", "                \"similarity_score\": summary.get(\"similarity_score\", 0.0),\n", "                \"source_type\": \"ICH\"\n", "            } for summary in relevant_ich_summaries\n", "        ],\n", "        \"relevant_fda_summaries\": [\n", "            {\n", "                \"_id\": str(summary[\"_id\"]),\n", "                \"content\": summary.get(\"content\", \"\")[:500] + \"...\" if len(summary.get(\"content\", \"\")) > 500 else summary.get(\"content\", \"\"),\n", "                \"similarity_score\": summary.get(\"similarity_score\", 0.0),\n", "                \"source_type\": \"FDA\"\n", "            } for summary in relevant_fda_summaries\n", "        ],\n", "        \"individual_summaries\": [\n", "            {\n", "                \"_id\": str(summary[\"_id\"]),\n", "                \"content\": summary.get(\"content\", \"\")[:300] + \"...\" if len(summary.get(\"content\", \"\")) > 300 else summary.get(\"content\", \"\"),\n", "                \"similarity_score\": summary.get(\"similarity_score\", 0.0),\n", "                \"source_type\": summary.get(\"source_type\", \"ICH\")\n", "            } for summary in individual_summaries\n", "        ],\n", "        \"combined_summary\": combined_summary,\n", "        \"processing_status\": \"success\"\n", "    }\n", "    \n", "    print(f\"✅ Completed processing {drug_type_name}\")\n", "    return result\n", "\n", "async def preprocess_all_drug_type_guidelines(drug_types_json_path: str = \"structured jsons/drug_types.json\") -> Dict:\n", "    \"\"\"\n", "    Preprocess guidelines for all drug types and store results.\n", "    \n", "    Args:\n", "        drug_types_json_path: Path to drug types JSON file\n", "        \n", "    Returns:\n", "        Dict: Complete drug type guidelines processing results\n", "    \"\"\"\n", "    print(f\"\\n{'='*100}\")\n", "    print(\"STARTING DRUG TYPE GUIDELINES PREPROCESSING\")\n", "    print(f\"{'='*100}\")\n", "    \n", "    # Load drug type definitions\n", "    drug_type_definitions = load_drug_type_definitions(drug_types_json_path)\n", "    print(f\"Loaded {len(drug_type_definitions)} drug type definitions\")\n", "    \n", "    # Process all drug types in parallel\n", "    drug_type_tasks = [\n", "        process_drug_type_guidelines(drug_type_name, drug_type_definition)\n", "        for drug_type_name, drug_type_definition in drug_type_definitions.items()\n", "    ]\n", "    \n", "    print(f\"🚀 Processing all {len(drug_type_tasks)} drug types in parallel...\")\n", "    drug_type_results = await asyncio.gather(*drug_type_tasks, return_exceptions=True)\n", "    \n", "    # Process results and handle exceptions\n", "    processed_drug_types = {}\n", "    successful_count = 0\n", "    failed_count = 0\n", "    \n", "    for i, result in enumerate(drug_type_results):\n", "        drug_type_name = list(drug_type_definitions.keys())[i]\n", "        \n", "        if isinstance(result, Exception):\n", "            print(f\"❌ Error processing {drug_type_name}: {result}\")\n", "            failed_count += 1\n", "            processed_drug_types[drug_type_name] = {\n", "                \"drug_type_name\": drug_type_name,\n", "                \"processing_status\": \"failed\",\n", "                \"error\": str(result),\n", "                \"combined_summary\": f\"Error processing guidelines for {drug_type_name}: {result}\"\n", "            }\n", "        else:\n", "            print(f\"✅ Successfully processed {drug_type_name}\")\n", "            successful_count += 1\n", "            processed_drug_types[drug_type_name] = result\n", "    \n", "    # Create final result structure\n", "    final_result = {\n", "        \"drug_type_guidelines\": processed_drug_types,\n", "        \"metadata\": {\n", "            \"processing_date\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            \"total_drug_types\": len(drug_type_definitions),\n", "            \"successful_processing\": successful_count,\n", "            \"failed_processing\": failed_count,\n", "            \"drug_types_processed\": list(drug_type_definitions.keys())\n", "        }\n", "    }\n", "    \n", "    print(f\"\\n{'='*100}\")\n", "    print(\"DRUG TYPE GUIDELINES PREPROCESSING COMPLETE\")\n", "    print(f\"{'='*100}\")\n", "    print(f\"✅ Successfully processed: {successful_count}/{len(drug_type_definitions)} drug types\")\n", "    print(f\"❌ Failed processing: {failed_count}/{len(drug_type_definitions)} drug types\")\n", "    \n", "    return final_result\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["\n", "# # DRUG TYPE GUIDELINES PREPROCESSING\n", "# # Preprocess guidelines for all drug types based on the classification results\n", "# print(\"\\n\" + \"=\"*100)\n", "# print(\"STARTING DRUG TYPE GUIDELINES PREPROCESSING\")\n", "# print(\"=\"*100)\n", "\n", "# # Preprocess all drug type guidelines\n", "# drug_type_guidelines_result = await preprocess_all_drug_type_guidelines(drug_types_json_path=DRUG_TYPES_JSON_PATH)\n", "\n", "# # Save the drug type guidelines processing results\n", "# write_to_json(drug_type_guidelines_result, \"drug_type_guidelines_processed.json\")\n", "\n", "# print(f\"\\n🎯 Drug Type Guidelines Preprocessing Results:\")\n", "# print(f\"📋 Total Drug Types: {drug_type_guidelines_result['metadata']['total_drug_types']}\")\n", "# print(f\"✅ Successfully Processed: {drug_type_guidelines_result['metadata']['successful_processing']}\")\n", "# print(f\"❌ Failed Processing: {drug_type_guidelines_result['metadata']['failed_processing']}\")\n", "# print(f\"📄 Detailed report saved to: drug_type_guidelines_processed.json\")\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["\n", "def inject_drug_type_context_into_prompt(\n", "    base_prompt: str,\n", "    drug_type_context: Dict\n", ") -> str:\n", "    \"\"\"\n", "    Inject drug type regulatory context at the very start of prompts.\n", "    \n", "    Args:\n", "        base_prompt: The original prompt string\n", "        drug_type_context: Drug type context from get_drug_type_context_for_gap_analysis()\n", "        \n", "    Returns:\n", "        Enhanced prompt with drug type context at the beginning\n", "    \"\"\"\n", "    if not drug_type_context.get('classification_available', False):\n", "        return base_prompt\n", "    \n", "    enhanced_context = drug_type_context.get('enhanced_context', '')\n", "    \n", "    if not enhanced_context.strip():\n", "        return base_prompt\n", "    \n", "    # Simply add context at the very beginning of the prompt\n", "    enhanced_prompt = enhanced_context + base_prompt\n", "    \n", "    return enhanced_prompt\n", "\n", "def create_drug_classification_batches(chunks: List[Dict], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_chunks_per_batch: int = 15) -> List[List[Dict]]:\n", "    \"\"\"\n", "    Create batches of chunks for efficient drug type classification.\n", "    \n", "    Args:\n", "        chunks: List of chunk dictionaries to classify\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_chunks_per_batch: Maximum number of chunks per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of chunks\n", "    \"\"\"\n", "    if not chunks:\n", "        return []\n", "    \n", "    # Calculate base tokens for system prompt and drug type definitions (estimated)\n", "    base_tokens = 8000  # Buffer for system prompt, drug type definitions, and structured output\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for chunk in chunks:\n", "        # Calculate tokens for this chunk including XML formatting\n", "        chunk_content = chunk.get('content', '')\n", "        chunk_id = str(chunk.get('_id', 'unknown'))\n", "        \n", "        # Estimate tokens including XML formatting\n", "        chunk_tokens = calculate_number_of_tokens(f\"<chunk_{len(current_batch) + 1}>\\nID: {chunk_id}\\nContent: {chunk_content}\\n</chunk_{len(current_batch) + 1}>\")\n", "        \n", "        # Check if adding this chunk would exceed limits\n", "        if (current_tokens + chunk_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_chunks_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add chunk to current batch\n", "        current_batch.append(chunk)\n", "        current_tokens += chunk_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["\n", "async def classify_drug_type_batch(chunk_batch: List[Dict], drug_type_definitions: Dict) -> DrugTypeClassification:\n", "    \"\"\"\n", "    Classify drug type for a single batch of chunks using LLM.\n", "    \n", "    Args:\n", "        chunk_batch: List of chunk dictionaries to classify\n", "        drug_type_definitions: Dictionary containing drug type definitions\n", "        \n", "    Returns:\n", "        DrugTypeClassification: Structured classification result with scores for all 6 drug types\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"\"\"You are an expert pharmaceutical regulatory scientist specializing in drug development and classification for regulatory submissions. Your task is to analyze input document chunks and classify the drug type described across all 6 regulatory categories used in Pre-IND, IND, and NDA submissions.\"\"\"\n", "\n", "            # Build batched chunks content\n", "            batched_chunks_content = \"<input_document_chunks>\\n\"\n", "            for i, chunk in enumerate(chunk_batch, 1):\n", "                chunk_id = str(chunk.get('_id', f'chunk_{i}'))\n", "                chunk_content = chunk.get('content', '')\n", "                batched_chunks_content += f\"<chunk_{i}>\\nID: {chunk_id}\\nContent: {chunk_content}\\n</chunk_{i}>\\n\\n\"\n", "            batched_chunks_content += \"</input_document_chunks>\"\n", "\n", "            # Build drug type definitions content\n", "            drug_types_content = \"<drug_type_definitions>\\n\"\n", "            for drug_type_name, definition in drug_type_definitions.items():\n", "                drug_types_content += f\"<drug_type>\\n\"\n", "                drug_types_content += f\"<name>{drug_type_name}</name>\\n\"\n", "                drug_types_content += f\"<description>{definition['description']}</description>\\n\"\n", "                if definition.get('referenced_ich_guidelines'):\n", "                    drug_types_content += f\"<referenced_ich_guidelines>{', '.join(definition['referenced_ich_guidelines'])}</referenced_ich_guidelines>\\n\"\n", "                if definition.get('referenced_fda_guidelines'):\n", "                    drug_types_content += f\"<referenced_fda_guidelines>{', '.join(definition['referenced_fda_guidelines'])}</referenced_fda_guidelines>\\n\"\n", "                drug_types_content += f\"</drug_type>\\n\\n\"\n", "            drug_types_content += \"</drug_type_definitions>\"\n", "\n", "            user_prompt = f\"\"\"\n", "You are analyzing input document chunks for drug type classification in the context of regulatory submissions (Pre-IND, IND, NDA). \n", "\n", "{batched_chunks_content}\n", "\n", "{drug_types_content}\n", "\n", "**CRITICAL CLASSIFICATION REQUIREMENTS:**\n", "\n", "1. **Regulatory Context**: This classification is for regulatory submission purposes (Pre-IND/IND/NDA), so focus on regulatory-relevant characteristics and terminology used in submissions.\n", "\n", "2. **Mandatory Analysis**: You MUST evaluate ALL 6 drug types and provide scores for each, even if some seem unlikely.\n", "\n", "3. **High Standards for High Scores**: Only assign scores ≥8 when you have strong, clear evidence from the input documents. Be conservative and rigorous in your scoring.\n", "\n", "4. **Evidence-Based Scoring**: Base your scores on specific textual evidence from the input chunks, not general assumptions.\n", "\n", "Before providing your final classification, conduct a thorough analysis in your thinking block using <drug_classification_analysis> tags:\n", "\n", "1. **Document Content Review**: \n", "   - Summarize the key content themes across all chunks\n", "   - Identify manufacturing processes, mechanisms of action, regulatory terminology\n", "   - Note any specific technical details that indicate drug type\n", "\n", "2. **Evidence Mapping for Each Drug Type**:\n", "   For each of the 6 drug types, analyze:\n", "   - What evidence FROM THE INPUT DOCUMENTS supports this classification\n", "   - What evidence contradicts this classification  \n", "   - Quote specific phrases or technical terms that are relevant\n", "   - Consider regulatory context and terminology used\n", "\n", "3. **Scoring Rationale**:\n", "   - Explain your confidence level for each drug type (0-10 scale)\n", "   - Justify why certain types score higher than others\n", "   - Ensure you're being appropriately conservative (high scores only for strong evidence)\n", "\n", "4. **Primary Classification Logic**:\n", "   - Determine which drug type has the strongest evidence\n", "   - Consider if this is a clear case or if multiple types are possible\n", "   - Assess overall confidence level\n", "\n", "**SCORING GUIDELINES:**\n", "- **9-10**: Overwhelming evidence, multiple clear indicators, high regulatory confidence\n", "- **8-8.9**: Strong evidence, clear indicators, good regulatory confidence  \n", "- **6-7.9**: Moderate evidence, some indicators, medium confidence\n", "- **4-5.9**: Weak evidence, few indicators, low confidence\n", "- **1-3.9**: Minimal evidence, contradictory indicators, very low confidence\n", "- **0**: No evidence or strong contradictory evidence\n", "\n", "Your final response must include:\n", "- Scores (0-10) for ALL 6 drug types with detailed justifications\n", "- Key evidence excerpts from the input documents for each score\n", "- Primary classification (highest scoring drug type)\n", "- Overall confidence level assessment\n", "- Summary justification for the primary classification\n", "\n", "Focus on regulatory submission context and be rigorous in your evidence requirements for high confidence scores.\n", "\"\"\"\n", "\n", "            output_type = str\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                output_type = DrugTypeClassification\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=critique_model,\n", "                    system_prompt=system_prompt,\n", "                    output_type=output_type,\n", "                    model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_critique)\n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            result = await retry_on_empty_response(make_api_call)\n", "\n", "            # Process the result\n", "            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):\n", "                return result\n", "            else:\n", "                # Parse JSON response for non-structured output\n", "                try:\n", "                    # Try to extract JSON from the response\n", "                    json_match = re.search(r'\\{.*\\}', result, re.DOTALL)\n", "                    if json_match:\n", "                        json_str = json_match.group(0)\n", "                        classification_data = json.loads(json_str)\n", "                        \n", "                        # Create DrugTypeClassification object\n", "                        drug_type_scores = []\n", "                        for score_data in classification_data.get(\"drug_type_scores\", []):\n", "                            drug_type_scores.append(DrugTypeScore(\n", "                                drug_type_name=score_data.get(\"drug_type_name\", \"\"),\n", "                                score=score_data.get(\"score\", 0.0),\n", "                                justification=score_data.get(\"justification\", \"\"),\n", "                                key_evidence=score_data.get(\"key_evidence\", [])\n", "                            ))\n", "                        \n", "                        return DrugTypeClassification(\n", "                            drug_type_scores=drug_type_scores,\n", "                            primary_classification=classification_data.get(\"primary_classification\", \"Unknown\"),\n", "                            confidence_level=classification_data.get(\"confidence_level\", \"uncertain\"),\n", "                            overall_justification=classification_data.get(\"overall_justification\", \"\")\n", "                        )\n", "                    else:\n", "                        # Fallback if JSON parsing fails\n", "                        print(\"Warning: Could not extract JSON from drug type classification response\")\n", "                        retry_count += 1\n", "                except json.JSONDecodeError as e:\n", "                    print(f\"Error parsing JSON response from drug type classification: {e}\")\n", "                    retry_count += 1\n", "                \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in classify_drug_type_batch (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Returning default classification.\")\n", "                # Return default classification with all drug types scored at 0\n", "                drug_type_names = list(drug_type_definitions.keys())\n", "                default_scores = [\n", "                    DrugTypeScore(\n", "                        drug_type_name=name,\n", "                        score=0.0,\n", "                        justification=\"Error occurred during classification\",\n", "                        key_evidence=[]\n", "                    ) for name in drug_type_names\n", "                ]\n", "                return DrugTypeClassification(\n", "                    drug_type_scores=default_scores,\n", "                    primary_classification=\"Unknown\",\n", "                    confidence_level=\"uncertain\",\n", "                    overall_justification=\"Error occurred during classification\"\n", "                )\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    drug_type_names = list(drug_type_definitions.keys())\n", "    default_scores = [\n", "        DrugTypeScore(\n", "            drug_type_name=name,\n", "            score=0.0,\n", "            justification=\"Unexpected error during classification\",\n", "            key_evidence=[]\n", "        ) for name in drug_type_names\n", "    ]\n", "    return DrugTypeClassification(\n", "        drug_type_scores=default_scores,\n", "        primary_classification=\"Unknown\",\n", "        confidence_level=\"uncertain\",\n", "        overall_justification=\"Unexpected error during classification\"\n", "    )\n", "\n", "async def aggregate_drug_type_classifications(batch_classifications: List[DrugTypeClassification], drug_type_definitions: Dict) -> AggregatedDrugTypeClassification:\n", "    \"\"\"\n", "    Aggregate drug type classifications from multiple batches into a final classification.\n", "    \n", "    Args:\n", "        batch_classifications: List of classification results from different batches\n", "        drug_type_definitions: Dictionary containing drug type definitions\n", "        \n", "    Returns:\n", "        AggregatedDrugTypeClassification: Final aggregated classification result\n", "    \"\"\"\n", "    if not batch_classifications:\n", "        # Return default classification if no batches\n", "        drug_type_names = list(drug_type_definitions.keys())\n", "        default_scores = [\n", "            DrugTypeScore(\n", "                drug_type_name=name,\n", "                score=0.0,\n", "                justification=\"No batches available for classification\",\n", "                key_evidence=[]\n", "            ) for name in drug_type_names\n", "        ]\n", "        return AggregatedDrugTypeClassification(\n", "            final_classification=\"Unknown\",\n", "            aggregated_scores=default_scores,\n", "            confidence_level=\"uncertain\",\n", "            batch_count=0,\n", "            consensus_strength=\"weak\",\n", "            final_justification=\"No classification batches available\"\n", "        )\n", "    \n", "    # Collect all drug type names from the first classification (should be consistent)\n", "    drug_type_names = [score.drug_type_name for score in batch_classifications[0].drug_type_scores]\n", "    \n", "    # Aggregate scores for each drug type\n", "    aggregated_scores = []\n", "    for drug_type_name in drug_type_names:\n", "        # Collect scores for this drug type across all batches\n", "        type_scores = []\n", "        type_justifications = []\n", "        type_evidence = []\n", "        \n", "        for classification in batch_classifications:\n", "            for score_obj in classification.drug_type_scores:\n", "                if score_obj.drug_type_name == drug_type_name:\n", "                    type_scores.append(score_obj.score)\n", "                    if score_obj.justification:\n", "                        type_justifications.append(score_obj.justification)\n", "                    type_evidence.extend(score_obj.key_evidence)\n", "                    break\n", "        \n", "        # Calculate aggregated score (weighted average with higher weights for higher scores)\n", "        if type_scores:\n", "            # Sort scores and weight them (higher scores get more weight)\n", "            sorted_scores = sorted(type_scores, reverse=True)\n", "            if len(sorted_scores) >= 3:\n", "                # Weight top 3 scores: 50%, 30%, 20%\n", "                aggregated_score = (sorted_scores[0] * 0.5 + sorted_scores[1] * 0.3 + sorted_scores[2] * 0.2)\n", "            elif len(sorted_scores) == 2:\n", "                # Weight top 2 scores: 70%, 30%\n", "                aggregated_score = (sorted_scores[0] * 0.7 + sorted_scores[1] * 0.3)\n", "            else:\n", "                # Single score\n", "                aggregated_score = sorted_scores[0]\n", "        else:\n", "            aggregated_score = 0.0\n", "        \n", "        # Combine justifications (take top justifications)\n", "        combined_justification = \" | \".join(type_justifications[:3]) if type_justifications else \"No justification available\"\n", "        \n", "        # Deduplicate evidence\n", "        unique_evidence = list(set(type_evidence))[:5]  # Top 5 unique evidence pieces\n", "        \n", "        aggregated_scores.append(DrugTypeScore(\n", "            drug_type_name=drug_type_name,\n", "            score=round(aggregated_score, 2),\n", "            justification=combined_justification,\n", "            key_evidence=unique_evidence\n", "        ))\n", "    \n", "    # Determine final classification (highest scoring drug type)\n", "    if aggregated_scores:\n", "        best_score = max(aggregated_scores, key=lambda x: x.score)\n", "        final_classification = best_score.drug_type_name\n", "        max_score = best_score.score\n", "    else:\n", "        final_classification = \"Unknown\"\n", "        max_score = 0.0\n", "    \n", "    # Determine final confidence level\n", "    if max_score >= 8.0:\n", "        confidence_level = \"high\"\n", "    elif max_score >= 6.0:\n", "        confidence_level = \"medium\"\n", "    elif max_score >= 4.0:\n", "        confidence_level = \"low\"\n", "    else:\n", "        confidence_level = \"uncertain\"\n", "    \n", "    # Calculate consensus strength\n", "    batch_count = len(batch_classifications)\n", "    if batch_count <= 1:\n", "        consensus_strength = \"weak\"\n", "    else:\n", "        # Check how many batches agree on the top classification\n", "        top_classifications = [cls.primary_classification for cls in batch_classifications]\n", "        top_count = top_classifications.count(final_classification)\n", "        consensus_ratio = top_count / batch_count\n", "        \n", "        if consensus_ratio >= 0.8:\n", "            consensus_strength = \"strong\"\n", "        elif consensus_ratio >= 0.6:\n", "            consensus_strength = \"moderate\"\n", "        else:\n", "            consensus_strength = \"weak\"\n", "    \n", "    # Create final justification\n", "    final_justification = f\"Based on analysis of {batch_count} document batches, {final_classification} scored highest ({max_score:.2f}/10) with {consensus_strength} consensus across batches.\"\n", "    \n", "    return AggregatedDrugTypeClassification(\n", "        final_classification=final_classification,\n", "        aggregated_scores=aggregated_scores,\n", "        confidence_level=confidence_level,\n", "        batch_count=batch_count,\n", "        consensus_strength=consensus_strength,\n", "        final_justification=final_justification\n", "    )\n", "\n", "async def perform_drug_type_classification(all_input_chunks: List[Dict], drug_types_json_path: str = \"structured jsons/drug_types.json\") -> AggregatedDrugTypeClassification:\n", "    \"\"\"\n", "    Perform comprehensive drug type classification on all input document chunks.\n", "    \n", "    Args:\n", "        all_input_chunks: List of all input document chunks to classify\n", "        drug_types_json_path: Path to the drug types JSON file\n", "        \n", "    Returns:\n", "        AggregatedDrugTypeClassification: Final classification result with confidence scores\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"STARTING DRUG TYPE CLASSIFICATION\")\n", "    print(\"=\"*80)\n", "    \n", "    # Load drug type definitions\n", "    drug_type_definitions = load_drug_type_definitions(drug_types_json_path)\n", "    print(f\"Loaded {len(drug_type_definitions)} drug type definitions\")\n", "    \n", "    # Filter out chunks with empty content\n", "    valid_chunks = [chunk for chunk in all_input_chunks if chunk.get('content', '').strip()]\n", "    \n", "    if not valid_chunks:\n", "        print(\"No valid chunks found for classification\")\n", "        return await aggregate_drug_type_classifications([], drug_type_definitions)\n", "    \n", "    print(f\"Processing {len(valid_chunks)} valid input document chunks...\")\n", "    \n", "    # Create batches for processing\n", "    batches = create_drug_classification_batches(valid_chunks)\n", "    print(f\"Created {len(batches)} batches for drug type classification\")\n", "    \n", "    # Process all batches in parallel\n", "    print(f\"🚀 Processing all {len(batches)} batches in parallel...\")\n", "    \n", "    # Create tasks for all batches\n", "    batch_tasks = [\n", "        classify_drug_type_batch(batch, drug_type_definitions)\n", "        for batch in batches\n", "    ]\n", "    \n", "    # Execute all batch tasks in parallel\n", "    batch_classifications = await asyncio.gather(*batch_tasks, return_exceptions=True)\n", "    \n", "    # Process results and handle any exceptions\n", "    valid_classifications = []\n", "    for i, classification in enumerate(batch_classifications):\n", "        if isinstance(classification, Exception):\n", "            print(f\"Error processing batch {i+1}: {classification}\")\n", "            # Create default classification for failed batch\n", "            drug_type_names = list(drug_type_definitions.keys())\n", "            default_scores = [\n", "                DrugTypeScore(\n", "                    drug_type_name=name,\n", "                    score=0.0,\n", "                    justification=\"Batch processing failed\",\n", "                    key_evidence=[]\n", "                ) for name in drug_type_names\n", "            ]\n", "            default_classification = DrugTypeClassification(\n", "                drug_type_scores=default_scores,\n", "                primary_classification=\"Unknown\",\n", "                confidence_level=\"uncertain\",\n", "                overall_justification=\"Batch processing failed\"\n", "            )\n", "            valid_classifications.append(default_classification)\n", "        else:\n", "            valid_classifications.append(classification)\n", "    \n", "    print(f\"✅ Completed processing {len(valid_classifications)} batches\")\n", "    \n", "    # Aggregate results from all batches\n", "    print(\"Aggregating results from all batches...\")\n", "    final_classification = await aggregate_drug_type_classifications(valid_classifications, drug_type_definitions)\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"DRUG TYPE CLASSIFICATION COMPLETE\")\n", "    print(\"=\"*80)\n", "    print(f\"Final Classification: {final_classification.final_classification}\")\n", "    print(f\"Confidence Level: {final_classification.confidence_level}\")\n", "    print(f\"Consensus Strength: {final_classification.consensus_strength}\")\n", "    print(f\"Batches Processed: {final_classification.batch_count}\")\n", "    \n", "    # Print top scores for all drug types\n", "    print(\"\\nScores for all drug types:\")\n", "    for score in final_classification.aggregated_scores:\n", "        print(f\"- {score.drug_type_name}: {score.score:.2f}/10\")\n", "    \n", "    print(\"=\"*80)\n", "    \n", "    return final_classification\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["\n", "# # Get all input chunks from MongoDB for drug type classification\n", "# print(\"Fetching all input document chunks for drug type classification...\")\n", "# db = mongodb_client['mednova']\n", "# chunk_collection = db['pre_ind_input_docs_chunks']\n", "\n", "# # Fetch all chunks and convert ObjectIds to strings for JSON serialization in parallel\n", "# async def process_chunk_for_classification(chunk):\n", "#     # Convert ObjectId to string for JSON serialization\n", "#     if '_id' in chunk:\n", "#         chunk['_id'] = str(chunk['_id'])\n", "#     return chunk\n", "\n", "# # Fetch all chunks first\n", "# raw_chunks = await chunk_collection.find({}).to_list()\n", "\n", "# # Process all chunks in parallel\n", "# all_input_chunks_for_classification = await asyncio.gather(*[process_chunk_for_classification(chunk) for chunk in raw_chunks])\n", "\n", "# print(f\"Retrieved {len(all_input_chunks_for_classification)} total input document chunks for drug type classification\")\n", "\n", "# # Perform drug type classification\n", "# drug_classification_result = await perform_drug_type_classification(all_input_chunks=all_input_chunks_for_classification, drug_types_json_path=DRUG_TYPES_JSON_PATH)\n", "\n", "# # Save the classification results\n", "# classification_report = {\n", "#     'drug_type_classification': {\n", "#         'metadata': {\n", "#             'classification_date': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "#             'total_chunks_analyzed': len(all_input_chunks_for_classification),\n", "#             'final_classification': drug_classification_result.final_classification,\n", "#             'confidence_level': drug_classification_result.confidence_level,\n", "#             'consensus_strength': drug_classification_result.consensus_strength,\n", "#             'batch_count': drug_classification_result.batch_count\n", "#         },\n", "#         'detailed_results': drug_classification_result.model_dump()\n", "#     }\n", "# }\n", "\n", "# write_to_json(classification_report, \"drug_type_classification_report.json\")\n", "\n", "# print(f\"\\n🎯 Drug Type Classification Results:\")\n", "# print(f\"📋 Final Classification: {drug_classification_result.final_classification}\")\n", "# print(f\"🎯 Confidence Level: {drug_classification_result.confidence_level}\")\n", "# print(f\"🤝 Consensus Strength: {drug_classification_result.consensus_strength}\")\n", "# print(f\"📊 Chunks Analyzed: {len(all_input_chunks_for_classification)}\")\n", "# print(f\"📄 Detailed report saved to: drug_type_classification_report.json\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Loading drug type context for gap analysis enhancement...\n", "✅ Drug type context loaded successfully:\n", "   📋 Primary Drug Type: Small-Molecule Drugs\n", "   📄 Guidelines Summary Length: 7922 characters\n", "Prompt:\n", " <drug_type_regulatory_context>\n", "**CRITICAL INSTRUCTION**: You are working exclusively with **Small-Molecule Drugs** products. ALL your thinking, evaluations, justifications, gap analysis and recommendations must be specifically tailored for **Small-Molecule Drugs** ONLY.\n", "\n", "**Drug Type**: Small-Molecule Drugs\n", "\n", "**IMPORTANT**: The regulatory guidelines summary provided below contains comprehensive information specifically relevant to **Small-Molecule Drugs**. This includes all applicable ICH and FDA guidelines that are pertinent to this drug type. Use this information as your primary reference for all regulatory requirements, evaluation criteria, and recommendations.\n", "\n", "**Relevant Regulatory Guidelines for Small-Molecule Drugs**:\n", "# **Unified Regulatory Guidelines for Small-Molecule Drugs (Pre-IND, IND, NDA)**  \n", "\n", "## **1. Overview of Small-Molecule Drugs**  \n", "Small-molecule drugs are chemically synthesized compounds with molecular weights typically <900 daltons. Their well-defined structures, stability under standard conditions (25°C/60% RH), and dose-dependent mechanisms of action (e.g., enzyme inhibition, receptor modulation) enable predictable pharmacology and scalable manufacturing. Key examples include Aspirin, Atorvastatin, Imatinib, and Metformin.  \n", "\n", "---\n", "\n", "## **2. Chemistry, Manufacturing, and Controls (CMC)**  \n", "### **2.1 Synthesis and Batch Consistency**  \n", "- Ensure **controlled chemical synthesis** with defined starting materials and validated flow diagrams for critical steps (ICH Q7, Q11).  \n", "- Maintain **batch traceability** with documentation for all manufacturing sites, including:  \n", "  - Identity, process details, and impurity content.  \n", "  - Graphical trends for assay/impurity data during scale-up (Summary 1).  \n", "- Apply **Quality by Design (QbD)** to identify critical-to-quality factors (ICH Q8).  \n", "\n", "### **2.2 Impurity Management**  \n", "| Duration of Use | Mutagenic Impurity Limit | Cumulative Dose Limit |  \n", "|------------------|--------------------------|------------------------|  \n", "| <1 month         | 120 µg/day               | -                      |  \n", "| 1–12 months      | 20 µg/day                | -                      |  \n", "| 1–10 years       | 10 µg/day                | 38.3 mg                |  \n", "| >10 years (lifetime) | 1.5 µg/day             | 38.3 mg                |  \n", "- **Impurity Classification**:  \n", "  - Organic (process-related, degradation).  \n", "  - Inorganic (heavy metals, solvents; ICH Q3C).  \n", "  - Residual solvents: Categorized per ICH Q3C (Class 1–3).  \n", "- **Purge Factor**: Justify negligible carryover with purge factor >1000 (Summary 3).  \n", "\n", "### **2.3 Polymorphism and Particle Size**  \n", "- Screen using **XRPD, DSC/DTA/TGA, Raman spectroscopy**, or solid-state NMR. Define acceptance criteria if polymorphism impacts bioavailability or stability (Summary 1 & 2).  \n", "- **Particle Size Testing**: Mandatory for solid dosage forms or liquids with undissolved drug. Use laser diffraction or microscopy (Summary 1).  \n", "\n", "### **2.4 Stability Testing**  \n", "| Condition Type       | Temperature/RH              | Minimum Duration | Notes                                  |  \n", "|----------------------|-----------------------------|------------------|----------------------------------------|  \n", "| Long-Term (General)  | 25°C ±2°C/60% RH ±5% RH     | 12 months        | Use marketed packaging.                |  \n", "| Accelerated          | 40°C ±2°C/75% RH ±5% RH     | 6 months         | Stop testing at 6 months if changes occur. |  \n", "| Intermediate         | 30°C ±2°C/65% RH ±5% RH     | Conditional      | Required if long-term at 25°C/60% RH.    |  \n", "- **Water Loss in Semi-Permeable Containers**: Threshold 5% after 3 months at 40°C/NMT 25% RH (Summary 1).  \n", "\n", "---\n", "\n", "## **3. Safety Assessment**  \n", "### **3.1 Genotoxicity Testing**  \n", "- **Mandatory Battery**:  \n", "  - In Vitro: Ames Test (TA98/TA100), Chromosome Aberration, or MLA.  \n", "  - In Vivo: Liver/bone marrow micronucleus (cytochalasin B) and Comet assay (Summary 2).  \n", "- **Decision Tree for Mutagenic Impurities**:  \n", "  1. Structural alerts absent → Class 5 (no further testing).  \n", "  2. Alerts/predictions positive → Expert review for in vivo testing relevance (Summary 3).  \n", "  3. Chronic dose >1 mg/day → Genotoxicity testing required (Summary 2).  \n", "\n", "### **3.2 Carcinogenicity and Species-Specific Testing**  \n", "- Exclude non-relevant mechanisms (e.g., CAR-mediated in mice). **2-Year NTP Studies** required for solvents like MIBK (rodent-specific nephropathy) (Summary 1).  \n", "- **Species Extrapolation**: Use surface area-based scaling (S = 10M^0.67) with conservative safety factors (F4 = 10 for teratogenic effects).  \n", "\n", "### **3.3 Toxicokinetics and Route-Specific Validation**  \n", "- **Key Metrics**: Cmax, AUC, tmax, half-life, and free plasma concentrations (e.g., tacrolimus).  \n", "- **Route Alignment**: Match administration route to clinical use (e.g., IV for injectables).  \n", "- **Decision Tree for Route Changes**:  \n", "  - If new route alters AUC/Cmax → Reassess safety margin.  \n", "  - Else → Focus on local toxicity (Summary 2).  \n", "\n", "---\n", "\n", "## **4. Efficacy and Clinical Development**  \n", "### **4.1 Dose-Response and Trial Design**  \n", "- Use **graphical methods** and **confidence intervals** during IND phases (Summary 1).  \n", "- **Adaptive/Platform Trials**: Permit dose adjustments with pre-specified rules (ICH E9(R1)).  \n", "\n", "### **4.2 Endpoints and Subgroup Analysis**  \n", "- Define mechanism-based surrogates (e.g., LDL cholesterol for statins).  \n", "- Include **subgroup analysis** for special populations (ICH E9).  \n", "\n", "### **4.3 Combination Drugs and Accelerated Approval**  \n", "- Prove synergy and dosing compliance per § 300.50.  \n", "- **Subpart H Accelerated Approval**: Apply to serious/life-threatening conditions with surrogate endpoints (21 CFR 314.510).  \n", "\n", "---\n", "\n", "## **5. Submission Requirements by Phase**  \n", "### **5.1 Pre-IND**  \n", "- **CMC**: Synthetic feasibility, stability, impurity screening (≥0.10% in drug substance).  \n", "- **Nonclinical**: Acute/subacute toxicity, genotoxicity, and safety pharmacology (QT interval).  \n", "- **Dose Justification**: Use AUC criteria (1000-fold exposure margin).  \n", "\n", "### **5.2 IND**  \n", "- **CMC**: Impurity characterization, formulation rationale, and process validation.  \n", "- **Nonclinical**: Extended toxicity studies, toxicokinetics, and local tolerance (if IV).  \n", "- **Safety Pharmacology**: Core battery (CNS, cardiovascular, respiratory) with in vivo QT testing (ICH S7A/S7B).  \n", "\n", "### **5.3 NDA**  \n", "- **CMC**: Full ICH Q3A/Q3B compliance, stability harmonization (25°C/60% RH vs. EU 6-month vs. US/Japan 9-month).  \n", "- **Safety**: Genotoxicity resolution (2 in vivo assays if positives), carcinogenicity reconciliation.  \n", "- **Efficacy**: Substantial evidence from adequate trials; include dose-dependent mechanism data.  \n", "\n", "---\n", "\n", "## **6. Key Compliance and Actionable Priorities**  \n", "### **6.1 CMC Priorities**  \n", "- **Impurity Profiling**: Use (Q)SAR and bacterial assays for impurities >1 mg/day.  \n", "- **Batch Consistency**: Ensure 3 production batches for stability studies (ICH Q6A).  \n", "\n", "### **6.2 Safety Pharmacology**  \n", "- **Core Battery**: Mandatory for CNS, cardiovascular, and respiratory systems.  \n", "- **Immunotoxicity**: Conduct 28-day TDAR studies for route-specific tissues (e.g., <PERSON><PERSON>’s patches for oral drugs).  \n", "\n", "### **6.3 Regulatory Milestones**  \n", "- **120-Day Rule**: Submit toxicology/stability data 120 days before clinical trial initiation.  \n", "- **90-Day Conference**: Mandatory for NCEs to address CMC/clinical data gaps (Summary 4).  \n", "\n", "---\n", "\n", "## **7. Compliance Checklist**  \n", "✅ **CMC**: Dissolution-BA correlation, batch consistency, impurity thresholds.  \n", "✅ **Safety**: Genotoxicity testing, DDI validation, special population AE stratification.  \n", "✅ **Efficacy**: Subgroup analysis, long-term data, surrogate marker justification.  \n", "✅ **ICH Guidelines**: Q6A/Q6B (CMC), Q1A (stability), S7 (safety pharmacology), M3(R2) (nonclinical).  \n", "\n", "---\n", "\n", "## **8. Conclusion**  \n", "Regulatory success for small-molecule drugs requires:  \n", "1. **CMC Precision**: Focus on polymorph validation, impurity profiling, and scalable synthesis.  \n", "2. **Safety Rigor**: Use harmonized genotoxicity/carcinogenicity testing and route-specific toxicokinetics.  \n", "3. **Efficacy Validation**: Align trials with mechanism-based endpoints and address dose-dependent effects.  \n", "4. **ICH Compliance**: Ensure alignment across CTD modules and regional requirements.  \n", "5. **Strategic Meetings**: Leverage 90-day and end-of-review conferences to resolve data gaps and labeling discrepancies.  \n", "\n", "This unified framework integrates FDA/EU/ICH guidance, emphasizing CMC rigor, scientific justification, and adaptive trial designs tailored to small-molecule pharmacological properties.\n", "\n", "**ANALYSIS REQUIREMENTS**:\n", "- Focus EXCLUSIVELY on Small-Molecule Drugs regulatory requirements\n", "- Apply ONLY Small-Molecule Drugs-specific evaluation criteria  \n", "- Generate recommendations suitable ONLY for Small-Molecule Drugs development\n", "- Consider ONLY Small-Molecule Drugs-relevant ICH/FDA guidelines from the summary above\n", "- Ignore any requirements not applicable to Small-Molecule Drugs\n", "\n", "</drug_type_regulatory_context>\n", "\n", "User Prompt:\n", "\n", "   🧬 Gap analysis will be enhanced with Small-Molecule Drugs-specific regulatory context\n"]}], "source": ["\n", "def load_drug_type_classification_result(classification_file_path: str = \"drug_type_classification_report.json\") -> Dict:\n", "    \"\"\"\n", "    Load the drug type classification result from the saved JSON file.\n", "    \n", "    Args:\n", "        classification_file_path: Path to the classification report JSON file\n", "        \n", "    Returns:\n", "        Dict containing classification metadata and detailed results\n", "    \"\"\"\n", "    try:\n", "        classification_data = read_json(classification_file_path)\n", "        return classification_data.get('drug_type_classification', {})\n", "    except Exception as e:\n", "        print(f\"Error loading drug type classification result: {e}\")\n", "        return {}\n", "\n", "def load_drug_type_processed_guidelines(guidelines_file_path: str = \"drug_type_guidelines_processed.json\") -> Dict:\n", "    \"\"\"\n", "    Load the processed drug type guidelines from the saved JSON file.\n", "    \n", "    Args:\n", "        guidelines_file_path: Path to the processed guidelines JSON file\n", "        \n", "    Returns:\n", "        Dict containing processed guidelines for all drug types\n", "    \"\"\"\n", "    try:\n", "        guidelines_data = read_json(guidelines_file_path)\n", "        return guidelines_data.get('drug_type_guidelines', {})\n", "    except Exception as e:\n", "        print(f\"Error loading drug type processed guidelines: {e}\")\n", "        return {}\n", "\n", "def get_drug_type_context_for_gap_analysis(\n", "    classification_file_path: str = \"drug_type_classification_report.json\",\n", "    guidelines_file_path: str = \"drug_type_guidelines_processed.json\"\n", ") -> Dict:\n", "    \"\"\"\n", "    Get simplified drug type context for gap analysis enhancement.\n", "    \n", "    Args:\n", "        classification_file_path: Path to the classification report JSON file\n", "        guidelines_file_path: Path to the processed guidelines JSON file\n", "        \n", "    Returns:\n", "        Dict containing:\n", "        {\n", "            'classification_available': bool,\n", "            'primary_drug_type': str,\n", "            'relevant_guidelines_summary': str,\n", "            'enhanced_context': str  # formatted context ready for prompt injection\n", "        }\n", "    \"\"\"\n", "    # Load classification result\n", "    classification_result = load_drug_type_classification_result(classification_file_path)\n", "    \n", "    if not classification_result:\n", "        return {\n", "            'classification_available': <PERSON><PERSON><PERSON>,\n", "            'primary_drug_type': 'Unknown',\n", "            'relevant_guidelines_summary': 'No relevant guidelines context available',\n", "            'enhanced_context': ''\n", "        }\n", "    \n", "    # Extract classification details - only the drug type\n", "    detailed_results = classification_result.get('detailed_results', {})\n", "    primary_drug_type = detailed_results.get('final_classification', 'Unknown')\n", "    \n", "    # Load processed guidelines\n", "    processed_guidelines = load_drug_type_processed_guidelines(guidelines_file_path)\n", "    \n", "    # Get relevant guidelines summary for the classified drug type - NO TRUNCATION\n", "    relevant_guidelines_summary = \"\"\n", "    if primary_drug_type in processed_guidelines:\n", "        drug_type_data = processed_guidelines[primary_drug_type]\n", "        if drug_type_data.get('processing_status') == 'success':\n", "            relevant_guidelines_summary = drug_type_data.get('combined_summary', '')\n", "        else:\n", "            relevant_guidelines_summary = f\"Guidelines processing failed for {primary_drug_type}\"\n", "    else:\n", "        relevant_guidelines_summary = f\"No processed guidelines available for {primary_drug_type}\"\n", "    \n", "    # Create enhanced context string for prompt injection with explicit drug type focus\n", "    enhanced_context = f\"\"\"<drug_type_regulatory_context>\n", "**CRITICAL INSTRUCTION**: You are working exclusively with **{primary_drug_type}** products. ALL your thinking, evaluations, justifications, gap analysis and recommendations must be specifically tailored for **{primary_drug_type}** ONLY.\n", "\n", "**Drug Type**: {primary_drug_type}\n", "\n", "**IMPORTANT**: The regulatory guidelines summary provided below contains comprehensive information specifically relevant to **{primary_drug_type}**. This includes all applicable ICH and FDA guidelines that are pertinent to this drug type. Use this information as your primary reference for all regulatory requirements, evaluation criteria, and recommendations.\n", "\n", "**Relevant Regulatory Guidelines for {primary_drug_type}**:\n", "{relevant_guidelines_summary}\n", "\n", "**ANALYSIS REQUIREMENTS**:\n", "- Focus EXCLUSIVELY on {primary_drug_type} regulatory requirements\n", "- Apply ONLY {primary_drug_type}-specific evaluation criteria  \n", "- Generate recommendations suitable ONLY for {primary_drug_type} development\n", "- Consider ONLY {primary_drug_type}-relevant ICH/FDA guidelines from the summary above\n", "- Ignore any requirements not applicable to {primary_drug_type}\n", "\n", "</drug_type_regulatory_context>\n", "\n", "\"\"\"\n", "    \n", "    return {\n", "        'classification_available': True,\n", "        'primary_drug_type': primary_drug_type,\n", "        'relevant_guidelines_summary': relevant_guidelines_summary,\n", "        'enhanced_context': enhanced_context\n", "    }\n", "\n", "# Initialize drug type context globally for use throughout gap analysis\n", "print(\"\\n🔍 Loading drug type context for gap analysis enhancement...\")\n", "DRUG_TYPE_CONTEXT = get_drug_type_context_for_gap_analysis()\n", "\n", "if DRUG_TYPE_CONTEXT['classification_available']:\n", "    print(f\"✅ Drug type context loaded successfully:\")\n", "    print(f\"   📋 Primary Drug Type: {DRUG_TYPE_CONTEXT['primary_drug_type']}\")\n", "    print(f\"   📄 Guidelines Summary Length: {len(DRUG_TYPE_CONTEXT['relevant_guidelines_summary'])} characters\")\n", "    print(\"Prompt:\\n\", inject_drug_type_context_into_prompt(\"User Prompt:\\n\", DRUG_TYPE_CONTEXT))\n", "    print(f\"   🧬 Gap analysis will be enhanced with {DRUG_TYPE_CONTEXT['primary_drug_type']}-specific regulatory context\")\n", "else:\n", "    print(\"⚠️ Drug type context not available - gap analysis will proceed without drug-type-specific context\")\n", "\n", "processed_doc = read_json(\"processed_ectd_guidelines_with_relevant_chunks.json\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["\n", "async def combine_justifications_for_checkpoint(\n", "    supporting_justifications: List[str],\n", "    gap_justifications: List[str], \n", "    checkpoint: str,\n", "    section_info: Dict,\n", ") -> Dict:\n", "    \"\"\"\n", "    Combine justifications using LLM to create comprehensive supporting evidence and gap analysis.\n", "    \n", "    Args:\n", "        supporting_justifications: List of justifications from high-scoring chunks (≥8)\n", "        gap_justifications: List of justifications from low-scoring chunks (<8)\n", "        checkpoint: The checkpoint question being analyzed\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Dict containing:\n", "        {\n", "            'combined_supporting_evidence': str,  # Combined explanation of what's present\n", "            'combined_gap_analysis': str         # Combined explanation of what's missing\n", "        }\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to analyze and combine justifications from document chunk evaluations to create comprehensive supporting evidence summaries and gap analyses.\"\"\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are analyzing justifications from a checkpoint coverage assessment for a Pre-IND briefing document.\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            user_prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                user_prompt += f\"\"\"\n", "Context: You are analyzing justifications for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this checkpoint contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            # Inject drug type context for enhanced justification combination\n", "            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)\n", "            \n", "            user_prompt += f\"\"\"\n", "Checkpoint Question:\n", "<checkpoint>\n", "{checkpoint}\n", "</checkpoint>\n", "\n", "You have been provided with two types of justifications:\n", "\n", "1. SUPPORTING JUSTIFICATIONS (from high-scoring chunks that address the checkpoint well):\n", "<supporting_justifications>\n", "{format_list_as_markdown(supporting_justifications) if supporting_justifications else \"None available\"}\n", "</supporting_justifications>\n", "\n", "2. GAP JUSTIFICATIONS (from low-scoring chunks that partially address or miss the checkpoint):\n", "<gap_justifications>\n", "{format_list_as_markdown(gap_justifications) if gap_justifications else \"None available\"}\n", "</gap_justifications>\n", "\n", "Your task is to analyze these justifications and create two comprehensive summaries:\n", "\n", "1. **Combined Supporting Evidence**: If supporting justifications are available, combine them into a single, non-redundant explanation of what information IS present in the input documents and why it addresses the checkpoint well. Remove redundancy while preserving specific details and evidence.\n", "\n", "2. **Combined Gap Analysis**: If gap justifications are available, combine them into a single, non-redundant explanation of what information is MISSING or insufficient in the input documents. Focus on specific gaps, missing data, or insufficient detail that prevents full checkpoint coverage.\n", "\n", "Guidelines for combination:\n", "- Preserve specific technical details and evidence\n", "- Remove redundant statements while maintaining comprehensiveness\n", "- Focus on actionable insights for regulatory professionals\n", "- Use clear, professional language appropriate for regulatory documentation\n", "- If no justifications are available for a category, state \"No relevant information available\"\n", "\n", "Your response should be in JSON format:\n", "{{\n", "  \"combined_supporting_evidence\": \"Combined explanation of what's present and working well\",\n", "  \"combined_gap_analysis\": \"Combined explanation of what's missing or insufficient\"\n", "}}\n", "\"\"\"\n", "            output_type = str\n", "            if get_structured_output_using_pydantic(GENERATOR_MODEL_BASE_URL):\n", "                output_type = CombinedJustifications\n", "\n", "            # Calculate tokens for monitoring\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=generator_model,\n", "                    system_prompt=system_prompt,\n", "                    output_type=output_type,\n", "                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            response: CombinedJustifications = await retry_on_empty_response(make_api_call)\n", "\n", "            if get_structured_output_using_pydantic(GENERATOR_MODEL_BASE_URL):\n", "                return response.model_dump()\n", "            else:\n", "                if response:\n", "                    # Parse the JSON response\n", "                    try:\n", "                        # Extract JSON from the response\n", "                        json_match = re.search(r'\\{.*\\}', response, re.DOTALL)\n", "                        if json_match:\n", "                            json_str = json_match.group(0)\n", "                            combined_result = json.loads(json_str)\n", "                            \n", "                            # output_tokens = calculate_number_of_tokens(json.dumps(combined_result))\n", "                            # total_tokens = system_tokens + user_tokens + output_tokens\n", "                            \n", "                            # print(f\"combine_justifications_for_checkpoint token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "                            \n", "                            return {\n", "                                'combined_supporting_evidence': combined_result.get('combined_supporting_evidence', 'No relevant information available'),\n", "                                'combined_gap_analysis': combined_result.get('combined_gap_analysis', 'No gaps identified')\n", "                            }\n", "                        else:\n", "                            # Fallback if JSON parsing fails\n", "                            print(\"Warning: Could not extract JSON from justification combination response\")\n", "                            retry_count += 1\n", "                    except json.JSONDecodeError as e:\n", "                        print(f\"Error parsing JSON response from justification combination: {e}\")\n", "                        retry_count += 1\n", "                else:\n", "                    print(\"Empty response received from justification combination\")\n", "                    retry_count += 1\n", "                \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in combine_justifications_for_checkpoint (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Returning default justification combination.\")\n", "                return {\n", "                    'combined_supporting_evidence': 'Error occurred during justification combination',\n", "                    'combined_gap_analysis': 'Error occurred during justification combination'\n", "                }\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return {\n", "        'combined_supporting_evidence': 'Unexpected error in justification combination',\n", "        'combined_gap_analysis': 'Unexpected error in justification combination'\n", "    }\n", "\n", "def categorize_checkpoints_by_coverage(checkpoint_details: List[Dict]) -> Dict:\n", "    \"\"\"\n", "    Categorize checkpoints based on their coverage scores using fixed ranges.\n", "    \n", "    Args:\n", "        checkpoint_details: List of checkpoint detail dictionaries with coverage scores\n", "        \n", "    Returns:\n", "        Dict containing categorized checkpoints:\n", "        {\n", "            'excellent_coverage': {...},\n", "            'good_coverage': {...},\n", "            'partial_coverage': {...},\n", "            'poor_coverage': {...},\n", "            'no_coverage': {...}\n", "        }\n", "    \"\"\"\n", "    # Define fixed score ranges\n", "    categories = {\n", "        'excellent_coverage': {'range': (8.0, 10.0), 'checkpoints': []},\n", "        'good_coverage': {'range': (6.0, 7.9), 'checkpoints': []},\n", "        'partial_coverage': {'range': (4.0, 5.9), 'checkpoints': []},\n", "        'poor_coverage': {'range': (2.0, 3.9), 'checkpoints': []},\n", "        'no_coverage': {'range': (0.0, 1.9), 'checkpoints': []}\n", "    }\n", "    \n", "    # Categorize each checkpoint\n", "    for checkpoint_detail in checkpoint_details:\n", "        score = checkpoint_detail.get('coverage_score', 0.0)\n", "        \n", "        # Find the appropriate category\n", "        for category_name, category_info in categories.items():\n", "            min_score, max_score = category_info['range']\n", "            if min_score <= score <= max_score:\n", "                categories[category_name]['checkpoints'].append(checkpoint_detail)\n", "                break\n", "    \n", "    # Create summary for each category\n", "    categorized_result = {}\n", "    for category_name, category_info in categories.items():\n", "        checkpoints = category_info['checkpoints']\n", "        categorized_result[category_name] = {\n", "            'checkpoint_count': len(checkpoints),\n", "            'score_range': category_info['range'],\n", "            'specific_checkpoints': [cp['checkpoint'] for cp in checkpoints],\n", "            'checkpoint_details': checkpoints  # Keep full details for further processing\n", "        }\n", "    \n", "    return categorized_result\n", "\n", "async def assess_checkpoint_coverage_against_all_chunks(checkpoint_question: str, all_input_chunks: List[Dict], section_info: Dict) -> Dict:\n", "    \"\"\"\n", "    Evaluates how well a checkpoint is covered by all available input chunks.\n", "    \n", "    Args:\n", "        checkpoint_question: The checkpoint question to evaluate\n", "        all_input_chunks: List of all input document chunks to evaluate against\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Dict containing coverage assessment with structure:\n", "        {\n", "            'checkpoint': str,\n", "            'total_chunks_evaluated': int,\n", "            'relevant_chunks': List[Dict],  # chunks that address this checkpoint\n", "            'coverage_score': float,  # 0-10 aggregate score\n", "            'coverage_quality': str,  # 'excellent', 'good', 'partial', 'poor', 'none'\n", "            'best_supporting_evidence': str,  # excerpt from best chunk\n", "            'coverage_gaps': List[str]  # what aspects are missing\n", "        }\n", "    \"\"\"\n", "    print(f\"Assessing checkpoint coverage: {checkpoint_question[:100]}...\")\n", "    \n", "    if not all_input_chunks:\n", "        return {\n", "            'checkpoint': checkpoint_question,\n", "            'total_chunks_evaluated': 0,\n", "            'relevant_chunks': [],\n", "            'coverage_score': 0.0,\n", "            'coverage_quality': 'none',\n", "            'best_supporting_evidence': '',\n", "            'coverage_gaps': ['No input documents available for evaluation']\n", "        }\n", "    \n", "    # Evaluate each chunk against the checkpoint\n", "    chunk_evaluations = []\n", "    relevant_chunks = []\n", "    \n", "    print(f\"Evaluating {len(all_input_chunks)} chunks against checkpoint...\")\n", "    \n", "    # Filter out chunks with empty content\n", "    valid_chunks = [chunk for chunk in all_input_chunks if chunk.get('content', '').strip()]\n", "    \n", "    if not valid_chunks:\n", "        print(\"No valid chunks found for evaluation\")\n", "        return {\n", "            'checkpoint': checkpoint_question,\n", "            'total_chunks_evaluated': len(all_input_chunks),\n", "            'relevant_chunks': [],\n", "            'coverage_score': 0.0,\n", "            'coverage_quality': 'none',\n", "            'best_supporting_evidence': '',\n", "            'coverage_gaps': ['No valid chunks available for evaluation'],\n", "            'supporting_evidence': 'No relevant information available',\n", "            'identified_gaps': 'No chunks available for evaluation'\n", "        }\n", "    \n", "    # Choose evaluation method based on configuration\n", "    if USE_BATCHED_CHUNK_EVALUATION and len(valid_chunks) > 1:\n", "        # Use batched approach for better performance\n", "        try:\n", "            print(f\"Using batched evaluation for {len(valid_chunks)} chunks\")\n", "            \n", "            # Create batches for chunks\n", "            batches = create_chunk_batches(valid_chunks, checkpoint_question)\n", "            print(f\"Created {len(batches)} batches for chunk evaluation\")\n", "            \n", "            # Create tasks for all batches\n", "            batch_tasks = []\n", "            for batch in batches:\n", "                task = evaluate_batch_chunks_checkpoint_relevance(batch, section_info, checkpoint_question)\n", "                batch_tasks.append((batch, task))\n", "            \n", "            # Execute all batches in parallel\n", "            batch_results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "            \n", "            # Process results from all batches\n", "            for (batch, _), batch_evaluations in zip(batch_tasks, batch_results):\n", "                for chunk, evaluation in zip(batch, batch_evaluations):\n", "                    chunk_evaluations.append({\n", "                        'chunk': chunk,\n", "                        'evaluation': evaluation,\n", "                        'relevance_score': evaluation.relevance_score\n", "                    })\n", "                    \n", "                    # Consider chunk relevant if it meets the threshold\n", "                    if evaluation.relevance_score >= RELEVANCE_THRESHOLD:\n", "                        relevant_chunk = chunk.copy()\n", "                        relevant_chunk['relevance_score'] = evaluation.relevance_score\n", "                        relevant_chunk['justification'] = evaluation.justification\n", "                        relevant_chunks.append(relevant_chunk)\n", "        except Exception as e:\n", "            print(f\"Batched chunk evaluation failed: {e}, falling back to individual evaluation\")\n", "            # Fallback to individual evaluation\n", "            batch_tasks = []\n", "            for chunk in valid_chunks:\n", "                chunk_content = chunk.get('content', '')\n", "                task = evaluate_individual_checkpoint_relevance(chunk_content, section_info, checkpoint_question)\n", "                batch_tasks.append((chunk, task))\n", "            \n", "            # Execute batch in parallel\n", "            batch_results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "            \n", "            # Process results\n", "            for (chunk, _), evaluation in zip(batch_tasks, batch_results):\n", "                chunk_evaluations.append({\n", "                    'chunk': chunk,\n", "                    'evaluation': evaluation,\n", "                    'relevance_score': evaluation.relevance_score\n", "                })\n", "                \n", "                # Consider chunk relevant if it meets the threshold\n", "                if evaluation.relevance_score >= RELEVANCE_THRESHOLD:\n", "                    relevant_chunk = chunk.copy()\n", "                    relevant_chunk['relevance_score'] = evaluation.relevance_score\n", "                    relevant_chunk['justification'] = evaluation.justification\n", "                    relevant_chunks.append(relevant_chunk)\n", "    else:\n", "        # Use individual evaluation (for testing/comparison or single chunk)\n", "        print(f\"Using individual evaluation for {len(valid_chunks)} chunks\")\n", "        batch_tasks = []\n", "        for chunk in valid_chunks:\n", "            chunk_content = chunk.get('content', '')\n", "            task = evaluate_individual_checkpoint_relevance(chunk_content, section_info, checkpoint_question)\n", "            batch_tasks.append((chunk, task))\n", "        \n", "        # Execute batch in parallel\n", "        batch_results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "        # Process results\n", "        for (chunk, _), evaluation in zip(batch_tasks, batch_results):\n", "            chunk_evaluations.append({\n", "                'chunk': chunk,\n", "                'evaluation': evaluation,\n", "                'relevance_score': evaluation.relevance_score\n", "            })\n", "            \n", "            # Consider chunk relevant if it meets the threshold\n", "            if evaluation.relevance_score >= RELEVANCE_THRESHOLD:\n", "                relevant_chunk = chunk.copy()\n", "                relevant_chunk['relevance_score'] = evaluation.relevance_score\n", "                relevant_chunk['justification'] = evaluation.justification\n", "                relevant_chunks.append(relevant_chunk)\n", "    \n", "    # Calculate aggregate coverage score\n", "    if not chunk_evaluations:\n", "        coverage_score = 0.0\n", "    else:\n", "        # Use weighted average of top-scoring chunks (not just arithmetic mean)\n", "        scores = [eval_data['relevance_score'] for eval_data in chunk_evaluations]\n", "        scores.sort(reverse=True)\n", "        \n", "        # Weight the top scores more heavily\n", "        if len(scores) >= 3:\n", "            # Take top 3 scores with weights 0.5, 0.3, 0.2\n", "            coverage_score = (scores[0] * 0.5 + scores[1] * 0.3 + scores[2] * 0.2)\n", "        el<PERSON> le<PERSON>(scores) == 2:\n", "            # Take top 2 scores with weights 0.7, 0.3\n", "            coverage_score = (scores[0] * 0.7 + scores[1] * 0.3)\n", "        elif len(scores) == 1:\n", "            # Single score\n", "            coverage_score = scores[0]\n", "        else:\n", "            coverage_score = 0.0\n", "    \n", "    # Determine coverage quality\n", "    if coverage_score >= 8.0:\n", "        coverage_quality = 'excellent'\n", "    elif coverage_score >= 6.0:\n", "        coverage_quality = 'good'\n", "    elif coverage_score >= 4.0:\n", "        coverage_quality = 'partial'\n", "    elif coverage_score >= 2.0:\n", "        coverage_quality = 'poor'\n", "    else:\n", "        coverage_quality = 'none'\n", "    \n", "    # Get best supporting evidence\n", "    best_supporting_evidence = ''\n", "    if relevant_chunks:\n", "        best_chunk = max(relevant_chunks, key=lambda x: x['relevance_score'])\n", "        # Extract a meaningful excerpt (first 300 chars)\n", "        content = best_chunk.get('content', '')\n", "        best_supporting_evidence = content\n", "    \n", "    # Collect justifications by score category\n", "    supporting_justifications = []  # From chunks scoring >= 8\n", "    gap_justifications = []         # From chunks scoring < 8\n", "    \n", "    for chunk_eval in chunk_evaluations:\n", "        if chunk_eval['relevance_score'] >= 8.0:\n", "            supporting_justifications.append(chunk_eval['evaluation'].justification)\n", "        else:\n", "            gap_justifications.append(chunk_eval['evaluation'].justification)\n", "    \n", "    # Combine justifications using LLM\n", "    combined_justifications = await combine_justifications_for_checkpoint(\n", "        supporting_justifications, \n", "        gap_justifications, \n", "        checkpoint_question, \n", "        section_info\n", "    )\n", "    \n", "    # Generate coverage gaps (simplified for now, can be enhanced with LLM later)\n", "    coverage_gaps = []\n", "    if coverage_score < 8.0:\n", "        if coverage_score < 2.0:\n", "            coverage_gaps.append('No relevant information found in input documents')\n", "        elif coverage_score < 4.0:\n", "            coverage_gaps.append('Limited information available - insufficient detail')\n", "        elif coverage_score < 6.0:\n", "            coverage_gaps.append('Partial information available - some aspects missing')\n", "        else:\n", "            coverage_gaps.append('Good information available - minor gaps in coverage')\n", "    \n", "    result = {\n", "        'checkpoint': checkpoint_question,\n", "        'total_chunks_evaluated': len(all_input_chunks),\n", "        'relevant_chunks': relevant_chunks,\n", "        'coverage_score': round(coverage_score, 2),\n", "        'coverage_quality': coverage_quality,\n", "        'best_supporting_evidence': best_supporting_evidence,\n", "        'coverage_gaps': coverage_gaps,\n", "        'supporting_evidence': combined_justifications['combined_supporting_evidence'],\n", "        'identified_gaps': combined_justifications['combined_gap_analysis']\n", "    }\n", "    \n", "    print(f\"Coverage assessment complete: {coverage_score:.2f}/10 ({coverage_quality}) - {len(relevant_chunks)} relevant chunks\")\n", "    return result\n", "\n", "async def generate_gap_recommendations(checkpoint: str, coverage_assessment: Dict, section_info: Dict) -> List[str]:\n", "    \"\"\"\n", "    Generates specific actionable recommendations for addressing a gap using LLM.\n", "    \n", "    Args:\n", "        checkpoint: The checkpoint question that has a gap\n", "        coverage_assessment: The coverage assessment result from assess_checkpoint_coverage_against_all_chunks\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        List[str]: List of specific actionable recommendations\n", "    \"\"\"\n", "    print(f\"Generating recommendations for gap: {checkpoint[:80]}...\")\n", "    \n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are a senior regulatory affairs expert specializing in pharmaceutical development and Pre-IND submissions. Your task is to provide specific, actionable recommendations for addressing regulatory gaps identified in Pre-IND documentation.\"\"\"\n", "\n", "            user_prompt = f\"\"\"You are analyzing a regulatory gap in a Pre-IND briefing document. Based on the information provided, generate specific, actionable recommendations to address this gap.\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            user_prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                user_prompt += f\"\"\"\n", "Context: You are generating recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how addressing this gap contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            # Inject drug type context for enhanced gap recommendations\n", "            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)\n", "            \n", "            user_prompt += f\"\"\"\n", "Gap Details:\n", "<gap_analysis>\n", "Checkpoint Question: {checkpoint}\n", "Coverage Score: {coverage_assessment.get('coverage_score', 0)}/10\n", "Coverage Quality: {coverage_assessment.get('coverage_quality', 'none')}\n", "</gap_analysis>\n", "\n", "Specific Gap Analysis:\n", "<identified_gaps>\n", "{coverage_assessment.get('identified_gaps', 'No specific gap analysis available')}\n", "</identified_gaps>\n", "\n", "Supporting Evidence Available:\n", "<supporting_evidence>\n", "{coverage_assessment.get('supporting_evidence', 'No supporting evidence available')}\n", "</supporting_evidence>\n", "\n", "Current Coverage Status:\n", "<coverage_status>\n", "Total Chunks Evaluated: {coverage_assessment.get('total_chunks_evaluated', 0)}\n", "Relevant Chunks Found: {len(coverage_assessment.get('relevant_chunks', []))}\n", "Coverage Gaps: {coverage_assessment.get('coverage_gaps', [])}\n", "</coverage_status>\n", "\n", "Before providing recommendations, analyze the gap information in your thinking block using <gap_analysis_thinking> tags:\n", "\n", "1. Review the specific gaps identified and their regulatory context\n", "2. Prioritize gaps by regulatory importance and impact\n", "3. Consider feasible solutions and required resources\n", "4. Map gaps to specific ICH guidelines and FDA expectations\n", "5. Plan the most effective sequence of recommendations\n", "\n", "Your task is to provide 3-5 specific, actionable recommendations that address this gap. Use the identified gaps section to understand exactly what information is missing or insufficient. Present your final recommendations in <final_recommendations> tags. Each recommendation should be:\n", "\n", "1. **Specific**: Include concrete actions, not vague suggestions\n", "2. **Actionable**: Clear steps that can be implemented\n", "3. **Regulatory-focused**: Aligned with FDA Pre-IND expectations\n", "4. **Prioritized**: Most critical actions first\n", "5. **Feasible**: Realistic given typical pharmaceutical development timelines\n", "6. **Targeted**: Directly address the specific gaps identified in the gap analysis\n", "\n", "Format your recommendations as a numbered list. Each recommendation should be 1-2 sentences and include specific deliverables or actions.\n", "\n", "Examples of good recommendations:\n", "- \"Conduct a comprehensive impurity profiling study using HPLC-MS/MS to identify and quantify all impurities >0.1% per ICH Q3A guidelines\"\n", "- \"Prepare a detailed manufacturing process validation protocol including critical process parameters and acceptance criteria\"\n", "- \"Generate 6-month stability data at 25°C/60%RH and 40°C/75%RH conditions following ICH Q1A guidelines\"\n", "\n", "Focus on practical steps that will directly address the identified gaps and satisfy regulatory requirements for Pre-IND submissions.\n", "\"\"\"\n", "            # system_tokens = calculate_number_of_tokens(system_prompt)\n", "            # user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=generator_model,\n", "                    system_prompt=system_prompt,\n", "                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.3),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "                \n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            response = await retry_on_empty_response(make_api_call)\n", "\n", "            # output_tokens = calculate_number_of_tokens(response)\n", "            # total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            if response:\n", "                # Extract content from final_recommendations tags first\n", "                extracted_content = extract_content_from_tags(response, 'final_recommendations')\n", "                \n", "                # Parse the response to extract recommendations\n", "                recommendations = parse_recommendations_from_response(extracted_content)\n", "                \n", "                if recommendations:\n", "                    print(f\"Generated {len(recommendations)} recommendations for gap\")\n", "                    return recommendations\n", "                else:\n", "                    print(\"No recommendations could be parsed from response\")\n", "                    retry_count += 1\n", "            else:\n", "                print(\"Empty response received from LLM\")\n", "                retry_count += 1\n", "                \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error generating recommendations (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Generating fallback recommendations.\")\n", "                # Generate basic fallback recommendations\n", "                return []\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback if all retries failed\n", "    return []\n", "\n", "def parse_recommendations_from_response(response: str) -> List[str]:\n", "    \"\"\"\n", "    Parse recommendations from LLM response text.\n", "    \n", "    Args:\n", "        response: The raw LLM response text\n", "        \n", "    Returns:\n", "        List[str]: Parsed recommendations\n", "    \"\"\"\n", "    recommendations = []\n", "    \n", "    # Split by lines and look for numbered items\n", "    lines = response.split('\\n')\n", "    current_recommendation = \"\"\n", "    \n", "    for line in lines:\n", "        line = line.strip()\n", "        if not line:\n", "            continue\n", "            \n", "        # Look for numbered items (1., 2., etc.)\n", "        if re.match(r'^\\d+\\.', line):\n", "            # Save previous recommendation if exists\n", "            if current_recommendation:\n", "                recommendations.append(current_recommendation.strip())\n", "            # Start new recommendation\n", "            current_recommendation = re.sub(r'^\\d+\\.\\s*', '', line)\n", "        elif current_recommendation:\n", "            # Continue current recommendation\n", "            current_recommendation += \" \" + line\n", "    \n", "    # Add the last recommendation\n", "    if current_recommendation:\n", "        recommendations.append(current_recommendation.strip())\n", "    \n", "    # If no numbered items found, try bullet points\n", "    if not recommendations:\n", "        for line in lines:\n", "            line = line.strip()\n", "            if line.startswith('- ') or line.startswith('• '):\n", "                recommendations.append(line[2:].strip())\n", "    \n", "    # Filter out empty recommendations\n", "    recommendations = [rec for rec in recommendations if rec and len(rec) > 10]\n", "    \n", "    return recommendations[:5]  # Limit to 5 recommendations\n", "\n", "async def generate_section_gap_analysis_report(section_info: Dict, all_input_chunks: List[Dict]) -> Dict:\n", "    \"\"\"\n", "    Generates a comprehensive gap analysis report for a single section using the new approach.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information including checkpoints\n", "        all_input_chunks: List of all input document chunks available for analysis\n", "        \n", "    Returns:\n", "        Dict: Structured comprehensive gap analysis report for the section\n", "    \"\"\"\n", "    section = section_info.get('section', 'Unknown')\n", "    title = section_info.get('title', 'Unknown Title')\n", "    \n", "    print(f\"\\n=== Starting Comprehensive Gap Analysis for Section {section}: {title} ===\")\n", "    \n", "    # Get checkpoints for this section\n", "    checkpoints = section_info.get('ich_guideline_combined_summaries_checkpoint_list', [])\n", "    referenced_guidelines = section_info.get('referenced_ich_guidelines', [])\n", "    \n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section}\")\n", "        return {\n", "            'section': section,\n", "            'section_title': title,\n", "            'error': 'No checkpoints available for gap analysis',\n", "            'summary': {\n", "                'total_checkpoints': 0,\n", "                'covered_checkpoints': 0,\n", "                'coverage_percentage': 0.0,\n", "            }\n", "        }\n", "    \n", "    print(f\"Analyzing {len(checkpoints)} checkpoints against {len(all_input_chunks)} input chunks...\")\n", "    \n", "    # Step 1: Process all checkpoints to get coverage assessments\n", "    print(\"Step 1: Assessing coverage for all checkpoints...\")\n", "\n", "    checkpoint_details = []\n", "    \n", "    # Process all checkpoints in parallel\n", "    print(f\"Processing all {len(checkpoints)} checkpoints...\")\n", "    \n", "    # Create tasks for all checkpoints\n", "    checkpoint_tasks = []\n", "    for checkpoint in checkpoints:\n", "        task = assess_checkpoint_coverage_against_all_chunks(checkpoint, all_input_chunks, section_info)\n", "        checkpoint_tasks.append((checkpoint, task))\n", "    \n", "    # Execute all checkpoints in parallel\n", "    checkpoint_results = await asyncio.gather(*[task for _, task in checkpoint_tasks])\n", "    \n", "    # Process results for each checkpoint\n", "    for (checkpoint, _), coverage_assessment in zip(checkpoint_tasks, checkpoint_results):\n", "        # Create checkpoint detail entry\n", "        checkpoint_detail = {\n", "            'checkpoint': checkpoint,\n", "            'ich_guideline_source': ' '.join(referenced_guidelines),\n", "            'coverage_score': coverage_assessment['coverage_score'],\n", "            'coverage_quality': coverage_assessment['coverage_quality'],\n", "            'supporting_evidence': coverage_assessment.get('supporting_evidence', 'No supporting evidence available'),\n", "            'identified_gaps': coverage_assessment.get('identified_gaps', 'No gaps identified'),\n", "            'supporting_chunks': []\n", "        }\n", "        \n", "        # Add supporting chunks information (limit to top 3 for readability)\n", "        relevant_chunks = coverage_assessment.get('relevant_chunks', [])\n", "        for chunk in relevant_chunks[:3]:\n", "            chunk_info = {\n", "                'chunk_id': chunk.get('_id', 'unknown'),\n", "                'document_source': chunk.get('input_doc_tag', 'unknown'),\n", "                'relevance_score': chunk.get('relevance_score', 0),\n", "                'evidence_excerpt': chunk.get('content', '')[:200] + '...' if len(chunk.get('content', '')) > 200 else chunk.get('content', '')\n", "            }\n", "            checkpoint_detail['supporting_chunks'].append(chunk_info)\n", "        \n", "        checkpoint_details.append(checkpoint_detail)\n", "    \n", "    # Step 2: Categorize checkpoints by coverage scores\n", "    print(\"Step 2: Categorizing checkpoints by coverage scores...\")\n", "    categorized_checkpoints = categorize_checkpoints_by_coverage(checkpoint_details)\n", "    \n", "    # Step 3: Combine evidence within each category\n", "    print(\"Step 3: Combining evidence within each coverage category...\")\n", "    enhanced_categories = await combine_evidence_by_category(categorized_checkpoints, section_info)\n", "    \n", "    # Step 4: Generate strategic recommendations\n", "    print(\"Step 4: Generating strategic recommendations...\")\n", "    strategic_recommendations = await generate_strategic_recommendations(enhanced_categories, section_info)\n", "    \n", "    # Step 5: Calculate summary statistics\n", "    total_checkpoints = len(checkpoint_details)\n", "    covered_checkpoints = len([cp for cp in checkpoint_details if cp['coverage_score'] >= 8.0])\n", "    coverage_percentage = (covered_checkpoints / total_checkpoints * 100) if total_checkpoints > 0 else 0.0\n", "    \n", "    # Create a mapping from checkpoint text to detailed analysis\n", "    checkpoint_to_details = {detail['checkpoint']: detail for detail in checkpoint_details}\n", "    \n", "    # Create category summary for the report with detailed checkpoint analysis included\n", "    category_summary = {}\n", "    for category_name, category_data in enhanced_categories.items():\n", "        # Get detailed analysis for each checkpoint in this category\n", "        checkpoint_details_for_category = []\n", "        for checkpoint_text in category_data['specific_checkpoints']:\n", "            if checkpoint_text in checkpoint_to_details:\n", "                checkpoint_details_for_category.append(checkpoint_to_details[checkpoint_text])\n", "        \n", "        category_summary[category_name] = {\n", "            'checkpoint_count': category_data['checkpoint_count'],\n", "            'score_range': category_data['score_range'],\n", "            'combined_supporting_evidence': category_data['combined_supporting_evidence'],\n", "            'combined_gap_analysis': category_data['combined_gap_analysis'],\n", "            'checkpoint_details': checkpoint_details_for_category  # Include detailed analysis within category\n", "        }\n", "    \n", "    # Create the comprehensive report\n", "    report = {\n", "        'section': section,\n", "        'section_title': title,\n", "        'summary': {\n", "            'total_checkpoints': total_checkpoints,\n", "            'covered_checkpoints': covered_checkpoints,\n", "            'coverage_percentage': round(coverage_percentage, 1),\n", "            'total_input_chunks_analyzed': len(all_input_chunks)\n", "        },\n", "        'coverage_categories': category_summary,\n", "        'strategic_recommendations': strategic_recommendations\n", "        # Removed detailed_checkpoint_analysis as it's now within each category\n", "    }\n", "    \n", "    print(f\"=== Comprehensive Gap Analysis Complete for Section {section} ===\")\n", "    print(f\"Coverage: {covered_checkpoints}/{total_checkpoints} ({coverage_percentage:.1f}%)\")\n", "    print(f\"Generated strategic recommendations\")\n", "    \n", "    return report\n", "\n", "async def perform_comprehensive_gap_analysis(processed_doc: Dict) -> Dict:\n", "    \"\"\"\n", "    Performs comprehensive gap analysis across all sections in the Pre-IND briefing document.\n", "    Uses all available input document chunks from MongoDB for comprehensive coverage analysis.\n", "    \n", "    Args:\n", "        processed_doc: The main JSON object containing all sections (after extract_relevant_chunks)\n", "        \n", "    Returns:\n", "        Dict: Comprehensive gap analysis report for all sections\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"STARTING COMPREHENSIVE GAP ANALYSIS FOR PRE-IND BRIEFING DOCUMENT\")\n", "    print(\"=\"*80)\n", "    \n", "    # Collect all sections that need gap analysis\n", "    sections_to_analyze = []\n", "    \n", "    def collect_sections(obj, path=\"\"):\n", "        nonlocal sections_to_analyze\n", "        \n", "        if isinstance(obj, dict):\n", "            # Check if this is a section with gap analysis requirements\n", "            if (obj and \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"] and\n", "                \"ich_guideline_combined_summaries_checkpoint_list\" in obj and obj[\"section\"] == \"1.3\"):\n", "                \n", "                sections_to_analyze.append({\n", "                    'section_info': obj,\n", "                    'path': path\n", "                })\n", "                print(f\"Found section {obj.get('section', 'unknown')} for gap analysis\")\n", "            \n", "            # Recursively process nested sections\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value, f\"{path}.{key}\" if path else key)\n", "    \n", "    # Collect all sections\n", "    collect_sections(processed_doc)\n", "    \n", "    print(f\"\\nCollected {len(sections_to_analyze)} sections for gap analysis\")\n", "    \n", "    if not sections_to_analyze:\n", "        print(\"No sections found for gap analysis\")\n", "        return {\n", "            'gap_analysis_report': {\n", "                'metadata': {\n", "                    'analysis_date': '2024-01-15',\n", "                    'sections_analyzed': 0,\n", "                    'total_checkpoints': 0,\n", "                    'overall_coverage_percentage': 0.0,\n", "                    'error': 'No sections found for gap analysis'\n", "                }\n", "            }\n", "        }\n", "    \n", "    # Get all input chunks from MongoDB for comprehensive analysis\n", "    print(\"Fetching all input document chunks from MongoDB...\")\n", "    db = mongodb_client['mednova']\n", "    chunk_collection = db['pre_ind_input_docs_chunks']\n", "    \n", "    # Get all chunks and convert ObjectIds to strings for JSON serialization in parallel\n", "    async def process_chunk(chunk):\n", "        # Convert ObjectId to string for JSON serialization\n", "        if '_id' in chunk:\n", "            chunk['_id'] = str(chunk['_id'])\n", "        return chunk\n", "    \n", "    # Fetch all chunks first\n", "    raw_chunks = await chunk_collection.find({}).to_list()\n", "    \n", "    # Process all chunks in parallel\n", "    all_input_chunks = await asyncio.gather(*[process_chunk(chunk) for chunk in raw_chunks])\n", "    \n", "    print(f\"Retrieved {len(all_input_chunks)} total input document chunks for analysis\")\n", "    # Process all sections in parallel using semaphores for rate limiting\n", "    section_analyses = {}\n", "    \n", "    print(f\"\\nProcessing {len(sections_to_analyze)} sections sequentially...\")\n", "    \n", "    # Process sections sequentially with intermediate saves\n", "    for i, section_data in enumerate(sections_to_analyze):\n", "        section_info = section_data['section_info']\n", "        section_key = section_info.get('section', f'unknown_{section_data[\"path\"]}')\n", "        \n", "        print(f\"Processing gap analysis for section {i+1}/{len(sections_to_analyze)}: {section_key}...\")\n", "        report = await generate_section_gap_analysis_report(section_info, all_input_chunks)\n", "        section_analyses[section_key] = report\n", "        \n", "        # Calculate current statistics for intermediate save\n", "        current_total_sections = len(section_analyses)\n", "        current_total_checkpoints = sum(report['summary']['total_checkpoints'] for report in section_analyses.values())\n", "        current_total_covered = sum(report['summary']['covered_checkpoints'] for report in section_analyses.values())\n", "        current_overall_coverage = (current_total_covered / current_total_checkpoints * 100) if current_total_checkpoints > 0 else 0.0\n", "        \n", "        # Create intermediate gap analysis report\n", "        intermediate_gap_analysis_report = {\n", "            'gap_analysis_report': {\n", "                'metadata': {\n", "                    'sections_analyzed': current_total_sections,\n", "                    'total_checkpoints': current_total_checkpoints,\n", "                    'overall_coverage_percentage': round(current_overall_coverage, 1),\n", "                    'total_input_chunks_analyzed': len(all_input_chunks),\n", "                    'progress_status': f\"Completed {i+1} of {len(sections_to_analyze)} sections\"\n", "                },\n", "                'section_analyses': section_analyses\n", "            }\n", "        }\n", "        \n", "        # Save intermediate progress\n", "        write_to_json(intermediate_gap_analysis_report, \"comprehensive_gap_analysis_report.json\")\n", "        \n", "        print(f\"✅ Completed gap analysis for section {i+1}/{len(sections_to_analyze)}: {section_key}\")\n", "        print(f\"   Current coverage: {current_overall_coverage:.1f}% ({current_total_covered}/{current_total_checkpoints} checkpoints)\")\n", "        print(f\"   Progress saved to: comprehensive_gap_analysis_report.json\")\n", "    \n", "    # Calculate overall statistics\n", "    total_sections = len(section_analyses)\n", "    total_checkpoints = sum(report['summary']['total_checkpoints'] for report in section_analyses.values())\n", "    total_covered = sum(report['summary']['covered_checkpoints'] for report in section_analyses.values())\n", "    overall_coverage = (total_covered / total_checkpoints * 100) if total_checkpoints > 0 else 0.0\n", "    # Identify critical gaps and strengths\n", "    critical_gaps = []\n", "    strengths = []\n", "    \n", "    for section_key, report in section_analyses.items():\n", "        # Critical gaps - now iterate through coverage categories\n", "        for category_name, category_data in report['coverage_categories'].items():\n", "            for checkpoint in category_data['checkpoint_details']:\n", "                if checkpoint['coverage_score'] < 4.0:\n", "                    critical_gaps.append({\n", "                        'section': section_key,\n", "                        'checkpoint': checkpoint['checkpoint'],\n", "                    })\n", "        \n", "        # Strengths (high coverage percentage)\n", "        if report['summary']['coverage_percentage'] >= 90:\n", "            strengths.append(f\"Section {section_key}: Excellent coverage ({report['summary']['coverage_percentage']:.1f}%)\")\n", "        elif report['summary']['coverage_percentage'] >= 80:\n", "            strengths.append(f\"Section {section_key}: Good coverage ({report['summary']['coverage_percentage']:.1f}%)\")\n", "\n", "    # Create the final comprehensive report\n", "    gap_analysis_report = {\n", "        'gap_analysis_report': {\n", "            'metadata': {\n", "                'sections_analyzed': total_sections,\n", "                'total_checkpoints': total_checkpoints,\n", "                'overall_coverage_percentage': round(overall_coverage, 1),\n", "                'total_input_chunks_analyzed': len(all_input_chunks),\n", "                'status': 'complete'\n", "            },\n", "            'section_analyses': section_analyses\n", "        }\n", "    }\n", "    \n", "    # Save final complete report\n", "    write_to_json(gap_analysis_report, \"comprehensive_gap_analysis_report.json\")\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"GAP ANALYSIS COMPLETE\")\n", "    print(\"=\"*80)\n", "    print(f\"Sections Analyzed: {total_sections}\")\n", "    print(f\"Total Checkpoints: {total_checkpoints}\")\n", "    print(f\"Overall Coverage: {overall_coverage:.1f}% ({total_covered}/{total_checkpoints})\")\n", "    print(f\"Final report saved to: comprehensive_gap_analysis_report.json\")\n", "    print(\"=\"*80)\n", "    \n", "    return gap_analysis_report\n", "\n", "async def combine_evidence_by_category(categorized_checkpoints: Dict, section_info: Dict) -> Dict:\n", "    \"\"\"\n", "    Combine supporting evidence and identified gaps within each coverage category using LLM.\n", "    \n", "    Args:\n", "        categorized_checkpoints: Output from categorize_checkpoints_by_coverage()\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Dict with enhanced category information including combined evidence\n", "    \"\"\"\n", "    enhanced_categories = {}\n", "    \n", "    # Create tasks for categories that need LLM processing\n", "    category_tasks = []\n", "    categories_to_process = []\n", "    \n", "    for category_name, category_data in categorized_checkpoints.items():\n", "        # Skip categories with no checkpoints\n", "        if category_data['checkpoint_count'] == 0:\n", "            enhanced_categories[category_name] = {\n", "                **category_data,\n", "                'combined_supporting_evidence': 'No checkpoints in this category',\n", "                'combined_gap_analysis': 'No checkpoints in this category'\n", "            }\n", "            continue\n", "        \n", "        # Extract supporting evidence and gaps from all checkpoints in this category\n", "        supporting_evidence_list = []\n", "        gap_analysis_list = []\n", "        \n", "        for checkpoint_detail in category_data['checkpoint_details']:\n", "            supporting_evidence = checkpoint_detail.get('supporting_evidence', '')\n", "            identified_gaps = checkpoint_detail.get('identified_gaps', '')\n", "            \n", "            if supporting_evidence and supporting_evidence != 'No relevant information available':\n", "                supporting_evidence_list.append(supporting_evidence)\n", "            \n", "            if identified_gaps and identified_gaps != 'No gaps identified':\n", "                gap_analysis_list.append(identified_gaps)\n", "        \n", "        # Create task for LLM processing if we have evidence to combine\n", "        if supporting_evidence_list or gap_analysis_list:\n", "            task = combine_category_evidence_with_llm(\n", "                category_name,\n", "                supporting_evidence_list,\n", "                gap_analysis_list,\n", "                category_data,\n", "                section_info\n", "            )\n", "            category_tasks.append(task)\n", "            categories_to_process.append((category_name, category_data))\n", "        else:\n", "            # Handle categories with no evidence immediately\n", "            enhanced_categories[category_name] = {\n", "                **category_data,\n", "                'combined_supporting_evidence': 'No supporting evidence available',\n", "                'combined_gap_analysis': 'No gaps identified'\n", "            }\n", "            # Remove checkpoint_details to keep the final report clean (optional)\n", "            if 'checkpoint_details' in enhanced_categories[category_name]:\n", "                del enhanced_categories[category_name]['checkpoint_details']\n", "    \n", "    # Execute all LLM tasks in parallel\n", "    if category_tasks:\n", "        combined_evidence_results = await asyncio.gather(*category_tasks)\n", "        \n", "        # Process results for each category\n", "        for (category_name, category_data), combined_evidence in zip(categories_to_process, combined_evidence_results):\n", "            # Create enhanced category data\n", "            enhanced_categories[category_name] = {\n", "                **category_data,\n", "                'combined_supporting_evidence': combined_evidence['combined_supporting_evidence'],\n", "                'combined_gap_analysis': combined_evidence['combined_gap_analysis']\n", "            }\n", "            \n", "            # Remove checkpoint_details to keep the final report clean (optional)\n", "            if 'checkpoint_details' in enhanced_categories[category_name]:\n", "                del enhanced_categories[category_name]['checkpoint_details']\n", "    \n", "    return enhanced_categories\n", "\n", "def create_evidence_batches(evidence_list: List[str], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_evidence_per_batch: int = 10) -> List[List[str]]:\n", "    \"\"\"\n", "    Create batches of evidence items for efficient combination.\n", "    \n", "    Args:\n", "        evidence_list: List of evidence strings\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_evidence_per_batch: Maximum number of evidence items per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of evidence strings\n", "    \"\"\"\n", "    if not evidence_list:\n", "        return []\n", "    \n", "    # Calculate base tokens for system prompt and context (estimated)\n", "    base_tokens = 2000  # Buffer for system prompt and context\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for evidence in evidence_list:\n", "        # Calculate tokens for this evidence item\n", "        evidence_tokens = calculate_number_of_tokens(evidence)\n", "        \n", "        # Check if adding this evidence would exceed limits\n", "        if (current_tokens + evidence_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_evidence_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add evidence to current batch\n", "        current_batch.append(evidence)\n", "        current_tokens += evidence_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n", "\n", "async def combine_single_evidence_batch(\n", "    evidence_batch: List[str],\n", "    evidence_type: str,  # \"supporting_evidence\" or \"gap_analysis\"\n", "    category_name: str,\n", "    category_data: Dict,\n", "    section_info: Dict,\n", ") -> str:\n", "    \"\"\"\n", "    Combine a single batch of evidence using LLM.\n", "    \n", "    Args:\n", "        evidence_batch: List of evidence strings in this batch\n", "        evidence_type: Type of evidence (\"supporting_evidence\" or \"gap_analysis\")\n", "        category_name: Name of the coverage category\n", "        category_data: Category information\n", "        section_info: Section information\n", "        \n", "    Returns:\n", "        String containing combined evidence for this batch\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to analyze and combine evidence within a specific coverage category to create comprehensive category-level insights.\"\"\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are analyzing evidence within a specific coverage category for a Pre-IND briefing document section.\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            user_prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                user_prompt += f\"\"\"\n", "Context: You are analyzing evidence for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this coverage category contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            # Inject drug type context for enhanced evidence combination\n", "            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)\n", "            \n", "            user_prompt += f\"\"\"\n", "Coverage Category: {category_name}\n", "Score Range: {category_data['score_range'][0]}-{category_data['score_range'][1]}\n", "Evidence Type: {evidence_type}\n", "\n", "Evidence to combine from {len(evidence_batch)} items:\n", "<evidence_batch>\n", "{format_list_as_markdown(evidence_batch)}\n", "</evidence_batch>\n", "\n", "Before combining the evidence, analyze the content in your thinking block using <evidence_analysis> tags:\n", "\n", "1. Review all evidence items for common themes and patterns\n", "2. Identify key technical details that must be preserved\n", "3. Note any redundant or overlapping information\n", "4. Plan the structure and organization of the combined summary\n", "5. Consider the regulatory context and coverage score range\n", "\n", "Your task is to combine this batch of evidence into a single, comprehensive summary in **markdown format** while removing redundancy. Present your final combined evidence in <final_combined_evidence> tags:\n", "\n", "\"\"\"\n", "            if evidence_type == \"supporting_evidence\":\n", "                user_prompt += \"\"\"\n", "Create a **Combined Supporting Evidence** summary that:\n", "- Combines all evidence into a single, non-redundant explanation\n", "- Focuses on what information IS present for this coverage category\n", "- Highlights common strengths and evidence themes\n", "- Preserves specific technical details while removing redundancy\n", "\"\"\"\n", "            else:\n", "                user_prompt += \"\"\"\n", "Create a **Combined Gap Analysis** summary that:\n", "- Combines all gaps into a single, non-redundant explanation\n", "- Focuses on what information is MISSING or insufficient for this coverage category\n", "- Highlights common gap themes and missing information patterns\n", "- Preserves specific technical details while removing redundancy\n", "\"\"\"\n", "\n", "            user_prompt += \"\"\"\n", "Guidelines for combination:\n", "- Remove redundancy while preserving specific technical details\n", "- Focus on category-level themes and patterns\n", "- Use clear, professional language appropriate for regulatory documentation\n", "- Consider the coverage score range when interpreting the evidence\n", "\n", "**Markdown Formatting Requirements:**\n", "- Use proper headings (## for main sections, ### for subsections)\n", "- Use bullet points (-) for lists of evidence or gaps\n", "- Use **bold** for key terms and important points\n", "- Use *italics* for emphasis where appropriate\n", "- Use > blockquotes for important regulatory quotes or citations\n", "- Structure content with clear sections and subsections\n", "- Make the output visually appealing and easy to read in a frontend interface\n", "\n", "Your response should be the combined evidence in <final_combined_evidence> tags in markdown format.\n", "\"\"\"\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=generator_model,\n", "                    system_prompt=system_prompt,\n", "                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            response = await retry_on_empty_response(make_api_call)\n", "\n", "            if response:\n", "                # Extract content from final_combined_evidence tags first\n", "                extracted_content = extract_content_from_tags(response, 'final_combined_evidence')\n", "                return extracted_content.strip()\n", "            else:\n", "                print(\"Empty response received from evidence batch combination\")\n", "                retry_count += 1\n", "                    \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in combine_single_evidence_batch (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Returning default evidence batch combination.\")\n", "                return f\"Error occurred during {evidence_type} batch combination\"\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return f\"Unexpected error in {evidence_type} batch combination\"\n", "\n", "async def combine_evidence_hierarchically(\n", "    evidence_list: List[str],\n", "    evidence_type: str,\n", "    category_name: str,\n", "    category_data: Dict,\n", "    section_info: Dict,\n", ") -> str:\n", "    \"\"\"\n", "    Combine evidence hierarchically using divide and conquer approach.\n", "    \n", "    Args:\n", "        evidence_list: List of evidence strings to combine\n", "        evidence_type: Type of evidence (\"supporting_evidence\" or \"gap_analysis\")\n", "        category_name: Name of the coverage category\n", "        category_data: Category information\n", "        section_info: Section information\n", "        \n", "    Returns:\n", "        String containing final combined evidence\n", "    \"\"\"\n", "    # Handle empty or single evidence\n", "    if not evidence_list:\n", "        return \"No relevant information available for this category\"\n", "    \n", "    if len(evidence_list) == 1:\n", "        return evidence_list[0]\n", "    \n", "    print(f\"Combining {len(evidence_list)} {evidence_type} items hierarchically for category {category_name}\")\n", "    \n", "    # Start with creating initial batches\n", "    current_evidence = evidence_list[:]\n", "    \n", "    # Iteratively combine evidence in batches until we have a single result\n", "    while len(current_evidence) > 1:\n", "        # Create optimal batches based on token count and number of evidence items\n", "        batches = create_evidence_batches(current_evidence)\n", "        \n", "        print(f\"Created {len(batches)} batches for {evidence_type} combination\")\n", "        \n", "        # Ensure no batch has only one item to avoid infinite loop\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, merge it with the last batch\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        # Process all batches in parallel\n", "        batch_tasks = [\n", "            combine_single_evidence_batch(batch, evidence_type, category_name, category_data, section_info)\n", "            for batch in batches\n", "        ]\n", "        \n", "        # Wait for all batches to complete\n", "        current_evidence = await asyncio.gather(*batch_tasks)\n", "        \n", "        # Filter out any empty results\n", "        current_evidence = [evidence for evidence in current_evidence if evidence and evidence.strip()]\n", "        \n", "        print(f\"Combined {len(batches)} batches into {len(current_evidence)} intermediate results\")\n", "        \n", "        # Safety check to prevent infinite loops\n", "        if len(current_evidence) == len(batches) and all(len(batch) == 1 for batch in batches):\n", "            # If we're not making progress and all batches are single items, just concatenate\n", "            print(\"Warning: No progress in hierarchical combination, concatenating results\")\n", "            return \"\\n\\n\".join(current_evidence)\n", "    \n", "    print(f\"Final hierarchical combination complete for {evidence_type}\")\n", "    return current_evidence[0] if current_evidence else f\"No {evidence_type} available\"\n", "\n", "async def combine_category_evidence_with_llm(\n", "    category_name: str,\n", "    supporting_evidence_list: List[str],\n", "    gap_analysis_list: List[str],\n", "    category_data: Dict,\n", "    section_info: Dict,\n", ") -> Dict:\n", "    \"\"\"\n", "    Use LLM to combine evidence within a specific coverage category using hierarchical approach.\n", "    \n", "    Args:\n", "        category_name: Name of the coverage category\n", "        supporting_evidence_list: List of supporting evidence from checkpoints in this category\n", "        gap_analysis_list: List of gap analyses from checkpoints in this category\n", "        category_data: Category information\n", "        section_info: Section information\n", "        \n", "    Returns:\n", "        Dict with combined evidence\n", "    \"\"\"\n", "    try:\n", "        # Process supporting evidence and gap analysis in parallel using hierarchical approach\n", "        supporting_evidence_task = combine_evidence_hierarchically(\n", "            supporting_evidence_list,\n", "            \"supporting_evidence\",\n", "            category_name,\n", "            category_data,\n", "            section_info\n", "        )\n", "        \n", "        gap_analysis_task = combine_evidence_hierarchically(\n", "            gap_analysis_list,\n", "            \"gap_analysis\",\n", "            category_name,\n", "            category_data,\n", "            section_info\n", "        )\n", "        \n", "        # Execute both tasks in parallel\n", "        combined_supporting_evidence, combined_gap_analysis = await asyncio.gather(\n", "            supporting_evidence_task,\n", "            gap_analysis_task\n", "        )\n", "        \n", "        return {\n", "            'combined_supporting_evidence': combined_supporting_evidence,\n", "            'combined_gap_analysis': combined_gap_analysis\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in combine_category_evidence_with_llm: {e}\")\n", "        return {\n", "            'combined_supporting_evidence': 'Error occurred during category evidence combination',\n", "            'combined_gap_analysis': 'Error occurred during category evidence combination'\n", "        }\n", "\n", "def create_category_summary_batches(category_summaries: List[Dict], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_categories_per_batch: int = 5) -> List[List[Dict]]:\n", "    \"\"\"\n", "    Create batches of category summaries for efficient strategic recommendation generation.\n", "    \n", "    Args:\n", "        category_summaries: List of category summary dictionaries\n", "        max_batch_tokens: Maximum tokens per batch\n", "        max_categories_per_batch: Maximum number of categories per batch\n", "        \n", "    Returns:\n", "        List of batches, where each batch is a list of category summaries\n", "    \"\"\"\n", "    if not category_summaries:\n", "        return []\n", "    \n", "    # Calculate base tokens for system prompt and context (estimated)\n", "    base_tokens = 2000  # Buffer for system prompt and context\n", "    \n", "    batches = []\n", "    current_batch = []\n", "    current_tokens = base_tokens\n", "    \n", "    for category_summary in category_summaries:\n", "        # Calculate tokens for this category summary\n", "        category_tokens = calculate_number_of_tokens(json.dumps(category_summary))\n", "        \n", "        # Check if adding this category would exceed limits\n", "        if (current_tokens + category_tokens > max_batch_tokens or \n", "            len(current_batch) >= max_categories_per_batch):\n", "            \n", "            # Start new batch if current batch is not empty\n", "            if current_batch:\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_tokens = base_tokens\n", "        \n", "        # Add category to current batch\n", "        current_batch.append(category_summary)\n", "        current_tokens += category_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    return batches\n", "\n", "async def generate_recommendations_from_batch(\n", "    category_batch: List[Dict],\n", "    section_info: Dict,\n", ") -> str:\n", "    \"\"\"\n", "    Generate strategic recommendations from a single batch of category summaries.\n", "    \n", "    Args:\n", "        category_batch: List of category summaries in this batch\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        String containing markdown-formatted strategic recommendations for this batch\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to generate strategic, prioritized recommendations based on comprehensive gap analysis across coverage categories.\"\"\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are analyzing a comprehensive gap analysis for a Pre-IND briefing document section and need to provide strategic recommendations.\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            user_prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                user_prompt += f\"\"\"\n", "Context: You are generating strategic recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how addressing these gaps contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            # Inject drug type context for enhanced strategic recommendations\n", "            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)\n", "            \n", "            user_prompt += f\"\"\"\n", "Coverage Category Analysis for {len(category_batch)} categories:\n", "<category_analysis>\n", "{format_category_summaries_as_markdown(category_batch)}\n", "</category_analysis>\n", "\n", "Before generating recommendations, analyze the gap information in your thinking block using <recommendation_analysis> tags:\n", "\n", "1. Review each category's coverage status and identified gaps\n", "2. Prioritize gaps by regulatory impact and feasibility\n", "3. Identify patterns and systematic issues across categories\n", "4. Consider existing strengths that can be leveraged\n", "5. Plan the most effective strategic recommendations\n", "\n", "Based on this gap analysis, generate strategic recommendations that:\n", "\n", "1. **Prioritize by Impact**: Focus on the most critical gaps first (poor and no coverage categories)\n", "2. **Consider Regulatory Importance**: Emphasize actions needed for ICH guideline compliance\n", "3. **Be Specific and Actionable**: Provide concrete steps the user can take\n", "4. **Leverage Existing Strengths**: Build on what's already working well\n", "5. **Address Systematic Issues**: Look for patterns across categories\n", "\n", "Present your final recommendations in <final_strategic_recommendations> tags.\n", "\n", "Guidelines for recommendations:\n", "- Start with highest priority actions (addressing poor/no coverage first)\n", "- Be specific about what documents, data, or studies are needed\n", "- Consider the regulatory timeline and submission requirements\n", "- Build on existing supporting evidence where possible\n", "- Focus on compliance with the referenced ICH guidelines\n", "- Provide 3-8 strategic recommendations for this batch\n", "\n", "Your response should be a markdown-formatted bullet point list of recommendations:\n", "\n", "- **Recommendation 1**: Specific actionable recommendation\n", "- **Recommendation 2**: Another specific actionable recommendation\n", "- **Recommendation 3**: Continue with more recommendations...\n", "\n", "Focus on the most critical and actionable recommendations for these specific categories.\n", "\"\"\"\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=generator_model,\n", "                    system_prompt=system_prompt,\n", "                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            response = await retry_on_empty_response(make_api_call)\n", "\n", "            if response:\n", "                # Extract content from final_strategic_recommendations tags first\n", "                extracted_content = extract_content_from_tags(response, 'final_strategic_recommendations')\n", "                return extracted_content.strip()\n", "            else:\n", "                print(\"Empty response received from recommendations batch generation\")\n", "                retry_count += 1\n", "                    \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in generate_recommendations_from_batch (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Returning default recommendations batch.\")\n", "                return \"- Error occurred during recommendations batch generation\"\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return \"- Unexpected error in recommendations batch generation\"\n", "\n", "async def merge_recommendation_batches(\n", "    recommendation_batches: List[str],\n", "    section_info: Dict,\n", ") -> str:\n", "    \"\"\"\n", "    Merge multiple batches of recommendations into a single prioritized markdown string.\n", "    \n", "    Args:\n", "        recommendation_batches: List of markdown recommendation strings to merge\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Single markdown string with merged and prioritized recommendations\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Get enhanced section context including outer section info\n", "            enhanced_context = get_enhanced_section_context(\n", "                section_info.get('section', ''),\n", "                section_info.get('title', ''),\n", "                section_info.get('description', ''),\n", "                section_info.get('referenced_ich_guidelines', [])\n", "            )\n", "            \n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to merge and prioritize strategic recommendations from multiple batches while removing redundancy and ensuring logical flow.\"\"\"\n", "            \n", "            user_prompt = f\"\"\"\n", "You are merging strategic recommendations from multiple batches for a Pre-IND briefing document section.\n", "\n", "\"\"\"\n", "            \n", "            # Add outer section context if available\n", "            if enhanced_context['has_outer_section']:\n", "                outer = enhanced_context['outer_section']\n", "                user_prompt += f\"\"\"\n", "Outer Section Context:\n", "<outer_section>\n", "Section: {outer['section']}\n", "Title: {outer['title']}\n", "Description: {outer['description']}\n", "</outer_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add current section information\n", "            current = enhanced_context['current_section']\n", "            user_prompt += f\"\"\"\n", "Current Section Information:\n", "<current_section>\n", "Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}\n", "Title: {current['title']}\n", "Description: {current['description']}\n", "Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}\n", "</current_section>\n", "\n", "\"\"\"\n", "            \n", "            # Add context explanation if there's an outer section\n", "            if enhanced_context['has_outer_section']:\n", "                user_prompt += f\"\"\"\n", "Context: You are merging recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how these recommendations contribute to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.\n", "\n", "\"\"\"\n", "            \n", "            # Inject drug type context for enhanced recommendation merging\n", "            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)\n", "            \n", "            user_prompt += f\"\"\"\n", "Recommendation Batches to Merge:\n", "<recommendation_batches>\n", "{format_evidence_batches_as_markdown(recommendation_batches)}\n", "</recommendation_batches>\n", "\n", "Before merging recommendations, analyze the content in your thinking block using <merge_analysis> tags:\n", "\n", "1. Review all recommendation batches for common themes and overlaps\n", "2. Identify the most critical and impactful recommendations\n", "3. Note any redundant or conflicting recommendations\n", "4. Plan the logical sequence and prioritization\n", "5. Consider regulatory importance and feasibility\n", "\n", "Your task is to merge these recommendations into a single, prioritized list. Present your final merged recommendations in <final_merged_recommendations> tags:\n", "\n", "1. **Removes Redundancy**: Eliminate duplicate or overlapping recommendations\n", "2. **Maintains Priorities**: Keep the most critical actions first\n", "3. **Ensures Clarity**: Each recommendation should be clear and actionable\n", "4. **Logical Flow**: Organize recommendations in a logical sequence\n", "5. **Regulatory Focus**: Maintain focus on ICH guideline compliance and Pre-IND requirements\n", "\n", "Guidelines for merging:\n", "- Combine similar recommendations into more comprehensive ones\n", "- Prioritize recommendations that address the most critical gaps\n", "- Limit final list to 5-10 strategic recommendations maximum\n", "- Ensure each recommendation is specific and actionable\n", "- Remove any recommendations that are too vague or redundant\n", "\n", "Your response should be a markdown-formatted bullet point list of the final merged recommendations:\n", "\n", "- **Priority 1**: Most critical actionable recommendation\n", "- **Priority 2**: Second most critical actionable recommendation  \n", "- **Priority 3**: Continue with remaining recommendations...\n", "\n", "Focus on the most impactful and feasible recommendations for regulatory compliance.\n", "\"\"\"\n", "\n", "            # Wrap the API call with retry_on_empty_response\n", "            async def make_api_call():\n", "                agent = Agent(\n", "                    model=generator_model,\n", "                    system_prompt=system_prompt,\n", "                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),\n", "                    retries=3\n", "                )\n", "\n", "                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)\n", "                return response.output\n", "\n", "            # Use the retry wrapper to handle empty responses\n", "            response = await retry_on_empty_response(make_api_call)\n", "\n", "            if response:\n", "                # Extract content from final_merged_recommendations tags first\n", "                extracted_content = extract_content_from_tags(response, 'final_merged_recommendations')\n", "                return extracted_content.strip()\n", "            else:\n", "                print(\"Empty response received from recommendations merging\")\n", "                retry_count += 1\n", "                    \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in merge_recommendation_batches (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(\"Maximum retries reached. Returning concatenated recommendations.\")\n", "                # Fallback: just concatenate all recommendations\n", "                return \"\\n\\n\".join(recommendation_batches)\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return \"\\n\\n\".join(recommendation_batches)\n", "\n", "async def generate_strategic_recommendations(enhanced_categories: Dict, section_info: Dict) -> str:\n", "    \"\"\"\n", "    Generate strategic recommendations based on comprehensive gap analysis using hierarchical approach.\n", "    \n", "    Args:\n", "        enhanced_categories: Output from combine_evidence_by_category() with combined evidence\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        str: Markdown-formatted string of strategic, prioritized recommendations\n", "    \"\"\"\n", "    try:\n", "        # Prepare category summaries for processing\n", "        category_summaries = []\n", "        for category_name, category_data in enhanced_categories.items():\n", "            if category_data['checkpoint_count'] > 0:\n", "                category_summaries.append({\n", "                    'category': category_name,\n", "                    'score_range': category_data['score_range'],\n", "                    'checkpoint_count': category_data['checkpoint_count'],\n", "                    'supporting_evidence': category_data['combined_supporting_evidence'],\n", "                    'gap_analysis': category_data['combined_gap_analysis']\n", "                })\n", "        \n", "        # Handle empty case\n", "        if not category_summaries:\n", "            return \"- No categories available for strategic recommendation generation\"\n", "        \n", "        # Handle single category case\n", "        if len(category_summaries) == 1:\n", "            return await generate_recommendations_from_batch(category_summaries, section_info)\n", "        \n", "        print(f\"Generating strategic recommendations hierarchically from {len(category_summaries)} categories\")\n", "        \n", "        # Create batches for category summaries\n", "        batches = create_category_summary_batches(category_summaries)\n", "        \n", "        print(f\"Created {len(batches)} batches for strategic recommendation generation\")\n", "        \n", "        # Generate recommendations from all batches in parallel\n", "        batch_tasks = [\n", "            generate_recommendations_from_batch(batch, section_info)\n", "            for batch in batches\n", "        ]\n", "        \n", "        # Execute all batch tasks in parallel\n", "        recommendation_batches = await asyncio.gather(*batch_tasks)\n", "        \n", "        # Filter out empty batches\n", "        recommendation_batches = [batch for batch in recommendation_batches if batch and batch.strip()]\n", "        \n", "        # If only one batch, return it directly\n", "        if len(recommendation_batches) == 1:\n", "            return recommendation_batches[0]\n", "        \n", "        # Merge multiple batches into final recommendations\n", "        print(f\"Merging {len(recommendation_batches)} recommendation batches\")\n", "        final_recommendations = await merge_recommendation_batches(recommendation_batches, section_info)\n", "        \n", "        print(f\"Generated strategic recommendations hierarchically\")\n", "        return final_recommendations\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in generate_strategic_recommendations: {e}\")\n", "        return \"\"\"- Error occurred during strategic recommendation generation\n", "- Please review the gap analysis manually and create appropriate recommendations\"\"\"\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "STARTING COMPREH<PERSON>SIVE GAP ANALYSIS FOR PRE-IND BRIEFING DOCUMENT\n", "================================================================================\n", "Found section 1.3 for gap analysis\n", "\n", "Collected 1 sections for gap analysis\n", "Fetching all input document chunks from MongoDB...\n", "Retrieved 129 total input document chunks for analysis\n", "\n", "Processing 1 sections sequentially...\n", "Processing gap analysis for section 1/1: 1.3...\n", "\n", "=== Starting Comprehensive Gap Analysis for Section 1.3: Pharmacological Class ===\n", "Analyzing 15 checkpoints against 129 input chunks...\n", "Step 1: Assessing coverage for all checkpoints...\n", "Processing all 15 checkpoints...\n", "Assessing checkpoint coverage: Is the pharmacological class of the investigational drug explicitly stated using a direct, unambiguo...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the pharmacological class statement provide sufficient context for FDA reviewers to anticipate ...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Are the referenced ICH guidelines (S7A, E8(R1)) explicitly cited in the pharmacological class subsec...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the statement avoid ambiguous or vague language that could obscure the drug's pharmacological c...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Is the pharmacological class statement positioned appropriately within the Introduction section to s...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the document justify any deviation from the standard pharmacological class examples provided (e...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Are there documented references to prior adverse effects observed in related pharmacological classes...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Has the therapeutic class of the substance been used to rationalize the selection of safety pharmaco...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Are ligand binding or enzyme assay data explicitly cited to justify the inclusion of specific safety...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the subsection address whether comparative metabolism data is available to determine if a tailo...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Are mechanisms of suspected or observed adverse effects specific to this pharmacological class clear...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Is there a justification for the balance between specificity and adaptability in study design, consi...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the pharmacological class designation align with classifications used in prior toxicology/clini...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Are new technologies or scientifically valid methods explicitly proposed for safety pharmacology stu...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Assessing checkpoint coverage: Does the pharmacological class description address the unmet medical need and disease context mentio...\n", "Evaluating 129 chunks against checkpoint...\n", "Using batched evaluation for 129 chunks\n", "Created 13 batches for chunk evaluation\n", "Coverage assessment complete: 1.80/10 (none) - 0 relevant chunks\n", "Coverage assessment complete: 3.50/10 (poor) - 0 relevant chunks\n", "Coverage assessment complete: 2.80/10 (poor) - 0 relevant chunks\n", "Coverage assessment complete: 2.50/10 (poor) - 0 relevant chunks\n", "Coverage assessment complete: 7.70/10 (good) - 1 relevant chunks\n", "Coverage assessment complete: 2.80/10 (poor) - 0 relevant chunks\n", "Coverage assessment complete: 2.50/10 (poor) - 0 relevant chunks\n", "Coverage assessment complete: 5.95/10 (partial) - 1 relevant chunks\n", "Coverage assessment complete: 4.05/10 (partial) - 0 relevant chunks\n", "Coverage assessment complete: 7.00/10 (good) - 0 relevant chunks\n", "Coverage assessment complete: 4.00/10 (partial) - 0 relevant chunks\n", "Coverage assessment complete: 9.30/10 (excellent) - 7 relevant chunks\n", "Coverage assessment complete: 8.50/10 (excellent) - 1 relevant chunks\n", "Coverage assessment complete: 6.30/10 (good) - 1 relevant chunks\n", "Coverage assessment complete: 2.60/10 (poor) - 0 relevant chunks\n", "Step 2: Categorizing checkpoints by coverage scores...\n", "Step 3: Combining evidence within each coverage category...\n", "Combining 2 supporting_evidence items hierarchically for category excellent_coverage\n", "Created 1 batches for supporting_evidence combination\n", "Combining 2 gap_analysis items hierarchically for category excellent_coverage\n", "Created 1 batches for gap_analysis combination\n", "Combining 2 supporting_evidence items hierarchically for category good_coverage\n", "Created 1 batches for supporting_evidence combination\n", "Combining 3 gap_analysis items hierarchically for category good_coverage\n", "Created 1 batches for gap_analysis combination\n", "Combining 2 gap_analysis items hierarchically for category partial_coverage\n", "Created 1 batches for gap_analysis combination\n", "Combining 6 gap_analysis items hierarchically for category poor_coverage\n", "Created 1 batches for gap_analysis combination\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for gap_analysis\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for gap_analysis\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for supporting_evidence\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for supporting_evidence\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for gap_analysis\n", "Combined 1 batches into 1 intermediate results\n", "Final hierarchical combination complete for gap_analysis\n", "Step 4: Generating strategic recommendations...\n", "Generating strategic recommendations hierarchically from 5 categories\n", "Created 1 batches for strategic recommendation generation\n", "=== Comprehensive Gap Analysis Complete for Section 1.3 ===\n", "Coverage: 2/15 (13.3%)\n", "Generated strategic recommendations\n", "JSON saved to comprehensive_gap_analysis_report.json\n", "✅ Completed gap analysis for section 1/1: 1.3\n", "   Current coverage: 13.3% (2/15 checkpoints)\n", "   Progress saved to: comprehensive_gap_analysis_report.json\n", "JSON saved to comprehensive_gap_analysis_report.json\n", "\n", "================================================================================\n", "GAP ANALYSIS COMPLETE\n", "================================================================================\n", "Sections Analyzed: 1\n", "Total Checkpoints: 15\n", "Overall Coverage: 13.3% (2/15)\n", "Final report saved to: comprehensive_gap_analysis_report.json\n", "================================================================================\n", "JSON saved to comprehensive_gap_analysis_report.json\n", "\n", "📊 Gap Analysis Report Summary:\n", "📋 Sections Analyzed: 1\n", "✅ Overall Coverage: 13.3%\n", "\n", "📄 Detailed report saved to: comprehensive_gap_analysis_report.json\n"]}], "source": ["# ===========================\n", "# COMPREHENSIVE GAP ANALYSIS\n", "# ===========================\n", "\n", "# Perform comprehensive gap analysis using all available input chunks\n", "# This now uses all input chunks from MongoDB by default (no filtering required)\n", "gap_analysis_results = await perform_comprehensive_gap_analysis(processed_doc)\n", "\n", "# Save the gap analysis report\n", "write_to_json(gap_analysis_results, \"comprehensive_gap_analysis_report.json\")\n", "\n", "print(\"\\n📊 Gap Analysis Report Summary:\")\n", "print(f\"📋 Sections Analyzed: {gap_analysis_results['gap_analysis_report']['metadata']['sections_analyzed']}\")\n", "print(f\"✅ Overall Coverage: {gap_analysis_results['gap_analysis_report']['metadata']['overall_coverage_percentage']}%\")\n", "\n", "print(f\"\\n📄 Detailed report saved to: comprehensive_gap_analysis_report.json\")"]}], "metadata": {"kernelspec": {"display_name": "NDA", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}