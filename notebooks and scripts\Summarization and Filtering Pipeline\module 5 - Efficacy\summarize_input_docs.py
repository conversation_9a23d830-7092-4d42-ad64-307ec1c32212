import os
import json
import asyncio
from openai import Async<PERSON>penA<PERSON>
from dotenv import load_dotenv
import glob
from db_utils import insert_chunks_in_db_for_efficacy_input_docs
from bson.objectid import ObjectId
from transformers import AutoTokenizer
from retry_on_empty_response import retry_on_empty_response

# Load environment variables
load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

print(GENERATOR_MODEL_NAME, GENERATOR_MODEL_BASE_URL, GENERATOR_MODEL_API_KEY)

# Maximum token limit for input (keeping some buffer for the prompt and output)
MAX_TOKEN_LIMIT = 20000

# Initialize AsyncOpenAI client
openai_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL,
    api_key=GENERATOR_MODEL_API_KEY
)


# Initialize tokenizer
tokenizer = None

def extract_summary_from_tags(content: str) -> str:
    """
    Extract summary from <summary> or <final_summary> tags if present, otherwise return content as is.
    
    Args:
        content: The raw content from the LLM response
        
    Returns:
        Extracted summary or original content if tags not found
    """
    if not content:
        return content
        
    # Look for <summary> tags first, then <final_summary> tags
    import re
    summary_match = re.search(r'<summary>(.*?)</summary>', content, re.DOTALL | re.IGNORECASE)
    if summary_match:
        return summary_match.group(1).strip()
    
    final_summary_match = re.search(r'<final_summary>(.*?)</final_summary>', content, re.DOTALL | re.IGNORECASE)
    if final_summary_match:
        return final_summary_match.group(1).strip()
    
    # Return original content if no tags found
    return content

def calculate_number_of_tokens(text):
    """Calculate the number of tokens in a text using Mistral tokenizer."""
    global tokenizer
    if tokenizer is None:
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")
    
    tokens = tokenizer.encode(text, add_special_tokens=False)
    return len(tokens)

def read_document(file_path):
    """Read a document file and return its contents."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

async def get_individual_summary(doc_text, doc_name):
    """Get summary of a single document using the LLM."""
    system_prompt = """You are an expert in pharmaceutical documentation and regulatory submissions. Your task is to create an exhaustive yet concise summary of a given document, capturing every critical piece of information while maintaining clarity and organization."""
    
    user_prompt = f"""
Here is the full text of the document:
<doc_text>
{doc_text}
</doc_text>

And here is the name of the document you need to summarize:
<doc_name>
{doc_name}
</doc_name>

Please follow these steps to create your summary:

1. Carefully read and analyze the document.
2. Inside <document_breakdown> tags in your thinking block, break down the document into its key components:
   - Quote key sentences or phrases from each major section of the document
   - Identify all critical information
   - Note any technical specifications or requirements
   - List all numerical values and their units, along with their context
   - Highlight any regulatory requirements
   - Organize the information into these predefined categories:
     a) Document Overview
     b) Technical Specifications
     c) Regulatory Requirements
     d) Validation Criteria
     e) Conclusion

3. Draft an initial summary based on your analysis.

4. Review your draft summary against the original document to ensure no critical information has been missed.

5. Refine and format your summary according to these requirements:
   - Use plain text only - no special formatting or markdown
   - Organize content into clear numbered sections
   - Use simple indentation for hierarchy
   - Use hyphens (-) for bullet points
   - Separate paragraphs with blank lines

6. Present your final summary in <summary> tags.

Remember these critical requirements:
1. Retain 100% of ALL critical information - no exceptions
2. Use precise, clear language to minimize verbosity
3. Avoid redundant or repetitive statements
4. Use precise technical terminology
5. Present specifications and requirements clearly
6. Include exact numerical values and units
7. Document methods and criteria efficiently
8. Present validation requirements concisely

Here's an example of the desired output structure (note that this is a generic example and your actual summary should be tailored to the specific content of the document):

<summary>
1. Document Overview
   - Title: [Document Title]
   - Date: [Document Date]
   - Purpose: [Brief description of document purpose]

2. Technical Specifications
   2.1 Product Details
       - Name: [Product Name]
       - Classification: [Product Classification]
   2.2 Manufacturing Process
       - Method: [Brief description of manufacturing method]
       - Key Parameters:
         - Temperature: [X°C ± Y°C]
         - Pressure: [X kPa ± Y kPa]

3. Regulatory Requirements
   - Governing Body: [Name of Regulatory Body]
   - Key Regulations:
     - [Regulation 1]: [Brief description]
     - [Regulation 2]: [Brief description]

4. Validation Criteria
   - Test Method: [Name of Test Method]
   - Acceptance Criteria:
     - [Criterion 1]: [Specific requirement]
     - [Criterion 2]: [Specific requirement]

5. Conclusion
   [Brief summary of key points and any critical considerations]
</summary>

Please proceed with your analysis and summary of the document. Your final output should consist only of the summary and should not duplicate or rehash any of the work you did in the thinking block."""

    async def make_api_call():
        response = await openai_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            temperature=0.2,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )
        
        return response.choices[0].message.content
    
    raw_summary = await retry_on_empty_response(make_api_call)
    
    # Extract summary from tags if present
    if raw_summary is None:
        summary = ""
    else:
        summary = extract_summary_from_tags(raw_summary)
    
    return summary

async def create_final_summary(individual_summaries):
    """Create a final summary from all individual summaries by combining multiple summaries at once while respecting token limits."""
    
    print(f"\nStarting final summary creation with {len(individual_summaries)} individual summaries")
    
    # Handle empty input
    if not individual_summaries:
        print("No summaries provided, returning empty string")
        return ""
        
    # Handle single summary case
    if len(individual_summaries) == 1:
        print("Only one summary provided, returning it as is")
        return individual_summaries[0]
    
    async def combine_summaries(summaries_to_combine):
        """Helper function to combine multiple summaries while preserving all information."""
        total_summaries = len(summaries_to_combine)
        print(f"\nCombining {total_summaries} summaries:")
        
        total_tokens = sum(calculate_number_of_tokens(summary) for summary in summaries_to_combine)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are an expert in pharmaceutical documentation and regulatory submissions. Your task is to combine multiple summaries into one comprehensive, unified document. This is a critical task that requires utmost attention to detail and accuracy."""

        user_prompt = f"""
Here are the summaries you need to combine:

<summaries>
        """
        
        # Add each summary with a header
        for i, summary in enumerate(summaries_to_combine, 1):
            user_prompt += f"""
        Summary {i}:
        <summary_{i}>
        {summary}
        </summary_{i}>
        """
        
        user_prompt += f"""
</summaries>

A total of {total_summaries} summaries are provided.

Your primary objective is to create a combined summary that preserves ALL information from all input summaries. No information should be lost or omitted.

Before creating the final combined summary, please complete the following steps inside the summary_integration_planning block:

<summary_integration_planning>
1. List all unique information points from each summary:
   [For each summary, list out all unique pieces of information]

2. Identify overlapping information across summaries:
   [List any information that appears in multiple summaries]

3. Create a detailed outline for the combined summary:
   [Provide a comprehensive outline of how you plan to organize all the information]

4. Plan the integration strategy:
   [Describe how you will merge unique and overlapping content from all summaries efficiently]

5. Self-verification process:
   [Explain how you will ensure all information is preserved and accurately represented]
</summary_integration_planning>

After completing your planning, create the combined summary using the following guidelines:

1. Information Preservation (HIGHEST PRIORITY):
   - Include EVERY piece of information from all summaries
   - Preserve all specific numbers, measurements, criteria, and technical specifications
   - Maintain all references, citations, examples, and case studies
   - Keep all requirements, recommendations, validation details, and risk assessments
   - Preserve all cross-references and dependencies

2. Integration Strategy:
   - Merge content logically while avoiding duplication
   - Use clear transitions between different topics
   - Maintain chronological order and hierarchical relationships where relevant

3. Quality Assurance:
   - Ensure all technical terms, regulatory requirements, and compliance information are preserved
   - Maintain all quality control measures, safety considerations, and efficacy-related information
   - Preserve all manufacturing details, storage instructions, and stability information

Output Format:
- Use plain text format only
- Use simple bullet points with hyphens (-) when needed
- Separate paragraphs with blank lines
- Use parentheses for references or additional information

Example structure (use appropriate content based on the summaries):

Summary Title

Introduction
[Provide a brief overview of the combined summary]

Key Findings
- Finding 1
- Finding 2
  - Subfinding 2.1
  - Subfinding 2.2
- Finding 3

Detailed Analysis
[Provide detailed information from all summaries, organized by relevant categories]

Conclusion
[Summarize the main points and any overarching conclusions]

References
[List all references and citations from the original summaries]

After creating the combined summary, please verify that ALL information from all summaries has been preserved by conducting a final check against the original summaries.

Present your final combined summary in <final_summary> tags. Your final output should consist only of the combined summary in the tags and should not duplicate or rehash any of the work you did in the summary_integration_planning block."""

        print(f"Sending request to LLM to combine {total_summaries} summaries...")
        async def make_api_call():
            response = await openai_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )
            return response.choices[0].message.content
        
        raw_combined_summary = await retry_on_empty_response(make_api_call)
        
        # Extract summary from tags if present
        if raw_combined_summary is None:
            combined_summary = ""
        else:
            combined_summary = extract_summary_from_tags(raw_combined_summary)
        
        print(f"Combined summary length: {len(combined_summary)} characters")
        print(f"Combined summary contains {len(combined_summary.split())} words")
        print(f"Combined summary token count: {calculate_number_of_tokens(combined_summary)}")
        
        return combined_summary

    # Process summaries iteratively, combining optimal batches in parallel
    current_summaries = individual_summaries.copy()
    iteration = 1
    
    while len(current_summaries) > 1:
        print(f"\nIteration {iteration}:")
        print(f"Number of summaries to process: {len(current_summaries)}")
        
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for summary in current_summaries:
            summary_tokens = calculate_number_of_tokens(summary)
            
            # If adding this summary would exceed the token limit, start a new batch
            if current_batch and current_token_count + summary_tokens > MAX_TOKEN_LIMIT:
                batches.append(current_batch)
                current_batch = [summary]
                current_token_count = summary_tokens
            else:
                current_batch.append(summary)
                current_token_count += summary_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for processing")
        for i, batch in enumerate(batches):
            print(f"Batch {i+1} contains {len(batch)} summaries with {sum(calculate_number_of_tokens(s) for s in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_summaries(batch) for batch in batches]
        
        # Wait for all combinations to complete
        print("Waiting for all batch combinations to complete...")
        new_summaries = await asyncio.gather(*tasks)
        current_summaries = new_summaries
        
        print(f"Iteration {iteration} complete. {len(current_summaries)} summaries remaining")
        iteration += 1
    
    print(f"\nFinal summary creation complete. Final summary length: {len(current_summaries[0])} characters")
    print(f"Final summary contains {len(current_summaries[0].split())} words")
    print(f"Final summary token count: {calculate_number_of_tokens(current_summaries[0])}")
    
    return current_summaries[0]

async def process_document(doc_file):
    """Process a single document and return its summary and metadata."""
    doc_name = os.path.basename(doc_file)
    print(f"Processing individual summary for {doc_name}...")
    
    # Read the document
    doc_text = read_document(doc_file)
    
    # Store original document in MongoDB
    doc_id = insert_chunks_in_db_for_efficacy_input_docs(
        chunks_data=doc_text,
        chunk_filename=doc_name,
        efficacy_input_doc_tag="input_docs",
        is_summary=False
    )
    
    # Get individual summary
    summary = await get_individual_summary(doc_text, doc_name)
    
    # Store the individual summary in MongoDB
    summary_id = insert_chunks_in_db_for_efficacy_input_docs(
        chunks_data=summary,
        chunk_filename=None,
        efficacy_input_doc_tag="input_docs",
        is_summary=True,
        summary_level=2,
        chunk_reference_ids=[str(doc_id)]
    )
    
    print(f"Completed individual summary for {doc_name}")
    
    return {
        "document": doc_name,
        "individual_summary": summary,
        "doc_id": str(doc_id),
        "summary_id": str(summary_id)
    }

async def process_documents(docs_dir):
    """Process all documents in parallel and create a final summary."""
    # Get all document files and sort them alphabetically
    doc_files = sorted(
        glob.glob(os.path.join(docs_dir, "*.txt")),
        key=lambda x: os.path.basename(x)
    )
    
    # Filter out .DS_Store files
    doc_files = [f for f in doc_files if not os.path.basename(f).startswith('.')]
    
    # Process all documents in parallel
    tasks = [process_document(doc_file) for doc_file in doc_files]
    all_summaries = await asyncio.gather(*tasks)
    
    # Create final summary from all individual summaries
    print("\nCreating final summary...")
    individual_summaries = [summary["individual_summary"] for summary in all_summaries]
    final_summary = await create_final_summary(individual_summaries)
    
    # Store final summary in MongoDB
    final_summary_id = insert_chunks_in_db_for_efficacy_input_docs(
        chunks_data=final_summary,
        chunk_filename=None,
        efficacy_input_doc_tag="input_docs",
        is_summary=True,
        summary_level=1,
        summary_reference_ids=[summary["summary_id"] for summary in all_summaries]
    )

async def main():
    # docs_dir = "Input Docs/module 3/chunks from development reports/stability section chunks"
    docs_dir = "development reports/final_chunks_with_summaries"
    await process_documents(docs_dir)

if __name__ == "__main__":
    asyncio.run(main())
