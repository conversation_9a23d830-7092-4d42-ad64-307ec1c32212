{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import os\n", "from openai import OpenAI, AsyncOpenAI\n", "from dotenv import load_dotenv\n", "from pymongo import MongoClient\n", "from bson.objectid import ObjectId\n", "from typing import List, Dict, Any, Tuple\n", "import numpy as np\n", "import asyncio\n", "from transformers import AutoTokenizer\n", "import traceback  # Added import for stack trace information\n", "import time  # Added import for timestamp logging\n", "import inspect    # For frame inspection\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent\n", "from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings\n", "from pydantic_ai.providers.anthropic import AnthropicProvider\n", "from pydantic_ai.models.openai import OpenAIModel, OpenAIResponsesModelSettings, ReasoningEffort\n", "from pydantic_ai.providers.openai import OpenAIProvider\n", "\n", "load_dotenv()\n", "\n", "GENERATOR_MODEL_NAME = os.getenv(\"GENERATOR_MODEL_NAME\")\n", "GENERATOR_MODEL_BASE_URL = os.getenv(\"GENERATOR_MODEL_BASE_URL\")\n", "GENERATOR_MODEL_API_KEY = os.getenv(\"GENERATOR_MODEL_API_KEY\")\n", "\n", "CRITIQUE_MODEL_NAME = os.getenv(\"CRITIQUE_MODEL_NAME\")\n", "CRITIQUE_MODEL_BASE_URL = os.getenv(\"CRITIQUE_MODEL_BASE_URL\")\n", "CRITIQUE_MODEL_API_KEY = os.getenv(\"CRITIQUE_MODEL_API_KEY\")\n", "\n", "INCLUDE_JSON_OUTPUT_TAGS = False\n", "\n", "MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000\n", "MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000\n", "\n", "RELEVANCE_THRESHOLD = 8.0\n", "\n", "critique_client = AsyncOpenAI(\n", "    base_url=CRITIQUE_MODEL_BASE_URL, \n", "    api_key=CRITIQUE_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "generator_client = AsyncOpenAI(\n", "    base_url=GENERATOR_MODEL_BASE_URL, \n", "    api_key=GENERATOR_MODEL_API_KEY,     \n", "    max_retries=3,\n", "    timeout=10000000\n", ")\n", "\n", "critique_model = OpenAIModel(\n", "    model_name=CRITIQUE_MODEL_NAME,\n", "    provider=OpenAIProvider(\n", "        base_url=CRITIQUE_MODEL_BASE_URL,\n", "        api_key=CRITIQUE_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "generator_model = OpenAIModel(\n", "    model_name=GENERATOR_MODEL_NAME,\n", "    provider=OpenAIProvider(\n", "        base_url=GENERATOR_MODEL_BASE_URL,\n", "        api_key=GENERATOR_MODEL_API_KEY\n", "    )\n", ")\n", "\n", "print(\"GENERATOR_MODEL_NAME\", GENERATOR_MODEL_NAME)\n", "print(\"GENERATOR_MODEL_BASE_URL\", GENERATOR_MODEL_BASE_URL)\n", "print(\"GENERATOR_MODEL_API_KEY\", GENERATOR_MODEL_API_KEY)\n", "\n", "print(\"CRITIQUE_MODEL_NAME\", CRITIQUE_MODEL_NAME)\n", "print(\"CRITIQUE_MODEL_BASE_URL\", CRITIQUE_MODEL_BASE_URL)\n", "print(\"CRITIQUE_MODEL_API_KEY\", CRITIQUE_MODEL_API_KEY)\n", "\n", "# Pydantic schema for structured output\n", "class CheckpointList(BaseModel):\n", "    \"\"\"Schema for checkpoint list generation\"\"\"\n", "    checkpoints: List[str] = Field(\n", "        description=\"List of specific checkpoint questions that should be answered in summaries for this section\",\n", "        min_items=2\n", "    )\n", "\n", "class RelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for summary relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers the checkpoint questions\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of your score\",\n", "        default=\"\"\n", "    )\n", "\n", "class CheckpointRelevanceEvaluation(BaseModel):\n", "    \"\"\"Schema for individual checkpoint relevance evaluation\"\"\"\n", "    relevance_score: float = Field(\n", "        description=\"Relevance score from 0-10 indicating how well the summary answers this specific checkpoint question.\",\n", "        ge=0.0,\n", "        le=10.0\n", "    )\n", "    justification: str = Field(\n", "        description=\"Brief explanation of your score for this specific checkpoint\",\n", "        default=\"\"\n", "    )\n", "\n", "class CritiqueEvaluation(BaseModel):\n", "    \"\"\"Schema for NDA critique evaluation\"\"\"\n", "    overall_rating: float = Field(\n", "        description=\"Overall rating from 1-10 for the NDA section quality\",\n", "        ge=1.0,\n", "        le=10.0\n", "    )\n", "    key_strengths: List[str] = Field(\n", "        description=\"List of main strengths identified in the NDA section\",\n", "        default=[]\n", "    )\n", "    critical_issues: List[str] = Field(\n", "        description=\"List of critical issues that need to be addressed\",\n", "        default=[]\n", "    )\n", "    required_improvements: List[str] = Field(\n", "        description=\"List of specific improvements required\",\n", "        default=[]\n", "    )\n", "    additional_recommendations: List[str] = Field(\n", "        description=\"List of additional recommendations for enhancement\",\n", "        default=[]\n", "    )\n", "\n", "def get_json_output_tags():\n", "    if INCLUDE_JSON_OUTPUT_TAGS:\n", "        return \"Give your json in <text_output> json </text_output> tags.\"\n", "    else:\n", "        return \"\"\n", "\n", "async def check_if_llm_is_available(client: AsyncOpenAI, model: str):\n", "    try:\n", "        response = await client.chat.completions.create(model=model, messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "            {\"role\": \"user\", \"content\": \"Hello, world!\"}\n", "        ])\n", "        print(f\"LLM {model} is available, response: {response.choices[0].message.content}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error in checking if LLM {model} is available: {e}\")\n", "        return False\n", "\n", "def calculate_adaptive_threshold(scores, model_name=None, percentile=70):\n", "    \"\"\"\n", "    Calculate an adaptive threshold based on the distribution of scores for a given model.\n", "    This can help address the LLM-specific threshold problem.\n", "    \n", "    Args:\n", "        scores: List of relevance scores from previous evaluations\n", "        model_name: Name of the LLM model (for future model-specific calibration)\n", "        percentile: Percentile to use for threshold calculation (default: 70th percentile)\n", "        \n", "    Returns:\n", "        float: Calculated adaptive threshold\n", "    \"\"\"\n", "    if not scores:\n", "        return 5.0  # <PERSON><PERSON><PERSON> fallback threshold\n", "    \n", "    import numpy as np\n", "    return np.percentile(scores, percentile)\n", "\n", "def write_to_json(data, filename):\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, indent=2, ensure_ascii=False)\n", "    print(f\"JSON saved to {filename}\")\n", "\n", "def read_json(filename):\n", "    with open(filename, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "def get_mongodb_client():\n", "    \"\"\"Get MongoDB client connection.\"\"\"\n", "    return MongoClient(os.getenv(\"MONGO_DB_URL\"))\n", "\n", "def parse_json_response(response_text: str) -> Any:\n", "    \"\"\"\n", "    Parse a JSON response that may be wrapped in backticks.\n", "    \n", "    Args:\n", "        response_text: The response text to parse\n", "        \n", "    Returns:\n", "        Any: The parsed JSON object\n", "    \"\"\"\n", "    # Remove any markdown code block syntax\n", "    response_text = re.sub(r'```json\\n?', '', response_text)\n", "    response_text = re.sub(r'```\\n?', '', response_text)\n", "    response_text = response_text.strip()\n", "    \n", "    try:\n", "        # print(f\"parse_json_response, response_text: {json.loads(response_text)}\")\n", "        return json.loads(response_text)\n", "    except json.JSONDecodeError:\n", "        # If the response is not valid JSON, try to extract a list from the text\n", "        # Look for lines that start with numbers, bullets, or dashes\n", "        lines = re.findall(r'^[\\d\\-\\*\\.]+\\.?\\s*(.+)$', response_text, re.MULTILINE)\n", "        if lines:\n", "            return lines\n", "        # If no lines found, split by newlines and clean up\n", "        # print(f\"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\\n') if line.strip()]}\")\n", "        return [line.strip() for line in response_text.split('\\n') if line.strip()]\n", "    \n", "def calculate_number_of_tokens(text):\n", "    # Load tokenizer for Mistral model\n", "    tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-32B\")\n", "\n", "    def count_tokens(text):\n", "        tokens = tokenizer.encode(text, add_special_tokens=False)\n", "        return len(tokens)\n", "\n", "    token_count = count_tokens(text)\n", "    return token_count\n", "\n", "async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):\n", "    \"\"\"\n", "    Wrapper function that retries an async LLM API call when the response is empty.\n", "    \n", "    Args:\n", "        async_func: The async function to call (usually an LLM API call)\n", "        *args: Positional arguments to pass to async_func\n", "        max_retries: Maximum number of retry attempts (default: 3)\n", "        **kwargs: Keyword arguments to pass to async_func\n", "        \n", "    Returns:\n", "        The result of the async_func call, ensuring it's not empty\n", "        \n", "    Raises:\n", "        Exception: If max_retries is reached and the response is still empty\n", "    \"\"\"\n", "    # Create logs directory if it doesn't exist\n", "    log_dir = \"error_logs\"\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    # Extract the function name for logging purposes\n", "    func_name = async_func.__name__ if hasattr(async_func, \"__name__\") else \"unknown_function\"\n", "    \n", "    # Try to get the caller's name from the stack\n", "    try:\n", "        caller_frame = inspect.currentframe().f_back\n", "        caller_name = caller_frame.f_code.co_name if caller_frame else \"unknown_caller\"\n", "    except Exception:\n", "        caller_name = \"unknown_caller\"\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            result = await async_func(*args, **kwargs)\n", "            \n", "            # Check if result is None or an empty string or just whitespace\n", "            if result is None or (isinstance(result, str) and result.strip() == \"\"):\n", "                print(f\"Warning: Received empty response from LLM (attempt {attempt+1}/{max_retries}), retrying...\")\n", "                \n", "                # Get debug information to log\n", "                debug_info = {\n", "                    'error_type': 'empty_response',\n", "                    'function': func_name,\n", "                    'caller': caller_name,\n", "                    'attempt': attempt + 1,\n", "                    'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "                }\n", "                \n", "                # Extract prompt information based on different API patterns\n", "                # For the direct messages pattern in kwargs\n", "                if 'messages' in kwargs:\n", "                    debug_info['messages'] = kwargs['messages']\n", "                    \n", "                # For the pattern where the func is a closure with local make_api_call\n", "                # Try to get source code of the async_func to check for patterns\n", "                try:\n", "                    source = inspect.getsource(async_func)\n", "                    if \"chat.completions.create\" in source:\n", "                        debug_info['api_pattern'] = \"chat_completions_closure\"\n", "                except Exception:\n", "                    pass\n", "                \n", "                # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "                try:\n", "                    if caller_frame:\n", "                        caller_locals = caller_frame.f_locals\n", "                        # Capture common patterns in this codebase\n", "                        if 'system_prompt' in caller_locals:\n", "                            debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                        # If this is using the OpenAI client pattern, get the model too\n", "                        if 'model' in caller_locals:\n", "                            debug_info['model'] = caller_locals['model']\n", "                        # For the antropic calls\n", "                        if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                        elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                        elif 'M<PERSON>' in caller_locals:\n", "                            debug_info['model'] = caller_locals['MODEL']\n", "                except Exception as e:\n", "                    debug_info['frame_inspection_error'] = str(e)\n", "                \n", "                # Save the debug information\n", "                timestamp = int(time.time())\n", "                log_filename = f\"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "                \n", "                try:\n", "                    with open(log_filename, 'w', encoding='utf-8') as f:\n", "                        json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                    print(f\"Logged empty response details to {log_filename}\")\n", "                except Exception as log_error:\n", "                    print(f\"Failed to log empty response details: {str(log_error)}\")\n", "                \n", "                # Continue to the next retry attempt\n", "                continue\n", "                \n", "            # If we get here, we have a non-empty response\n", "            return result\n", "            \n", "        except Exception as e:\n", "            error_type = type(e).__name__\n", "            error_msg = str(e)\n", "            print(f\"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}\")\n", "            \n", "            # Get debug information to log\n", "            debug_info = {\n", "                'error_type': error_type,\n", "                'error_message': error_msg,\n", "                'function': func_name,\n", "                'caller': caller_name,\n", "                'attempt': attempt + 1,\n", "                'timestamp': time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                'stack_trace': traceback.format_exc()\n", "            }\n", "            \n", "            # Extract prompt information based on different API patterns\n", "            # For the direct messages pattern in kwargs\n", "            if 'messages' in kwargs:\n", "                debug_info['messages'] = kwargs['messages']\n", "                \n", "            # For the pattern where the func is a closure with local make_api_call\n", "            # Try to get source code of the async_func to check for patterns\n", "            try:\n", "                source = inspect.getsource(async_func)\n", "                if \"chat.completions.create\" in source:\n", "                    debug_info['api_pattern'] = \"chat_completions_closure\"\n", "            except Exception:\n", "                pass\n", "            \n", "            # Try to extract system_prompt and user_prompt from the caller's frame if available\n", "            try:\n", "                if caller_frame:\n", "                    caller_locals = caller_frame.f_locals\n", "                    # Capture common patterns in this codebase\n", "                    if 'system_prompt' in caller_locals:\n", "                        debug_info['system_prompt'] = caller_locals['system_prompt']\n", "                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:\n", "                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']\n", "                    # If this is using the OpenAI client pattern, get the model too\n", "                    if 'model' in caller_locals:\n", "                        debug_info['model'] = caller_locals['model']\n", "                    # For the antropic calls\n", "                    if 'CRITIQUE_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']\n", "                    elif 'GENERATOR_MODEL_NAME' in caller_locals:\n", "                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']\n", "                    elif 'M<PERSON>' in caller_locals:\n", "                        debug_info['model'] = caller_locals['MODEL']\n", "            except Exception as frame_error:\n", "                debug_info['frame_inspection_error'] = str(frame_error)\n", "            \n", "            # Save the debug information\n", "            timestamp = int(time.time())\n", "            log_filename = f\"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json\"\n", "            \n", "            try:\n", "                with open(log_filename, 'w', encoding='utf-8') as f:\n", "                    json.dump(debug_info, f, indent=2, ensure_ascii=False)\n", "                print(f\"Logged error details to {log_filename}\")\n", "            except Exception as log_error:\n", "                print(f\"Failed to log error details: {str(log_error)}\")\n", "            \n", "            if attempt == max_retries - 1:\n", "                # If we've exhausted all retries and still have an error\n", "                print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "                return None\n", "            \n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** attempt))\n", "    \n", "    # If we've exhausted all retries and still have an empty response\n", "    print(f\"Failed to get non-empty response after {max_retries} attempts\")\n", "    return None\n", "\n", "await check_if_llm_is_available(generator_client, GENERATOR_MODEL_NAME)\n", "await check_if_llm_is_available(critique_client, CRITIQUE_MODEL_NAME)\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ich_ectd_guideline_referenced_with_quality_guidelines = read_json(\"/Users/<USER>/projects/scalegen/MedNova/structured jsons/ich_ectd_guideline_referenced_with_quality_guidelines.json\")\n", "\n", "doc_to_work_on = ich_ectd_guideline_referenced_with_quality_guidelines[\"module3\"][\"3.2\"][\"3.2.P\"][\"3.2.P.8\"][\"3.2.P.8.1\"]\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoint questions from a single individual summary.\n", "    \n", "    Args:\n", "        summary_content: The content of the individual summary\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions generated from the individual summary\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive checklist of specific questions based on the provided summary for a section of ICH eCTD guidelines.\"\n", "            \n", "            prompt = f\"\"\"\n", "First, review the following information:\n", "\n", "Golden Instructions and Checkpoints:\n", "<golden_instructions_and_checkpoints>\n", "{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "</golden_instructions_and_checkpoints>\n", "\n", "Summary Content:\n", "<summary_content>\n", "{summary_content}\n", "</summary_content>\n", "\n", "Section Number:\n", "<section_number>\n", "{section_info.get('section', '')}\n", "</section_number>\n", "\n", "Section Title:\n", "<section_title>\n", "{section_info.get('title', '')}\n", "</section_title>\n", "\n", "Before creating the checklist, analyze the provided information and formulate your approach inside your thinking block using <checklist_preparation> tags:\n", "\n", "1. Summarize the key points from the summary content.\n", "2. Quote relevant phrases from the summary content and golden instructions.\n", "3. Identify the main topics and requirements mentioned in the golden instructions and checkpoints.\n", "4. List any specific technical or regulatory aspects that need to be addressed.\n", "5. Note any particular areas of focus (e.g., physical characteristics, chemical properties, manufacturing processes, quality control measures, stability data, packaging specifications, compatibility information).\n", "6. Brainstorm potential questions based on the quoted phrases and identified topics.\n", "7. Ensure alignment with ICH guidelines.\n", "8. Consider how to phrase each point as a specific, answerable question.\n", "\n", "Now, create a detailed checklist of specific questions based on your analysis. The checklist should:\n", "\n", "1. Include all key requirements and recommendations from the summary expressed as specific questions.\n", "2. Cover all aspects mentioned in the summary.\n", "3. Strictly adhere to the golden instructions and checkpoints provided.\n", "4. Be specific enough to clearly determine if an input document addresses each point.\n", "5. Format EACH checkpoint as a question that can be answered with yes/no or specific information.\n", "6. Focus on technical and regulatory content needed for NDA documentation.\n", "7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.\n", "8. Cover stability data, packaging specifications, and compatibility information where appropriate.\n", "9. Include questions about the validation and verification methods used.\n", "10. Use the material prepared in the thinking block to ensure comprehensive coverage.\n", "\n", "After creating the checklist, review it to ensure:\n", "- All points from the summary are covered\n", "- Questions align with the golden instructions and checkpoints\n", "- Each question is specific and answerable\n", "- The checklist is comprehensive and accurate\n", "\n", "Present your final checklist after this review. Your output should consist only of the checklist and should not duplicate or rehash any of the work you did in the thinking block.\n", "\"\"\"\n", "            \n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                    \"extra_body\": {\n", "                        # \"return_reasoning\": True\n", "                    }\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await checkpoint_list_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # print(f\"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Generating basic checkpoints from summary as fallback.\")\n", "                # Generate basic checkpoints as fallback\n", "                try:\n", "                    # Extract key sentences from summary as basic checkpoints\n", "                    sentences = [s.strip() + \"?\" for s in summary_content.split(\".\") if len(s.strip()) > 20]\n", "                    # Convert statements to questions where possible\n", "                    questions = []\n", "                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload\n", "                        if not sentence.endswith(\"?\"):\n", "                            questions.append(f\"Does the document address: {sentence}?\")\n", "                        else:\n", "                            questions.append(sentence)\n", "                    return questions\n", "                except Exception:\n", "                    print(\"Fallback checkpoint generation failed. Returning empty list.\")\n", "                    return []\n", "            # Add a small delay before retry with exponential backoff\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n", "async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Generate checkpoints incrementally from a list of individual summaries.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        list_of_individual_summary_contents: List of individual summary contents\n", "        \n", "    Returns:\n", "        List[str]: A comprehensive list of checkpoints generated from all individual summaries\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not list_of_individual_summary_contents:\n", "        return []\n", "        \n", "    # Handle single summary case\n", "    if len(list_of_individual_summary_contents) == 1:\n", "        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)\n", "    \n", "    # Maximum token limit for checkpoint list batches\n", "    \n", "    # Generate initial checkpoints from each summary in parallel\n", "    tasks = [_generate_checkpoints_from_one_summary(summary, section_info) for summary in list_of_individual_summary_contents]\n", "    current_checkpoints = await asyncio.gather(*tasks)\n", "    \n", "    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):\n", "        \"\"\"Helper function to combine multiple checkpoint lists while preserving all unique checkpoints.\"\"\"\n", "        total_lists = len(checkpoint_lists)\n", "        print(f\"\\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints\")\n", "        \n", "        # Calculate token count for all lists combined\n", "        combined_text = \"\\n\".join([\"\\n\".join(cp) for cp in checkpoint_lists])\n", "        total_tokens = calculate_number_of_tokens(combined_text)\n", "        print(f\"Total input tokens for checkpoint merging: {total_tokens}\")\n", "        \n", "        system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive merged list of checkpoint questions for a specific section of ICH eCTD guidelines.\"\n", "        \n", "        user_prompt = f\"\"\"\n", "First, review the following information:\n", "\n", "Section:\n", "<section_info>\n", "{section_info.get('section', '')}\n", "</section_info>\n", "\n", "Title:\n", "<section_title>\n", "{section_info.get('title', '')}\n", "</section_title>\n", "\n", "Golden Instructions and Checkpoints:\n", "<enhanced_instructions_and_checkpoints>\n", "{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "</enhanced_instructions_and_checkpoints>\n", "\n", "You will be processing {total_lists} lists of checkpoint questions. \n", "\n", "\"\"\"\n", "        \n", "        # Add each checkpoint list\n", "        for i, checkpoints in enumerate(checkpoint_lists, 1):\n", "            user_prompt += f\"\"\"\n", "Checkpoint List {i}:\n", "<checkpoints_{i}>\n", "{json.dumps(checkpoints, indent=2)}\n", "</checkpoints_{i}>\n", "\n", "\"\"\"\n", "\n", "        user_prompt += f\"\"\"\n", "Your goal is to merge these lists into a single comprehensive list while adhering to the following requirements:\n", "\n", "1. Preserve ALL unique questions from all lists\n", "2. Remove any duplicate or redundant questions\n", "3. Ensure the merged list is comprehensive and covers all aspects\n", "4. Maintain the specificity and clarity of each question\n", "5. Keep the question format consistent (e.g., \"Does the document mention...\")\n", "6. Ensure each question focuses on a single specific point\n", "7. Group related questions together when possible\n", "8. Strictly adhere to the referenced list of golden instructions and checkpoints\n", "9. Keep all questions focused on technical regulatory content for NDA documentation\n", "\n", "Before producing the final merged list, wrap your analysis inside <checkpoint_analysis> tags in your thinking block. In this analysis:\n", "1. Summarize the key points from the section_info, section_title, and enhanced_instructions_and_checkpoints.\n", "2. Analyze the first list of checkpoints, noting any patterns or themes.\n", "3. Plan how you will approach merging subsequent lists (even though we only have the first list now).\n", "4. Ensure all requirements are met and pay special attention to accuracy and best practices in regulatory documentation.\n", "\n", "After your analysis, provide the merged list of checkpoint questions. \n", "\n", "Your final output should consist only of the merged list of checkpoint questions and should not duplicate or rehash any of the work you did in the checkpoint analysis section.\n", "        \"\"\"\n", "        \n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "        checkpoint_list_agent = Agent(\n", "            model=generator_model,\n", "            system_prompt=system_prompt,\n", "            output_type=CheckpointList,\n", "            model_settings={\n", "                \"temperature\": 0.1,\n", "                \"extra_body\": {\n", "                    # \"return_reasoning\": True\n", "                }\n", "            },\n", "            retries=3\n", "        )\n", "\n", "        response = await checkpoint_list_agent.run(user_prompt)\n", "\n", "        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "        # print(f\"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "        merged_checkpoints = response.output.checkpoints\n", "        \n", "        # Validate the output\n", "        if len(merged_checkpoints) < 2:\n", "            raise ValueError(f\"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}\")\n", "        \n", "        print(f\"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints\")\n", "        \n", "        return merged_checkpoints\n", "    \n", "    # Iteratively merge checkpoint lists in optimal batches\n", "    while len(current_checkpoints) > 1:\n", "        # Create optimal batches based on token count and number of checkpoints\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        current_checkpoint_count = 0\n", "        \n", "        for checkpoint_list in current_checkpoints:\n", "            # Calculate tokens for this checkpoint list\n", "            checkpoint_text = \"\\n\".join(checkpoint_list)\n", "            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)\n", "            \n", "            # Check if this single checkpoint list exceeds the token limit\n", "            if checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT:\n", "                print(f\"Warning: Single checkpoint list has {checkpoint_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT}\")\n", "                # Force this large list to be merged with the current batch to avoid infinite loop\n", "                if current_batch:\n", "                    # Add to current batch despite exceeding limit\n", "                    current_batch.append(checkpoint_list)\n", "                    current_token_count += checkpoint_tokens\n", "                    current_checkpoint_count += len(checkpoint_list)\n", "                    print(f\"Forcing merge of oversized checkpoint list with current batch (total tokens: {current_token_count})\")\n", "                    # Finalize this batch\n", "                    batches.append(current_batch)\n", "                    current_batch = []\n", "                    current_token_count = 0\n", "                    current_checkpoint_count = 0\n", "                else:\n", "                    # If current_batch is empty, we need to pair this with the next checkpoint list\n", "                    # to avoid it being processed alone repeatedly\n", "                    current_batch = [checkpoint_list]\n", "                    current_token_count = checkpoint_tokens\n", "                    current_checkpoint_count = len(checkpoint_list)\n", "                    print(f\"Starting new batch with oversized checkpoint list ({checkpoint_tokens} tokens)\")\n", "            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch\n", "            elif (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):\n", "                batches.append(current_batch)\n", "                current_batch = [checkpoint_list]\n", "                current_token_count = checkpoint_tokens\n", "                current_checkpoint_count = len(checkpoint_list)\n", "            else:\n", "                current_batch.append(checkpoint_list)\n", "                current_token_count += checkpoint_tokens\n", "                current_checkpoint_count += len(checkpoint_list)\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        # Ensure no batch has only one checkpoint list to avoid infinite loop\n", "        # If we have a single-item batch, try to merge it with another batch\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, we need to handle it\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                # Merge with the last batch\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                # This is the only batch, which means we have only one checkpoint list left\n", "                # This should not happen in the while loop condition, but just in case\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        print(f\"Created {len(batches)} batches for checkpoint list merging\")\n", "        for i, batch in enumerate(batches):\n", "            total_checkpoints = sum(len(cp) for cp in batch)\n", "            total_tokens = sum(calculate_number_of_tokens(\"\\n\".join(cp)) for cp in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints and {total_tokens} total tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]\n", "        \n", "        # Wait for all merges to complete\n", "        current_checkpoints = await asyncio.gather(*tasks)\n", "    \n", "    print(f\"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}\")\n", "    return current_checkpoints[0]\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["async def generate_checkpoint_list(section, title, description, referenced_quality_guidelines, enhanced_instructions_and_checkpoints):\n", "    \"\"\"\n", "    Generate a checklist of questions that should be answered by summaries for this section.\n", "    \n", "    Args:\n", "        section: The section identifier (e.g., \"3.2.P.2.2.1\")\n", "        title: The title of the section\n", "        description: The description of the section\n", "        referenced_quality_guidelines: List of quality guidelines referenced\n", "        enhanced_instructions_and_checkpoints: A checkpoint list of golden instructions that must be strictly adhered.\n", "        \n", "    Returns:\n", "        List[str]: A list of checkpoint questions that should be answered in summaries\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            # Create a prompt for the LLM to generate a checklist\n", "            system_prompt = \"You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive list of checkpoint questions for a specific section of ICH eCTD guidelines.\"\n", "\n", "            prompt = f\"\"\"\n", "First, carefully review the following information:\n", "\n", "Section:\n", "<section>\n", "{section}\n", "</section>\n", "\n", "Title:\n", "<title>\n", "{title}\n", "</title>\n", "\n", "Description:\n", "<description>\n", "{description}\n", "</description>\n", "\n", "Enhanced Instructions and Checkpoints:\n", "<enhanced_instructions_and_checkpoints>\n", "{enhanced_instructions_and_checkpoints}\n", "</enhanced_instructions_and_checkpoints>\n", "\n", "Referenced Quality Guidelines:\n", "<referenced_quality_guidelines>\n", "{', '.join(referenced_quality_guidelines)}\n", "</referenced_quality_guidelines>\n", "\n", "Your goal is to create a detailed checklist of specific questions that should be answered in summaries for this section. Follow these guidelines:\n", "\n", "1. Include all key requirements and recommendations, expressed as specific questions.\n", "2. Cover all aspects mentioned in the title and description.\n", "3. Strictly adhere to the enhanced instructions and checkpoints provided.\n", "4. Address all points from the referenced quality guidelines.\n", "5. Ensure each question is specific enough to clearly determine if a summary addresses the point.\n", "6. Format each checkpoint as a question that can be answered with yes/no or specific information.\n", "7. Focus on technical and regulatory content needed for NDA documentation.\n", "8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.\n", "9. Cover stability data, packaging specifications, and compatibility information where appropriate.\n", "10. Include questions about the validation and verification methods used.\n", "11. Do not include references to sections/guidelines in the questions themselves.\n", "\n", "Before formulating the final list, conduct your analysis within <checkpoint_planning> tags inside your thinking block:\n", "a) Quote key phrases from each input section\n", "b) Break down main themes and requirements\n", "c) List and number potential areas for checkpoint questions\n", "\n", "Pay special attention to the enhanced instructions and checkpoints, as these are derived from actual NDA output and provide high-quality guidance.\n", "\n", "After your analysis, present the final list of checkpoint questions\n", "\n", "Each checkpoint should be a string in the list of checkpoints.\n", "\n", "Your final output should consist only of the checkpoint list and should not duplicate or rehash any of the work you did in the thinking block.\n", "\n", "Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.\n", "\"\"\"\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "            \n", "            checkpoint_list_agent = Agent(\n", "                model=generator_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointList,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                    \"extra_body\": {\n", "                        # \"return_reasoning\": True\n", "                    }\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await checkpoint_list_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            print(f\"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            checkpoints = response.output.checkpoints\n", "            \n", "            # Validate the output\n", "            if len(checkpoints) < 2:\n", "                raise ValueError(f\"Expected at least 2 checkpoints, got: {len(checkpoints)}\")\n", "            \n", "            return checkpoints\n", "            \n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            retry_count += 1\n", "            print(f\"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning empty list.\")\n", "                return []\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # If we somehow exit the loop without returning, return empty list\n", "    return []\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["async def evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint_question):\n", "    \"\"\"\n", "    Evaluate if a summary is relevant to a single checkpoint question.\n", "    \n", "    Args:\n", "        summary_content: The content of the summary to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoint_question: Single checkpoint question to evaluate against\n", "        \n", "    Returns:\n", "        CheckpointRelevanceEvaluation: Structured evaluation result\n", "    \"\"\"\n", "    max_retries = 3\n", "    retry_count = 0\n", "    \n", "    while retry_count < max_retries:\n", "        try:\n", "            system_prompt = \"\"\"You are an expert in pharmaceutical regulatory documentation and ICH guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate if a given summary addresses a specific checkpoint question for an ICH eCTD section.\"\"\"\n", "\n", "            prompt = f\"\"\"\n", "Evaluate the relevance of the provided summary to a specific checkpoint question for the following ICH eCTD section:\n", "\n", "Summary Content to Evaluate:\n", "<summary_content>\n", "{summary_content}\n", "</summary_content>\n", "\n", "Section Information:\n", "<section_info>\n", "{section_info.get('section', '')}\n", "</section_info>\n", "\n", "Title Information:\n", "<title_info>\n", "{section_info.get('title', '')}\n", "</title_info>\n", "\n", "Please evaluate the relevance of the summary to this specific checkpoint question:\n", "<checkpoint_question>\n", "{checkpoint_question}\n", "</checkpoint_question>\n", "\n", "Evaluation Process:\n", "1. Carefully read the checkpoint question to understand the specific information it's requesting.\n", "2. Analyze the summary content to identify information that directly or indirectly addresses the checkpoint question.\n", "3. Consider the depth, specificity, and quality of the information provided.\n", "4. Remember that the summary doesn't need to explicitly mention the question itself, only provide relevant information that would help answer it.\n", "\n", "Conduct your evaluation in the relevance_evaluation thinking block:\n", "\n", "1. Quote Extraction and Relevance Assessment:\n", "   - List specific parts of the summary that relate to the checkpoint question.\n", "   - For each quote, explain how well it addresses the question (completely, partially, or not at all).\n", "\n", "2. Gap Analysis:\n", "   - Identify any missing information that would be necessary to fully address the checkpoint question.\n", "   - Explain the significance of these gaps in relation to the question.\n", "\n", "3. Depth and Specificity Assessment:\n", "   - Evaluate the level of detail and precision in the relevant information.\n", "   - Consider if the information is sufficiently specific to the particular checkpoint question.\n", "\n", "4. Regulatory Perspective:\n", "   - Elaborate on how a regulatory professional could use this information to address the checkpoint.\n", "   - Consider any potential regulatory concerns or questions that might arise from the provided information.\n", "\n", "5. Scoring Justification:\n", "   - Based on your analysis, assign a preliminary score from 0 to 10 using the following criteria:\n", "     - 8-10: Clearly and adequately addresses the question with specific, relevant information\n", "     - 6-7: <PERSON><PERSON> addresses the question but lacks some detail or specificity\n", "     - 4-5: Provides limited information related to the question\n", "     - 1-3: Provides minimal or tangential information related to the question\n", "     - 0: Does not address the question at all\n", "   - Provide a detailed justification for your score, referencing specific elements of your analysis.\n", "\n", "IMPORTANT: Be strict and precise in your evaluation. Only if the checkpoint is answered comprehensively in the summary should you score it 8 or above, indicating adequate relevance.\n", "\n", "After your analysis, provide your final evaluation.\n", "\n", "Your final evaluation should be concise and should not duplicate the detailed work from your relevance evaluation thinking block.\n", "\"\"\"\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(prompt)\n", "\n", "            checkpoint_evaluation_agent = Agent(\n", "                model=critique_model,\n", "                system_prompt=system_prompt,\n", "                output_type=CheckpointRelevanceEvaluation,\n", "                model_settings={\n", "                    \"temperature\": 0.1,\n", "                    \"extra_body\": {\n", "                        # \"return_reasoning\": True\n", "                    }\n", "                },\n", "                retries=3\n", "            )\n", "\n", "            response = await checkpoint_evaluation_agent.run(prompt)\n", "\n", "            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "            # Uncomment for detailed token tracking\n", "            # print(f\"evaluate_individual_checkpoint_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "            return response.output\n", "            \n", "        except Exception as e:\n", "            retry_count += 1\n", "            print(f\"Error in evaluate_individual_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}\")\n", "            if retry_count >= max_retries:\n", "                print(f\"Maximum retries reached. Returning default evaluation.\")\n", "                # Return default evaluation\n", "                return CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Error occurred during evaluation\"\n", "                )\n", "            await asyncio.sleep(1 * (2 ** (retry_count - 1)))\n", "    \n", "    # Fallback return (shouldn't reach here)\n", "    return CheckpointRelevanceEvaluation(\n", "        relevance_score=0.0,\n", "        justification=\"Unexpected error\"\n", "    )\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def evaluate_summary_relevance(summary_content, section_info, checkpoints):\n", "    \"\"\"\n", "    Use an LLM to evaluate if a summary is relevant to a section by evaluating each checkpoint individually.\n", "    This approach provides more accurate relevance determination by:\n", "    1. Evaluating each checkpoint question individually in parallel\n", "    2. Using a clear 8+ threshold for relevance determination\n", "    3. Aggregating individual scores and applying threshold to final average\n", "    \n", "    Args:\n", "        summary_content: The content of the summary to evaluate\n", "        section_info: Dictionary containing section information\n", "        checkpoints: List of checkpoint questions that should be answered\n", "        \n", "    Returns:\n", "        Tuple[bool, float, int]: Tuple containing boolean relevance, average score, and number of relevant checkpoints\n", "    \"\"\"\n", "    # Handle empty checkpoint list\n", "    if not checkpoints:\n", "        print(\"No checkpoints provided for evaluation\")\n", "        return False, 0.0, 0\n", "    \n", "    try:\n", "        print(f\"Evaluating summary relevance using {len(checkpoints)} individual checkpoints\")\n", "        \n", "        # Create tasks for parallel evaluation of each checkpoint\n", "        tasks = [\n", "            evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint)\n", "            for checkpoint in checkpoints\n", "        ]\n", "        \n", "        # Execute all evaluations in parallel\n", "        checkpoint_evaluations = await asyncio.gather(*tasks, return_exceptions=True)\n", "        \n", "        # Process results and handle any exceptions\n", "        valid_evaluations = []\n", "        for i, evaluation in enumerate(checkpoint_evaluations):\n", "            if isinstance(evaluation, Exception):\n", "                print(f\"Error evaluating checkpoint {i+1}: {evaluation}\")\n", "                # Create default evaluation for failed checkpoint\n", "                valid_evaluations.append(CheckpointRelevanceEvaluation(\n", "                    relevance_score=0.0,\n", "                    justification=\"Evaluation failed\"\n", "                ))\n", "            else:\n", "                valid_evaluations.append(evaluation)\n", "        \n", "        # Calculate simple average of all individual scores\n", "        total_checkpoints = len(valid_evaluations)\n", "        total_score = sum(eval.relevance_score for eval in valid_evaluations)\n", "        average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0\n", "        number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)\n", "        \n", "        # Determine if summary is relevant based on average score\n", "        is_relevant = number_of_relevant_checkpoints >= 1\n", "\n", "        if not is_relevant:\n", "            print(\"Summary is not relevant, evaluations:\", \"\\n\".join([f\"Relevance Score: {eval.relevance_score:.2f}, Justification: {eval.justification}\" for eval in valid_evaluations]))\n", "\n", "        return is_relevant, average_score, number_of_relevant_checkpoints\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in evaluate_summary_relevance: {e}\")\n", "        traceback.print_exc()\n", "        return False, 0.0, 0\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["async def search_summaries_by_llm(section_info, quality_guidelines, summary_level=1):\n", "    \"\"\"\n", "    Search for summaries in MongoDB based on relevance to the section using an LLM.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        quality_guidelines: List of quality guidelines to search in (e.g., [\"Q6A\", \"Q6B\"])\n", "        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)\n", "        \n", "    Returns:\n", "        List[Dict]: List of relevant summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    collection = db['quality_docs_summaries']\n", "    \n", "    # Find all summaries matching the criteria\n", "    if len(quality_guidelines) > 0:\n", "        summaries = list(collection.find({\n", "            \"quality_guideline\": {\"$in\": quality_guidelines},\n", "            \"summary_level\": summary_level\n", "        }))\n", "    else:\n", "        summaries = list(collection.find({\n", "            \"summary_level\": summary_level\n", "        }))\n", "\n", "    # summaries = list(collection.find({\n", "    #     \"summary_level\": summary_level\n", "    # }))\n", "\n", "    print(f\"Found {len(summaries)} summaries for section {section_info.get('section', '')} quality guidelines {quality_guidelines} at level {summary_level}\")\n", "    \n", "    if not summaries:\n", "        print(f\"No summaries found for quality guidelines {quality_guidelines} at level {summary_level}\")\n", "        return []\n", "    \n", "    # Get checkpoints for the section\n", "    checkpoints = section_info.get(\"checkpoint_list\", [])\n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section_info.get('section', '')}\")\n", "        # Generate checkpoints if not already available\n", "        checkpoints = await generate_checkpoint_list(\n", "            section_info.get(\"section\", \"\"),\n", "            section_info.get(\"title\", \"\"),\n", "            section_info.get(\"description\", \"\"),\n", "            section_info.get(\"referenced_quality_guidelines\", []),\n", "            section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "        )\n", "    \n", "    # Evaluate each summary\n", "    evaluated_summaries = []\n", "    results = []\n", "    for summary in summaries:\n", "        is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints)\n", "        print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "        results.append(is_relevant)\n", "    \n", "    for summary, is_relevant in zip(summaries, results):\n", "        if is_relevant:\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "            summary[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "            evaluated_summaries.append(summary)\n", "        else:\n", "            print(f\"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "    \n", "    # No need to sort by relevance score since all relevant summaries have same score\n", "    # evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "    \n", "    client.close()\n", "    return evaluated_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def get_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the chunks associated with a summary.\n", "    \n", "    Args:\n", "        summary_id: The ID of the summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['quality_docs_summaries']\n", "    chunk_collection = db['quality_docs_chunks']\n", "    \n", "    # Get the summary\n", "    summary = summary_collection.find_one({\"_id\": ObjectId(summary_id)})\n", "    \n", "    if not summary or \"chunk_reference_ids\" not in summary:\n", "        print(f\"No chunk references found for summary {summary_id}\")\n", "        return []\n", "    \n", "    # Get the chunks\n", "    chunk_ids = summary[\"chunk_reference_ids\"]\n", "    chunks = list(chunk_collection.find({\"_id\": {\"$in\": chunk_ids}}))\n", "    \n", "    client.close()\n", "    return chunks\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the individual summaries associated with a final summary.\n", "    \n", "    Args:\n", "        final_summary_id: The ID of the final summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of individual summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['quality_docs_summaries']\n", "    \n", "    # Get the final summary\n", "    final_summary = summary_collection.find_one({\"_id\": ObjectId(final_summary_id)})\n", "    \n", "    if not final_summary or \"summary_reference_ids\" not in final_summary:\n", "        print(f\"No summary references found for final summary {final_summary_id}\")\n", "        return []\n", "    \n", "    # Get the individual summaries\n", "    summary_ids = final_summary[\"summary_reference_ids\"]\n", "    individual_summaries = list(summary_collection.find({\"_id\": {\"$in\": summary_ids}}))\n", "    \n", "    client.close()\n", "    return individual_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:\n", "    \"\"\"\n", "    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information (section, title, description, etc.)\n", "        \n", "    Returns:\n", "        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks\n", "    \"\"\"\n", "    # Extract section information\n", "    section = section_info.get(\"section\", \"\")\n", "    title = section_info.get(\"title\", \"\")\n", "    description = section_info.get(\"description\", \"\")\n", "    referenced_quality_guidelines = section_info.get(\"referenced_quality_guidelines\", [])\n", "    \n", "    # Step 1: Search for relevant final summaries (level 1)\n", "    final_summaries = await search_summaries_by_llm(\n", "        section_info=section_info,\n", "        quality_guidelines=referenced_quality_guidelines,\n", "        summary_level=1\n", "    )\n", "    \n", "    # Step 2: For each final summary, get relevant individual summaries (level 2)\n", "    individual_summaries = []\n", "    for final_summary in final_summaries:\n", "        summaries = get_summaries_for_final_summary(final_summary[\"_id\"])\n", "        \n", "        # Evaluate each individual summary\n", "        evaluated_summaries = []\n", "        results = []\n", "        for summary in summaries:\n", "            is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary[\"content\"], section_info, section_info.get(\"checkpoint_list\", []))\n", "            print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "            results.append(is_relevant)\n", "        \n", "        for summary, is_relevant in zip(summaries, results):\n", "            if is_relevant:\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "                summary[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "                evaluated_summaries.append(summary)\n", "            else:\n", "                print(f\"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "        \n", "        # No need to sort by relevance score since all relevant summaries have same score\n", "        # evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "        individual_summaries.extend(evaluated_summaries)\n", "    \n", "    # Step 3: For each individual summary, get relevant chunks\n", "    chunks = []\n", "    # BATCH_SIZE = 50\n", "    \n", "    # for i in range(0, len(individual_summaries), BATCH_SIZE):\n", "    #     batch = individual_summaries[i:i + BATCH_SIZE]\n", "    #     batch_tasks = []\n", "        \n", "    #     for individual_summary in batch:\n", "    #         summary_chunks = get_chunks_for_summary(individual_summary[\"_id\"])\n", "    #         # Create tasks for parallel evaluation of chunks\n", "    #         tasks = [evaluate_summary_relevance(chunk[\"content\"], section_info, section_info.get(\"quality_guideline_combined_summaries_checkpoint_list\", [])) for chunk in summary_chunks]\n", "    #         batch_tasks.extend(zip(summary_chunks, tasks))\n", "        \n", "    #     # Execute all tasks in parallel\n", "    #     results = await asyncio.gather(*[task for _, task in batch_tasks])\n", "        \n", "    #     # Process results\n", "    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):\n", "    #         if relevance_score >= get_relevance_threshold(0):\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    #             chunk[\"similarity_score\"] = relevance_score\n", "    #             chunks.append(chunk)\n", "    #         else:\n", "    #             print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}\")\n", "    \n", "    return final_summaries, individual_summaries, chunks\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["async def extract_relevant_chunks(json_obj):\n", "    \"\"\"\n", "    Recursively iterates through the nested JSON structure, extracts titles and descriptions,\n", "    generates checkpoint lists, and finds relevant summaries and chunks for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with added relevant summaries and chunks\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that needs processing\n", "            if (obj and \"title\" in obj and \"description\" in obj and \n", "                \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        # Generate checkpoint list for this section\n", "        section[\"checkpoint_list\"] = await generate_checkpoint_list(\n", "            section[\"section\"], \n", "            section[\"title\"], \n", "            section[\"description\"], \n", "            section.get(\"referenced_quality_guidelines\", []),\n", "            section.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "        )\n", "        \n", "        # Find relevant summaries and chunks for this section\n", "        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(\n", "            section\n", "        )\n", "\n", "        # Print summary of results\n", "        print(f\"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks\")\n", "        \n", "        # Add the results to the section\n", "        section[\"relevant_final_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"quality_guideline\": s[\"quality_guideline\"], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            } for s in final_summaries\n", "        ]\n", "        \n", "        section[\"relevant_individual_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"quality_guideline\": s[\"quality_guideline\"], \n", "                \"similarity_score\": s.get(\"similarity_score\", 0), \n", "                \"content\": s[\"content\"]\n", "            } for s in individual_summaries\n", "        ]\n", "\n", "        # Create a final summary from the relevant individual summaries\n", "        # if len(section[\"relevant_individual_summaries\"]) > 0:\n", "        #     final_summary = await create_combined_summary([summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]])\n", "        #     section[\"quality_guideline_combined_summary\"] = final_summary\n", "        # else:\n", "        #     # If no relevant summaries found, create a summary from section information\n", "        #     final_summary = await generate_summary_from_section_info(section)\n", "        #     section[\"quality_guideline_combined_summary\"] = final_summary\n", "\n", "        # Generate checkpoints incrementally from individual summaries\n", "        if len(section[\"relevant_individual_summaries\"]) > 0:\n", "            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(\n", "                section,\n", "                [summary[\"content\"] for summary in section[\"relevant_individual_summaries\"]]\n", "            )\n", "        else:\n", "            # If no relevant summaries found, generate checkpoints from section information\n", "            checkpoints = await generate_checkpoint_list(\n", "                section[\"section\"],\n", "                section[\"title\"],\n", "                section[\"description\"],\n", "                section.get(\"referenced_quality_guidelines\", []),\n", "                section.get(\"enhanced_instructions_and_checkpoints\", \"\")\n", "            )\n", "        \n", "        print(f\"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}\")\n", "        section[\"quality_guideline_combined_summaries_checkpoint_list\"] = checkpoints\n", "        \n", "        return section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_doc = await extract_relevant_chunks(doc_to_work_on)\n", "\n", "# Save the results to a JSON file\n", "write_to_json(processed_doc, \"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["processed_doc = read_json(\"processed_ectd_guidelines_with_relevant_chunks.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["async def search_input_docs_by_llm(section_info, input_doc_tag, summary_level=1):\n", "    \"\"\"\n", "    Search for input document summaries in MongoDB based on relevance to the section using an LLM.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        input_doc_tag: The input document tag to search for\n", "        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)\n", "        \n", "    Returns:\n", "        List[Dict]: List of relevant summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    collection = db['input_docs_summaries']\n", "    \n", "    # Find all summaries matching the criteria\n", "    if input_doc_tag:\n", "        summaries = list(collection.find({\n", "            \"input_doc_tag\": input_doc_tag,\n", "            \"summary_level\": summary_level\n", "        }))\n", "    else:\n", "        summaries = list(collection.find({\n", "            \"summary_level\": summary_level\n", "        }))\n", "    \n", "    print(f\"Found {len(summaries)} input document summaries for section {section_info.get('section', '')} with tag {input_doc_tag} at level {summary_level}\")\n", "    \n", "    if not summaries:\n", "        print(f\"No input document summaries found for tag {input_doc_tag} at level {summary_level}\")\n", "        return []\n", "    \n", "    # Get checkpoints for the section - prefer using checkpoints from quality_guideline_combined_summary\n", "    print(f\"Using {len(section_info.get('quality_guideline_combined_summaries_checkpoint_list', []))} quality_guideline_combined_summaries_checkpoint_list from quality_guideline_combined_summary for section {section_info.get('section', '')}\")\n", "    checkpoints = section_info.get(\"quality_guideline_combined_summaries_checkpoint_list\", [])\n", "\n", "    if not checkpoints:\n", "        print(f\"No checkpoints found for section {section_info.get('section', '')}\")\n", "        return []\n", "    \n", "    # Evaluate each summary\n", "    evaluated_summaries = []\n", "    results = []\n", "    for summary in summaries:\n", "        is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary[\"content\"], section_info, checkpoints)\n", "        print(f\"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "        results.append(is_relevant)\n", "    \n", "    for summary, is_relevant in zip(summaries, results):\n", "        if is_relevant:\n", "            print(f\"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "            summary[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "            evaluated_summaries.append(summary)\n", "        else:\n", "            print(f\"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "    \n", "    # No need to sort by relevance score since all relevant summaries have same score\n", "    # evaluated_summaries.sort(key=lambda x: x[\"similarity_score\"], reverse=True)\n", "    \n", "    client.close()\n", "    return evaluated_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def get_input_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the input document chunks associated with a summary.\n", "    \n", "    Args:\n", "        summary_id: The ID of the summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['input_docs_summaries']\n", "    chunk_collection = db['input_docs_chunks']\n", "    \n", "    # Get the summary\n", "    summary = summary_collection.find_one({\"_id\": ObjectId(summary_id)})\n", "    \n", "    if not summary or \"chunk_reference_ids\" not in summary:\n", "        print(f\"No chunk references found for summary {summary_id}\")\n", "        return []\n", "    \n", "    # Get the chunks\n", "    chunk_ids = summary[\"chunk_reference_ids\"]\n", "    chunks = list(chunk_collection.find({\"_id\": {\"$in\": chunk_ids}}))\n", "    \n", "    client.close()\n", "    return chunks\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def get_input_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Get the individual input document summaries associated with a final summary.\n", "    \n", "    Args:\n", "        final_summary_id: The ID of the final summary\n", "        \n", "    Returns:\n", "        List[Dict]: List of individual summaries with their content and metadata\n", "    \"\"\"\n", "    client = get_mongodb_client()\n", "    db = client['mednova']\n", "    summary_collection = db['input_docs_summaries']\n", "    \n", "    # Get the final summary\n", "    final_summary = summary_collection.find_one({\"_id\": ObjectId(final_summary_id)})\n", "    \n", "    if not final_summary or \"summary_reference_ids\" not in final_summary:\n", "        print(f\"No summary references found for final summary {final_summary_id}\")\n", "        return []\n", "    \n", "    # Get the individual summaries\n", "    summary_ids = final_summary[\"summary_reference_ids\"]\n", "    individual_summaries = list(summary_collection.find({\"_id\": {\"$in\": summary_ids}}))\n", "    \n", "    client.close()\n", "    return individual_summaries\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["async def filter_input_docs_by_section(section_info: Dict[str, Any], input_doc_tag: str = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:\n", "    \"\"\"\n", "    Filter input document summaries based on section information, going from summary level 1 to summary level 2 to chunks.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information (section, title, description, etc.)\n", "        input_doc_tag: The input document tag to search for\n", "        \n", "    Returns:\n", "        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks\n", "    \"\"\"\n", "    # Step 1: Search for relevant final summaries (level 1)\n", "    final_summaries = await search_input_docs_by_llm(\n", "        section_info=section_info,\n", "        input_doc_tag=input_doc_tag,\n", "        summary_level=1\n", "    )\n", "    \n", "    # Step 2: For each final summary, get relevant individual summaries (level 2)\n", "    individual_summaries = []\n", "    \n", "    for final_summary in final_summaries:\n", "        summaries = get_input_summaries_for_final_summary(final_summary[\"_id\"])\n", "        # Skip individual summary evaluation, directly check chunks instead\n", "        for summary in summaries:\n", "            individual_summaries.append(summary)\n", "    \n", "    # Step 3: For each individual summary, get relevant chunks\n", "    chunks = []\n", "\n", "    for individual_summary in individual_summaries:\n", "        summary_chunks = get_input_chunks_for_summary(individual_summary[\"_id\"])\n", "        \n", "        for chunk in summary_chunks:\n", "            is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(\n", "                chunk[\"content\"], \n", "                section_info, \n", "                section_info.get(\"quality_guideline_combined_summaries_checkpoint_list\", [])\n", "            )\n", "            print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints\")\n", "            \n", "            if is_relevant:\n", "                print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is RELEVANT\")\n", "                chunk[\"similarity_score\"] = 1.0  # Set to 1.0 since it's relevant\n", "                chunks.append(chunk)\n", "            else:\n", "                print(f\"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is NOT RELEVANT\")\n", "    \n", "    return final_summaries, individual_summaries, chunks\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["async def extract_relevant_input_docs(json_obj, input_doc_tag: str = None):\n", "    \"\"\"\n", "    Recursively iterates through the nested JSON structure, finds relevant quality guideline summaries,\n", "    creates a final summary, and then finds relevant input document summaries and chunks for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        input_doc_tag: The input document tag to search for\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with added relevant input document summaries and chunks\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that has relevant quality guideline summaries\n", "            if (obj and \"relevant_final_summaries\" in obj and \n", "                obj[\"relevant_final_summaries\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        print(f\"Processing section {section.get('section', 'unknown')} for input documents\")\n", "\n", "        # Find relevant input document summaries and chunks\n", "        input_final_summaries, input_individual_summaries, input_chunks = await filter_input_docs_by_section(\n", "            section,\n", "            input_doc_tag\n", "        )\n", "        \n", "        # Add the results to the section\n", "        section[\"relevant_input_final_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"input_doc_tag\": s[\"input_doc_tag\"], \n", "                \"similarity_score\": s[\"similarity_score\"], \n", "                \"content\": s[\"content\"]\n", "            } for s in input_final_summaries\n", "        ]\n", "        \n", "        section[\"relevant_input_individual_summaries\"] = [\n", "            {\n", "                \"_id\": str(s[\"_id\"]), \n", "                \"input_doc_tag\": s[\"input_doc_tag\"], \n", "                \"similarity_score\": s.get(\"similarity_score\", 0),\n", "                \"content\": s[\"content\"]\n", "            } for s in input_individual_summaries\n", "        ]\n", "        \n", "        section[\"relevant_input_chunks\"] = [\n", "            {\n", "                \"_id\": str(c[\"_id\"]), \n", "                \"input_doc_tag\": c[\"input_doc_tag\"], \n", "                \"chunk_filename\": c.get(\"chunk_filename\", \"\"), \n", "                \"similarity_score\": c.get(\"similarity_score\", 0), \n", "                \"content\": c.get(\"content\", \"\")\n", "            } for c in input_chunks\n", "        ]\n", "        \n", "        # Print summary of results\n", "        print(f\"Section {section.get('section', 'unknown')}: Found {len(input_final_summaries)} relevant input final summaries, {len(input_individual_summaries)} relevant input individual summaries, and {len(input_chunks)} relevant input chunks\")\n", "        \n", "        return section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process the document to find relevant input documents\n", "processed_doc_with_input_docs = await extract_relevant_input_docs(processed_doc, input_doc_tag=\"input_docs\")\n", "\n", "# Save the results to a JSON file\n", "write_to_json(processed_doc_with_input_docs, \"processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["processed_doc_with_input_docs = read_json(\"processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["async def create_incremental_plan(section_info, individual_summaries):\n", "    \"\"\"\n", "    Create a comprehensive NDA section plan by incrementally combining plans generated from individual quality guideline summaries.\n", "    Uses batch processing to efficiently process multiple summaries.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        individual_summaries: List of individual relevant quality guideline summaries\n", "        \n", "    Returns:\n", "        str: Comprehensive plan for NDA section content\n", "    \"\"\"\n", "    \n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def combine_plans(plans_to_combine):\n", "        \"\"\"Helper function to combine multiple plans while preserving all requirements.\"\"\"\n", "        total_plans = len(plans_to_combine)\n", "        print(f\"\\nCombining {total_plans} plans:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(plan) for plan in plans_to_combine)\n", "        print(f\"Total input tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple section plans into one comprehensive unified plan that will guide the generation of a complete, regulatory-compliant NDA section.\"\"\"\n", "\n", "        user_prompt = f\"\"\"You will be provided with <total_plans>{total_plans}</total_plans> section plans. Here is the content of each plan: \"\"\"\n", "        \n", "        # Add each plan with a header\n", "        for i, plan in enumerate(plans_to_combine, 1):\n", "            user_prompt += f\"\"\"\n", "\n", "Plan <plan_number_{i}>{i}</plan_number_{i}>:\n", "<plan_content_{i}>\n", "{plan}\n", "</plan_content_{i}>\n", "\"\"\"\n", "\n", "        user_prompt += f\"\"\"\n", "Your goal is to create a unified plan that incorporates all unique elements from the provided plans while adhering to the following critical requirements:\n", "\n", "1. Plan Comprehensiveness (HIGHEST PRIORITY):\n", "   - Preserve every requirement, element, and consideration from all plans\n", "   - Ensure no regulatory requirement is lost or diluted\n", "   - Maintain all technical specifications from all plans\n", "   - Include all validation and verification details\n", "   - Preserve all cross-references and dependencies\n", "   - Keep all acceptance criteria and thresholds\n", "   - Maintain all risk assessments and control strategies\n", "   - Include all testing requirements and methodologies\n", "   - Preserve all regulatory compliance elements\n", "\n", "2. ICH eCTD Compliance:\n", "   - Follow ICH eCTD v3.2.2 structure requirements\n", "   - Maintain proper section organization and hierarchy\n", "   - Include all required eCTD elements and subsections\n", "   - Preserve regulatory guideline references\n", "\n", "3. Integration Strategy:\n", "   - Create a logical, cohesive document structure\n", "   - Group related requirements together\n", "   - Eliminate redundancies without losing information\n", "   - Organize requirements in order of regulatory importance\n", "   - Ensure technical specifications are properly contextualized\n", "   - Maintain relationships between different requirements\n", "   - Create clear section and subsection organization\n", "   - Establish information hierarchy and flow\n", "\n", "4. Technical Depth:\n", "   - Specify all technical parameters to include\n", "   - Maintain detailed specifications for tests and analyses\n", "   - Preserve methodological details\n", "   - Include precise acceptance criteria\n", "   - Specify validation requirements\n", "   - Maintain detailed documentation standards\n", "   - Include specific technical cross-references\n", "\n", "5. Documentation Requirements:\n", "   - Specify all tables, figures, and data presentations needed\n", "   - Detail formatting requirements for technical information\n", "   - Include citations and reference standards\n", "   - Specify any appendices or supplementary materials\n", "   - Maintain documentation conventions from all plans\n", "   - Specify any required attachments or supporting documents\n", "\n", "6. Quality Assurance Elements:\n", "   - Include all quality control procedures\n", "   - Maintain quality assurance checkpoints\n", "   - Preserve change control processes\n", "   - Include batch release criteria\n", "   - Specify stability and shelf-life considerations\n", "   - Maintain monitoring and surveillance requirements\n", "\n", "7. Risk Management:\n", "   - Include all risk assessment requirements\n", "   - Maintain mitigation strategies\n", "   - Preserve safety considerations\n", "   - Include contingency requirements\n", "   - Specify monitoring and review processes\n", "   - Maintain risk-benefit analyses\n", "\n", "Before creating the final unified plan, complete the following steps inside <plan_analysis> tags in your thinking block:\n", "\n", "1. Summarize key points from each plan\n", "2. Identify unique elements and potential overlaps between plans\n", "3. Draft a structure for the unified plan\n", "4. Consider how to maintain ICH eCTD compliance in the unified plan\n", "\n", "After your analysis, create the unified plan using the following structure:\n", "\n", "<unified_plan>\n", "1. [Main Section Title]\n", "   1.1 [Subsection Title]\n", "       1.1.1 [Sub-subsection Title]\n", "             - [Requirement or specification]\n", "             - [Technical parameter]\n", "             - [Acceptance criteria]\n", "   1.2 [Subsection Title]\n", "       ...\n", "\n", "2. [Main Section Title]\n", "   ...\n", "\n", "[Include all necessary sections to cover all requirements]\n", "\n", "</unified_plan>\n", "\n", "Ensure that your unified plan:\n", "- Is structured hierarchically with numbered sections and subsections\n", "- Clearly delineates requirements, specifications, and expectations\n", "- Specifically notates required tables, figures, and data presentations\n", "- Precisely lists technical parameters and acceptance criteria\n", "- Clearly indicates required cross-references and dependencies\n", "- Is organized logically following ICH eCTD structure\n", "\n", "After creating the unified plan, verify it against this checklist:\n", "\n", "1. Does the combined plan include ALL requirements from all <total_plans>{total_plans}</total_plans> plans?\n", "2. Are ALL technical specifications preserved?\n", "3. Are ALL validation and verification details maintained?\n", "4. Are ALL cross-references and dependencies included?\n", "5. Are ALL regulatory compliance elements preserved?\n", "6. Does the plan follow ICH eCTD structure?\n", "7. Is the plan logically organized with clear sections and subsections?\n", "8. Are ALL required tables, figures, and data presentations specified?\n", "9. Are ALL acceptance criteria and thresholds preserved?\n", "10. Are ALL risk assessment and management requirements included?\n", "\n", "If any item on the checklist is not fully met, revise the unified plan accordingly before finalizing it.\n", "\n", "Your final output should consist only of the unified plan and should not duplicate or rehash any of the work you did in the plan analysis section.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2,\n", "                extra_body={\n", "                    # \"return_reasoning\": True\n", "                }\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"combine_plans token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "    \n", "    async def create_plan_from_one_summary(summary_content, section_info):\n", "        \"\"\"Generate a plan from a single summary content.\"\"\"\n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert specializing in ICH eCTD guidelines and NDA preparation. Your task is to create a detailed, structured plan for an NDA section based on the provided information.\"\"\"\n", "\n", "        user_prompt = f\"\"\"\n", "First, review the following section information and quality guideline summary:\n", "\n", "<section_info>\n", "Section: <section>{section_info.get('section', '')}</section>\n", "Title: <title>{section_info.get('title', '')}</title>\n", "Description: <description>{section_info.get('description', '')}</description>\n", "</section_info>\n", "\n", "Golden Instructions and Checkpoints:\n", "<instructions_and_checkpoints>\n", "{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "</instructions_and_checkpoints>\n", "\n", "Formatting Instructions:\n", "<formatting_instructions>\n", "{section_info.get(\"formatting_instructions\", \"\")}\n", "</formatting_instructions>\n", "\n", "Quality Guideline Summary:\n", "<quality_guideline_summary>\n", "{summary_content}\n", "</quality_guideline_summary>\n", "\n", "Your goal is to create a comprehensive blueprint for generating complete, regulatory-compliant content for this NDA section. Follow these critical requirements:\n", "\n", "1. Ensure full regulatory compliance with ICH eCTD v3.2.2 guidelines.\n", "2. Address all elements specified in the section description and reference text.\n", "3. Follow all provided instructions and checkpoints.\n", "4. Meet all requirements from the quality guideline summary.\n", "5. Adhere to all formatting instructions.\n", "\n", "Create a hierarchical, logical document structure with clearly numbered sections and subsections. Organize requirements by regulatory importance and group related items together.\n", "\n", "Specify all technical parameters, required tests, analyses, and methodologies. Include precise acceptance criteria, thresholds, and validation requirements.\n", "\n", "Detail all required tables, figures, data presentations, and formatting requirements for technical information. Specify any necessary appendices, supplementary materials, or supporting documents.\n", "\n", "Ensure comprehensive coverage of every point in the quality guideline summary, including all technical specifications, validation details, risk assessment and management requirements, and safety and efficacy considerations.\n", "\n", "Before providing your final plan, conduct your regulatory analysis inside <regulatory_analysis> tags in your thinking block. This will help guarantee a thorough and accurate plan. In your analysis:\n", "\n", "1. List out key regulatory requirements from the provided information.\n", "2. Break down technical parameters and methodologies mentioned.\n", "3. Outline potential risks and their mitigation strategies.\n", "4. List required tables, figures, and data presentations.\n", "\n", "Your output should follow this format:\n", "\n", "<regulatory_analysis>\n", "[Your step-by-step analysis of the requirements and approach to creating the plan]\n", "</regulatory_analysis>\n", "\n", "<plan>\n", "1. [Main Section Title]\n", "   1.1. [Subsection Title]\n", "        - [Specific requirement or content item]\n", "        - [Technical parameter or acceptance criterion]\n", "        - [Required table, figure, or data presentation]\n", "   1.2. [Subsection Title]\n", "        - ...\n", "\n", "2. [Main Section Title]\n", "   2.1. [Subsection Title]\n", "        - ...\n", "\n", "[Continue with all necessary sections and subsections]\n", "</plan>\n", "\n", "Ensure your plan is detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, addressing all regulatory requirements and technical details. Your final output should consist only of the plan and should not duplicate or rehash any of the work you did in the regulatory analysis.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2,\n", "                extra_body={\n", "                    # \"return_reasoning\": True\n", "                }\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"create_plan_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    # Handle empty input\n", "    if not individual_summaries:\n", "        return \"\"\n", "        \n", "    # Handle single summary case\n", "    if len(individual_summaries) == 1:\n", "        return await create_plan_from_one_summary(individual_summaries[0], section_info)\n", "    \n", "    # First generate initial plans from each summary\n", "    tasks = [create_plan_from_one_summary(summary, section_info) for summary in individual_summaries]\n", "    current_plans = await asyncio.gather(*tasks)\n", "    \n", "    # Process plans iteratively, combining batches in parallel\n", "    while len(current_plans) > 1:\n", "        # Create optimal batches based on token count\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for plan in current_plans:\n", "            plan_tokens = calculate_number_of_tokens(plan)\n", "            \n", "            # Check if this single plan exceeds the token limit\n", "            if plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                print(f\"Warning: Single plan has {plan_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}\")\n", "                # Force this large plan to be merged with the current batch to avoid infinite loop\n", "                if current_batch:\n", "                    # Add to current batch despite exceeding limit\n", "                    current_batch.append(plan)\n", "                    current_token_count += plan_tokens\n", "                    print(f\"Forcing merge of oversized plan with current batch (total tokens: {current_token_count})\")\n", "                    # Finalize this batch\n", "                    batches.append(current_batch)\n", "                    current_batch = []\n", "                    current_token_count = 0\n", "                else:\n", "                    # If current_batch is empty, we need to pair this with the next plan\n", "                    # to avoid it being processed alone repeatedly\n", "                    current_batch = [plan]\n", "                    current_token_count = plan_tokens\n", "                    print(f\"Starting new batch with oversized plan ({plan_tokens} tokens)\")\n", "            # If adding this plan would exceed the token limit, start a new batch\n", "            elif current_batch and current_token_count + plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                batches.append(current_batch)\n", "                current_batch = [plan]\n", "                current_token_count = plan_tokens\n", "            else:\n", "                current_batch.append(plan)\n", "                current_token_count += plan_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        # Ensure no batch has only one plan to avoid infinite loop\n", "        # If we have a single-item batch, try to merge it with another batch\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, we need to handle it\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                # Merge with the last batch\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                # This is the only batch, which means we have only one plan left\n", "                # This should not happen in the while loop condition, but just in case\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        print(f\"Created {len(batches)} batches for plan processing\")\n", "        for i, batch in enumerate(batches):\n", "            total_tokens = sum(calculate_number_of_tokens(p) for p in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} plans with {total_tokens} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_plans(batch) for batch in batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        current_plans = await asyncio.gather(*tasks)\n", "\n", "    print(\"create_incremental_plan tokens: \", calculate_number_of_tokens(current_plans[0]))\n", "    \n", "    return current_plans[0]\n", "\n", "async def create_incremental_content(plan, section_info, input_chunks, critique_feedback=None):\n", "    \"\"\"\n", "    Generate content incrementally using the plan created in Stage 1 and input document chunks.\n", "    Uses batch processing to efficiently process input chunks and combine content.\n", "    \n", "    Args:\n", "        plan: The comprehensive plan for the NDA section\n", "        section_info: Dictionary containing section information\n", "        input_chunks: List of input document chunks to use for content generation\n", "        critique_feedback: Optional critique feedback from previous iterations to address missing content\n", "        \n", "    Returns:\n", "        str: Generated NDA section content\n", "    \"\"\"\n", "    \n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def generate_content_from_chunks(plan, chunks, section_info, critique_feedback=None):\n", "        \"\"\"Generate content from a batch of input chunks using the plan.\"\"\"\n", "        total_chunks = len(chunks)\n", "        print(f\"\\nGenerating content from {total_chunks} chunks:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(chunk.get('content', '')) for chunk in chunks)\n", "        print(f\"Total input chunk tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are an expert pharmaceutical regulatory documentation specialist tasked with generating high-quality, detailed, and regulatory-compliant content for a New Drug Application (NDA) section. Your expertise includes deep knowledge of ICH eCTD guidelines and NDA preparation. Your goal is to create content that is indistinguishable from a professionally written NDA submission.\"\"\"\n", "\n", "        # Prepare input chunks text\n", "        input_chunks_text = \"\\n\\n\".join([f\"Input Chunk {i+1}:<input_chunk_{i+1}>{chunk.get('content', '')}</input_chunk_{i+1}>\" for i, chunk in enumerate(chunks)])\n", "\n", "        # Prepare critique feedback section if available\n", "        critique_section = \"\"\n", "        if critique_feedback:\n", "            critique_section = f\"\"\"\n", "<critique_feedback>\n", "Overall Rating: {critique_feedback.get('overall_rating', 'N/A')}/10\n", "\n", "Critical Issues to Address:\n", "<critical_issues>\n", "{chr(10).join(f\"- {issue}\" for issue in critique_feedback.get('critical_issues', []))}\n", "</critical_issues>\n", "\n", "Required Improvements to Implement:\n", "<required_improvements>\n", "{chr(10).join(f\"- {improvement}\" for improvement in critique_feedback.get('required_improvements', []))}\n", "</required_improvements>\n", "\n", "Additional Recommendations to Consider:\n", "<additional_recommendations>\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in critique_feedback.get('additional_recommendations', []))}\n", "</additional_recommendations>\n", "</critique_feedback>\n", "\"\"\"\n", "\n", "        user_prompt = f\"\"\"\n", "Here is the essential information for the section you will be working on:\n", "\n", "<section_info>\n", "Section: <section>{section_info.get('section', '')}</section>\n", "Title: <title>{section_info.get('title', '')}</title>\n", "Description: <description>{section_info.get('description', '')}</description>\n", "Enhanced Instructions and Checkpoints: <instructions_and_checkpoints>{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}</instructions_and_checkpoints>\n", "Formatting Instructions: <formatting_instructions>{section_info.get(\"formatting_instructions\", \"\")}</formatting_instructions>\n", "</section_info>\n", "\n", "The following detailed plan will serve as your primary guide for content generation. Use this plan to filter and organize the information from the input chunks:\n", "\n", "<detailed_plan>\n", "{plan}\n", "</detailed_plan>\n", "        \n", "To improve the quality of your output, consider the following critique feedback from a previous version. Use this feedback to enhance your content, but do not reference it directly in your output:\n", "\n", "{critique_section}\n", "\n", "You will be provided with input document chunks. Each chunk will be presented in the following format:\n", "\n", "<input_chunk>\n", "{input_chunks_text}\n", "</input_chunk>\n", "\n", "CRITICAL INSTRUCTIONS:\n", "\n", "1. Plan-Guided Content Generation:\n", "   - Use the detailed plan as your primary filter for content selection.\n", "   - Only include information from input chunks that directly addresses elements in the plan.\n", "   - Discard any chunk content that does not align with specific plan requirements.\n", "   - For chunks with mixed content, extract only the portions relevant to the plan.\n", "   - Prioritize chunks containing technical specifications, validation data, regulatory compliance information, test results, and analytical methods mentioned in the plan.\n", "\n", "2. Regulatory Compliance:\n", "   - Adhere strictly to ICH eCTD v3.2.2 standards.\n", "   - Address all requirements specified in the plan.\n", "   - Follow all formatting instructions provided.\n", "   - Ensure compliance with all enhanced instructions and checkpoints.\n", "\n", "3. Table Tag Handling:\n", "   - Preserve all table tags exactly as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`).\n", "   - Do not modify, rename, or change table tags in any way.\n", "   - Maintain the exact position and order of table tags as they appear in the input chunks.\n", "   - Include table tags in the exact location where the table should appear in your content.\n", "   - Use table summary information to write brief contextual statements about tables if needed, but always preserve the tags.\n", "\n", "4. Figure Preservation:\n", "   - Maintain exact figure numbers and captions as they appear in source documents.\n", "   - Include all figures from the input source documents that are relevant to the plan.\n", "   - Do not modify the content of any figures.\n", "\n", "5. Content Quality:\n", "   - Generate comprehensive, technically precise content.\n", "   - Include all required technical details, parameters, and specifications as specified in the plan.\n", "   - Provide detailed explanations and justifications for plan-required elements.\n", "   - Use formal, professional regulatory language appropriate for an NDA submission.\n", "   - Ensure content is scientifically accurate and technically sound.\n", "   - Focus on depth rather than breadth - cover plan requirements thoroughly.\n", "\n", "6. Documentation Standards:\n", "   - Use clear, precise, and professional language.\n", "   - Format content according to regulatory standards.\n", "   - Provide detailed captions and explanations for figures.\n", "   - Use consistent terminology throughout.\n", "   - Follow professional technical writing standards.\n", "   - Maintain logical flow and organization.\n", "\n", "7. Narrative Style:\n", "   - Write in a cohesive, flowing narrative style.\n", "   - Connect technical information with clear transitions.\n", "   - Provide context and background for technical details as required by the plan.\n", "   - Explain the significance of findings and data relevant to plan requirements.\n", "   - Ensure information is presented in a logical sequence following the plan structure.\n", "\n", "8. Output Format:\n", "   - Use markdown format without backticks or code blocks.\n", "   - Do not include section headings.\n", "   - Preserve table tags exactly as they appear in input chunks.\n", "   - Maintain exact figure numbers and captions.\n", "\n", "9. Prohibited Content:\n", "   - Do not include any meta-commentary about the generation process or compliance verification.\n", "   - Do not include verification checklists or statements about compliance with plans.\n", "   - Do not include process-related commentary or formatting instructions.\n", "   - Generate only the actual NDA content that would appear in the final submission.\n", "\n", "Before generating the final content, please analyze the task and outline your approach in <task_breakdown> tags inside your thinking block. This breakdown should be thorough and can be quite long. Include the following steps:\n", "\n", "1. Review the section information and detailed plan.\n", "2. Identify key requirements and priorities from the plan.\n", "3. Evaluate the critique feedback and determine how to address critical issues and required improvements.\n", "4. Outline the main topics to be covered based on the plan.\n", "5. Review input chunks and map relevant information to the outlined topics.\n", "   - For each topic, list relevant chunk numbers and key points.\n", "   - Note any technical specifications, validation data, or analytical methods that align with the plan.\n", "6. Plan the logical flow of information to ensure a cohesive narrative.\n", "7. Identify areas where technical depth needs to be emphasized.\n", "8. Note any table tags and figure references that need to be preserved.\n", "9. Double-check alignment with regulatory requirements and ICH eCTD v3.2.2 standards.\n", "10. Outline a strategy for maintaining consistent terminology and professional language throughout.\n", "11. Plan how to incorporate critique feedback into the content generation process.\n", "12. Identify any potential gaps in information and how to address them within the constraints of the available input.\n", "\n", "Now, please generate the detailed, regulatory-compliant content for this NDA section based on the provided plan, input chunks, and the analysis you've just completed. Remember to adhere strictly to all instructions and requirements outlined above. Your final output should consist only of the NDA content and should not duplicate or rehash any of the work you did in the task breakdown.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.3,\n", "                extra_body={\n", "                    # \"return_reasoning\": True\n", "                }\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"generate_content_from_chunks token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    async def combine_content_sections(content_sections):\n", "        \"\"\"Combine multiple content sections while ensuring proper flow and consistency.\"\"\"\n", "        total_sections = len(content_sections)\n", "        print(f\"\\nCombining {total_sections} content sections:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(content) for content in content_sections)\n", "        print(f\"Total content section tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine sections of NDA content into a cohesive, unified document that maintains regulatory compliance, technical accuracy, and narrative flow.\"\"\"\n", "\n", "        user_prompt = f\"\"\"Here are the content sections you need to integrate:\n", "<content_sections>\n", "\"\"\"\n", "        \n", "        # Add each content section with a header\n", "        for i, content in enumerate(content_sections, 1):\n", "            user_prompt += f\"\"\"\n", "CONTENT SECTION {i}:\n", "<content_section_{i}>\n", "{content}\n", "</content_section_{i}>\n", "\"\"\"\n", "\n", "        user_prompt += f\"\"\"\n", "</content_sections>\n", "\"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "You will be combining {total_sections} sections in total.\n", "\n", "Before you begin the integration process, please conduct a thorough analysis of the content and your approach. Conduct this analysis inside <integration_planning> tags in your thinking block.\n", "\n", "In your integration planning:\n", "1. List out each section's title and key topics.\n", "2. Identify and list all key technical information from each section.\n", "3. Note all table tags and figure references.\n", "4. Identify potential areas of overlap or contradiction between sections.\n", "5. Create a rough outline of the integrated document, ensuring a logical flow of information.\n", "6. Plan the structure of your integrated document, ensuring a logical flow of information.\n", "7. Outline your strategy for maintaining regulatory compliance and technical accuracy.\n", "8. Describe how you will ensure narrative coherence across the combined sections.\n", "9. Explain your approach to eliminating redundancies without losing information.\n", "10. Detail your method for harmonizing terminology and phrasing.\n", "11. Describe how you will double-check your work against the critical requirements.\n", "\n", "After your analysis, proceed with combining the content sections. Your output must adhere to the following critical requirements:\n", "\n", "1. Content Preservation (HIGHEST PRIORITY):\n", "   - Preserve ALL technical information from all content sections.\n", "   - Ensure NO technical details, data, or regulatory elements are lost.\n", "   - Maintain ALL parameters, specifications, measurements, methodologies, approaches, validation details, risk assessments, control strategies, and compliance justifications.\n", "\n", "2. <PERSON>-Commentary:\n", "   - Generate ONLY the actual NDA content that would appear in the final submission.\n", "   - DO NOT include any commentary about the combination process, verification checklists, or statements about compliance.\n", "\n", "3. Table and Figure Handling:\n", "   - PRESERVE ALL table tags EXACTLY as they appear (e.g., `<TABLE_TAG_table_1>`). DO NOT modify these in any way.\n", "   - MAINTAIN EXACT figure numbers and captions. DO NOT rename or modify any figures.\n", "   - Include ALL figures and table tags in their original positions.\n", "\n", "4. Integration Quality and Narrative Coherence:\n", "   - Create seamless transitions between combined content.\n", "   - Eliminate redundancies without losing information.\n", "   - Harmonize terminology and phrasing.\n", "   - Ensure the document reads as a single, cohesive narrative with logical progression.\n", "\n", "5. Regulatory Standards and Technical Accuracy:\n", "   - Maintain ICH eCTD v3.2.2 compliance throughout.\n", "   - Ensure all combined technical information is accurate and consistent.\n", "   - Resolve any contradictions in technical specifications.\n", "   - Preserve scientific accuracy in all explanations.\n", "\n", "Output Format:\n", "- Professional, regulatory-compliant content with seamless integration.\n", "- Well-structured paragraphs with logical flow.\n", "- Consistent formatting throughout.\n", "- No section headings.\n", "- Markdown format without backticks or code blocks.\n", "- No other formatting (XML, HTML, plaintext) should be included.\n", "\n", "The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach.\n", "\n", "After you've completed the integration, use <final_review> tags to conduct a final review, ensuring all critical requirements have been met.\n", "\n", "Your final output should consist only of the integrated content and should not duplicate or rehash any of the work you did in the integration planning section.\"\"\"\n", "\n", "        # Wrap the API call with retry_on_empty_response\n", "        async def make_api_call():\n", "            response = await generator_client.chat.completions.create(\n", "                model=GENERATOR_MODEL_NAME,\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": system_prompt},\n", "                    {\"role\": \"user\", \"content\": user_prompt}\n", "                ],\n", "                temperature=0.2,\n", "                extra_body={\n", "                    # \"return_reasoning\": True\n", "                }\n", "            )\n", "\n", "            system_tokens = calculate_number_of_tokens(system_prompt)\n", "            user_tokens = calculate_number_of_tokens(user_prompt)\n", "            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "            total_tokens = system_tokens + user_tokens + output_tokens\n", "            print(f\"combine_content_sections token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "            \n", "            return response.choices[0].message.content\n", "\n", "        # Use the retry wrapper to handle empty responses\n", "        return await retry_on_empty_response(make_api_call)\n", "\n", "    # Handle empty input\n", "    if not input_chunks:\n", "        return \"\"\n", "\n", "    # Process input chunks in batches based on token count\n", "    batch_contents = []\n", "    \n", "    # Create optimal batches based on token count\n", "    batches = []\n", "    current_batch = []\n", "    current_token_count = 0\n", "    \n", "    for chunk in input_chunks:\n", "        chunk_tokens = calculate_number_of_tokens(chunk.get('content', ''))\n", "        \n", "        # Check if this single chunk exceeds the token limit\n", "        if chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "            print(f\"Warning: Single chunk has {chunk_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}\")\n", "            # Force this large chunk to be merged with the current batch to avoid infinite loop\n", "            if current_batch:\n", "                # Add to current batch despite exceeding limit\n", "                current_batch.append(chunk)\n", "                current_token_count += chunk_tokens\n", "                print(f\"Forcing merge of oversized chunk with current batch (total tokens: {current_token_count})\")\n", "                # Finalize this batch\n", "                batches.append(current_batch)\n", "                current_batch = []\n", "                current_token_count = 0\n", "            else:\n", "                # If current_batch is empty, we need to pair this with the next chunk\n", "                # to avoid it being processed alone repeatedly\n", "                current_batch = [chunk]\n", "                current_token_count = chunk_tokens\n", "                print(f\"Starting new batch with oversized chunk ({chunk_tokens} tokens)\")\n", "        # If adding this chunk would exceed the token limit, start a new batch\n", "        elif current_batch and current_token_count + chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "            batches.append(current_batch)\n", "            current_batch = [chunk]\n", "            current_token_count = chunk_tokens\n", "        else:\n", "            current_batch.append(chunk)\n", "            current_token_count += chunk_tokens\n", "    \n", "    # Add the last batch if it's not empty\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "    \n", "    # Ensure no batch has only one chunk to avoid infinite loop\n", "    # If we have a single-item batch, try to merge it with another batch\n", "    final_batches = []\n", "    single_item_batch = None\n", "    \n", "    for batch in batches:\n", "        if len(batch) == 1 and single_item_batch is None:\n", "            single_item_batch = batch\n", "        elif len(batch) == 1 and single_item_batch is not None:\n", "            # Merge two single-item batches\n", "            merged_batch = single_item_batch + batch\n", "            final_batches.append(merged_batch)\n", "            single_item_batch = None\n", "        else:\n", "            if single_item_batch is not None:\n", "                # Merge the single-item batch with this multi-item batch\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                final_batches.append(batch)\n", "    \n", "    # If we still have a single-item batch left, we need to handle it\n", "    if single_item_batch is not None:\n", "        if final_batches:\n", "            # Merge with the last batch\n", "            final_batches[-1].extend(single_item_batch)\n", "        else:\n", "            # This is the only batch, which means we have only one chunk left\n", "            # This should not happen in normal operation, but just in case\n", "            final_batches.append(single_item_batch)\n", "    \n", "    batches = final_batches\n", "    \n", "    print(f\"Created {len(batches)} batches for chunk processing\")\n", "    for i, batch in enumerate(batches):\n", "        print(f\"Batch {i+1} contains {len(batch)} chunks with {sum(calculate_number_of_tokens(c.get('content', '')) for c in batch)} tokens\")\n", "    \n", "        # Process all batches in parallel\n", "    tasks = [generate_content_from_chunks(plan, batch, section_info, critique_feedback) for batch in batches]\n", "    batch_contents = await asyncio.gather(*tasks)\n", "    \n", "    # If only one batch, return it directly\n", "    if len(batch_contents) == 1:\n", "        return batch_contents[0]\n", "    \n", "    # Combine batch contents iteratively\n", "    while len(batch_contents) > 1:\n", "        # Create optimal batches based on token count\n", "        content_batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for content in batch_contents:\n", "            content_tokens = calculate_number_of_tokens(content)\n", "            \n", "            # Check if this single content exceeds the token limit\n", "            if content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                print(f\"Warning: Single content has {content_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}\")\n", "                # Force this large content to be merged with the current batch to avoid infinite loop\n", "                if current_batch:\n", "                    # Add to current batch despite exceeding limit\n", "                    current_batch.append(content)\n", "                    current_token_count += content_tokens\n", "                    print(f\"Forcing merge of oversized content with current batch (total tokens: {current_token_count})\")\n", "                    # Finalize this batch\n", "                    content_batches.append(current_batch)\n", "                    current_batch = []\n", "                    current_token_count = 0\n", "                else:\n", "                    # If current_batch is empty, we need to pair this with the next content\n", "                    # to avoid it being processed alone repeatedly\n", "                    current_batch = [content]\n", "                    current_token_count = content_tokens\n", "                    print(f\"Starting new batch with oversized content ({content_tokens} tokens)\")\n", "            # If adding this content would exceed the token limit, start a new batch\n", "            elif current_batch and current_token_count + content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                content_batches.append(current_batch)\n", "                current_batch = [content]\n", "                current_token_count = content_tokens\n", "            else:\n", "                current_batch.append(content)\n", "                current_token_count += content_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            content_batches.append(current_batch)\n", "        \n", "        # Ensure no batch has only one content to avoid infinite loop\n", "        # If we have a single-item batch, try to merge it with another batch\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in content_batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, we need to handle it\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                # Merge with the last batch\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                # This is the only batch, which means we have only one content left\n", "                # This should not happen in the while loop condition, but just in case\n", "                final_batches.append(single_item_batch)\n", "        \n", "        content_batches = final_batches\n", "        \n", "        print(f\"Created {len(content_batches)} batches for content combining\")\n", "        for i, batch in enumerate(content_batches):\n", "            total_tokens = sum(calculate_number_of_tokens(c) for c in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} content sections with {total_tokens} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_content_sections(batch) for batch in content_batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        batch_contents = await asyncio.gather(*tasks)\n", "\n", "    print(\"create_incremental_content tokens: \", calculate_number_of_tokens(batch_contents[0]))\n", "    \n", "    return batch_contents[0]\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["async def refine_content(draft_content, section_info, evaluation_feedback=None):\n", "    \"\"\"\n", "    Refine the draft content based on evaluation feedback and ensure it meets all requirements.\n", "    Can use both the original draft content and previous output (if available) to make improvements.\n", "    \n", "    Args:\n", "        draft_content: The draft content generated in Stage 2\n", "        section_info: Dictionary containing section information\n", "        evaluation_feedback: Evaluation feedback JSON (optional)\n", "        \n", "    Returns:\n", "        str: Refined NDA section content\n", "    \"\"\"\n", "    \n", "    system_prompt = \"\"\"You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to refine and perfect draft NDA section content based on evaluation feedback and ensure it fully meets all regulatory requirements.\"\"\"\n", "\n", "    # Prepare evaluation feedback text\n", "    feedback_text = \"\"\n", "    previous_output_text = \"\"\n", "    \n", "    if evaluation_feedback:\n", "        # Check if we have previous output in the enhanced feedback\n", "        if \"previous_output\" in evaluation_feedback:\n", "            previous_output_text = f\"\"\"{evaluation_feedback.get('previous_output', '')}\"\"\"\n", "            \n", "        # First try to use the structured feedback if we have a valid rating and sufficient feedback\n", "        has_valid_structured_feedback = (\n", "            evaluation_feedback.get(\"overall_rating\") is not None and\n", "            (evaluation_feedback.get(\"key_strengths\") or\n", "             evaluation_feedback.get(\"critical_issues\") or\n", "             evaluation_feedback.get(\"required_improvements\"))\n", "        )\n", "        \n", "        if has_valid_structured_feedback:\n", "            # Use structured feedback\n", "            feedback_text = f\"\"\"\n", "<evaluation_feedback>\n", "Overall Rating: <overall_rating>{evaluation_feedback.get('overall_rating', 'N/A')}/10</overall_rating>\n", "\n", "Key Strengths:\n", "<key_strengths>\n", "{chr(10).join(f\"- {strength}\" for strength in evaluation_feedback.get('key_strengths', []))}\n", "</key_strengths>\n", "\n", "Critical Issues to Address:\n", "<critical_issues>\n", "{chr(10).join(f\"- {issue}\" for issue in evaluation_feedback.get('critical_issues', []))}\n", "</critical_issues>\n", "\n", "Required Improvements:\n", "<required_improvements>\n", "{chr(10).join(f\"- {improvement}\" for improvement in evaluation_feedback.get('required_improvements', []))}\n", "</required_improvements>\n", "\n", "Additional Recommendations:\n", "<additional_recommendations>\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in evaluation_feedback.get('additional_recommendations', []))}\n", "</additional_recommendations>\n", "</evaluation_feedback>\n", "\"\"\"\n", "    \n", "    user_prompt = f\"\"\"First, review the following information carefully:\n", "    \n", "1. Draft Content to Refine:\n", "<draft_content>\n", "{draft_content}\n", "</draft_content>\n", "\n", "2. Section Information:\n", "<section_info>\n", "Section: <section>{section_info.get('section', '')}</section>\n", "Title: <title>{section_info.get('title', '')}</title>\n", "Description: <description>{section_info.get('description', '')}</description>\n", "Enhanced Instructions and Checkpoints: <instructions>{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}</instructions>\n", "Formatting Instructions: <formatting>{section_info.get(\"formatting_instructions\", \"\")}</formatting>\n", "</section_info>\n", "    \n", "3. Evaluation Feedback:\n", "{feedback_text}\n", "\n", "4. Previous Output (that feedback applies to):\n", "<previous_output>\n", "{previous_output_text}\n", "</previous_output>\n", "\n", "Now, please follow these instructions to refine and perfect the draft content:\n", "\n", "1. Analyze the Information:\n", "   Begin by analyzing the provided information. Consider how to address all issues, implement improvements, and enhance the content while maintaining compliance with ICH eCTD v3.2.2 standards. Develop your refinement plan inside <refinement_plan> tags in your thinking block.\n", "\n", "2. Content Refinement:\n", "   a. Address all critical issues identified in the evaluation feedback.\n", "   b. Implement all required improvements.\n", "   c. Follow additional recommendations where applicable.\n", "   d. Maintain and enhance the identified strengths.\n", "   e. Ensure the refined content addresses all evaluation concerns.\n", "   f. Improve technical precision, clarity, and explanations.\n", "   g. Strengthen scientific rationale and justifications.\n", "   <PERSON>. <PERSON><PERSON>ce data presentation and interpretation.\n", "   i. Improve narrative flow and cohesion.\n", "   j. Ensure comprehensive treatment of all required topics.\n", "\n", "3. Regulatory Compliance:\n", "   a. Strictly adhere to ICH eCTD v3.2.2 standards.\n", "   b. Address all requirements from the section description.\n", "   c. Follow all reference text specifications.\n", "   d. Comply with all formatting instructions.\n", "   e. Ensure all technical content meets regulatory standards.\n", "   f. Verify all cross-references and dependencies are correct.\n", "\n", "4. Documentation Standards:\n", "   a. Use professional language and terminology consistently.\n", "   b. Improve formatting and structure as specified.\n", "   c. Enhance references to tables and figures.\n", "   d. Strengthen justifications for critical decisions.\n", "   e. Improve documentation of assumptions and limitations.\n", "   f. Enhance overall technical documentation quality.\n", "\n", "5. Table and Figure Handling:\n", "   a. Maintain exact table references in the format \"Table {{X}}: {{table heading}}\".\n", "   b. Do not modify or remove any `<TABLE_TAG_table_x>` tags.\n", "   c. Keep all table tags in their original position in the text.\n", "   d. Do not attempt to construct or format the tables yourself.\n", "   e. Use provided table summaries to inform your writing, but maintain the table placeholders exactly as they appear.\n", "   f. <PERSON>tain exact figure numbers and captions as they appear in the source documents.\n", "   g. Do not rename any tables or figures.\n", "   h. Include all tables and figures that appear in the input source documents or draft content.\n", "   i. Do not modify the content of any figures.\n", "   j. Include references to all tables and figures in your text.\n", "\n", "6. Output Format:\n", "   a. Generate professional, regulatory-compliant content with all issues resolved.\n", "   b. Use well-structured paragraphs with improved logical flow.\n", "   c. Do not include section headings.\n", "   d. Use markdown format without backticks or code blocks.\n", "   e. Do not include any other formatting (XML, HTML, plaintext).\n", "   f. Preserve all `<TABLE_TAG_table_x>` tags exactly as they appear.\n", "\n", "7. Critical Restrictions:\n", "   a. Do not include any commentary about how you addressed the feedback.\n", "   b. Do not include verification checklists or statements about compliance.\n", "   c. Do not include meta-statements like \"This addresses the previous critique\" or \"Based on feedback\".\n", "   d. Do not include process-related commentary.\n", "   e. Do not include formatting instructions or comments about document structure in the output.\n", "   f. Do not reference the feedback, plans, or improvement process in your output.\n", "   g. Generate only the actual NDA content that would appear in the final submission.\n", "   h. Do not include any self-referential statements about the refinement process.\n", "\n", "After completing your refinement plan, generate the refined content that represents a significant improvement over previous versions, addressing all feedback points while maintaining the identified strengths. Your output should be indistinguishable from content written directly for an NDA submission, with no process commentary. Your final output should consist only of the refined content and should not duplicate or rehash any of the work you did in the refinement plan.\"\"\"\n", "\n", "    # Wrap the API call with retry_on_empty_response\n", "    async def make_api_call():\n", "        response = await generator_client.chat.completions.create(\n", "            model=GENERATOR_MODEL_NAME,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            temperature=0.3,\n", "            extra_body={\n", "                # \"return_reasoning\": True\n", "            }\n", "        )\n", "\n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "        output_tokens = calculate_number_of_tokens(response.choices[0].message.content)\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "        print(f\"refine_content token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "        \n", "        return response.choices[0].message.content\n", "\n", "    # Use the retry wrapper to handle empty responses\n", "    return await retry_on_empty_response(make_api_call)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["async def optimized_nda_output_generation(section_info, critique_feedback=None, reuse_plan=None):\n", "    \"\"\"\n", "    Generate NDA output using a multi-stage, batch processing approach to reduce context size and optimize performance.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        critique_feedback: Optional critique feedback from previous iterations to address missing content\n", "        reuse_plan: Optional pre-generated plan to reuse (skips Stage 1 for performance)\n", "        \n", "    Returns:\n", "        tuple: (Generated NDA section content, plan used) if reuse_plan is None, else just content\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA output generation for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    # Stage 1: Create plan from quality guideline summaries (skip if reusing plan)\n", "    if reuse_plan is not None:\n", "        print(\"Stage 1: Reusing existing plan (performance optimization)\")\n", "        plan = reuse_plan\n", "    else:\n", "        individual_summaries = []\n", "        if \"relevant_individual_summaries\" in section_info:\n", "            individual_summaries = [summary.get(\"content\", \"\") for summary in section_info.get(\"relevant_individual_summaries\", [])]\n", "        \n", "        # Fall back to combined summary if individual summaries aren't available\n", "        if not individual_summaries and \"quality_guideline_combined_summary\" in section_info:\n", "            individual_summaries = [section_info[\"quality_guideline_combined_summary\"]]\n", "        \n", "        print(f\"Stage 1: Creating incremental plan from {len(individual_summaries)} individual summaries\")\n", "        plan = await create_incremental_plan(section_info, individual_summaries)\n", "\n", "        print(\"plan: \", plan)\n", "    \n", "    # Stage 2: Generate content from input chunks\n", "    input_chunks = section_info.get(\"relevant_input_chunks\", [])\n", "    print(f\"Stage 2: Creating incremental content from {len(input_chunks)} input chunks using the plan\")\n", "    draft_content = await create_incremental_content(plan, section_info, input_chunks, critique_feedback)\n", "\n", "    print(\"draft_content: \", draft_content)\n", "    \n", "    # Stage 3: Refine content based on evaluation feedback\n", "    evaluation_feedback = section_info.get(\"nda_output_evaluation_json\", None)\n", "    \n", "    if evaluation_feedback:\n", "        print(f\"Stage 3: Refining content based on evaluation feedback\")\n", "        refined_content = await refine_content(draft_content, section_info, evaluation_feedback)\n", "\n", "        print(\"refined_content: \", refined_content)\n", "\n", "        # Return content and plan if this is the first iteration, else just content\n", "        if reuse_plan is None:\n", "            return refined_content, plan\n", "        else:\n", "            return refined_content\n", "    else:\n", "        print(f\"No evaluation feedback available, returning draft content\")\n", "        # Return content and plan if this is the first iteration, else just content\n", "        if reuse_plan is None:\n", "            return draft_content, plan\n", "        else:\n", "            return draft_content\n", "    \n", "async def optimized_nda_critique(section_info, nda_output):\n", "    \"\"\"\n", "    Generate a comprehensive NDA critique using a batch processing approach with individual quality guideline summaries.\n", "    Uses parallel processing to generate critiques from multiple summaries simultaneously.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    # Extract individual summaries\n", "    individual_summaries = []\n", "    if \"relevant_individual_summaries\" in section_info:\n", "        individual_summaries = [summary.get(\"content\", \"\") for summary in section_info.get(\"relevant_individual_summaries\", [])]\n", "    \n", "    # Fall back to combined summary if individual summaries aren't available\n", "    if not individual_summaries and \"quality_guideline_combined_summary\" in section_info:\n", "        individual_summaries = [section_info[\"quality_guideline_combined_summary\"]]\n", "    \n", "    # If no summaries available, generate critique based only on section info\n", "    if not individual_summaries:\n", "        print(\"No quality guideline summaries available, generating critique based only on section info\")\n", "        return await generate_critique_without_guidelines(section_info, nda_output)\n", "    \n", "    print(f\"Generating individual critiques from {len(individual_summaries)} quality guideline summaries in parallel\")\n", "    \n", "    # Generate individual critiques in parallel using asyncio.gather\n", "    tasks = [generate_critique_from_one_summary(summary, section_info, nda_output) \n", "             for summary in individual_summaries]\n", "    individual_critiques = await asyncio.gather(*tasks)\n", "    \n", "    # If only one critique, return it directly\n", "    if len(individual_critiques) == 1:\n", "        return individual_critiques[0]\n", "    \n", "    # Combine critiques using batch processing\n", "    print(\"Combining individual critiques using batch processing\")\n", "    return await combine_critiques_incrementally(individual_critiques, section_info)\n", "\n", "\n", "async def generate_critique_from_one_summary(summary_content, section_info, nda_output):\n", "    \"\"\"\n", "    Generate a critique of NDA output based on a single quality guideline summary.\n", "    \n", "    Args:\n", "        summary_content: Content of a single quality guideline summary\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are a highly experienced regulatory compliance expert and quality assurance specialist in the pharmaceutical industry. Your task is to critically evaluate an NDA (New Drug Application) section output against specific quality guidelines and ICH eCTD v3.2.2 requirements. Your evaluation must be thorough, precise, and adhere strictly to the provided guidelines.\"\"\"\n", "\n", "    user_prompt = f\"\"\"First, review the following enhanced instructions and checkpoints for this section:\n", "\n", "<enhanced_instructions_and_checkpoints>\n", "{section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "</enhanced_instructions_and_checkpoints>\n", "\n", "Next, thoroughly review the quality guideline summary that you will use as the basis for your evaluation:\n", "\n", "<quality_guideline_summary>\n", "{summary_content}\n", "</quality_guideline_summary>\n", "\n", "Now, you will evaluate the following NDA output against the provided quality guideline summary and ICH eCTD requirements:\n", "\n", "<nda_output>\n", "{nda_output}\n", "</nda_output>\n", "\n", "Now, review the following section information:\n", "\n", "<section_info>\n", "Section: {section_info.get('section', '')}\n", "Title: {section_info.get('title', '')}\n", "Description: {section_info.get('description', '')}\n", "</section_info>\n", "\n", "Take note of the specific formatting instructions:\n", "\n", "<formatting_instructions>\n", "{section_info.get(\"formatting_instructions\", \"\")}\n", "</formatting_instructions>\n", "\n", "Conduct your evaluation by following these steps:\n", "\n", "1. Golden Instructions and Formatting Compliance:\n", "   - Strictly evaluate adherence to Golden Instructions and Checkpoints\n", "   - Verify exact compliance with Formatting Instructions\n", "   - Consider non-compliance with these instructions as critical issues\n", "\n", "2. Table and Figure Preservation Assessment:\n", "   - Verify that ALL table placeholders and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "   - Confirm that the exact position and order of all \"`<TABLE_TAG_table_x>`\" tags is maintained in the text\n", "   - Check that NO attempt has been made to construct or format the tables\n", "   - Verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "\n", "3. Content Appropriateness and Relevance Assessment:\n", "   - Verify that ALL content directly relates to and supports the quality guideline summary requirements\n", "   - Identify any inappropriate tables, figures, or content that should NOT be in this section\n", "   - Check if any content contradicts or conflicts with the quality guideline summary\n", "\n", "4. ICH eCTD v3.2.2 Compliance Assessment:\n", "   - Evaluate adherence to eCTD structure and format\n", "   - Verify compliance with section description and reference text\n", "   - Ensure all requirements from the ICH eCTD guidelines are met\n", "\n", "5. Quality Guidelines Summary Coverage and Alignment:\n", "   - Systematically verify all key requirements from the quality guideline summary are addressed\n", "   - Assess if the depth of coverage matches quality guideline expectations\n", "\n", "6. Technical Accuracy and Completeness:\n", "   - Verify technical specifications align with quality guideline requirements\n", "   - Check parameter accuracy against quality guideline standards\n", "\n", "7. Hallucination and Fabrication Detection:\n", "   - Identify any content not supported by the quality guideline summary\n", "   - Check for fabricated data or claims not found in quality guidelines\n", "\n", "8. Quality Guideline Gap Analysis:\n", "   - Identify any quality guideline requirements completely missing from output\n", "   - Flag incomplete implementation of quality guideline recommendations\n", "\n", "For each step of your evaluation, use <evaluation_process> tags inside your thinking block to show your thought process and findings. In this section:\n", "\n", "- List key points from the quality guideline summary\n", "- Identify and quote relevant parts of the NDA output for each evaluation step\n", "- For each evaluation step, consider both compliant and non-compliant aspects\n", "\n", "After completing your analysis, provide a summary of your evaluation using the following structure:\n", "\n", "<evaluation_summary>\n", "  <overall_rating>\n", "    [Provide a numerical rating from 1-10, where 1-3 indicates major deficiencies and significant non-compliance, 4-6 indicates moderate issues and partial compliance, 7-8 indicates minor issues and mostly compliant, and 9-10 indicates excellent compliance with minimal improvements needed]\n", "  </overall_rating>\n", "  \n", "  <key_findings>\n", "    [List the most important findings from your evaluation, both positive and negative]\n", "  </key_findings>\n", "  \n", "  <critical_issues>\n", "    [Highlight any critical issues or non-compliance that require immediate attention]\n", "  </critical_issues>\n", "  \n", "  <recommendations>\n", "    [Provide specific, actionable recommendations for improvement, focusing on aligning the NDA output with the quality guideline summary]\n", "  </recommendations>\n", "</evaluation_summary>\n", "\n", "Remember to maintain a strict, critical perspective throughout your evaluation, ensuring that every aspect of the NDA output is scrutinized against the provided guidelines and requirements.\n", "\n", "Your final output should consist only of the evaluation summary and should not duplicate or rehash any of the work you did in the evaluation process section.\"\"\"\n", "\n", "    system_tokens = calculate_number_of_tokens(system_prompt)\n", "    user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "    critique_evaluation_agent = Agent(\n", "        model=critique_model,\n", "        system_prompt=system_prompt,\n", "        output_type=CritiqueEvaluation,\n", "        model_settings={\n", "            \"temperature\": 0.2,\n", "            \"extra_body\": {\n", "                # \"return_reasoning\": True\n", "            }\n", "        },\n", "        retries=3\n", "    )\n", "\n", "    response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "    total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "    print(f\"generate_critique_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "    return response.output\n", "\n", "\n", "async def generate_critique_without_guidelines(section_info, nda_output):\n", "    \"\"\"\n", "    Generate a critique of NDA output based only on section information without quality guidelines.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        \n", "    Returns:\n", "        CritiqueEvaluation: Structured evaluation object\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are a highly skilled regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate an NDA (New Drug Application) section output against ICH eCTD v3.2.2 requirements and provided section information.\"\"\"\n", "\n", "    user_prompt = f\"\"\"\n", "    \n", "First, review the following NDA output and section information:\n", "\n", "<nda_output>\n", "{nda_output}\n", "</nda_output>\n", "\n", "<section_info>\n", "Section: {section_info.get('section', '')}\n", "Title: {section_info.get('title', '')}\n", "Description: {section_info.get('description', '')}\n", "Golden Instructions and Checkpoints: {section_info.get(\"enhanced_instructions_and_checkpoints\", \"\")}\n", "Formatting Instructions: {section_info.get(\"formatting_instructions\", \"\")}\n", "</section_info>\n", "\n", "Your evaluation should cover the following categories, in order of priority:\n", "\n", "1. Golden Instructions and Formatting Compliance\n", "2. Table and Figure Preservation Assessment\n", "3. ICH eCTD v3.2.2 Compliance Assessment\n", "4. Technical Accuracy and Completeness\n", "5. Content Quality Assessment\n", "6. Hallucination Detection\n", "7. Regulatory Compliance\n", "8. Documentation Standards\n", "9. Improvement Areas\n", "10. Table and Section Handling Evaluation\n", "\n", "For each category, first list the specific elements you're looking for, then wrap your evaluation inside <detailed_assessment> tags to show your thought process and detailed assessment. For the Golden Instructions and Formatting Compliance category, quote relevant parts of the instructions and check them off as you go. For the Table and Figure Preservation Assessment, count and list each table and figure placeholder you find. After evaluating all categories, provide a numerical rating (1-10) and a summary of your findings.\n", "\n", "Critical Requirements:\n", "- Strictly adhere to Golden Instructions and Checkpoints.\n", "- Verify exact compliance with Formatting Instructions.\n", "- Ensure all table placeholders (in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\") are preserved exactly.\n", "- Maintain the position and order of all table tags.\n", "- Confirm that no attempt has been made to construct tables.\n", "- Verify that all figure numbers and captions are preserved exactly.\n", "- Check that all tables and figures from source documents are included.\n", "- Assess technical accuracy, completeness, and documentation quality.\n", "- Identify any hallucinations or unsupported claims.\n", "\n", "Rating Criteria:\n", "1-3: Major deficiencies, significant non-compliance\n", "4-6: Moderate issues, partial compliance\n", "7-8: Minor issues, mostly compliant\n", "9-10: Excellent compliance, minimal improvements needed\n", "\n", "Provide your evaluation in the given format.\n", "\n", "Remember to be thorough, critical, and provide specific, actionable feedback for improvement in each category. Your final output should consist only of the evaluation in the format specified above and should not duplicate or rehash any of the work you did in the detailed assessment sections.\"\"\"\n", "\n", "    system_tokens = calculate_number_of_tokens(system_prompt)\n", "    user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "    critique_evaluation_agent = Agent(\n", "        model=critique_model,\n", "        system_prompt=system_prompt,\n", "        output_type=CritiqueEvaluation,\n", "        model_settings={\n", "            \"temperature\": 0.2,\n", "            \"extra_body\": {\n", "                # \"return_reasoning\": True\n", "            }\n", "        },\n", "        retries=3\n", "    )\n", "\n", "    response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "    total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "    print(f\"generate_critique_without_guidelines token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "    return response.output\n", "\n", "\n", "async def combine_critiques_incrementally(critiques, section_info):\n", "    \"\"\"\n", "    Combine multiple critiques incrementally using a batch processing approach.\n", "    \n", "    Args:\n", "        critiques: List of individual critiques to combine\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        str: Comprehensive combined critique\n", "    \"\"\"\n", "    # Maximum token limit for input (keeping some buffer for the prompt and output)\n", "    \n", "    async def combine_critiques_batch(critiques_batch):\n", "        \"\"\"Helper function to combine multiple critiques in a batch.\"\"\"\n", "        total_critiques = len(critiques_batch)\n", "        print(f\"\\nCombining {total_critiques} critiques:\")\n", "        \n", "        total_tokens = sum(calculate_number_of_tokens(critique.model_dump_json()) for critique in critiques_batch)\n", "        print(f\"Total input tokens: {total_tokens}\")\n", "        \n", "        system_prompt = \"\"\"You are a senior pharmaceutical regulatory documentation expert with specialized expertise in critiquing NDA (New Drug Application) submissions. Your task is to combine multiple separate evaluations of an NDA section into one comprehensive, unified critique that captures all key insights and maintains a consistent, thorough evaluation structure.\"\"\"\n", "\n", "        # Convert CritiqueEvaluation objects to text format for processing\n", "        critique_texts = []\n", "        for critique in critiques_batch:\n", "            if isinstance(critique, CritiqueEvaluation):\n", "                # Convert CritiqueEvaluation to text format\n", "                critique_text = f\"\"\"Overall Rating: {critique.overall_rating}\n", "\n", "Key Strengths:\n", "{chr(10).join(f\"- {strength}\" for strength in critique.key_strengths)}\n", "\n", "Critical Issues:\n", "{chr(10).join(f\"- {issue}\" for issue in critique.critical_issues)}\n", "\n", "Required Improvements:\n", "{chr(10).join(f\"- {improvement}\" for improvement in critique.required_improvements)}\n", "\n", "Additional Recommendations:\n", "{chr(10).join(f\"- {recommendation}\" for recommendation in critique.additional_recommendations)}\"\"\"\n", "                critique_texts.append(critique_text)\n", "            else:\n", "                # Handle legacy text format\n", "                critique_texts.append(str(critique))\n", "\n", "        user_prompt = f\"\"\"Here are the evaluations you need to combine:\n", "<evaluations>\"\"\"\n", "        \n", "        # Add each critique with a header\n", "        for i, critique_text in enumerate(critique_texts, 1):\n", "            user_prompt += f\"\"\"\n", "\n", "EVALUATION {i}:\n", "<evaluation_{i}>\n", "{critique_text}\n", "</evaluation_{i}>\n", "\"\"\"\n", "        \n", "        user_prompt += f\"\"\"\n", "{total_critiques} evaluations will be provided above. Each evaluation is labeled as EVALUATION {{i}}, where i is the evaluation number.\n", "</evaluations>\n", "\n", "Please follow these steps to create a combined critique:\n", "\n", "1. Carefully read and analyze all evaluations.\n", "2. For each evaluation, extract and categorize:\n", "   - Critical issues\n", "   - Required improvements\n", "   - Key strengths\n", "   - Additional recommendations\n", "   - Table and figure information\n", "   - Original rating\n", "3. Combine all extracted information, eliminating redundancies while ensuring no important points are lost.\n", "4. Group related issues and improvements together.\n", "5. Resolve any contradictions between the critiques.\n", "6. Organize feedback in order of regulatory importance.\n", "7. Verify table and figure preservation:\n", "   - Ensure ALL table placeholders and tags are preserved EXACTLY in the format \"Table X: table heading\" followed by \"`<TABLE_TAG_table_x>`\"\n", "   - Maintain the exact position and order of all `<TABLE_TAG_table_x>` tags in the text\n", "   - Confirm that NO attempt has been made to construct or format the tables\n", "   - Verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents\n", "   - Check that all tables and figures are properly referenced in the text\n", "8. Determine a balanced overall rating considering all original ratings.\n", "9. Write a comprehensive critique that incorporates all key points while maintaining a cohesive structure.\n", "10. Review the combined critique for consistency, accuracy, and adherence to ICH eCTD requirements.\n", "\n", "Before writing the final combined critique, wrap your analysis inside <critique_analysis> tags in your thinking block. Your analysis should show your thought process and how you're addressing each of the above steps. Pay particular attention to how you're preserving critical information and maintaining accuracy. Include the following in your analysis:\n", "\n", "- List out critical issues, required improvements, key strengths, and additional recommendations from each evaluation\n", "- Explicitly note any contradictions between evaluations\n", "- List out all table and figure references\n", "- Propose an overall rating based on individual ratings\n", "\n", "After your analysis, present your final combined critique within <combined_critique> tags. The combined critique should:\n", "- Preserve ALL critical issues, required improvements, key strengths, and additional recommendations from all original critiques\n", "- Maintain the most detailed and specific feedback\n", "- Use consistent terminology and phrasing\n", "- Maintain a formal, professional evaluation tone\n", "- Ensure all feedback is specific and actionable\n", "- Accurately reflect the combined feedback in the final rating\n", "\n", "Remember, the preservation of table and figure information is of HIGHEST PRIORITY. Any alteration, removal, or change in position of table tags should be considered a CRITICAL issue.\n", "\n", "Your final output should consist only of the combined critique and should not duplicate or rehash any of the work you did in the critique analysis.\"\"\"\n", "\n", "        system_tokens = calculate_number_of_tokens(system_prompt)\n", "        user_tokens = calculate_number_of_tokens(user_prompt)\n", "\n", "        critique_evaluation_agent = Agent(\n", "            model=critique_model,\n", "            system_prompt=system_prompt,\n", "            output_type=CritiqueEvaluation,\n", "            model_settings={\n", "                \"temperature\": 0.2,\n", "                \"extra_body\": {\n", "                    # \"return_reasoning\": True\n", "                }\n", "            },\n", "            retries=3\n", "        )\n", "\n", "        response = await critique_evaluation_agent.run(user_prompt)\n", "\n", "        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())\n", "        total_tokens = system_tokens + user_tokens + output_tokens\n", "\n", "        print(f\"combine_critiques_incrementally token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}\")\n", "\n", "        return response.output\n", "    \n", "    # Handle base cases\n", "    if not critiques:\n", "        return \"\"\n", "    \n", "    if len(critiques) == 1:\n", "        return critiques[0]\n", "    \n", "    # Process critiques iteratively, combining in batches\n", "    current_critiques = critiques.copy()\n", "    \n", "    while len(current_critiques) > 1:\n", "        # Create optimal batches based on token count\n", "        batches = []\n", "        current_batch = []\n", "        current_token_count = 0\n", "        \n", "        for critique in current_critiques:\n", "            critique_tokens = calculate_number_of_tokens(critique.model_dump_json())\n", "            \n", "            # Check if this single critique exceeds the token limit\n", "            if critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                print(f\"Warning: Single critique has {critique_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}\")\n", "                # Force this large critique to be merged with the current batch to avoid infinite loop\n", "                if current_batch:\n", "                    # Add to current batch despite exceeding limit\n", "                    current_batch.append(critique)\n", "                    current_token_count += critique_tokens\n", "                    print(f\"Forcing merge of oversized critique with current batch (total tokens: {current_token_count})\")\n", "                    # Finalize this batch\n", "                    batches.append(current_batch)\n", "                    current_batch = []\n", "                    current_token_count = 0\n", "                else:\n", "                    # If current_batch is empty, we need to pair this with the next critique\n", "                    # to avoid it being processed alone repeatedly\n", "                    current_batch = [critique]\n", "                    current_token_count = critique_tokens\n", "                    print(f\"Starting new batch with oversized critique ({critique_tokens} tokens)\")\n", "            # If adding this critique would exceed the token limit, start a new batch\n", "            elif current_batch and current_token_count + critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:\n", "                batches.append(current_batch)\n", "                current_batch = [critique]\n", "                current_token_count = critique_tokens\n", "            else:\n", "                current_batch.append(critique)\n", "                current_token_count += critique_tokens\n", "        \n", "        # Add the last batch if it's not empty\n", "        if current_batch:\n", "            batches.append(current_batch)\n", "        \n", "        # Ensure no batch has only one critique to avoid infinite loop\n", "        # If we have a single-item batch, try to merge it with another batch\n", "        final_batches = []\n", "        single_item_batch = None\n", "        \n", "        for batch in batches:\n", "            if len(batch) == 1 and single_item_batch is None:\n", "                single_item_batch = batch\n", "            elif len(batch) == 1 and single_item_batch is not None:\n", "                # Merge two single-item batches\n", "                merged_batch = single_item_batch + batch\n", "                final_batches.append(merged_batch)\n", "                single_item_batch = None\n", "            else:\n", "                if single_item_batch is not None:\n", "                    # Merge the single-item batch with this multi-item batch\n", "                    merged_batch = single_item_batch + batch\n", "                    final_batches.append(merged_batch)\n", "                    single_item_batch = None\n", "                else:\n", "                    final_batches.append(batch)\n", "        \n", "        # If we still have a single-item batch left, we need to handle it\n", "        if single_item_batch is not None:\n", "            if final_batches:\n", "                # Merge with the last batch\n", "                final_batches[-1].extend(single_item_batch)\n", "            else:\n", "                # This is the only batch, which means we have only one critique left\n", "                # This should not happen in the while loop condition, but just in case\n", "                final_batches.append(single_item_batch)\n", "        \n", "        batches = final_batches\n", "        \n", "        print(f\"Created {len(batches)} batches for critique combining\")\n", "        for i, batch in enumerate(batches):\n", "            total_tokens = sum(calculate_number_of_tokens(c.model_dump_json()) for c in batch)\n", "            print(f\"Batch {i+1} contains {len(batch)} critiques with {total_tokens} tokens\")\n", "        \n", "        # Process all batches in parallel\n", "        tasks = [combine_critiques_batch(batch) for batch in batches]\n", "        \n", "        # Wait for all combinations to complete\n", "        current_critiques = await asyncio.gather(*tasks)\n", "    \n", "    print(f\"combine_critiques_incrementally tokens: {calculate_number_of_tokens(current_critiques[0].model_dump_json())}\")\n", "    return current_critiques[0]\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def optimized_nda_critique_with_parsing(section_info, nda_output, max_retries=3):\n", "    \"\"\"\n", "    Generate a comprehensive NDA critique using structured output.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        nda_output: The NDA output to critique\n", "        max_retries: Maximum number of retry attempts for failures\n", "        \n", "    Returns:\n", "        Tuple[str, Dict]: Raw critique text representation and structured evaluation dict\n", "    \"\"\"\n", "    print(f\"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}\")\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            # Generate the critique using structured output\n", "            critique_evaluation = await optimized_nda_critique(section_info, nda_output)\n", "            \n", "            # Convert CritiqueEvaluation to text representation for compatibility\n", "            critique_text = f\"\"\"1. Overall Rating:\n", "   {critique_evaluation.overall_rating}\n", "\n", "2. Key Strengths:\n", "{chr(10).join(f\"   - {strength}\" for strength in critique_evaluation.key_strengths)}\n", "\n", "3. Critical Issues:\n", "{chr(10).join(f\"   - {issue}\" for issue in critique_evaluation.critical_issues)}\n", "\n", "4. Required Improvements:\n", "{chr(10).join(f\"   - {improvement}\" for improvement in critique_evaluation.required_improvements)}\n", "\n", "5. Additional Recommendations:\n", "{chr(10).join(f\"   - {recommendation}\" for recommendation in critique_evaluation.additional_recommendations)}\"\"\"\n", "            \n", "            # Convert to dict for compatibility\n", "            evaluation_dict = {\n", "                \"overall_rating\": critique_evaluation.overall_rating,\n", "                \"key_strengths\": critique_evaluation.key_strengths,\n", "                \"critical_issues\": critique_evaluation.critical_issues,\n", "                \"required_improvements\": critique_evaluation.required_improvements,\n", "                \"additional_recommendations\": critique_evaluation.additional_recommendations\n", "            }\n", "            \n", "            print(f\"Successfully generated critique with overall rating: {critique_evaluation.overall_rating}\")\n", "            return critique_text, evaluation_dict\n", "            \n", "        except Exception as e:\n", "            print(f\"Error in critique generation (attempt {attempt+1}/{max_retries}): {str(e)}\")\n", "            if attempt == max_retries - 1:\n", "                # Last attempt, return a default response\n", "                default_dict = {\n", "                    \"overall_rating\": 5.0,  # Middle value as default\n", "                    \"key_strengths\": [\"Content provides basic information\"],\n", "                    \"critical_issues\": [\"Unable to properly evaluate the content\"],\n", "                    \"required_improvements\": [\"Review all regulatory requirements\"],\n", "                    \"additional_recommendations\": [\"Ensure compliance with ICH eCTD guidelines\"]\n", "                }\n", "                default_text = \"\"\"1. Overall Rating:\n", "   5.0\n", "\n", "2. Key Strengths:\n", "   - Content provides basic information\n", "\n", "3. Critical Issues:\n", "   - Unable to properly evaluate the content\n", "\n", "4. Required Improvements:\n", "   - Review all regulatory requirements\n", "\n", "5. Additional Recommendations:\n", "   - Ensure compliance with ICH eCTD guidelines\"\"\"\n", "                print(\"Exhausted all retry attempts, returning default evaluation\")\n", "                return default_text, default_dict\n", "    \n", "    # This should not be reached due to the return in the last attempt, but just in case\n", "    default_dict = {\n", "        \"overall_rating\": 5.0,\n", "        \"key_strengths\": [\"Default evaluation\"],\n", "        \"critical_issues\": [\"Parsing error occurred\"],\n", "        \"required_improvements\": [\"Review output format\"],\n", "        \"additional_recommendations\": [\"Ensure critique follows expected format\"]\n", "    }\n", "    default_text = \"Error: Unexpected execution path\"\n", "    return default_text, default_dict\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["async def rate_nda_output_and_self_improve(section_info):\n", "    \"\"\"\n", "    Generate NDA output, evaluate it, and improve it based on feedback.\n", "    Uses an optimized approach where the plan is generated once and reused\n", "    in subsequent iterations for improved performance.\n", "    \n", "    Args:\n", "        section_info: Dictionary containing section information\n", "        \n", "    Returns:\n", "        Dict: Updated section information with NDA output and evaluation\n", "    \"\"\"\n", "    overall_rating = 0\n", "    counter = 0\n", "    answers_list = []\n", "\n", "    # Generate plan and content for the first iteration, save the plan for reuse\n", "    original_nda_output, reusable_plan = await optimized_nda_output_generation(section_info)        \n", "    current_nda_output = original_nda_output\n", "    \n", "    # Use the robust critique function that handles parsing errors\n", "    nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)\n", "    \n", "    section_info[\"nda_output_evaluation_json\"] = nda_output_evaluation_json\n", "    section_info[\"nda_output_evaluation_text\"] = nda_output_evaluation  # Save the raw critique text\n", "    overall_rating = nda_output_evaluation_json[\"overall_rating\"]  # Now guaranteed to be a valid number\n", "    counter += 1\n", "\n", "    answers_list.append(\n", "        {\n", "            \"nda_output\": current_nda_output,\n", "            \"nda_output_evaluation_json\": nda_output_evaluation_json,\n", "            \"nda_output_evaluation_text\": nda_output_evaluation,  # Include raw critique text\n", "            \"overall_rating\": overall_rating\n", "        }\n", "    )\n", "\n", "    print(f\"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}\")\n", "\n", "    # Regenerate content in subsequent iterations using feedback and reusing the plan for performance\n", "    while(overall_rating < 9 and counter < 3):\n", "        # Create an enhanced feedback object that includes the previous output\n", "        enhanced_feedback = nda_output_evaluation_json.copy()\n", "        enhanced_feedback[\"previous_output\"] = current_nda_output\n", "        enhanced_feedback[\"evaluation_text\"] = nda_output_evaluation  # Include the raw critique text\n", "        \n", "        # Regenerate content using critique feedback and reusing the plan (performance optimization)\n", "        print(f\"Regenerating content with critique feedback and reused plan to address issues (rating: {overall_rating})\")\n", "        regenerated_nda_output = await optimized_nda_output_generation(section_info, enhanced_feedback, reusable_plan)\n", "\n", "        print(\"regenerated_nda_output: \", regenerated_nda_output)\n", "\n", "        current_nda_output = regenerated_nda_output\n", "        \n", "        # Use the robust critique function that handles parsing errors\n", "        nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)\n", "        \n", "        section_info[\"nda_output_evaluation_json\"] = nda_output_evaluation_json\n", "        section_info[\"nda_output_evaluation_text\"] = nda_output_evaluation  # Save the raw critique text\n", "        overall_rating = nda_output_evaluation_json[\"overall_rating\"]  # Now guaranteed to be a valid number\n", "        counter += 1\n", "\n", "        answers_list.append(\n", "            {\n", "                \"nda_output\": current_nda_output,\n", "                \"nda_output_evaluation_json\": nda_output_evaluation_json,\n", "                \"nda_output_evaluation_text\": nda_output_evaluation,  # Include raw critique text\n", "                \"overall_rating\": overall_rating\n", "            }\n", "        )\n", "\n", "        print(f\"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}\")\n", "\n", "    # Select best answer based on overall rating\n", "    best_answer = max(answers_list, key=lambda x: x[\"overall_rating\"])\n", "    section_info[\"nda_output\"] = best_answer[\"nda_output\"]\n", "    section_info[\"nda_output_evaluation_json\"] = best_answer[\"nda_output_evaluation_json\"]\n", "    section_info[\"nda_output_evaluation_text\"] = best_answer[\"nda_output_evaluation_text\"]  # Save the raw critique text\n", "\n", "    print(f\"Selecting best answer for section {section_info.get('section', 'unknown')} with rating {best_answer['overall_rating']}\")\n", "\n", "    return section_info\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["async def generate_nda_for_each_section(json_obj):\n", "    \"\"\"\n", "    Recursively process a JSON object and generate NDA output for each section.\n", "    \n", "    Args:\n", "        json_obj: The JSON object to process\n", "        \n", "    Returns:\n", "        Dict: The processed JSON object with NDA outputs\n", "    \"\"\"\n", "    # First, collect all sections that need processing\n", "    sections_to_process = []\n", "    \n", "    def collect_sections(obj):\n", "        if isinstance(obj, dict):\n", "            # Check if this is a section that needs NDA output generation\n", "            if (obj and \"have_to_generate_output_for_this_section\" in obj and \n", "                obj[\"have_to_generate_output_for_this_section\"]):\n", "                sections_to_process.append(obj)\n", "            \n", "            # Process each key-value pair in the dictionary\n", "            for key, value in obj.items():\n", "                if isinstance(value, dict):\n", "                    collect_sections(value)\n", "    \n", "    # Collect all sections that need processing\n", "    collect_sections(json_obj)\n", "    \n", "    # Process all sections in parallel\n", "    async def process_section(section):\n", "        print(f\"Generating NDA output for section {section.get('section', 'unknown')}\")\n", "        \n", "        # Generate NDA output for this section\n", "        processed_section = await rate_nda_output_and_self_improve(section)\n", "        \n", "        print(f\"Completed NDA output generation for section {section.get('section', 'unknown')}\")\n", "        \n", "        return processed_section\n", "    \n", "    # Process all sections in parallel\n", "    if sections_to_process:\n", "        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])\n", "        \n", "        # Update the original sections with the processed results\n", "        for i, section in enumerate(sections_to_process):\n", "            section.update(processed_sections[i])\n", "\n", "        # for section in sections_to_process:\n", "        #     processed_section = await process_section(section)\n", "        #     section.update(processed_section)\n", "    \n", "    return json_obj\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process the document to find relevant input documents\n", "nda_output_json = await generate_nda_for_each_section(processed_doc_with_input_docs)\n", "\n", "# Save the results to a JSON file\n", "write_to_json(nda_output_json, \"nda_output_json.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pypandoc\n", "\n", "def format_answer(json_obj, formatted_text=\"\", depth=1):\n", "    \"\"\"\n", "    Recursively formats the answer JSON into a structured Markdown document.\n", "    Uses '#' for headings based on depth.\n", "    \"\"\"\n", "    if isinstance(json_obj, dict):\n", "        if \"section\" in json_obj and \"title\" in json_obj:\n", "            formatted_text += f\"{'#' * (depth)} {json_obj['section']} {json_obj['title']}\\n\\n\"\n", "            if \"nda_output\" in json_obj:\n", "                formatted_text += f\"{json_obj['nda_output']}\\n\\n\"\n", "\n", "        for key, value in json_obj.items():\n", "            if isinstance(value, dict):\n", "                formatted_text = format_answer(value, formatted_text, depth + 1)  # Increase depth\n", "\n", "    return formatted_text\n", "\n", "def save_answer_file(json_obj, filename=\"formatted_answer.md\"):\n", "    \"\"\"\n", "    Saves the formatted answer text to a file.\n", "    \"\"\"\n", "    formatted_text = format_answer(json_obj)\n", "    \n", "    # Replace table tags with backtick-enclosed versions\n", "    import re\n", "    # formatted_text = re.sub(r'<TABLE_TAG_table_(\\d+)>', r'`<TABLE_TAG_table_\\1>`', formatted_text)\n", "    \n", "    with open(filename, \"w\", encoding=\"utf-8\") as file:\n", "        file.write(formatted_text)\n", "    print(f\"Formatted answer saved to {filename}\")\n", "\n", "save_answer_file(nda_output_json)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pypandoc.convert_file(\"formatted_answer.md\", 'docx', outputfile=\"formatted_answer.docx\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from docx import Document\n", "from docx.document import Document as DocumentObject\n", "from docx.shared import Inches\n", "from copy import deepcopy\n", "import os\n", "import shutil\n", "import zipfile\n", "from docx.oxml.ns import qn\n", "from docx.table import Table\n", "from docx.section import Paragraph\n", "from typing import List\n", "import json\n", "from docx.oxml import OxmlElement\n", "\n", "def copy_page_setup(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy page setup (size, orientation, margins) from source to target document\"\"\"\n", "    source_section = source_doc.sections[0]\n", "    target_section = target_doc.sections[0]\n", "    \n", "    # Copy page size and orientation\n", "    target_section.page_width = source_section.page_width\n", "    target_section.page_height = source_section.page_height\n", "    target_section.orientation = source_section.orientation\n", "    \n", "    # Copy margins\n", "    target_section.top_margin = source_section.top_margin\n", "    target_section.bottom_margin = source_section.bottom_margin\n", "    target_section.left_margin = source_section.left_margin\n", "    target_section.right_margin = source_section.right_margin\n", "    \n", "    print(f\"📏 Copied page setup: {source_section.page_width} x {source_section.page_height}\")\n", "\n", "def copy_table_styles_completely(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy complete table style definitions including all visual formatting\"\"\"\n", "    try:\n", "        # Extract the styles.xml from both documents  \n", "        source_styles_part = source_doc.part.part_related_by(\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\")\n", "        target_styles_part = target_doc.part.part_related_by(\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles\")\n", "        \n", "        if source_styles_part and target_styles_part:\n", "            # Get the root elements\n", "            source_styles_root = source_styles_part.element\n", "            target_styles_root = target_styles_part.element\n", "            \n", "            # Find all table styles in source\n", "            source_table_styles = source_styles_root.findall('.//w:style[@w:type=\"table\"]', source_styles_root.nsmap)\n", "            \n", "            for style in source_table_styles:\n", "                style_id = style.get(qn('w:styleId'))\n", "                \n", "                # Check if this style exists in target\n", "                existing_style = target_styles_root.find(f'.//w:style[@w:styleId=\"{style_id}\"]', target_styles_root.nsmap)\n", "                \n", "                if existing_style is not None:\n", "                    # Remove existing style\n", "                    existing_style.getparent().remove(existing_style)\n", "                \n", "                # Add the complete style from source\n", "                target_styles_root.append(deepcopy(style))\n", "            \n", "            print(f\"🎨 Copied {len(source_table_styles)} table styles\")\n", "            return True\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy table styles: {e}\")\n", "        return False\n", "    \n", "    return False\n", "\n", "def apply_table_style_explicitly(table: Table, style_name: str):\n", "    \"\"\"Apply table style and ensure all conditional formatting is applied\"\"\"\n", "    try:\n", "        # Set the table style\n", "        table.style = style_name\n", "        \n", "        # Force conditional formatting by updating tblLook\n", "        tbl_element = table._tbl\n", "        tblPr = tbl_element.find(qn('w:tblPr'))\n", "        \n", "        if tblPr is not None:\n", "            # Find or create tblLook element\n", "            tblLook = tblPr.find(qn('w:tblLook'))\n", "            if tbl<PERSON>ook is None:\n", "                tblLook = OxmlElement('w:tblLook')\n", "                tblPr.append(tblLook)\n", "            \n", "            # Set attributes to enable all conditional formatting\n", "            tblLook.set(qn('w:val'), '04A0')\n", "            tblLook.set(qn('w:firstRow'), '1')\n", "            tblLook.set(qn('w:lastRow'), '0')\n", "            tblLook.set(qn('w:firstColumn'), '1')\n", "            tblLook.set(qn('w:lastColumn'), '0')\n", "            tblLook.set(qn('w:noHBand'), '0')\n", "            tblLook.set(qn('w:noVBand'), '1')\n", "            \n", "            return True\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not apply table style: {e}\")\n", "        return False\n", "    \n", "    return False\n", "\n", "def copy_document_styles(source_doc: DocumentObject, target_doc: DocumentObject):\n", "    \"\"\"Copy styles from source document to target document\"\"\"\n", "    try:\n", "        # First copy table styles completely (with full definitions)\n", "        table_styles_copied = copy_table_styles_completely(source_doc, target_doc)\n", "        \n", "        if not table_styles_copied:\n", "            # Fallback to basic style copying\n", "            for style in source_doc.styles:\n", "                if style.name not in [s.name for s in target_doc.styles]:\n", "                    # Add the style to target document\n", "                    target_doc.styles.add_style(style.name, style.type)\n", "        \n", "        print(\"🎨 Copied document styles\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy all styles: {e}\")\n", "\n", "def copy_cell_formatting(source_cell, target_cell):\n", "    \"\"\"Copy complete cell formatting including background, borders, and text formatting\"\"\"\n", "    try:\n", "        # Copy cell background/shading\n", "        source_cell_element = source_cell._tc\n", "        target_cell_element = target_cell._tc\n", "        \n", "        # Find and copy cell properties (tcPr)\n", "        source_tcPr = source_cell_element.find(qn('w:tcPr'))\n", "        if source_tcPr is not None:\n", "            # Remove existing tcPr if present\n", "            existing_tcPr = target_cell_element.find(qn('w:tcPr'))\n", "            if existing_tcPr is not None:\n", "                target_cell_element.remove(existing_tcPr)\n", "            \n", "            # Copy the complete cell properties\n", "            target_cell_element.insert(0, deepcopy(source_tcPr))\n", "        \n", "        # Copy paragraph and run formatting more thoroughly\n", "        for i, source_para in enumerate(source_cell.paragraphs):\n", "            if i < len(target_cell.paragraphs):\n", "                target_para = target_cell.paragraphs[i]\n", "                \n", "                # Copy paragraph-level formatting\n", "                source_para_element = source_para._element\n", "                target_para_element = target_para._element\n", "                \n", "                # Copy paragraph properties (pPr)\n", "                source_pPr = source_para_element.find(qn('w:pPr'))\n", "                if source_pPr is not None:\n", "                    existing_pPr = target_para_element.find(qn('w:pPr'))\n", "                    if existing_pPr is not None:\n", "                        target_para_element.remove(existing_pPr)\n", "                    target_para_element.insert(0, deepcopy(source_pPr))\n", "                \n", "                # Copy run formatting more comprehensively\n", "                for j, source_run in enumerate(source_para.runs):\n", "                    if j < len(target_para.runs):\n", "                        target_run = target_para.runs[j]\n", "                        \n", "                        # Copy run properties (rPr) at XML level\n", "                        source_run_element = source_run._element\n", "                        target_run_element = target_run._element\n", "                        \n", "                        source_rPr = source_run_element.find(qn('w:rPr'))\n", "                        if source_rPr is not None:\n", "                            existing_rPr = target_run_element.find(qn('w:rPr'))\n", "                            if existing_rPr is not None:\n", "                                target_run_element.remove(existing_rPr)\n", "                            target_run_element.insert(0, deepcopy(source_rPr))\n", "                        \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not copy cell formatting: {e}\")\n", "\n", "def copy_table_with_complete_formatting(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):\n", "    \"\"\"Copy table with complete formatting preservation including cell shading and text formatting\"\"\"\n", "    # Create a new paragraph for the heading\n", "    heading_paragraph = insert_after_paragraph.insert_paragraph_before()\n", "    heading_run = heading_paragraph.add_run(table_heading)\n", "    heading_run.bold = True\n", "    \n", "    # Get the table element and its style\n", "    tbl_element = source_table._tbl\n", "    source_table_style = None\n", "    \n", "    # Extract table style name\n", "    tblPr = tbl_element.find(qn('w:tblPr'))\n", "    if tblPr is not None:\n", "        tblStyle = tblPr.find(qn('w:tblStyle'))\n", "        if tblStyle is not None:\n", "            source_table_style = tblStyle.get(qn('w:val'))\n", "    \n", "    # Create a deep copy of the table element with all formatting\n", "    tbl_copy = deepcopy(tbl_element)\n", "    \n", "    # Insert the copied table after the heading paragraph\n", "    heading_paragraph._element.addnext(tbl_copy)\n", "    \n", "    # Enhanced formatting preservation\n", "    try:\n", "        # Find the newly inserted table in the document\n", "        target_doc = insert_after_paragraph._parent\n", "        target_table = None\n", "        \n", "        # Get the table that was just inserted\n", "        for table in target_doc.tables:\n", "            if table._tbl == tbl_copy:\n", "                target_table = table\n", "                break\n", "        \n", "        if target_table and source_table:\n", "            # Apply the table style explicitly if we found one\n", "            if source_table_style:\n", "                apply_table_style_explicitly(target_table, source_table_style)\n", "            \n", "            # Copy table-level properties more thoroughly\n", "            source_tblPr = source_table._tbl.find(qn('w:tblPr'))\n", "            if source_tblPr is not None:\n", "                target_tblPr = target_table._tbl.find(qn('w:tblPr'))\n", "                if target_tblPr is not None:\n", "                    target_table._tbl.remove(target_tblPr)\n", "                target_table._tbl.insert(0, deepcopy(source_tblPr))\n", "            \n", "            # Copy cell-by-cell formatting\n", "            for i, source_row in enumerate(source_table.rows):\n", "                if i < len(target_table.rows):\n", "                    target_row = target_table.rows[i]\n", "                    for j, source_cell in enumerate(source_row.cells):\n", "                        if j < len(target_row.cells):\n", "                            target_cell = target_row.cells[j]\n", "                            copy_cell_formatting(source_cell, target_cell)\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not apply enhanced formatting: {e}\")\n", "    \n", "    # Insert an empty paragraph after the table (acts as a line break)\n", "    p = OxmlElement('w:p')\n", "    tbl_copy.addnext(p)\n", "    \n", "    # Clear the original table tag paragraph instead of removing it\n", "    insert_after_paragraph.clear()\n", "    \n", "    # Return the paragraph after the table for proper positioning of next elements\n", "    return insert_after_paragraph._element.getnext()\n", "\n", "def remove_all_tables(doc: DocumentObject):\n", "    body = doc.element.body\n", "    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]\n", "    for tbl in tables_to_remove:\n", "        body.remove(tbl)\n", "        \n", "def extract_images(docx_path, extract_folder):\n", "    with zipfile.ZipFile(docx_path, 'r') as zip_ref:\n", "        zip_ref.extractall(extract_folder)\n", "\n", "def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:\n", "    return [para for para in doc.paragraphs if search_text in para.text]\n", "\n", "def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):\n", "    \"\"\"Copy table with heading and line break after - DEPRECATED, use copy_table_with_complete_formatting\"\"\"\n", "    copy_table_with_complete_formatting(source_table, insert_after_paragraph, table_heading)\n", "\n", "def load_table_headings(json_file_path: str):\n", "    \"\"\"Load table headings from JSON file\"\"\"\n", "    with open(json_file_path, 'r', encoding='utf-8') as f:\n", "        table_index = json.load(f)\n", "    \n", "    table_headings = {}\n", "    for table_hash, table_info in table_index.items():\n", "        table_id = table_info['id']\n", "        table_heading = table_info['heading']\n", "        table_headings[table_id] = table_heading\n", "    \n", "    return table_headings\n", "\n", "def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):\n", "    run = paragraph.insert_paragraph_before().add_run()\n", "    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed\n", "\n", "def main():\n", "    source_docx = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report.docx'\n", "    target_docx = '/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/formatted_answer.docx'\n", "    extract_folder = 'source_extracted'\n", "    \n", "    # Path for table headings\n", "    json_file_path = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report_tables_with_summaries_and_tags/table_index.json'\n", "\n", "    # Step 1: Extract images from source.docx\n", "    if os.path.exists(extract_folder):\n", "        shutil.rmtree(extract_folder)\n", "    extract_images(source_docx, extract_folder)\n", "\n", "    media_folder = os.path.join(extract_folder, 'word', 'media')\n", "    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []\n", "\n", "    # Load table headings from JSON file\n", "    table_headings = load_table_headings(json_file_path)\n", "    print(f\"📊 Loaded headings for {len(table_headings)} tables\")\n", "\n", "    source_doc = Document(source_docx)\n", "    target_doc = Document(target_docx)\n", "\n", "    # Step 1.2: Copy page setup and styles from source to target\n", "    copy_page_setup(source_doc, target_doc)\n", "    copy_document_styles(source_doc, target_doc)\n", "\n", "    # Step 1.5: Remove all existing tables from target doc\n", "    remove_all_tables(target_doc)\n", "    print(\"🗑️ Removed all existing tables from target document\")\n", "\n", "    # Step 2: Process tables with complete formatting preservation\n", "    # First, collect all table tag matches\n", "    table_matches = []\n", "    for idx, source_table in enumerate(source_doc.tables, start=1):\n", "        table_id = f\"table_{idx}\"\n", "        reference_text = f\"<TABLE_TAG_{table_id}>\"\n", "        \n", "        # Get the heading for this table from JSON, fallback to generic heading\n", "        table_heading = table_headings.get(table_id, f\"Table {idx}\")\n", "        \n", "        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)\n", "        if matched_paragraphs:\n", "            for para in matched_paragraphs:\n", "                table_matches.append((para, source_table, table_heading, reference_text, idx))\n", "        else:\n", "            print(f\"⚠️ Reference '{reference_text}' not found in target document\")\n", "    \n", "    # Process matches in order\n", "    for para, source_table, table_heading, reference_text, idx in table_matches:\n", "        next_element = copy_table_with_complete_formatting(source_table, para, table_heading)\n", "        print(f\"✅ Inserted Table {idx} with complete formatting\")\n", "\n", "    # Step 3: Process figures/images\n", "    for idx, image_file in enumerate(media_files, start=1):\n", "        reference_text = f\"Figure {idx}:\"\n", "        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)\n", "        image_path = os.path.join(media_folder, image_file)\n", "        if matched_paragraphs:\n", "            for para in matched_paragraphs:\n", "                insert_image_before_paragraph(target_doc, image_path, para)\n", "            print(f\"✅ Inserted Figure {idx}\")\n", "        else:\n", "            print(f\"⚠️ Reference '{reference_text}' not found in target document\")\n", "\n", "    # Step 4: Save output\n", "    target_doc.save('/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/table_and_image_formatted_answer.docx')\n", "    print(\"✅ All done. Saved as 'table_and_image_formatted_answer.docx'\")\n", "    print(\"🎨 Complete visual formatting preserved from source document\")\n", "    \n", "    if os.path.exists(extract_folder):\n", "        shutil.rmtree(extract_folder)\n", "        print(f\"🗑️ Cleaned up temporary folder: {extract_folder}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}