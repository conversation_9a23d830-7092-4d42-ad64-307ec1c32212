#!/usr/bin/env python3
"""
Comprehensive PDF Content Extraction with VLM

This script processes PDF documents by:
1. Taking high-resolution screenshots of all pages using PyMuPDF
2. Using a Vision Language Model (VLM) to extract ALL content from each page:
   - Complete text content with proper formatting
   - Table data in structured format
   - Detailed descriptions of images, figures, diagrams, charts, etc.
3. Outputs everything in formatted markdown for easy processing
4. Constructs the complete document from VLM-extracted content

Dependencies:
    pip install PyMuPDF Pillow openai python-dotenv
"""

import os
import sys
import base64
import json
import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import traceback

import fitz  # PyMuPDF
import pdfplumber
from PIL import Image
import io

from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from retry_on_empty_response import retry_on_empty_response

# Import the pdf_to_txt script functions
from pdf_to_txt_tables_plus_text import process_pdf_to_txt

load_dotenv()

OPENAI_MODEL_BASE_URL = os.getenv("OPENAI_MODEL_BASE_URL")
OPENAI_MODEL_API_KEY = os.getenv("OPENAI_MODEL_API_KEY")
OPENAI_MODEL_NAME = os.getenv("OPENAI_MODEL_NAME")

# OPENAI_MODEL_BASE_URL = "http://**************:45624/v1"
# OPENAI_MODEL_API_KEY = "none"
# OPENAI_MODEL_NAME = "mistralai/Mistral-Small-3.2-24B-Instruct-2506"

# Configuration
MAX_CONCURRENT_REQUESTS = 10
api_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

# Initialize OpenAI clients
async_openai_client = AsyncOpenAI(api_key=OPENAI_MODEL_API_KEY, base_url=OPENAI_MODEL_BASE_URL, timeout=180)

# Pydantic Schemas for Comprehensive Content Extraction
class ContentStatistics(BaseModel):
    """Statistics about extracted content"""
    word_count: int = Field(description="Approximate word count of extracted text", default=0)
    table_count: int = Field(description="Number of tables found", default=0)
    figure_count: int = Field(description="Number of figures/images found", default=0)
    heading_count: int = Field(description="Number of headings/sections found", default=0)

class PageProcessingStrategy(BaseModel):
    """Strategy for processing a page or page range"""
    page_range: List[int] = Field(description="List of page numbers to process together (1-indexed)")
    screenshot_type: str = Field(description="Type of screenshot: 'single' or 'multi_page'")
    contains_spanning_table: bool = Field(description="Whether this range contains a spanning table", default=False)
    table_info: Optional[Dict[str, Any]] = Field(description="Information about spanning tables", default=None)

class PageContentResult(BaseModel):
    """Schema for complete page content extraction result"""
    page_number: int = Field(description="Page number being processed")
    markdown_content: str = Field(description="Complete page content in formatted markdown including text, tables, and figure descriptions")
    content_statistics: ContentStatistics = Field(description="Statistics about the extracted content")
    extraction_notes: List[str] = Field(description="Any notes about extraction challenges or special content", default=[])
    confidence_score: float = Field(description="Confidence in the extraction quality (0-10 scale)", ge=0.0, le=10.0, default=5.0)
    processing_time: Optional[float] = Field(description="Time taken to process this page in seconds", default=None)

class DocumentExtractionResult(BaseModel):
    """Schema for complete document extraction result"""
    document_name: str = Field(description="Name of the processed document")
    total_pages: int = Field(description="Total number of pages in the document")
    page_results: List[PageContentResult] = Field(description="Extraction results for each page")
    combined_markdown: str = Field(description="Complete document in markdown format", default="")
    overall_statistics: ContentStatistics = Field(description="Overall document statistics")
    processing_summary: Dict[str, Any] = Field(description="Summary of processing statistics and metadata", default={})

class ContentBounds(BaseModel):
    """Bounding box for content regions"""
    x0: float = Field(description="Left coordinate")
    y0: float = Field(description="Top coordinate") 
    x1: float = Field(description="Right coordinate")
    y1: float = Field(description="Bottom coordinate")
    page: int = Field(description="Page number (1-indexed)")

class SimpleScreenshotStrategy(BaseModel):
    """Simple strategy for table-aware screenshots with coordinate-based cropping"""
    page_range: List[int] = Field(description="Pages to include in this screenshot")
    screenshot_type: str = Field(description="'single_page', 'table_extended', or 'coordinate_crop'")
    crop_bounds: Optional[ContentBounds] = Field(description="Exact crop coordinates if using coordinate_crop", default=None)
    spanning_table_info: Optional[Dict[str, Any]] = Field(description="Info about spanning table if any", default=None)

def create_coordinate_based_strategies(total_pages: int, table_analysis: Dict[str, Any]) -> List[SimpleScreenshotStrategy]:
    """Create coordinate-based screenshot strategies
    
    Rules:
    1. Use table coordinates to determine crop boundaries
    2. If table spans to next page, extend crop to include complete table
    3. Start next crop from where previous table ended
    
    Args:
        total_pages: Total pages in document
        table_analysis: Table analysis from pdf_to_txt preprocessing
        
    Returns:
        List of SimpleScreenshotStrategy objects with coordinate-based cropping
    """
    print("\nCreating coordinate-based screenshot strategies...")
    
    if not table_analysis:
        # No table analysis, fall back to single pages
        strategies = []
        for page in range(1, total_pages + 1):
            strategies.append(SimpleScreenshotStrategy(
                page_range=[page],
                screenshot_type="single_page"
            ))
        print(f"  No table analysis - created {len(strategies)} single-page strategies")
        return strategies
    
    # Get page table mapping and spanning info
    page_table_mapping = table_analysis.get('page_table_mapping', {})
    continuation_chains = table_analysis.get('continuation_chains', [])
    
    # Create a mapping of page -> spanning table info
    page_to_spanning_table = {}
    for chain in continuation_chains:
        for page_num in chain['pages']:
            page_to_spanning_table[page_num] = {
                'table_id': chain['table_id'],
                'all_pages': sorted(chain['pages']),
                'is_start': page_num == min(chain['pages']),
                'is_end': page_num == max(chain['pages'])
            }
    
    strategies = []
    processed_pages = set()
    current_page = 1
    
    while current_page <= total_pages:
        if current_page in processed_pages:
            current_page += 1
            continue
        
        print(f"  Processing page {current_page}...")
        
        # Check if this page has a spanning table that starts here
        if current_page in page_to_spanning_table:
            spanning_info = page_to_spanning_table[current_page]
            
            if spanning_info['is_start']:
                # Create coordinate-based strategy for spanning table
                table_pages = spanning_info['all_pages']
                table_id = spanning_info['table_id']
                
                # Mark for coordinate cropping - actual coordinates calculated during screenshot
                strategy = SimpleScreenshotStrategy(
                    page_range=table_pages,
                    screenshot_type="coordinate_crop",
                    crop_bounds=None,  # Will be calculated in screenshot function
                    spanning_table_info={
                        'table_id': table_id,
                        'pages': table_pages,
                        'reason': 'spanning_table'
                    }
                )
                strategies.append(strategy)
                processed_pages.update(table_pages)
                
                print(f"    Coordinate crop for spanning table {table_id}: pages {table_pages}")
                current_page = max(table_pages) + 1
            else:
                # Middle or end of spanning table - already processed
                current_page += 1
        else:
            # Check if page has tables for coordinate-based cropping
            page_tables = page_table_mapping.get(current_page, [])
            
            if page_tables:
                # Create coordinate-based crop for tables on this page
                strategy = SimpleScreenshotStrategy(
                    page_range=[current_page],
                    screenshot_type="coordinate_crop",
                    crop_bounds=None,  # Will be calculated in screenshot function
                    spanning_table_info={
                        'table_count': len(page_tables),
                        'reason': 'page_tables'
                    }
                )
                strategies.append(strategy)
                
                print(f"    Coordinate crop for page {current_page}: {len(page_tables)} tables")
            else:
                # No tables - regular single page
                strategy = SimpleScreenshotStrategy(
                    page_range=[current_page],
                    screenshot_type="single_page"
                )
                strategies.append(strategy)
                print(f"    Single page: {current_page} (no tables)")
            
            processed_pages.add(current_page)
            current_page += 1
    
    print(f"✓ Created {len(strategies)} coordinate-based strategies")
    return strategies

def _get_spanning_table_bounds(table_pages: List[int], page_table_mapping: Dict, table_id: str) -> ContentBounds:
    """Get coordinate bounds for a spanning table across multiple pages"""
    
    # Find the specific table across all pages
    all_positions = []
    page_widths = []
    
    for page_num in table_pages:
        page_tables = page_table_mapping.get(page_num, [])
        for table_info in page_tables:
            if table_info['table_id'] == table_id:
                position = table_info.get('position', {})
                all_positions.append({
                    'page': page_num,
                    'top': position.get('top', 0),
                    'bottom': position.get('bottom', 100),
                    'x0': position.get('x0', 0),
                    'x1': position.get('x1', 600)  # fallback width
                })
                page_widths.append(position.get('x1', 600))
    
    if not all_positions:
        # Fallback if no position info found
        return ContentBounds(x0=0, y0=0, x1=600, y1=800, page=table_pages[0])
    
    # Calculate bounds across all pages
    min_x0 = min(pos['x0'] for pos in all_positions)
    max_x1 = max(pos['x1'] for pos in all_positions)
    
    # For spanning tables, crop from start of first table to end of last table
    first_page_pos = next(pos for pos in all_positions if pos['page'] == min(table_pages))
    last_page_pos = next(pos for pos in all_positions if pos['page'] == max(table_pages))
    
    # Start from slightly above first table, end slightly below last table
    y0 = max(0, first_page_pos['top'] - 20)  # Add some context above
    y1 = last_page_pos['bottom'] + 20  # Add some context below
    
    return ContentBounds(
        x0=min_x0,
        y0=y0,
        x1=max_x1,
        y1=y1,
        page=table_pages[0]  # Primary page for reference
    )

def _get_page_table_bounds(page_num: int, page_tables: List[Dict]) -> ContentBounds:
    """Get coordinate bounds for all tables on a single page"""
    
    if not page_tables:
        # Fallback to full page if no tables
        return ContentBounds(x0=0, y0=0, x1=600, y1=800, page=page_num)
    
    # Get bounds of all tables on this page
    all_positions = []
    for table_info in page_tables:
        position = table_info.get('position', {})
        all_positions.append({
            'top': position.get('top', 0),
            'bottom': position.get('bottom', 100),
            'x0': position.get('x0', 0),
            'x1': position.get('x1', 600)
        })
    
    # Calculate combined bounds
    min_x0 = min(pos['x0'] for pos in all_positions)
    max_x1 = max(pos['x1'] for pos in all_positions)
    min_top = min(pos['top'] for pos in all_positions)
    max_bottom = max(pos['bottom'] for pos in all_positions)
    
    # Add some context padding
    y0 = max(0, min_top - 30)  # Context above first table
    y1 = max_bottom + 30  # Context below last table
    
    return ContentBounds(
        x0=min_x0,
        y0=y0,
        x1=max_x1,
        y1=y1,
        page=page_num
    )

# Preprocessing Functions
# Note: Table analysis is now handled directly by pdf_to_txt_tables_plus_text.py

def run_table_preprocessing(pdf_path: str, temp_dir: str) -> Tuple[str, Dict[str, Any]]:
    """Run the pdf_to_txt preprocessing to extract table information
    
    Args:
        pdf_path: Path to the input PDF
        temp_dir: Temporary directory for intermediate files
        
    Returns:
        Tuple of (table_index_path, table_analysis_dict)
    """
    try:
        # Create temporary paths
        temp_txt_path = os.path.join(temp_dir, "temp_extracted.txt")
        temp_tables_dir = os.path.join(temp_dir, "temp_tables")
        
        print("Running table preprocessing...")
        
        # Run the pdf_to_txt extraction with analysis
        extracted_tables, table_analysis = process_pdf_to_txt(pdf_path, temp_txt_path, temp_tables_dir)
        
        # Get the table index path
        table_index_path = os.path.join(temp_tables_dir, "table_index.json")
        
        print(f"Preprocessing complete:")
        print(f"  Spanning tables found: {len(table_analysis['spanning_tables'])}")
        print(f"  Single page tables: {len(table_analysis['single_page_tables'])}")
        print(f"  Pages with tables: {len(table_analysis['page_table_mapping'])}")
        
        return table_index_path, table_analysis
        
    except Exception as e:
        print(f"Error in table preprocessing: {e}")
        traceback.print_exc()
        return "", {}

def create_screenshot_strategy_from_analysis(total_pages: int, table_analysis: Dict[str, Any], max_multi_page_range: int = 5) -> List[PageProcessingStrategy]:
    """Create a strategy for taking screenshots based on table analysis from pdf_to_txt preprocessing
    
    Args:
        total_pages: Total number of pages in the document
        table_analysis: Analysis results from pdf_to_txt_tables_plus_text
        max_multi_page_range: Maximum number of pages to include in a single multi-page screenshot
        
    Returns:
        List of PageProcessingStrategy objects in sequential page order
    """
    if not table_analysis:
        # Fallback to single-page processing
        print("No table analysis available, using single-page strategy for all pages")
        return [PageProcessingStrategy(
            page_range=[i],
            screenshot_type="single",
            contains_spanning_table=False
        ) for i in range(1, total_pages + 1)]
    
    page_table_mapping = table_analysis.get('page_table_mapping', {})
    spanning_tables = table_analysis.get('spanning_tables', [])
    continuation_chains = table_analysis.get('continuation_chains', [])
    
    print(f"Creating screenshot strategy from table analysis:")
    print(f"  Pages with tables: {len(page_table_mapping)}")
    print(f"  Spanning tables: {len(spanning_tables)}")
    print(f"  Continuation chains: {len(continuation_chains)}")
    
    # Create a mapping of pages to their continuation chains for quick lookup
    page_to_chain = {}
    for chain in continuation_chains:
        for page_num in chain['pages']:
            page_to_chain[page_num] = chain
    
    strategies = []
    processed_pages = set()
    
    # Process pages in sequential order
    current_page = 1
    while current_page <= total_pages:
        if current_page in processed_pages:
            print(f"  Skipping page {current_page} (already processed)")
            current_page += 1
            continue
        
        print(f"  Processing page {current_page}...")
        
        # Check if this page is part of a continuation chain (spanning table)
        if current_page in page_to_chain:
            chain = page_to_chain[current_page]
            table_id = chain['table_id']
            chain_pages = sorted(chain['pages'])
            
            print(f"    Page {current_page} is part of chain {table_id} (pages {chain_pages})")
            
            # Find unprocessed pages in this chain
            unprocessed_chain_pages = [p for p in chain_pages if p not in processed_pages]
            print(f"    Unprocessed pages in chain: {unprocessed_chain_pages}")
            
            if unprocessed_chain_pages:
                # Only process if we're at the start of the unprocessed portion
                if current_page == min(unprocessed_chain_pages):
                    print(f"    Starting processing of chain {table_id} from page {current_page}")
                    # Start with the unprocessed continuation chain pages
                    extended_pages = unprocessed_chain_pages.copy()
                    
                    # Extend range if there are additional tables on nearby pages after the chain
                    last_chain_page = max(unprocessed_chain_pages)
                    check_page = last_chain_page + 1
                    
                    while (check_page <= total_pages and 
                           check_page not in processed_pages and 
                           check_page not in page_to_chain and  # Don't extend into another chain
                           len(extended_pages) < max_multi_page_range):
                        
                        if check_page in page_table_mapping:
                            tables_on_page = page_table_mapping[check_page]
                            # Extend if this page has tables
                            if tables_on_page:
                                extended_pages.append(check_page)
                                print(f"  Extended chain {table_id} to include page {check_page} ({len(tables_on_page)} tables)")
                                check_page += 1
                            else:
                                break  # No tables, natural break
                        else:
                            break  # No tables, natural break
                    
                    # Gather all table info for this range
                    all_tables_in_range = []
                    page_mapping = {}
                    
                    for page_num in extended_pages:
                        page_mapping[page_num] = []
                        if page_num in page_table_mapping:
                            for table_info in page_table_mapping[page_num]:
                                table_id_on_page = table_info['table_id']
                                page_mapping[page_num].append(table_id_on_page)
                                
                                # Add to all_tables_in_range if not already present
                                if not any(t.get('table_id') == table_id_on_page for t in all_tables_in_range):
                                    all_tables_in_range.append({
                                        'table_id': table_id_on_page,
                                        'pages': table_info['total_pages'],
                                        'char_count': table_info['char_count'],
                                        'is_spanning': table_info['is_spanning'],
                                        'position_info': table_info.get('position', {})
                                    })
                    
                    strategies.append(PageProcessingStrategy(
                        page_range=extended_pages,
                        screenshot_type="multi_page",
                        contains_spanning_table=True,
                        table_info={
                            "tables": all_tables_in_range,
                            "page_mapping": page_mapping,
                            "total_tables": len(all_tables_in_range),
                            "primary_spanning_table": table_id,
                            "reason": "continuation_chain"
                        }
                    ))
                    
                    processed_pages.update(extended_pages)
                    total_table_count = len(all_tables_in_range)
                    print(f"Multi-page strategy: pages {extended_pages} ({total_table_count} tables, spanning chain {table_id})")
                    
                    # Jump to after the extended range
                    current_page = max(extended_pages) + 1
                else:
                    # We're in the middle of a chain that was already processed, skip
                    print(f"    Page {current_page} is in middle of already processed chain, skipping")
                    current_page += 1
            else:
                # All pages in this chain are already processed, move to next page
                print(f"    All pages in chain {table_id} already processed, moving to next page")
                current_page += 1
            continue
        
        # Check if this page and subsequent pages have tables that should be grouped
        if current_page in page_table_mapping:
            current_page_tables = page_table_mapping[current_page]
            
            if current_page_tables:
                # Look for consecutive pages with tables (not part of spanning tables)
                consecutive_pages = [current_page]
                check_page = current_page + 1
                
                while (check_page <= total_pages and 
                       check_page not in processed_pages and 
                       check_page not in page_to_chain and  # Don't include pages that are part of spanning tables
                       len(consecutive_pages) < max_multi_page_range):
                    
                    if check_page in page_table_mapping and page_table_mapping[check_page]:
                        consecutive_pages.append(check_page)
                        print(f"  Found consecutive table page {check_page} with {len(page_table_mapping[check_page])} tables")
                        check_page += 1
                    else:
                        break  # No tables, natural break
                
                # Create multi-page strategy if we found multiple consecutive pages
                if len(consecutive_pages) > 1:
                    all_tables = []
                    page_mapping = {}
                    
                    for page_num in consecutive_pages:
                        page_tables_list = page_table_mapping.get(page_num, [])
                        all_tables.extend(page_tables_list)
                        page_mapping[page_num] = [t['table_id'] for t in page_tables_list]
                    
                    strategies.append(PageProcessingStrategy(
                        page_range=consecutive_pages,
                        screenshot_type="multi_page",
                        contains_spanning_table=False,
                        table_info={
                            "tables": [{'table_id': t['table_id'], 'pages': t['total_pages'], 'char_count': t['char_count']} for t in all_tables],
                            "page_mapping": page_mapping,
                            "total_tables": len(all_tables),
                            "reason": "consecutive_table_pages"
                        }
                    ))
                    
                    processed_pages.update(consecutive_pages)
                    current_page = max(consecutive_pages) + 1
                    print(f"Multi-page strategy: pages {consecutive_pages} ({len(all_tables)} tables across consecutive pages)")
                    continue
        
        # Single page strategy
        table_info = {}
        if current_page in page_table_mapping:
            page_tables = page_table_mapping[current_page]
            table_info = {
                "tables": [{'table_id': t['table_id'], 'pages': t['total_pages']} for t in page_tables],
                "total_tables": len(page_tables)
            }
        else:
            table_info = {"total_tables": 0}
        
        strategies.append(PageProcessingStrategy(
            page_range=[current_page],
            screenshot_type="single",
            contains_spanning_table=False,
            table_info=table_info
        ))
        
        processed_pages.add(current_page)
        table_count = len(page_table_mapping.get(current_page, []))
        print(f"Single page strategy: page {current_page} ({table_count} tables)")
        current_page += 1
    
    # Verify all strategies are in correct order
    prev_max_page = 0
    for i, strategy in enumerate(strategies):
        min_page = min(strategy.page_range)
        max_page = max(strategy.page_range)
        if min_page <= prev_max_page:
            print(f"WARNING: Strategy {i+1} pages {strategy.page_range} overlaps with previous strategies!")
        prev_max_page = max_page
    
    print(f"Created {len(strategies)} strategies covering all {total_pages} pages in sequential order")
    return strategies

class PDFContentExtractor:
    """Comprehensive PDF content extractor using Vision Language Model with smart preprocessing"""
    
    def __init__(self, pdf_path: str, output_dir: str = None, enable_preprocessing: bool = True):
        """Initialize the PDF content extractor
        
        Args:
            pdf_path: Path to the input PDF file
            output_dir: Directory to save outputs (defaults to pdf_path directory)
            enable_preprocessing: Whether to run table preprocessing for smart screenshots
        """
        self.pdf_path = Path(pdf_path)
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        if output_dir is None:
            output_dir = self.pdf_path.parent / f"{self.pdf_path.stem}_extraction"
        
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        self.screenshots_dir = self.output_dir / "page_screenshots"
        self.screenshots_dir.mkdir(exist_ok=True)
        
        self.markdown_dir = self.output_dir / "page_markdown"
        self.markdown_dir.mkdir(exist_ok=True)
        
        self.temp_dir = self.output_dir / "temp_preprocessing"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Preprocessing attributes
        self.enable_preprocessing = enable_preprocessing
        self.table_analysis: Dict[str, Any] = {}
        self.processing_strategies: List[PageProcessingStrategy] = []
        self.table_index_path: str = ""
        
        # Simple table-aware screenshot strategies
        self.simple_strategies: List[SimpleScreenshotStrategy] = []
        self.use_simple_approach: bool = True  # Use simple table-aware approach by default
        
        print(f"PDF Content Extractor initialized:")
        print(f"  Input: {self.pdf_path}")
        print(f"  Output: {self.output_dir}")
        print(f"  Preprocessing: {'Enabled' if enable_preprocessing else 'Disabled'}")

    def run_preprocessing(self) -> bool:
        """Run table preprocessing to identify spanning tables and create screenshot strategy
        
        Returns:
            True if preprocessing successful, False otherwise
        """
        if not self.enable_preprocessing:
            print("Preprocessing disabled, using single-page strategy for all pages")
            return True
        
        try:
            print("\n" + "="*60)
            print("PHASE 1: TABLE PREPROCESSING")
            print("="*60)
            
            # Run table extraction preprocessing
            self.table_index_path, self.table_analysis = run_table_preprocessing(
                str(self.pdf_path), str(self.temp_dir)
            )
            
            # Get total pages from PDF
            with fitz.open(str(self.pdf_path)) as pdf_doc:
                total_pages = len(pdf_doc)
            
            # Create coordinate-based table-aware strategies
            if self.use_simple_approach:
                try:
                    self.simple_strategies = create_coordinate_based_strategies(total_pages, self.table_analysis)
                    
                    print(f"\nSimple Table-Aware Strategy Results:")
                    print(f"  Total strategies: {len(self.simple_strategies)}")
                    
                    # Print strategy summary
                    single_page_count = sum(1 for s in self.simple_strategies if s.screenshot_type == "single_page")
                    table_extended_count = sum(1 for s in self.simple_strategies if s.screenshot_type == "table_extended")
                    
                    print(f"  Single page screenshots: {single_page_count}")
                    print(f"  Table-extended screenshots: {table_extended_count}")
                    
                    # Show details of table-extended screenshots
                    for strategy in self.simple_strategies:
                        if strategy.screenshot_type == "table_extended":
                            table_id = strategy.spanning_table_info['table_id']
                            pages = strategy.spanning_table_info['pages']
                            print(f"    Extended: {table_id} spans pages {pages}")
                    
                except Exception as e:
                    print(f"Warning: Simple strategy creation failed: {e}")
                    print("Falling back to legacy page-based approach")
                    self.use_simple_approach = False
            
            # Fallback to legacy approach if simple approach fails
            if not self.use_simple_approach:
                print("Creating legacy page-based strategies...")
                self.processing_strategies = create_screenshot_strategy_from_analysis(total_pages, self.table_analysis)
                
                single_page_count = sum(1 for s in self.processing_strategies if s.screenshot_type == "single")
                multi_page_count = sum(1 for s in self.processing_strategies if s.screenshot_type == "multi_page")
                print(f"  Legacy approach: {single_page_count} single + {multi_page_count} multi-page")
            
            print(f"\nPreprocessing Results:")
            print(f"  Total pages: {total_pages}")
            print(f"  Spanning tables found: {len(self.table_analysis.get('spanning_tables', []))}")
            
            return True
            
        except Exception as e:
            print(f"Error in preprocessing: {e}")
            traceback.print_exc()
            # Fallback to single-page processing
            print("Falling back to single-page processing for all pages")
            self.enable_preprocessing = False
            return False

    def take_strategic_screenshots(self, dpi: int = 150) -> List[Tuple[str, PageProcessingStrategy]]:
        """Take screenshots based on processing strategies (simple table-aware or legacy page-based)
        
        Args:
            dpi: Resolution for screenshots
            
        Returns:
            List of tuples (screenshot_path, strategy)
        """
        if self.use_simple_approach and self.simple_strategies:
            # Use new simple table-aware approach
            return self.take_simple_table_aware_screenshots(dpi)
        elif self.processing_strategies:
            # Use legacy page-based approach
            return self.take_legacy_page_screenshots(dpi)
        else:
            # Fallback to single-page processing if no strategies
            return self.take_fallback_screenshots(dpi)

    def take_simple_table_aware_screenshots(self, dpi: int = 150) -> List[Tuple[str, PageProcessingStrategy]]:
        """Take screenshots using simple table-aware approach
        
        Args:
            dpi: Resolution for screenshots
            
        Returns:
            List of tuples (screenshot_path, converted_strategy) for compatibility
        """
        screenshot_data = []
        
        try:
            pdf_document = fitz.open(str(self.pdf_path))
            
            print(f"\n" + "="*60)
            print("PHASE 2: SIMPLE TABLE-AWARE SCREENSHOTS")
            print("="*60)
            
            for strategy_idx, simple_strategy in enumerate(self.simple_strategies):
                if simple_strategy.screenshot_type == "single_page":
                    # Single page screenshot
                    page_num = simple_strategy.page_range[0]
                    screenshot_path = self._take_single_page_screenshot(
                        pdf_document, page_num, dpi, strategy_idx
                    )
                    screenshot_type = "single_page"
                    
                elif simple_strategy.screenshot_type == "coordinate_crop":
                    # Coordinate-based crop screenshot
                    screenshot_path = self._take_coordinate_crop_screenshot(
                        pdf_document, simple_strategy, dpi, strategy_idx
                    )
                    screenshot_type = "coordinate_crop"
                    
                elif simple_strategy.screenshot_type == "table_extended":
                    # Multi-page screenshot for spanning table (legacy fallback)
                    screenshot_path = self._take_multi_page_screenshot(
                        pdf_document, simple_strategy.page_range, dpi, strategy_idx
                    )
                    screenshot_type = "table_extended"
                    
                else:
                    print(f"Unknown simple strategy type: {simple_strategy.screenshot_type}")
                    continue
                
                if screenshot_path:
                    # Convert SimpleScreenshotStrategy to PageProcessingStrategy for compatibility
                    page_strategy = PageProcessingStrategy(
                        page_range=simple_strategy.page_range,
                        screenshot_type=screenshot_type,
                        contains_spanning_table=simple_strategy.screenshot_type == "table_extended",
                        table_info={
                            "simple_strategy": True,
                            "original_type": simple_strategy.screenshot_type,
                            "spanning_table_info": simple_strategy.spanning_table_info
                        }
                    )
                    
                    screenshot_data.append((screenshot_path, page_strategy))
                    
                    if simple_strategy.screenshot_type == "single_page":
                        print(f"  ✓ Single page screenshot {strategy_idx + 1}: page {simple_strategy.page_range[0]}")
                    elif simple_strategy.screenshot_type == "coordinate_crop":
                        if len(simple_strategy.page_range) > 1:
                            table_id = simple_strategy.spanning_table_info.get('table_id', 'spanning_table')
                            print(f"  ✓ Coordinate crop {strategy_idx + 1}: {table_id} (pages {simple_strategy.page_range})")
                        else:
                            table_count = simple_strategy.spanning_table_info.get('table_count', 0)
                            print(f"  ✓ Coordinate crop {strategy_idx + 1}: page {simple_strategy.page_range[0]} ({table_count} tables)")
                    else:
                        table_id = simple_strategy.spanning_table_info.get('table_id', 'table')
                        print(f"  ✓ Table-extended screenshot {strategy_idx + 1}: {table_id} (pages {simple_strategy.page_range})")
            
            pdf_document.close()
            print(f"✓ All {len(screenshot_data)} simple table-aware screenshots saved")
            
        except Exception as e:
            print(f"Error taking simple table-aware screenshots: {e}")
            traceback.print_exc()
        
        return screenshot_data

    def take_legacy_page_screenshots(self, dpi: int = 150) -> List[Tuple[str, PageProcessingStrategy]]:
        """Take screenshots using legacy page-based approach
        
        Args:
            dpi: Resolution for screenshots
            
        Returns:
            List of tuples (screenshot_path, strategy)
        """
        screenshot_data = []
        
        try:
            pdf_document = fitz.open(str(self.pdf_path))
            
            print(f"\n" + "="*60)
            print("PHASE 2: LEGACY PAGE SCREENSHOTS")
            print("="*60)
            
            for strategy_idx, strategy in enumerate(self.processing_strategies):
                if strategy.screenshot_type == "single":
                    screenshot_path = self._take_single_page_screenshot(
                        pdf_document, strategy.page_range[0], dpi, strategy_idx
                    )
                elif strategy.screenshot_type == "multi_page":
                    screenshot_path = self._take_multi_page_screenshot(
                        pdf_document, strategy.page_range, dpi, strategy_idx
                    )
                else:
                    print(f"Unknown screenshot type: {strategy.screenshot_type}")
                    continue
                
                if screenshot_path:
                    screenshot_data.append((screenshot_path, strategy))
                    print(f"  ✓ Screenshot {strategy_idx + 1}: {strategy.screenshot_type} for pages {strategy.page_range}")
            
            pdf_document.close()
            print(f"✓ All {len(screenshot_data)} strategic screenshots saved")
            
        except Exception as e:
            print(f"Error taking strategic screenshots: {e}")
            traceback.print_exc()
        
        return screenshot_data

    def _take_coordinate_crop_screenshot(self, pdf_document, simple_strategy: SimpleScreenshotStrategy, dpi: int, strategy_idx: int) -> str:
        """Take a coordinate-based cropped screenshot using real PDF coordinates
        
        Args:
            pdf_document: Open PDF document
            simple_strategy: SimpleScreenshotStrategy with page range info
            dpi: Resolution for screenshot
            strategy_idx: Index for filename
            
        Returns:
            Path to saved screenshot
        """
        try:
            page_range = simple_strategy.page_range
            
            if len(page_range) == 1:
                # Single page coordinate crop - get real coordinates from PDF
                page_num = page_range[0]
                crop_bounds = _get_page_table_bounds_from_pdf(str(self.pdf_path), page_num)
                
                page = pdf_document[page_num - 1]  # Convert to 0-indexed
                
                # Create transformation matrix for desired DPI
                mat = fitz.Matrix(dpi/72, dpi/72)
                
                # Define crop rectangle in PDF coordinates
                crop_rect = fitz.Rect(crop_bounds.x0, crop_bounds.y0, crop_bounds.x1, crop_bounds.y1)
                
                # Ensure crop rectangle is within page bounds
                page_rect = page.rect
                crop_rect = crop_rect.intersect(page_rect)
                
                if crop_rect.is_empty:
                    print(f"Warning: Crop rectangle is empty for page {page_num}, using full page")
                    crop_rect = page_rect
                
                # Get pixmap for the cropped region
                pix = page.get_pixmap(matrix=mat, clip=crop_rect)
                
                # Create filename with coordinate info
                filename = f"page_{page_num:03d}_coord_crop_table.png"
                screenshot_path = self.screenshots_dir / filename
                
                # Save the cropped screenshot
                pix.save(str(screenshot_path))
                
                print(f"    Cropped page {page_num}: ({crop_rect.width:.0f}x{crop_rect.height:.0f}px)")
                
                return str(screenshot_path)
                
            else:
                # Multi-page coordinate crop (spanning table) - get real coordinates
                table_id = simple_strategy.spanning_table_info.get('table_id', 'spanning_table')
                crop_bounds = _get_spanning_table_bounds_from_pdf(str(self.pdf_path), page_range, table_id)
                
                return self._take_multi_page_coordinate_crop_with_bounds(pdf_document, simple_strategy, crop_bounds, dpi, strategy_idx)
                
        except Exception as e:
            pages_str = ",".join(str(p) for p in simple_strategy.page_range)
            print(f"Error taking coordinate crop screenshot for pages {pages_str}: {e}")
            traceback.print_exc()
            return ""

    def _take_multi_page_coordinate_crop_with_bounds(self, pdf_document, simple_strategy: SimpleScreenshotStrategy, crop_bounds: ContentBounds, dpi: int, strategy_idx: int) -> str:
        """Take a multi-page coordinate crop for spanning tables using provided bounds
        
        This crops each page according to table boundaries and combines them
        """
        try:
            from PIL import Image
            
            page_range = simple_strategy.page_range
            
            # Create transformation matrix
            mat = fitz.Matrix(dpi/72, dpi/72)
            
            # Process each page and crop according to table bounds
            page_images = []
            total_height = 0
            max_width = 0
            
            for i, page_num in enumerate(page_range):
                page = pdf_document[page_num - 1]
                page_rect = page.rect
                
                if i == 0:
                    # First page: crop from table start to page bottom (or table end if single page)
                    if len(page_range) == 1:
                        crop_rect = fitz.Rect(crop_bounds.x0, crop_bounds.y0, crop_bounds.x1, crop_bounds.y1)
                    else:
                        crop_rect = fitz.Rect(crop_bounds.x0, crop_bounds.y0, crop_bounds.x1, page_rect.height)
                        
                elif i == len(page_range) - 1:
                    # Last page: crop from page top to table end
                    crop_rect = fitz.Rect(crop_bounds.x0, 0, crop_bounds.x1, crop_bounds.y1)
                else:
                    # Middle pages: crop the table area (usually full width for spanning tables)
                    crop_rect = fitz.Rect(crop_bounds.x0, 0, crop_bounds.x1, page_rect.height)
                
                # Ensure crop is within page bounds
                crop_rect = crop_rect.intersect(page_rect)
                
                if crop_rect.is_empty:
                    print(f"Warning: Empty crop on page {page_num}, using full page")
                    crop_rect = page_rect
                
                # Get pixmap for this page's crop
                pix = page.get_pixmap(matrix=mat, clip=crop_rect)
                
                # Convert to PIL Image
                img_data = pix.tobytes("ppm")
                page_img = Image.open(io.BytesIO(img_data))
                
                page_images.append(page_img)
                total_height += page_img.height
                max_width = max(max_width, page_img.width)
                
                print(f"    Page {page_num} crop: ({crop_rect.width:.0f}x{crop_rect.height:.0f}px)")
            
            # Combine all page images vertically
            combined_img = Image.new('RGB', (max_width, total_height), 'white')
            
            current_y = 0
            for page_img in page_images:
                combined_img.paste(page_img, (0, current_y))
                current_y += page_img.height
            
            # Save combined image
            table_id = simple_strategy.spanning_table_info.get('table_id', 'spanning')
            page_range_str = "_".join(str(p) for p in page_range)
            first_page = min(page_range)
            screenshot_path = self.screenshots_dir / f"page_{first_page:03d}_coord_crop_spanning_{table_id}.png"
            combined_img.save(str(screenshot_path))
            
            print(f"    Combined crop: {max_width}x{total_height}px across {len(page_range)} pages")
            
            return str(screenshot_path)
            
        except Exception as e:
            table_id = simple_strategy.spanning_table_info.get('table_id', 'unknown')
            print(f"Error taking multi-page coordinate crop for {table_id}: {e}")
            traceback.print_exc()
            return ""

    def _take_single_page_screenshot(self, pdf_document, page_num: int, dpi: int, strategy_idx: int) -> str:
        """Take a screenshot of a single page"""
        try:
            page = pdf_document[page_num - 1]  # Convert to 0-indexed
            
            # Create a transformation matrix for the desired DPI
            mat = fitz.Matrix(dpi/72, dpi/72)
            
            # Render page to pixmap
            pix = page.get_pixmap(matrix=mat)
            
            # Save as PNG
            screenshot_path = self.screenshots_dir / f"page_{page_num:03d}_single.png"
            pix.save(str(screenshot_path))
            
            return str(screenshot_path)
            
        except Exception as e:
            print(f"Error taking single page screenshot for page {page_num}: {e}")
            return ""

    def _take_multi_page_screenshot(self, pdf_document, page_range: List[int], dpi: int, strategy_idx: int) -> str:
        """Take a combined screenshot of multiple pages"""
        try:
            from PIL import Image
            
            # Create transformation matrix
            mat = fitz.Matrix(dpi/72, dpi/72)
            
            # Get pixmaps for all pages
            pixmaps = []
            total_height = 0
            max_width = 0
            
            for page_num in page_range:
                page = pdf_document[page_num - 1]  # Convert to 0-indexed
                pix = page.get_pixmap(matrix=mat)
                pixmaps.append(pix)
                total_height += pix.height
                max_width = max(max_width, pix.width)
            
            # Create combined image
            combined_img = Image.new('RGB', (max_width, total_height), 'white')
            
            current_y = 0
            for pix in pixmaps:
                # Convert pixmap to PIL Image
                img_data = pix.tobytes("ppm")
                page_img = Image.open(io.BytesIO(img_data))
                
                # Paste into combined image
                combined_img.paste(page_img, (0, current_y))
                current_y += pix.height
            
            # Save combined image
            first_page = min(page_range)
            page_range_str = "_".join(str(p) for p in page_range)
            screenshot_path = self.screenshots_dir / f"page_{first_page:03d}_multi_pages_{page_range_str}.png"
            combined_img.save(str(screenshot_path))
            
            return str(screenshot_path)
            
        except Exception as e:
            print(f"Error taking multi-page screenshot for pages {page_range}: {e}")
            traceback.print_exc()
            return ""

    def take_fallback_screenshots(self, dpi: int = 150) -> List[Tuple[str, PageProcessingStrategy]]:
        """Fallback method for single-page screenshots when preprocessing fails"""
        screenshot_data = []
        
        try:
            pdf_document = fitz.open(str(self.pdf_path))
            total_pages = len(pdf_document)
            
            print(f"Taking fallback single-page screenshots for {total_pages} pages...")
            
            for page_num in range(total_pages):
                page = pdf_document[page_num]
                
                # Create a transformation matrix for the desired DPI
                mat = fitz.Matrix(dpi/72, dpi/72)
                
                # Render page to pixmap
                pix = page.get_pixmap(matrix=mat)
                
                # Save as PNG
                screenshot_path = self.screenshots_dir / f"fallback_page_{page_num + 1:03d}.png"
                pix.save(str(screenshot_path))
                
                # Create simple strategy for this page
                strategy = PageProcessingStrategy(
                    page_range=[page_num + 1],
                    screenshot_type="single",
                    contains_spanning_table=False
                )
                
                screenshot_data.append((str(screenshot_path), strategy))
                print(f"  Screenshot saved: fallback_page_{page_num + 1:03d}.png")
            
            pdf_document.close()
            print(f"✓ All {total_pages} fallback screenshots saved")
            
        except Exception as e:
            print(f"Error taking fallback screenshots: {e}")
            traceback.print_exc()
        
        return screenshot_data

    def encode_image_to_base64(self, image_path: str) -> str:
        """Encode image to base64 string"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    async def extract_content_with_vllm(self, screenshot_path: str, strategy: PageProcessingStrategy) -> PageContentResult:
        """Extract complete content using VLLM (single or multi-page)
        
        Args:
            screenshot_path: Path to the screenshot
            strategy: PageProcessingStrategy containing page range and context
            
        Returns:
            PageContentResult with complete content in markdown
        """
        # Enhanced system prompt using Anthropic's best practices
        system_prompt = '''
You are an expert document processing system designed to extract ALL content from pharmaceutical and medical research document pages. 

Your task is to analyze the provided page image systematically and extract EVERYTHING in properly formatted markdown.

CRITICAL INSTRUCTIONS:

1. **THINKING PROCESS**: First, think through your analysis step-by-step within <thinking> tags
2. **STRUCTURED OUTPUT**: Provide your final markdown content within <content> tags
3. **COMPREHENSIVE EXTRACTION**: Extract every piece of information visible on the page

STEP-BY-STEP ANALYSIS FRAMEWORK:

<thinking>
1. **OVERALL PAGE SCAN**: What type of content is on this page? (text, tables, figures, charts, etc.)
2. **TEXT IDENTIFICATION**: What are the main headings, paragraphs, lists, and text elements?
3. **TABLE ANALYSIS**: Are there any tables? What data do they contain? What insights do they provide?
4. **VISUAL ELEMENTS**: Are there any figures, charts, diagrams, images? What do they show in detail?
5. **LAYOUT STRUCTURE**: What is the logical reading order from top to bottom?
6. **SPECIAL CONTENT**: Any formulas, equations, chemical structures, or technical diagrams?
</thinking>

CONTENT EXTRACTION REQUIREMENTS:

**TEXT CONTENT**:
- Extract all readable text with proper formatting
- Use appropriate markdown headers (# ## ### ####)
- Preserve paragraphs, bullet points, numbered lists
- Include captions, footnotes, references
- Maintain scientific terminology exactly

**TABLE CONTENT** (SUMMARY ONLY):
- **Table Summary**: Write a comprehensive 2-3 sentence summary explaining what the table shows, key findings, and significance
- **Table Title/Caption**: Include the exact table title or caption if visible
- DO NOT extract the actual table data/content - summary description only

**FIGURE/IMAGE CONTENT** (CRITICAL - DETAILED DESCRIPTIONS REQUIRED):
For EVERY figure, chart, diagram, or image:
- **Figure Title**: Include the exact figure number and title
- **Visual Description**: Provide detailed 3-4 sentence description of what the image shows
- **Key Elements**: Describe specific elements like axes, data points, trends, structures
- **Text in Images**: Extract any text, labels, or annotations visible within the image
- **Scientific Context**: Explain what the figure demonstrates in the research context

**FORMATTING**:
- Headers: # ## ### #### for hierarchy
- Bold: **text** for emphasis
- Italic: *text* for scientific names
- Lists: - or 1. for bullets/numbers
- Tables: | col1 | col2 | format
- Code blocks: ``` for formulas

**EXCLUSIONS**:
- Skip watermarks, "CONFIDENTIAL", "DRAFT" markings
- Ignore automated document metadata
- Exclude security/administrative markings

EXAMPLES OF GOOD OUTPUT:

**Good Table Section**:
```
## Table 1: Patient Demographics and Baseline Characteristics

**Table Summary**: This table presents the demographic and baseline clinical characteristics of 150 patients enrolled in the study, showing that the treatment and control groups were well-matched with mean ages of 65.2 and 64.8 years respectively, and similar distributions of comorbidities including diabetes, hypertension, and other relevant medical conditions.
```

**Good Figure Section**:
```
## Figure 2: Efficacy Outcomes Over 12-Week Treatment Period

**Figure Description**: This line graph displays the primary efficacy endpoint (symptom severity score) measured weekly over 12 weeks for both treatment and control groups. The treatment group (blue line with circles) shows a steady decline from baseline score of 8.2 to 3.1 at week 12, while the control group (red line with squares) shows minimal improvement from 8.0 to 7.1. Error bars represent standard error of the mean.

**Key Findings**: The graph demonstrates significant separation between groups starting at week 4, with the treatment group achieving a 62% reduction in symptom severity compared to 11% in the control group by study end.
```

RESPONSE FORMAT:
<thinking>
[Your step-by-step analysis here]
</thinking>

<content>
[Your complete markdown content here - no code blocks, no explanatory text, just pure markdown content]
</content>
'''

        # Generate context-aware prompts based on strategy
        if strategy.screenshot_type == "single":
            page_context = f"page {strategy.page_range[0]}"
            content_type = "single page"
            special_instructions = ""
        else:
            page_context = f"pages {'-'.join(str(p) for p in strategy.page_range)}"
            content_type = "multi-page section"
            
            table_info = ""
            if strategy.contains_spanning_table and strategy.table_info:
                table_ids = []
                for table in strategy.table_info.get("tables", []):
                    table_ids.append(table["table_id"])
                table_info = f"This section contains tables that span multiple pages: {', '.join(table_ids)}. "
            
            total_table_count = strategy.table_info.get("total_tables", 0) if strategy.table_info else 0
            table_reason = strategy.table_info.get("reason", "") if strategy.table_info else ""
            
            special_instructions = f"""
**MULTI-PAGE PROCESSING NOTES**:
- This image contains content from multiple consecutive pages ({len(strategy.page_range)} pages total)
- {table_info}This section contains {total_table_count} tables total across all pages
- Pay special attention to table continuations across page boundaries
- Look carefully for any tables that might be partially visible at page edges - ensure they are included in your analysis
- Provide comprehensive summaries that cover the complete content across all pages
- Note any page breaks or transitions in your content organization  
- Ignore any headers or footers that are present in the image and occur between page transitions
- For spanning tables, provide comprehensive summaries of the complete table content, not just individual page portions
- For content-heavy sections with multiple tables, ensure all tables are captured and summarized{"" if not table_reason else f" (Reason: {table_reason})"}
"""

        user_prompt = f'''
Please analyze {page_context} of this pharmaceutical/medical research document ({content_type}).

REQUIRED ANALYSIS STEPS:
1. First, think through what you see systematically within <thinking> tags
2. Then provide the complete extracted content within <content> tags

EXTRACTION PRIORITIES:
- **Images/Figures**: Provide detailed 3-4 sentence descriptions of what each figure/chart/diagram actually shows
- **Tables**: Include comprehensive summaries ONLY (no table data extraction needed)
- **Text**: All paragraphs, headings, lists, captions with proper formatting
- **Technical Content**: Formulas, chemical structures, experimental setups

CRITICAL REQUIREMENTS:
- For figures: Don't just say "Figure X shows..." - describe what is actually visible in detail
- For tables: Provide meaningful summaries of what the data shows, not the raw table structure
- Maintain scientific accuracy and terminology
- Follow the logical reading order of the document{special_instructions}

Remember: Think step-by-step first, then provide the complete markdown content.
'''
        
        import time
        start_time = time.time()
        
        try:
            # Encode the screenshot
            base64_image = self.encode_image_to_base64(screenshot_path)

            # Create the API call function for retry wrapper
            async def make_api_call():
                return await async_openai_client.chat.completions.create(
                    model=OPENAI_MODEL_NAME,
                    messages=[
                        {
                            "role": "system",
                            "content": system_prompt
                        },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": user_prompt
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}",
                                        "detail": "high"
                                    }
                                }
                            ]
                        }
                    ],
                    temperature=0.15
                )
            
            # Use retry wrapper for the API call
            async with api_semaphore:
                response = await retry_on_empty_response(make_api_call, max_retries=3)
                
            if response is None:
                raise Exception("Failed to get valid response after retries")
            
            processing_time = time.time() - start_time
            
            # Get the response content
            if hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content
            else:
                content = str(response)
            
            # Parse the response as direct markdown
            return self._parse_markdown_response(content, strategy, processing_time)
                
        except Exception as e:
            processing_time = time.time() - start_time if 'start_time' in locals() else 0.0
            page_desc = f"pages {strategy.page_range}" if len(strategy.page_range) > 1 else f"page {strategy.page_range[0]}"
            print(f"Error extracting content from {page_desc}: {e}")
            traceback.print_exc()
            # Return default result with error info
            return PageContentResult(
                page_number=strategy.page_range[0],  # Use first page as primary page number
                markdown_content=f"# Error Extracting {page_desc.title()}\n\nError occurred during content extraction: {str(e)}",
                content_statistics=ContentStatistics(),
                extraction_notes=[f"Extraction failed: {str(e)}", f"Strategy: {strategy.screenshot_type}", f"Page range: {strategy.page_range}"],
                confidence_score=0.0,
                processing_time=processing_time
            )

    def _debug_response(self, content: str, page_number: int):
        """Debug helper to print response details"""
        print(f"\n=== DEBUG: Raw response for page {page_number} ===")
        print(f"Content length: {len(content)}")
        print(f"First 300 chars: {content[:300]}")
        print(f"Last 300 chars: {content[-300:]}")
        print("=" * 50)

    def _extract_xml_content(self, content: str, debug: bool = False) -> Tuple[str, str]:
        """Extract thinking and content sections from XML-tagged response
        
        Returns:
            Tuple of (thinking_content, markdown_content)
        """
        import re
        
        # Extract thinking section
        thinking_pattern = r'<thinking>(.*?)</thinking>'
        thinking_match = re.search(thinking_pattern, content, re.DOTALL | re.IGNORECASE)
        thinking_content = thinking_match.group(1).strip() if thinking_match else ""
        
        # Extract content section
        content_pattern = r'<content>(.*?)</content>'
        content_match = re.search(content_pattern, content, re.DOTALL | re.IGNORECASE)
        markdown_content = content_match.group(1).strip() if content_match else ""
        
        if debug:
            print(f"XML Extraction Debug:")
            print(f"  Thinking found: {bool(thinking_match)}")
            print(f"  Content found: {bool(content_match)}")
            print(f"  Thinking length: {len(thinking_content)}")
            print(f"  Content length: {len(markdown_content)}")
        
        # If no XML tags found, treat entire content as markdown (fallback)
        if not markdown_content and not thinking_content:
            # Check if it looks like direct markdown (starts with # or has markdown patterns)
            if (content.strip().startswith('#') or 
                re.search(r'^#+\s', content, re.MULTILINE) or 
                '|' in content or 
                re.search(r'^\*\s', content, re.MULTILINE)):
                markdown_content = content.strip()
                thinking_content = "Direct markdown response (no XML tags found)"
                if debug:
                    print("  Fallback: Using entire content as markdown")
            else:
                # Treat as thinking content if it doesn't look like markdown
                thinking_content = content.strip()
                markdown_content = "# Content Extraction Failed\n\nNo content section found in XML-tagged response."
                if debug:
                    print("  Fallback: Using entire content as thinking")
        
        return thinking_content, markdown_content
    
    def _remove_markdown_code_blocks(self, content: str) -> str:
        """Remove markdown code blocks (```markdown ... ```) from content"""
        import re
        
        # Pattern to match ```markdown ... ``` blocks (with optional language specification)
        patterns = [
            r'```markdown\s*\n(.*?)\n```',
            r'```md\s*\n(.*?)\n```',
            r'```\s*\n(.*?)\n```'  # Generic code blocks that might contain markdown
        ]
        
        cleaned_content = content
        
        for pattern in patterns:
            # Find all matches
            matches = re.findall(pattern, cleaned_content, re.DOTALL | re.IGNORECASE)
            
            if matches:
                print(f"Found {len(matches)} markdown code block(s), extracting content...")
                
                # For markdown blocks, replace the entire block with just the inner content
                if 'markdown' in pattern or 'md' in pattern:
                    cleaned_content = re.sub(pattern, r'\1', cleaned_content, flags=re.DOTALL | re.IGNORECASE)
                else:
                    # For generic code blocks, check if they look like markdown content
                    for match in matches:
                        # Simple heuristic: if it contains markdown headers or common markdown syntax
                        if (re.search(r'^#+\s', match, re.MULTILINE) or 
                            re.search(r'\|.*\|', match) or 
                            re.search(r'^\*\s', match, re.MULTILINE) or
                            re.search(r'^-\s', match, re.MULTILINE)):
                            # This looks like markdown, extract it
                            cleaned_content = re.sub(pattern, r'\1', cleaned_content, flags=re.DOTALL)
                            break
        
        # Also remove any remaining triple backticks that might be orphaned
        cleaned_content = re.sub(r'^```.*$', '', cleaned_content, flags=re.MULTILINE)
        cleaned_content = re.sub(r'```\s*$', '', cleaned_content, flags=re.MULTILINE)
        
        # Clean up extra whitespace
        cleaned_content = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_content)  # Remove excessive newlines
        cleaned_content = cleaned_content.strip()
        
        return cleaned_content
    
    def _parse_markdown_response(self, content: str, strategy: PageProcessingStrategy, processing_time: float, debug: bool = True) -> PageContentResult:
        """Parse XML-tagged response from VLM"""
        page_desc = f"pages {strategy.page_range}" if len(strategy.page_range) > 1 else f"page {strategy.page_range[0]}"
        primary_page = strategy.page_range[0]  # Use first page as primary page number
        
        if debug:
            print(f"{page_desc.title()} processing complete")
            # self._debug_response(content, primary_page)
            
        try:
            # Extract thinking and content from XML tags
            thinking_content, markdown_content = self._extract_xml_content(content, debug)
            
            # Clean the markdown content
            original_length = len(markdown_content)
            markdown_content = self._remove_markdown_code_blocks(markdown_content)
            code_blocks_removed = original_length != len(markdown_content)
            
            if not markdown_content:
                print(f"Warning: Empty markdown content for {page_desc}")
                if thinking_content:
                    print(f"Thinking content was present: {thinking_content[:200]}...")
                return PageContentResult(
                    page_number=primary_page,
                    markdown_content=f"# {page_desc.title()}\n\n*No content could be extracted from this {strategy.screenshot_type.replace('_', ' ')} section.*",
                    content_statistics=ContentStatistics(),
                    extraction_notes=["Empty markdown content from VLM", f"Thinking content length: {len(thinking_content)}", f"Strategy: {strategy.screenshot_type}"],
                    confidence_score=0.0,
                    processing_time=processing_time
                )
            
            # Calculate content statistics
            word_count = len(markdown_content.split())
            
            # Count markdown elements
            import re
            
            # Count headers (lines starting with #)
            heading_count = len(re.findall(r'^#+\s', markdown_content, re.MULTILINE))
            
            # Count tables (lines with |)
            table_lines = re.findall(r'\|.*\|', markdown_content)
            # Estimate table count (rough: table header + separator = 2 lines minimum)
            table_count = max(0, len([line for line in table_lines if '---' not in line]) // 3) if table_lines else 0
            
            # Count figures/images (sections starting with ## Figure, ## Chart, ## Image, etc.)
            figure_patterns = [
                r'#+\s*Figure\s+\d+',
                r'#+\s*Chart\s+\d+', 
                r'#+\s*Image\s+\d+',
                r'#+\s*Diagram\s+\d+',
                r'#+\s*Graph\s+\d+',
                r'#+\s*Table\s+\d+'  # Some figures might be described as tables
            ]
            figure_count = 0
            for pattern in figure_patterns:
                figure_count += len(re.findall(pattern, markdown_content, re.IGNORECASE))
            
            content_statistics = ContentStatistics(
                word_count=word_count,
                table_count=table_count,
                figure_count=figure_count,
                heading_count=heading_count
            )
            
            # Generate extraction notes based on content analysis
            extraction_notes = []
            
            # Add strategy context
            extraction_notes.append(f"Processing strategy: {strategy.screenshot_type}")
            extraction_notes.append(f"Page range: {strategy.page_range}")
            
            if strategy.contains_spanning_table:
                table_count_from_strategy = len(strategy.table_info.get("tables", [])) if strategy.table_info else 0
                extraction_notes.append(f"Contains {table_count_from_strategy} spanning table(s)")
            
            # Add thinking content info
            if thinking_content:
                thinking_word_count = len(thinking_content.split())
                extraction_notes.append(f"VLM thinking content: {thinking_word_count} words")
            
            if code_blocks_removed:
                extraction_notes.append("Removed markdown code blocks from VLM output")
            
            if word_count < 50:
                extraction_notes.append("Very short content - may indicate extraction issues")
            
            if '[unclear text]' in markdown_content.lower():
                extraction_notes.append("Contains unclear text sections")
                
            if 'watermark' in markdown_content.lower() or 'confidential' in markdown_content.lower():
                extraction_notes.append("Warning: May contain watermark content despite instructions")
            
            # Estimate confidence based on content quality indicators
            confidence_score = 8.0  # Start with base confidence
            
            # Adjust based on content length
            if word_count < 20:
                confidence_score -= 3.0
            elif word_count < 100:
                confidence_score -= 1.0
            
            # Adjust based on structure
            if heading_count > 0:
                confidence_score += 0.5
            if table_count > 0:
                confidence_score += 0.5
            if figure_count > 0:
                confidence_score += 0.5
            
            # Bonus for multi-page processing with spanning tables
            if strategy.screenshot_type == "multi_page" and strategy.contains_spanning_table:
                confidence_score += 1.0  # Reward for handling complex multi-page content
                
            # Adjust for issues (but account for strategy-related notes)
            issue_notes = [note for note in extraction_notes 
                          if not note.startswith(("Processing strategy:", "Page range:", "Contains"))]
            if issue_notes:
                confidence_score -= 0.5 * len(issue_notes)
            
            # Clamp confidence score
            confidence_score = max(0.0, min(10.0, confidence_score))
            
            return PageContentResult(
                page_number=primary_page,
                markdown_content=markdown_content,
                content_statistics=content_statistics,
                extraction_notes=extraction_notes,
                confidence_score=confidence_score,
                processing_time=processing_time
            )
                
        except Exception as e:
            print(f"Error parsing markdown response for {page_desc}: {e}")
            traceback.print_exc()
            
            # Return error result with whatever content we have
            raw_content_preview = content[:1000] + ('...' if len(content) > 1000 else '')
            return PageContentResult(
                page_number=primary_page,
                markdown_content=f"# {page_desc.title()} (Parse Error)\n\nError during content parsing: {str(e)}\n\nRaw content:\n{raw_content_preview}",
                content_statistics=ContentStatistics(word_count=len(content.split()) if content else 0),
                extraction_notes=[f"Parse error: {str(e)}", f"Strategy: {strategy.screenshot_type}", f"Page range: {strategy.page_range}"],
                confidence_score=1.0,
                processing_time=processing_time
            )

    async def analyze_all_content(self, screenshot_data: List[Tuple[str, PageProcessingStrategy]]) -> DocumentExtractionResult:
        """Analyze all content using strategic screenshots
        
        Args:
            screenshot_data: List of tuples (screenshot_path, strategy)
            
        Returns:
            Complete document extraction result
        """
        print(f"\n" + "="*60)
        print("PHASE 3: VLM CONTENT ANALYSIS")
        print("="*60)
        print(f"Analyzing {len(screenshot_data)} strategic screenshots with VLLM...")
        
        # Create tasks for concurrent processing
        tasks = []
        for screenshot_path, strategy in screenshot_data:
            task = self.extract_content_with_vllm(screenshot_path, strategy)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        valid_results = []
        for i, (result, (screenshot_path, strategy)) in enumerate(zip(results, screenshot_data)):
            if isinstance(result, Exception):
                page_desc = f"pages {strategy.page_range}" if len(strategy.page_range) > 1 else f"page {strategy.page_range[0]}"
                print(f"Error analyzing {page_desc}: {result}")
                # Create default result for failed content
                valid_results.append(PageContentResult(
                    page_number=strategy.page_range[0],
                    markdown_content=f"# Error Extracting {page_desc.title()}\n\nError occurred during content extraction: {str(result)}",
                    content_statistics=ContentStatistics(),
                    extraction_notes=[f"Extraction failed: {str(result)}", f"Strategy: {strategy.screenshot_type}", f"Page range: {strategy.page_range}"],
                    confidence_score=0.0,
                    processing_time=0.0
                ))
            else:
                valid_results.append(result)
        
        # Get total pages from all strategies
        all_pages = set()
        for _, strategy in screenshot_data:
            all_pages.update(strategy.page_range)
        total_pages = len(all_pages)
        
        # Compile overall statistics
        total_word_count = sum(page.content_statistics.word_count for page in valid_results)
        total_table_count = sum(page.content_statistics.table_count for page in valid_results)
        total_figure_count = sum(page.content_statistics.figure_count for page in valid_results)
        total_heading_count = sum(page.content_statistics.heading_count for page in valid_results)
        total_processing_time = sum(page.processing_time or 0.0 for page in valid_results)
        
        overall_statistics = ContentStatistics(
            word_count=total_word_count,
            table_count=total_table_count,
            figure_count=total_figure_count,
            heading_count=total_heading_count
        )
        
        combined_markdown = "\n\n".join(page.markdown_content for page in valid_results)
        
        # Count strategies
        single_page_strategies = sum(1 for _, s in screenshot_data if s.screenshot_type == "single")
        multi_page_strategies = sum(1 for _, s in screenshot_data if s.screenshot_type == "multi_page")
        spanning_table_count = len(self.table_analysis.get('spanning_tables', []))
        
        document_result = DocumentExtractionResult(
            document_name=self.pdf_path.name,
            total_pages=total_pages,
            page_results=valid_results,
            combined_markdown=combined_markdown,
            overall_statistics=overall_statistics,
            processing_summary={
                "analysis_timestamp": datetime.now().isoformat(),
                "total_pages_analyzed": total_pages,
                "total_processing_time": total_processing_time,
                "average_confidence": sum(page.confidence_score for page in valid_results) / len(valid_results) if valid_results else 0.0,
                "preprocessing_enabled": self.enable_preprocessing,
                "spanning_tables_found": spanning_table_count,
                "single_page_strategies": single_page_strategies,
                "multi_page_strategies": multi_page_strategies,
                "total_strategies": len(screenshot_data)
            }
        )
        
        print(f"✓ Analysis complete:")
        print(f"  Total pages covered: {total_pages}")
        print(f"  Single-page strategies: {single_page_strategies}")
        print(f"  Multi-page strategies: {multi_page_strategies}")
        print(f"  Spanning tables handled: {spanning_table_count}")
        print(f"  Total word count: {total_word_count}")
        print(f"  Total table count: {total_table_count}")
        print(f"  Total figure count: {total_figure_count}")
        print(f"  Total heading count: {total_heading_count}")
        print(f"  Average confidence: {document_result.processing_summary['average_confidence']:.1f}/10")
        
        return document_result

    def save_analysis_results(self, analysis_result: DocumentExtractionResult):
        """Save analysis results to files"""
        # Save main analysis as JSON
        analysis_file = self.output_dir / "document_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result.model_dump(), f, indent=2, ensure_ascii=False)
        print(f"✓ Analysis saved to: {analysis_file}")
        
        # Save individual page markdown
        for page_result in analysis_result.page_results:
            page_file = self.markdown_dir / f"page_{page_result.page_number:03d}_content.md"
            with open(page_file, 'w', encoding='utf-8') as f:
                f.write(page_result.markdown_content)
        
        # Save combined markdown
        combined_file = self.output_dir / "document_content.md"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(analysis_result.combined_markdown)
        
        # Save summary report
        summary_file = self.output_dir / "analysis_summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"PDF Content Extraction Summary\n")
            f.write(f"=" * 50 + "\n\n")
            f.write(f"Document: {analysis_result.document_name}\n")
            f.write(f"Total Pages: {analysis_result.total_pages}\n")
            f.write(f"Total Word Count: {analysis_result.overall_statistics.word_count}\n")
            f.write(f"Total Table Count: {analysis_result.overall_statistics.table_count}\n")
            f.write(f"Total Figure Count: {analysis_result.overall_statistics.figure_count}\n")
            f.write(f"Total Heading Count: {analysis_result.overall_statistics.heading_count}\n\n")
            
            f.write("Overall Statistics:\n")
            f.write(f"  Average Confidence: {analysis_result.processing_summary['average_confidence']:.1f}/10\n")
            f.write(f"  Total Processing Time: {analysis_result.processing_summary['total_processing_time']:.2f} seconds\n")
            
            f.write("\nPage-wise Statistics:\n")
            for page_result in analysis_result.page_results:
                f.write(f"  Page {page_result.page_number}:\n")
                f.write(f"    Confidence: {page_result.confidence_score:.1f}/10\n")
                f.write(f"    Word Count: {page_result.content_statistics.word_count}\n")
                f.write(f"    Table Count: {page_result.content_statistics.table_count}\n")
                f.write(f"    Figure Count: {page_result.content_statistics.figure_count}\n")
                f.write(f"    Heading Count: {page_result.content_statistics.heading_count}\n")
                f.write(f"    Extraction Notes: {', '.join(page_result.extraction_notes)}\n")
        
        print(f"✓ Summary saved to: {summary_file}")

    def save_complete_document(self, analysis_result: DocumentExtractionResult, 
                              output_path: str = None) -> str:
        """Save the complete extracted document content
        
        Args:
            analysis_result: Document extraction result with all page content
            output_path: Path for the complete document output
            
        Returns:
            Path to the complete document file
        """
        if output_path is None:
            output_path = self.output_dir / f"{self.pdf_path.stem}_complete_document.md"
        
        try:
            # Write the complete document
            with open(output_path, 'w', encoding='utf-8') as f:
                # Add document header
                f.write(f"# {self.pdf_path.stem}\n\n")
                f.write(f"*Complete document extracted using Vision Language Model*\n\n")
                f.write(f"**Extraction Summary:**\n")
                f.write(f"- Total Pages: {analysis_result.total_pages}\n")
                f.write(f"- Total Word Count: {analysis_result.overall_statistics.word_count}\n")
                f.write(f"- Total Tables: {analysis_result.overall_statistics.table_count}\n")
                f.write(f"- Total Figures: {analysis_result.overall_statistics.figure_count}\n")
                f.write(f"- Total Headings: {analysis_result.overall_statistics.heading_count}\n")
                f.write(f"- Average Confidence: {analysis_result.processing_summary['average_confidence']:.1f}/10\n\n")
                f.write("---\n\n")
                
                # Write the combined content
                f.write(analysis_result.combined_markdown)
            
            print(f"✓ Complete document saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            print(f"Error saving complete document: {e}")
            traceback.print_exc()
            return None

    # Legacy method - now deprecated
    def integrate_with_pdf_text_extraction(self, analysis_result: DocumentExtractionResult, 
                                         text_output_path: str = None) -> str:
        """Legacy method - now just calls save_complete_document"""
        print("Warning: integrate_with_pdf_text_extraction is deprecated. Use save_complete_document instead.")
        return self.save_complete_document(analysis_result, text_output_path)

def _get_real_table_coordinates_from_pdf(pdf_path: str, page_num: int) -> List[Dict]:
    """Extract actual table coordinates from PDF using pdfplumber
    
    Args:
        pdf_path: Path to PDF file
        page_num: Page number (1-indexed)
        
    Returns:
        List of table coordinate dictionaries with top, bottom, x0, x1
    """
    try:
        import pdfplumber
        
        # Use pdfplumber to detect table areas (same as pdf_to_txt_tables_plus_text.py)
        with pdfplumber.open(pdf_path) as pdf:
            if page_num - 1 < len(pdf.pages):
                page = pdf.pages[page_num - 1]  # Convert to 0-indexed
                table_areas = page.find_tables()
                
                table_coords = []
                for table_area in table_areas:
                    coords = {
                        'top': table_area.bbox[1],
                        'bottom': table_area.bbox[3],
                        'x0': table_area.bbox[0],
                        'x1': table_area.bbox[2]
                    }
                    table_coords.append(coords)
                    print(f"    Detected table on page {page_num}: ({coords['x0']:.0f},{coords['top']:.0f})-({coords['x1']:.0f},{coords['bottom']:.0f})")
                
                # Sort by vertical position (top to bottom)
                table_coords.sort(key=lambda t: t['top'])
                return table_coords
                
    except Exception as e:
        print(f"Error extracting table coordinates from page {page_num}: {e}")
    
    return []

def _get_spanning_table_bounds_from_pdf(pdf_path: str, table_pages: List[int], table_id: str) -> ContentBounds:
    """Get coordinate bounds for a spanning table by detecting actual tables in PDF
    
    Args:
        pdf_path: Path to PDF file
        table_pages: List of page numbers for the spanning table
        table_id: Table ID for logging
        
    Returns:
        ContentBounds with real coordinates
    """
    print(f"    Extracting real coordinates for spanning table {table_id} on pages {table_pages}")
    
    all_tables = []
    
    # Get actual table coordinates from each page
    for page_num in table_pages:
        page_tables = _get_real_table_coordinates_from_pdf(pdf_path, page_num)
        
        # Include ALL tables on this page, not just the first one
        if page_tables:
            for table in page_tables:
                table['page'] = page_num
                all_tables.append(table)
            print(f"    Page {page_num}: included {len(page_tables)} tables in bounds calculation")
        else:
            print(f"    Page {page_num}: no tables detected")
    
    if not all_tables:
        # Fallback if no tables detected - get page dimensions from PyMuPDF
        print(f"    Warning: No tables detected for {table_id}, using full page bounds")
        import fitz
        with fitz.open(pdf_path) as pdf_doc:
            page = pdf_doc[table_pages[0] - 1]
            page_rect = page.rect
            return ContentBounds(
                x0=0, y0=0, x1=page_rect.width, y1=page_rect.height, 
                page=table_pages[0]
            )
    
    # Get page dimensions to ensure we use full width
    import fitz
    with fitz.open(pdf_path) as pdf_doc:
        page = pdf_doc[table_pages[0] - 1]
        page_rect = page.rect
        page_width = page_rect.width

    # Calculate bounds across all detected tables
    # Always use full page width to capture text that extends beyond table bounds
    min_top = min(table['top'] for table in all_tables)
    max_bottom = max(table['bottom'] for table in all_tables)
    
    # Use full page width instead of table width to capture all content
    x0_padded = 0                    # Start from page left edge
    x1_padded = page_width           # Extend to page right edge
    y0 = max(0, min_top - 80)        # 80px context above first table
    y1 = max_bottom + 80             # 80px context below last table
    
    print(f"    Calculated bounds from {len(all_tables)} tables: ({x0_padded:.0f},{y0:.0f}) to ({x1_padded:.0f},{y1:.0f}) [FULL PAGE WIDTH]")
    
    return ContentBounds(
        x0=x0_padded,
        y0=y0,
        x1=x1_padded,
        y1=y1,
        page=table_pages[0]  # Primary page for reference
    )

def _get_page_table_bounds_from_pdf(pdf_path: str, page_num: int) -> ContentBounds:
    """Get coordinate bounds for all tables on a single page by detecting actual tables
    
    Args:
        pdf_path: Path to PDF file
        page_num: Page number (1-indexed)
        
    Returns:
        ContentBounds with real coordinates
    """
    print(f"    Extracting real coordinates for tables on page {page_num}")
    
    page_tables = _get_real_table_coordinates_from_pdf(pdf_path, page_num)
    
    if not page_tables:
        # No tables detected - use full page
        print(f"    No tables detected on page {page_num}, using full page")
        import fitz
        with fitz.open(pdf_path) as pdf_doc:
            page = pdf_doc[page_num - 1]
            page_rect = page.rect
            return ContentBounds(
                x0=0, y0=0, x1=page_rect.width, y1=page_rect.height,
                page=page_num
            )
    
    # Calculate combined bounds of all tables on page
    min_x0 = min(table['x0'] for table in page_tables)
    max_x1 = max(table['x1'] for table in page_tables)
    min_top = min(table['top'] for table in page_tables)
    max_bottom = max(table['bottom'] for table in page_tables)
    
    # Get full page dimensions for context
    import fitz
    with fitz.open(pdf_path) as pdf_doc:
        page = pdf_doc[page_num - 1]
        page_rect = page.rect
        page_height = page_rect.height
        page_width = page_rect.width
    
    # For single pages with tables, be very generous with bounds to capture:
    # - Section headings above tables
    # - Content, figures, and text below tables
    # - Text that extends beyond table width (use full page width)
    x0_padded = 0                    # Start from page left edge
    x1_padded = page_width           # Extend to page right edge (full width)
    
    # Start from page top if table is in upper half, otherwise give generous context
    y0 = 0 if min_top < page_height / 2 else max(0, min_top - 200)
    
    # For single pages, extend to page bottom to capture all content (sections, figures, etc.)
    # This ensures we don't miss content like section 4.5 and graphs below tables
    y1 = page_height
    
    print(f"    Calculated bounds for {len(page_tables)} tables: ({x0_padded:.0f},{y0:.0f}) to ({x1_padded:.0f},{y1:.0f}) [FULL PAGE WIDTH & HEIGHT]")
    
    return ContentBounds(
        x0=x0_padded,
        y0=y0,
        x1=x1_padded,
        y1=y1,
        page=page_num
    )

async def main():
    """Main function to run the enhanced PDF content extraction with direct markdown output"""
    # Example usage
    pdf_path = "/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report - Pre-IND-Test.pdf"  # Update this path
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        print("Usage: python get_all_images_and_descriptions_using_llm.py [pdf_path]")
        return
    
    try:
        print("Enhanced PDF Content Extraction")
        print("Features: Direct markdown output, token counting, retry logic, watermark exclusion")
        print("-" * 60)
        
        # Initialize content extractor with preprocessing enabled
        extractor = PDFContentExtractor(pdf_path, enable_preprocessing=True)
        
        # Step 1: Run preprocessing to identify spanning tables
        preprocessing_success = extractor.run_preprocessing()
        
        if not preprocessing_success:
            print("Warning: Preprocessing failed, falling back to single-page processing")
        
        # Step 2: Take strategic screenshots based on analysis
        screenshot_data = extractor.take_strategic_screenshots(dpi=150)
        
        if not screenshot_data:
            print("Error: No screenshots were created")
            return

        exit()
        
        # Step 3: Extract content using strategic analysis
        extraction_result = await extractor.analyze_all_content(screenshot_data)
        
        # Step 4: Save extraction results
        extractor.save_analysis_results(extraction_result)
        
        # Step 5: Save complete document
        complete_document_path = extractor.save_complete_document(extraction_result)
        
        # Step 6: Cleanup temporary files (optional)
        # import shutil
        # shutil.rmtree(extractor.temp_dir, ignore_errors=True)
        
        print("\n" + "=" * 80)
        print("CONTENT EXTRACTION COMPLETE")
        print("=" * 80)
        print(f"✓ Screenshots: {extractor.screenshots_dir}")
        print(f"✓ Page markdown: {extractor.markdown_dir}")
        print(f"✓ Analysis: {extractor.output_dir}/document_analysis.json")
        print(f"✓ Complete document: {complete_document_path}")
        print(f"✓ Summary: {extractor.output_dir}/analysis_summary.txt")
        print(f"✓ Extracted content from {extraction_result.total_pages} pages")
        
        # Show preprocessing results
        if extractor.enable_preprocessing:
            print(f"✓ Preprocessing: {len(extractor.table_analysis.get('spanning_tables', []))} spanning tables found")
            print(f"✓ Strategies: {extraction_result.processing_summary.get('single_page_strategies', 0)} single + {extraction_result.processing_summary.get('multi_page_strategies', 0)} multi-page")
        
        print(f"✓ Total word count: {extraction_result.overall_statistics.word_count:,}")
        print(f"✓ Total tables: {extraction_result.overall_statistics.table_count}")
        print(f"✓ Total figures: {extraction_result.overall_statistics.figure_count}")
        print(f"✓ Processing time: {extraction_result.processing_summary.get('total_processing_time', 0):.2f}s")
        print(f"✓ Average confidence: {extraction_result.processing_summary['average_confidence']:.1f}/10")
        
    except Exception as e:
        print(f"Error during content extraction: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())