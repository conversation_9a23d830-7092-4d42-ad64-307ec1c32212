# %%
import re
import json
import os
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, <PERSON>ple
import numpy as np
import asyncio
import time
import traceback
import inspect
from transformers import AutoTokenizer

load_dotenv()

print("GENERATOR_MODEL_BASE_URL", os.getenv("GENERATOR_MODEL_BASE_URL"))
print("GENERATOR_MODEL_API_KEY", os.getenv("GENERATOR_MODEL_API_KEY"))
print("GENERATOR_MODEL_NAME", os.getenv("GENERATOR_MODEL_NAME"))

openai_client = OpenAI(base_url=os.getenv("GENERATOR_MODEL_BASE_URL"), api_key=os.getenv("GENERATOR_MODEL_API_KEY"))
openai_async_client = AsyncOpenAI(base_url=os.getenv("GENERATOR_MODEL_BASE_URL"), api_key=os.getenv("GENERATOR_MODEL_API_KEY"))
MODEL = os.getenv("GENERATOR_MODEL_NAME")

INCLUDE_JSON_OUTPUT_TAGS = False

TOPMOST_SUMMARY_THRESHOLD = 3.0
MIDLEVEL_SUMMARY_THRESHOLD = 5.0
CHUNK_SUMMARY_THRESHOLD = 7.0

def get_json_output_tags():
    if INCLUDE_JSON_OUTPUT_TAGS:
        return "Give your json in <text_output> json </text_output> tags."
    else:
        return ""

def get_relevance_threshold(summary_level):
    if summary_level == 1:
        return TOPMOST_SUMMARY_THRESHOLD
    elif summary_level == 2:
        return MIDLEVEL_SUMMARY_THRESHOLD
    else:
        return CHUNK_SUMMARY_THRESHOLD

def write_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"JSON saved to {filename}")

def read_json(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def get_mongodb_client():
    """Get MongoDB client connection."""
    return MongoClient(os.getenv("MONGO_DB_URL"))

def parse_json_response(response_text: str) -> Any:
    """
    Parse a JSON response that may be wrapped in backticks.
    
    Args:
        response_text: The response text to parse
        
    Returns:
        Any: The parsed JSON object
    """
    # Remove any markdown code block syntax
    response_text = re.sub(r'```json\n?', '', response_text)
    response_text = re.sub(r'```\n?', '', response_text)
    response_text = response_text.strip()
    
    try:
        # print(f"parse_json_response, response_text: {json.loads(response_text)}")
        return json.loads(response_text)
    except json.JSONDecodeError:
        # If the response is not valid JSON, try to extract a list from the text
        # Look for lines that start with numbers, bullets, or dashes
        lines = re.findall(r'^[\d\-\*\.]+\.?\s*(.+)$', response_text, re.MULTILINE)
        if lines:
            return lines
        # If no lines found, split by newlines and clean up
        # print(f"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\n') if line.strip()]}")
        return [line.strip() for line in response_text.split('\n') if line.strip()]
    
def calculate_number_of_tokens(text):
    # Load tokenizer for Mistral model
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    return token_count
    
async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):
    """
    Wrapper function that retries an async LLM API call when the response is empty.
    
    Args:
        async_func: The async function to call (usually an LLM API call)
        *args: Positional arguments to pass to async_func
        max_retries: Maximum number of retry attempts (default: 3)
        **kwargs: Keyword arguments to pass to async_func
        
    Returns:
        The result of the async_func call, ensuring it's not empty
        
    Raises:
        Exception: If max_retries is reached and the response is still empty
    """
    # Create logs directory if it doesn't exist
    log_dir = "error_logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Extract the function name for logging purposes
    func_name = async_func.__name__ if hasattr(async_func, "__name__") else "unknown_function"
    
    # Try to get the caller's name from the stack
    try:
        caller_frame = inspect.currentframe().f_back
        caller_name = caller_frame.f_code.co_name if caller_frame else "unknown_caller"
    except Exception:
        caller_name = "unknown_caller"
    
    for attempt in range(max_retries):
        try:
            result = await async_func(*args, **kwargs)
            
            # Check if result is None, empty string, just whitespace, or contains tool calls
            is_empty = result is None or (isinstance(result, str) and result.strip() == "")
            contains_tool_call = False
            
            if isinstance(result, str):
                # Check for tool_call patterns
                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)
                contains_tool_call_text = 'tool_call' in result.lower()
                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text
            
            if is_empty or contains_tool_call:
                if is_empty:
                    reason = "empty response"
                    error_type = 'empty_response'
                elif contains_tool_call:
                    reason = "response contains tool calls"
                    error_type = 'tool_call_response'
                
                print(f"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...")
                
                # Get debug information to log
                debug_info = {
                    'error_type': error_type,
                    'function': func_name,
                    'caller': caller_name,
                    'attempt': attempt + 1,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]
                }
                
                # Extract prompt information based on different API patterns
                # For the direct messages pattern in kwargs
                if 'messages' in kwargs:
                    debug_info['messages'] = kwargs['messages']
                    
                # For the pattern where the func is a closure with local make_api_call
                # Try to get source code of the async_func to check for patterns
                try:
                    source = inspect.getsource(async_func)
                    if "chat.completions.create" in source:
                        debug_info['api_pattern'] = "chat_completions_closure"
                except Exception:
                    pass
                
                # Try to extract system_prompt and user_prompt from the caller's frame if available
                try:
                    if caller_frame:
                        caller_locals = caller_frame.f_locals
                        # Capture common patterns in this codebase
                        if 'system_prompt' in caller_locals:
                            debug_info['system_prompt'] = caller_locals['system_prompt']
                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                        # If this is using the OpenAI client pattern, get the model too
                        if 'model' in caller_locals:
                            debug_info['model'] = caller_locals['model']
                        # For the antropic calls
                        if 'CRITIQUE_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                        elif 'GENERATOR_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                        elif 'MODEL' in caller_locals:
                            debug_info['model'] = caller_locals['MODEL']
                except Exception as e:
                    debug_info['frame_inspection_error'] = str(e)
                
                # Save the debug information
                timestamp = int(time.time())
                log_filename = f"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json"
                
                try:
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        json.dump(debug_info, f, indent=2, ensure_ascii=False)
                    print(f"Logged empty response details to {log_filename}")
                except Exception as log_error:
                    print(f"Failed to log empty response details: {str(log_error)}")
                
                # Continue to the next retry attempt
                continue
                
            # If we get here, we have a non-empty response
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            print(f"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}")
            
            # Get debug information to log
            debug_info = {
                'error_type': error_type,
                'error_message': error_msg,
                'function': func_name,
                'caller': caller_name,
                'attempt': attempt + 1,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'stack_trace': traceback.format_exc()
            }
            
            # Extract prompt information based on different API patterns
            # For the direct messages pattern in kwargs
            if 'messages' in kwargs:
                debug_info['messages'] = kwargs['messages']
                
            # For the pattern where the func is a closure with local make_api_call
            # Try to get source code of the async_func to check for patterns
            try:
                source = inspect.getsource(async_func)
                if "chat.completions.create" in source:
                    debug_info['api_pattern'] = "chat_completions_closure"
            except Exception:
                pass
            
            # Try to extract system_prompt and user_prompt from the caller's frame if available
            try:
                if caller_frame:
                    caller_locals = caller_frame.f_locals
                    # Capture common patterns in this codebase
                    if 'system_prompt' in caller_locals:
                        debug_info['system_prompt'] = caller_locals['system_prompt']
                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                    # If this is using the OpenAI client pattern, get the model too
                    if 'model' in caller_locals:
                        debug_info['model'] = caller_locals['model']
                    # For the antropic calls
                    if 'CRITIQUE_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                    elif 'GENERATOR_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                    elif 'MODEL' in caller_locals:
                        debug_info['model'] = caller_locals['MODEL']
            except Exception as frame_error:
                debug_info['frame_inspection_error'] = str(frame_error)
            
            # Save the debug information
            timestamp = int(time.time())
            log_filename = f"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json"
            
            try:
                with open(log_filename, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, indent=2, ensure_ascii=False)
                print(f"Logged error details to {log_filename}")
            except Exception as log_error:
                print(f"Failed to log error details: {str(log_error)}")
            
            if attempt == max_retries - 1:
                # If we've exhausted all retries and still have an error
                print(f"Failed to get non-empty response after {max_retries} attempts")
                return None
            
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** attempt))
    
    # If we've exhausted all retries and still have an empty response
    print(f"Failed to get non-empty response after {max_retries} attempts")
    return None
    
def caculate_number_of_tokens(text):
    # Load tokenizer for Mistral model
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    return token_count

# %%
ich_ectd_guideline_referenced_with_quality_guidelines = read_json("/Users/<USER>/projects/scalegen/MedNova/structured jsons/ich_ectd_guideline_referenced_with_quality_guidelines.json")

doc_to_work_on = ich_ectd_guideline_referenced_with_quality_guidelines["module3"]["3.2"]["3.2.P"]["3.2.P.8"]

# %%
async def create_combined_summary(individual_summaries):
    """Create a combined summary from all individual summaries."""
    system_prompt = """You are an expert in pharmaceutical quality guidelines and regulatory compliance. Your task is to combine multiple individual summaries into one comprehensive combined summary, maintaining organization and clarity while avoiding redundancy.

    OUTPUT FORMAT:
    - Provide the summary in plain text format only
    - Do not use any special formatting, markdown, or symbols
    - Use simple bullet points with hyphens (-) when needed
    - Separate paragraphs with blank lines
    - Use parentheses for references or additional information

    CRITICAL REQUIREMENTS:
    1. Integration Strategy:
       - Merge content from all summaries efficiently
       - Maintain logical flow while minimizing length
       - Avoid duplicating information
       - Preserve cross-references without redundancy
       - Document dependencies concisely
       - Focus on creating a cohesive, unified document"""

    combined_summaries = "\n\n=== SUMMARY SEPARATOR ===\n\n".join(individual_summaries)
    
    user_prompt = f"""Please create a comprehensive combined summary that combines all the following individual summaries into one cohesive document. Maintain all critical information while eliminating redundancy.

    Individual Summaries:
    {combined_summaries}

    Requirements:
    1. Integrate all summaries into a single, cohesive document
    2. Eliminate redundant information
    3. Maintain all unique and critical information
    4. Ensure logical flow and organization
    5. Preserve technical accuracy and precision
    6. Keep cross-references and dependencies clear"""

    import tiktoken
    encoding = tiktoken.encoding_for_model("gpt-4o")
    system_tokens = len(encoding.encode(system_prompt))
    user_tokens = len(encoding.encode(user_prompt))

    async def make_api_call():
        response = await openai_async_client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2
        )
        return response.choices[0].message.content
    
    combined_summary = await retry_on_empty_response(make_api_call)

    output_tokens = calculate_number_of_tokens(combined_summary)

    total_tokens = system_tokens + user_tokens + output_tokens
    print(f"create_combined_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
    
    return combined_summary

# %%
async def generate_summary_from_section_info(section_info):
    """
    Generate a summary from section information when no relevant quality guideline summaries are found.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        
    Returns:
        str: A comprehensive summary generated from the section information
    """
    print(f"No relevant quality guideline summaries found for section {section_info.get('section', 'unknown')}, creating summary from section information")
    
    # Create a prompt for the LLM to generate a summary from section information
    system_prompt = """You are an expert in pharmaceutical quality guidelines and regulatory compliance. Your task is to create a comprehensive summary of a section of ICH eCTD guidelines based on the provided information.

    OUTPUT FORMAT:
    - Provide the summary in plain text format only
    - Do not use any special formatting, markdown, or symbols
    - Use simple bullet points with hyphens (-) when needed
    - Separate paragraphs with blank lines
    - Use parentheses for references or additional information

    CRITICAL REQUIREMENTS:
    1. Content Coverage:
       - Cover all aspects mentioned in the title and description
       - Address all points from the referenced quality guidelines
       - Include all key requirements and recommendations
       - Be specific enough to evaluate if other summaries cover all necessary information
    
    2. Structure and Clarity:
       - Maintain logical flow and organization
       - Use clear, concise language
       - Present technical information accurately
       - Focus on creating a cohesive, unified document"""
    
    user_prompt = f"""Please create a comprehensive summary of the following section of ICH eCTD guidelines:

    Section: {section_info.get('section', '')}
    Title: {section_info.get('title', '')}
    Description: {section_info.get('description', '')}
    Referenced Quality Guidelines: {', '.join(section_info.get('referenced_quality_guidelines', []))}

    Requirements:
    1. Cover all aspects mentioned in the title and description
    2. Address all points from the referenced quality guidelines
    3. Include all key requirements and recommendations
    4. Be specific enough to evaluate if other summaries cover all necessary information
    5. Maintain logical flow and organization
    6. Use clear, concise language
    7. Present technical information accurately"""
    
    async def make_api_call():
        response = await openai_async_client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2
        )
        return response.choices[0].message.content
    
    summary = await retry_on_empty_response(make_api_call)
    
    # Number of tokens in the combined summary
    summary_tokens = calculate_number_of_tokens(summary)
    print(f"generate_summary_from_section_info output tokens: {summary_tokens}")
    
    return summary

# %%
async def generate_checkpoints_from_combined_summary(section_info):
    """
    Generate checkpoints from the quality guideline combined summary.
    
    Args:
        section_info: Dictionary containing section information including quality_guideline_combined_summary
        
    Returns:
        List[str]: A list of checkpoints generated from the combined summary
    """
    checkpoints = []
    
    # Check if quality_guideline_combined_summary exists
    if "quality_guideline_combined_summary" in section_info and section_info["quality_guideline_combined_summary"]:
        print(f"Generating checkpoints from quality_guideline_combined_summary for section {section_info.get('section', '')}")
        
        # Create a prompt for the LLM to generate checkpoints from the combined summary
        prompt = f"""
        Create a detailed checklist of points that should be covered in input documents for the following section of ICH eCTD guidelines:
        
        Section: {section_info.get('section', '')}
        Title: {section_info.get('title', '')}

        Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
        
        Quality Guideline Summary:
        {section_info.get('quality_guideline_combined_summary', '')}
        
        The checklist should:
        1. Include all key requirements and recommendations for this section expressed as specific questions
        2. Cover all aspects mentioned in the quality guideline summary
        3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.
        4. Be specific enough to evaluate if an input document covers all necessary information
        5. Focus on what information should be present in input documents
        6. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., "Does the document mention if the product is in powder or liquid form?")
        7. Focus on technical and regulatory content needed for NDA documentation
        8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant
        9. Cover stability data, packaging specifications, and compatibility information where appropriate
        10. Include questions about the validation and verification methods used
        
        Return the checklist as a JSON array of strings, with each string being a checkpoint.
        {get_json_output_tags()}
        """

        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines."
        
        # Call the LLM to generate the checklist

        async def make_api_call():
            response = await openai_async_client.chat.completions.create(
                model=MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            return response.choices[0].message.content
        
        checkpoints = await retry_on_empty_response(make_api_call)
        
        # Number of tokens in the combined checkpoints
        checkpoints_tokens = calculate_number_of_tokens(checkpoints)

        system_tokens = caculate_number_of_tokens(system_prompt)
        user_tokens = caculate_number_of_tokens(prompt)
        output_tokens = checkpoints_tokens
        total_tokens = system_tokens + user_tokens + output_tokens
        print(f"generate_checkpoints_from_combined_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        
        # Parse the response using our helper function
        checkpoints = parse_json_response(checkpoints)
        if type(checkpoints) == dict:
            checkpoints = checkpoints[list(checkpoints.keys())[0]]
        print(f"Generated {len(checkpoints)} checkpoints from quality_guideline_combined_summary")
    
    return checkpoints

# %%
async def generate_checkpoint_list(section, title, description, referenced_quality_guidelines, enhanced_instructions_and_checkpoints):
    """
    Generate a checklist of points that should be covered in summaries for this section.
    
    Args:
        section: The section identifier (e.g., "3.2.P.2.2.1")
        title: The title of the section
        description: The description of the section
        referenced_quality_guidelines: List of quality guidelines referenced
        enhanced_instructions_and_checkpoints: A checkpoint list of golden instructions that must be strictly adhered.
        
    Returns:
        List[str]: A list of checkpoints that should be covered in summaries
    """
    # Create a prompt for the LLM to generate a checklist
    prompt = f"""
    Create a detailed checklist of points that should be covered in summaries for the following section of ICH eCTD guidelines:
    
    Section: {section}
    Title: {title}
    Description: {description}

    Golden Instructions and Checkpoints: {enhanced_instructions_and_checkpoints}
    
    Referenced Quality Guidelines: {', '.join(referenced_quality_guidelines)}
    
    The checklist should:
    1. Include all key requirements and recommendations for this section expressed as specific questions
    2. Cover all aspects mentioned in the title and description
    3. The referenced list of golden instructions and checkpoints must be strictly adhered. These are very high quality instructions which are derived from an actual NDA output and provide checkpoints and instructions based on its content.
    4. Address all points from the referenced quality guidelines
    5. Be specific enough to evaluate if a summary covers all necessary information
    6. Format EACH checkpoint as a question that can be answered with yes/no or specific information (e.g., "Does the document mention if the product is in powder or liquid form?")
    7. Focus on technical and regulatory content needed for NDA documentation
    8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant
    9. Cover stability data, packaging specifications, and compatibility information where appropriate
    10. Include questions about the validation and verification methods used

    Don't include references to sections/ guidelines in the checklist.
    
    Return the checklist as a JSON array of strings, with each string being a checkpoint.
    {get_json_output_tags()}
    """

    system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines."
    system_tokens = caculate_number_of_tokens(system_prompt)
    user_tokens = caculate_number_of_tokens(prompt)
    
    # Call the LLM to generate the checklist

    async def make_api_call():
        response = await openai_async_client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        return response.choices[0].message.content
    
    checkpoints = await retry_on_empty_response(make_api_call)
    
    # Number of tokens in the combined checkpoints
    checkpoints_tokens = calculate_number_of_tokens(checkpoints)

    output_tokens = checkpoints_tokens
    total_tokens = system_tokens + user_tokens + output_tokens
    print(f"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
    
    # Parse the response using our helper function
    checkpoints = parse_json_response(checkpoints)
    if type(checkpoints) == dict:
        checkpoints = checkpoints[list(checkpoints.keys())[0]]
    return checkpoints

# %%
async def evaluate_summary_relevance(summary_content, section_info, checkpoints):
    """
    Use an LLM to evaluate if a summary is relevant to a section.
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoints: List of checkpoints that should be covered
        
    Returns:
        float: relevance_score
    """
    # Create a prompt for the LLM to evaluate the summary
    prompt = f"""
    Evaluate if the following summary is relevant to the ICH eCTD guideline section described below by determining if it answers any of the checkpoint questions.
    
    SECTION INFORMATION:
    Section: {section_info.get('section', '')}
    Title: {section_info.get('title', '')}
    
    CHECKPOINT QUESTIONS:
    {json.dumps(checkpoints, indent=2)}
    
    SUMMARY TO EVALUATE:
    {summary_content}
    
    Task:
    Carefully analyze if the summary contains information that would answer any of the checkpoint questions listed above. A summary is considered relevant if it provides information to answer at least one checkpoint question, even partially.
    
    Guidelines for evaluation:
    1. For each checkpoint question, determine if the summary provides any information that helps answer it
    2. The summary does not need to explicitly mention the question itself, only provide relevant information
    3. Even partial answers to questions should count toward relevance
    4. Technical content that directly relates to the question topic is particularly important
    5. If multiple questions are answered, the summary should receive a higher relevance score
    6. Consider the depth and detail of the information provided when scoring relevance
    7. If any ONE of the checkpoint questions is answered in the summary, then the summary is relevant

    Please provide your evaluation as:
    1. Is this summary relevant to the section? (Yes/No)
    2. How relevant is it on a scale of 0-10? (0 = not relevant, 10 = highly relevant)
    - Score 0-2: Answers no questions or provides only tangential information
    - Score 3-5: Partially answers at least one question with limited detail
    - Score 6-8: Fully answers one question or partially answers multiple questions
    - Score 9-10: Comprehensively answers multiple questions with specific details
    
    IMPORTANT: You MUST return your evaluation as a JSON object with the following structure:
    {{
        "relevance_score": 0-10
    }}
    
    Do not include any text before or after this JSON structure. Ensure the response is valid JSON that can be parsed.
    {get_json_output_tags()}
    """

    system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines with exceptional abilities in evaluating the relevance of content to specific technical questions. You must follow the exact output format specified in the prompt."
    system_tokens = caculate_number_of_tokens(system_prompt)
    user_tokens = caculate_number_of_tokens(prompt)
    
    # Call the LLM to evaluate the summary

    async def make_api_call():
        response = await openai_async_client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
            temperature=0.1
        )
        return response.choices[0].message.content
    
    evaluation = await retry_on_empty_response(make_api_call)

    output_tokens = caculate_number_of_tokens(evaluation)
    total_tokens = system_tokens + user_tokens + output_tokens
    print(f"evaluate_summary_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
    
    # Parse the response using our helper function
    evaluation = parse_json_response(evaluation)
    return evaluation.get("relevance_score", 0)

# %%
async def search_summaries_by_llm(section_info, quality_guidelines, summary_level=1):
    """
    Search for summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        quality_guidelines: List of quality guidelines to search in (e.g., ["Q6A", "Q6B"])
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['quality_docs_summaries']
    
    # Find all summaries matching the criteria
    if len(quality_guidelines) > 0:
        summaries = list(collection.find({
            "quality_guideline": {"$in": quality_guidelines},
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))

    # summaries = list(collection.find({
    #     "summary_level": summary_level
    # }))

    print(f"Found {len(summaries)} summaries for section {section_info.get('section', '')} quality guidelines {quality_guidelines} at level {summary_level}")
    
    if not summaries:
        print(f"No summaries found for quality guidelines {quality_guidelines} at level {summary_level}")
        return []
    
    # Get checkpoints for the section
    checkpoints = section_info.get("checkpoint_list", [])
    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        # Generate checkpoints if not already available
        checkpoints = await generate_checkpoint_list(
            section_info.get("section", ""),
            section_info.get("title", ""),
            section_info.get("description", ""),
            section_info.get("referenced_quality_guidelines", []),
            section_info.get("enhanced_instructions_and_checkpoints", "")
        )
    
    # Evaluate each summary
    evaluated_summaries = []
    tasks = [evaluate_summary_relevance(summary["content"], section_info, checkpoints) for summary in summaries]
    results = await asyncio.gather(*tasks)
    
    for summary, relevance_score in zip(summaries, results):
        if relevance_score >= get_relevance_threshold(summary_level):
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
            summary["similarity_score"] = relevance_score
            evaluated_summaries.append(summary)
        else:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
    
    # Sort by relevance score
    evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries

# %%
def get_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['quality_docs_summaries']
    chunk_collection = db['quality_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks

# %%
def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['quality_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries

# %%
async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Extract section information
    section = section_info.get("section", "")
    title = section_info.get("title", "")
    description = section_info.get("description", "")
    referenced_quality_guidelines = section_info.get("referenced_quality_guidelines", [])
    
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_summaries_by_llm(
        section_info=section_info,
        quality_guidelines=referenced_quality_guidelines,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    for final_summary in final_summaries:
        summaries = get_summaries_for_final_summary(final_summary["_id"])
        
        # Evaluate each individual summary
        evaluated_summaries = []
        tasks = [evaluate_summary_relevance(summary["content"], section_info, section_info.get("checkpoint_list", [])) for summary in summaries]
        results = await asyncio.gather(*tasks)
        
        for summary, relevance_score in zip(summaries, results):
            if relevance_score >= get_relevance_threshold(2):
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
                summary["similarity_score"] = relevance_score
                evaluated_summaries.append(summary)
            else:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
        
        # Sort by relevance score
        evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
        individual_summaries.extend(evaluated_summaries)
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    # BATCH_SIZE = 50
    
    # for i in range(0, len(individual_summaries), BATCH_SIZE):
    #     batch = individual_summaries[i:i + BATCH_SIZE]
    #     batch_tasks = []
        
    #     for individual_summary in batch:
    #         summary_chunks = get_chunks_for_summary(individual_summary["_id"])
    #         # Create tasks for parallel evaluation of chunks
    #         tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
    #         batch_tasks.extend(zip(summary_chunks, tasks))
        
    #     # Execute all tasks in parallel
    #     results = await asyncio.gather(*[task for _, task in batch_tasks])
        
    #     # Process results
    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):
    #         if relevance_score >= get_relevance_threshold(0):
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    #             chunk["similarity_score"] = relevance_score
    #             chunks.append(chunk)
    #         else:
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks

# %%
async def extract_relevant_chunks(json_obj):
    """
    Recursively iterates through the nested JSON structure, extracts titles and descriptions,
    generates checkpoint lists, and finds relevant summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with added relevant summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs processing
            if (obj and "title" in obj and "description" in obj and 
                "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        # Generate checkpoint list for this section
        section["checkpoint_list"] = await generate_checkpoint_list(
            section["section"], 
            section["title"], 
            section["description"], 
            section.get("referenced_quality_guidelines", []),
            section.get("enhanced_instructions_and_checkpoints", "")
        )
        
        # Find relevant summaries and chunks for this section
        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(
            section
        )

        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks")
        
        # Add the results to the section
        section["relevant_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "quality_guideline": s["quality_guideline"], 
                "similarity_score": s["similarity_score"],
                "content": s["content"]
            } for s in final_summaries
        ]
        
        section["relevant_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "quality_guideline": s["quality_guideline"], 
                "similarity_score": s.get("similarity_score", 0),
                "content": s["content"]
            } for s in individual_summaries
        ]

        # Create a final summary from the relevant individual summaries
        if len(section["relevant_individual_summaries"]) > 0:
            final_summary = await create_combined_summary([summary["content"] for summary in section["relevant_individual_summaries"]])
            section["quality_guideline_combined_summary"] = final_summary
        else:
            # If no relevant summaries found, create a summary from section information
            final_summary = await generate_summary_from_section_info(section)
            section["quality_guideline_combined_summary"] = final_summary

        # Generate checkpoints from quality_guideline_combined_summary
        checkpoints = await generate_checkpoints_from_combined_summary(section)
        print(f"Generated {len(checkpoints)} checkpoints from quality_guideline_combined_summary for section {section.get('section', '')}")

        section["quality_guideline_combined_summaries_checkpoint_list"] = checkpoints
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj

# %%
processed_doc = await extract_relevant_chunks(doc_to_work_on)

# Save the results to a JSON file
write_to_json(processed_doc, "processed_ectd_guidelines_with_relevant_chunks.json")

# %%
processed_doc = read_json("processed_ectd_guidelines_with_relevant_chunks.json")

# %%
async def search_input_docs_by_llm(section_info, input_doc_tag, summary_level=1):
    """
    Search for input document summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        input_doc_tag: The input document tag to search for
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['input_docs_summaries']
    
    # Find all summaries matching the criteria
    if input_doc_tag:
        summaries = list(collection.find({
            "input_doc_tag": input_doc_tag,
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))
    
    print(f"Found {len(summaries)} input document summaries for section {section_info.get('section', '')} with tag {input_doc_tag} at level {summary_level}")
    
    if not summaries:
        print(f"No input document summaries found for tag {input_doc_tag} at level {summary_level}")
        return []
    
    # Get checkpoints for the section - prefer using checkpoints from quality_guideline_combined_summary
    print(f"Using {len(section_info.get('quality_guideline_combined_summaries_checkpoint_list', []))} quality_guideline_combined_summaries_checkpoint_list from quality_guideline_combined_summary for section {section_info.get('section', '')}")
    checkpoints = section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])

    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        return []
    
    # Evaluate each summary
    evaluated_summaries = []
    tasks = [evaluate_summary_relevance(summary["content"], section_info, checkpoints) for summary in summaries]
    results = await asyncio.gather(*tasks)
    
    for summary, relevance_score in zip(summaries, results):
        if relevance_score >= get_relevance_threshold(summary_level):
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
            summary["similarity_score"] = relevance_score
            evaluated_summaries.append(summary)
        else:
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(summary_level)}")
    
    # Sort by relevance score
    evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries

# %%
def get_input_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the input document chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['input_docs_summaries']
    chunk_collection = db['input_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks

# %%
def get_input_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual input document summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['input_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries

# %%
async def filter_input_docs_by_section(section_info: Dict[str, Any], input_doc_tag: str = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter input document summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        input_doc_tag: The input document tag to search for
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_input_docs_by_llm(
        section_info=section_info,
        input_doc_tag=input_doc_tag,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    BATCH_SIZE = 50
    
    for i in range(0, len(final_summaries), BATCH_SIZE):
        batch = final_summaries[i:i + BATCH_SIZE]
        batch_tasks = []
        
        for final_summary in batch:
            summaries = get_input_summaries_for_final_summary(final_summary["_id"])
            # Create tasks for parallel evaluation of individual summaries
            tasks = [evaluate_summary_relevance(summary["content"], section_info, section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])) for summary in summaries]
            batch_tasks.extend(zip(summaries, tasks))
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*[task for _, task in batch_tasks])
        
        # Process results
        for (summary, _), (relevance_score) in zip(batch_tasks, results):
            if relevance_score >= get_relevance_threshold(2):
                print(f"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
                summary["similarity_score"] = relevance_score
                individual_summaries.append(summary)
            else:
                print(f"filter_input_docs_by_section, Summary {summary['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(2)}")
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    
    for i in range(0, len(individual_summaries), BATCH_SIZE):
        batch = individual_summaries[i:i + BATCH_SIZE]
        batch_tasks = []
        
        for individual_summary in batch:
            summary_chunks = get_input_chunks_for_summary(individual_summary["_id"])
            # Create tasks for parallel evaluation of chunks
            tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
            batch_tasks.extend(zip(summary_chunks, tasks))
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*[task for _, task in batch_tasks])
        
        # Process results
        for (chunk, _), (relevance_score) in zip(batch_tasks, results):
            if relevance_score >= get_relevance_threshold(0):
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
                chunk["similarity_score"] = relevance_score
                chunks.append(chunk)
            else:
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks

# %%
async def extract_relevant_input_docs(json_obj, input_doc_tag: str = None):
    """
    Recursively iterates through the nested JSON structure, finds relevant quality guideline summaries,
    creates a final summary, and then finds relevant input document summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        input_doc_tag: The input document tag to search for
        
    Returns:
        Dict: The processed JSON object with added relevant input document summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that has relevant quality guideline summaries
            if (obj and "relevant_final_summaries" in obj and 
                obj["relevant_final_summaries"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Processing section {section.get('section', 'unknown')} for input documents")

        # Find relevant input document summaries and chunks
        input_final_summaries, input_individual_summaries, input_chunks = await filter_input_docs_by_section(
            section,
            input_doc_tag
        )
        
        # Add the results to the section
        section["relevant_input_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "input_doc_tag": s["input_doc_tag"], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            } for s in input_final_summaries
        ]
        
        section["relevant_input_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "input_doc_tag": s["input_doc_tag"], 
                "similarity_score": s.get("similarity_score", 0), 
                "content": s["content"]
            } for s in input_individual_summaries
        ]
        
        section["relevant_input_chunks"] = [
            {
                "_id": str(c["_id"]), 
                "input_doc_tag": c["input_doc_tag"], 
                "chunk_filename": c.get("chunk_filename", ""), 
                "similarity_score": c.get("similarity_score", 0), 
                "content": c.get("content", "")
            } for c in input_chunks
        ]
        
        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(input_final_summaries)} relevant input final summaries, {len(input_individual_summaries)} relevant input individual summaries, and {len(input_chunks)} relevant input chunks")
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj

# %%
# Process the document to find relevant input documents
processed_doc_with_input_docs = await extract_relevant_input_docs(processed_doc, input_doc_tag="input_docs")

# Save the results to a JSON file
write_to_json(processed_doc_with_input_docs, "processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")

# %%
processed_doc_with_input_docs = read_json("processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")

# %%
import os
from openai import AsyncOpenAI
from dotenv import load_dotenv

load_dotenv()

CRITIQUE_MODEL_API_KEY = os.getenv("CRITIQUE_MODEL_API_KEY")
CRITIQUE_MODEL_BASE_URL = os.getenv("CRITIQUE_MODEL_BASE_URL")
CRITIQUE_MODEL_NAME = os.getenv("CRITIQUE_MODEL_NAME")

print("CRITIQUE_MODEL_API_KEY", CRITIQUE_MODEL_API_KEY)
print("CRITIQUE_MODEL_BASE_URL", CRITIQUE_MODEL_BASE_URL)
print("CRITIQUE_MODEL_NAME", CRITIQUE_MODEL_NAME)

anthropic_client = AsyncOpenAI(base_url=CRITIQUE_MODEL_BASE_URL, api_key=CRITIQUE_MODEL_API_KEY)

# %%
def create_nda_output_prompt(section_info):
    """
    Creates a comprehensive prompt for generating NDA (New Drug Application) output based on section information.
    This function is a critical component in the NDA documentation pipeline, responsible for constructing
    detailed prompts that guide the generation of regulatory-compliant documentation.

    The function handles multiple aspects of prompt creation:
    1. Incorporates previous evaluation feedback and outputs if available
    2. Constructs a detailed system prompt with specific requirements for NDA generation
    3. Organizes input document chunks and quality guideline summaries
    4. Ensures compliance with ICH eCTD v3.2.2 requirements
    5. Maintains context from previous iterations and evaluations

    The generated prompts emphasize:
    - Strict adherence to ICH eCTD guidelines and formatting requirements
    - Comprehensive technical documentation and validation details
    - Professional narrative style with clear structure and organization
    - Integration of tables, figures, and cross-references
    - Handling of decision trees and complex regulatory requirements

    Args:
        section_info (dict): A dictionary containing:
            - section (str): The section identifier
            - title (str): The section title
            - description (str): Detailed section description
            - enhanced_instructions_and_checkpoints (str): Golden instructions and checkpoints
            - formatting_instructions (str): Specific formatting requirements
            - quality_guideline_combined_summary (str, optional): Summary of relevant quality guidelines
            - relevant_input_chunks (list, optional): List of relevant input document chunks
            - nda_output_evaluation_json (dict, optional): Previous evaluation results
            - nda_output (str, optional): Previous NDA output

    Returns:
        tuple: A tuple containing:
            - str: System prompt with detailed instructions and requirements
            - str: User prompt with section-specific information and context

    Note:
        The function prioritizes comprehensive documentation over brevity, as per regulatory
        requirements. It ensures that all critical information is preserved and presented
        in a format suitable for regulatory submission.
    """
    # First get the evaluation feedback and previous output if they exist
    evaluation_feedback = ""
    previous_output = ""
    if "nda_output_evaluation_json" in section_info and "nda_output" in section_info:
        eval_json = section_info["nda_output_evaluation_json"]
        previous_output = section_info["nda_output"]
        evaluation_feedback = f"""
    Previous Output and Evaluation Feedback:
    
    Previous NDA Output:
    {previous_output}
    
    Evaluation Results:
    - Overall Rating: {eval_json['overall_rating']}/10
    
    Key Strengths:
    {chr(10).join(f"- {strength}" for strength in eval_json['key_strengths'])}
    
    Critical Issues to Address:
    {chr(10).join(f"- {issue}" for issue in eval_json['critical_issues'])}
    
    Required Improvements:
    {chr(10).join(f"- {improvement}" for improvement in eval_json['required_improvements'])}
    
    Additional Recommendations:
    {chr(10).join(f"- {recommendation}" for recommendation in eval_json['additional_recommendations'])}
    """

    system_prompt = f"""
    You are an expert in pharmaceutical documentation and ICH guidelines. Generate a comprehensive NDA section based on the following information. Remember that in NDA documentation, providing extra information is better than missing critical details. Also make sure you are creating the output for a newdrug application (NDA) for a novel drug and not an abbreviated new drug application (aNDA). Use relevant excerpts from the context to ensure accuracy. The response should be formatted professionally in accordance with the ICH eCTD template and suitable for submission. Structure the response appropriately for ICH eCTD compliance. Also output tables whenever mentioned in the descriptions or there are tables present in the given context.
    You'll be given a section description, reference text, quality guideline summary, input document chunks and feedback from previous output.

   Instructions for NDA Generation:

    0. Golden Instructions and Formatting Requirements (HIGHEST PRIORITY):
       - STRICTLY follow the Golden Instructions and Checkpoints provided
       - STRICTLY adhere to the Formatting Instructions provided
       - These instructions take precedence over all other requirements
       - Ensure complete compliance with both sets of instructions
       - Address every point mentioned in the Golden Instructions
       - Follow the exact format specified in the Formatting Instructions
       - Prioritize these instructions over all other considerations

    1. ICH eCTD Section Requirements (HIGH PRIORITY):
       - STRICTLY follow the section description
       - STRICTLY adhere to the reference text
       - Ensure complete compliance with ICH eCTD v3.2.2 requirements
       - Address every point mentioned in the section description
       - Include all elements specified in the reference text
       - Follow the exact structure and format required by ICH eCTD
       - Prioritize regulatory compliance over other considerations

    2. Content Requirements:
       - Address ALL points mentioned in the section description
       - Include comprehensive details for each requirement
       - Provide specific examples and data where applicable
       - Reference relevant sections and guidelines explicitly
       - Include any cross-references to other sections
       - Specifically address any critical issues from previous evaluation
       - Implement all required improvements from feedback
       - Maintain and enhance the strengths identified in previous output
       - Avoid repeating any issues identified in previous evaluation

    3. Technical Depth:
       - Provide detailed technical explanations
       - Include specific parameters, measurements, and criteria
       - Explain methodologies and approaches used
       - Discuss any deviations or special considerations
       - Include validation and verification details
       - Ensure all technical specifications meet previous evaluation standards
       - Add missing technical details identified in previous evaluation
       - Enhance technical documentation where weaknesses were found

    4. Documentation Standards:
       - Follow ICH eCTD format requirements
       - Use clear, precise, and professional language
       - Include all necessary tables, figures, and references
       - Provide justification for critical decisions
       - Document any assumptions or limitations
       - Address any documentation-related feedback from previous evaluation
       - Improve formatting and structure based on previous feedback
       - Ensure all required documentation elements are present

    5. ICH eCTD Guidelines Compliance:
       - Address all requirements from the ICH eCTD guidelines summary
       - Include specific acceptance criteria
       - Document validation methods
       - Provide justification for any deviations
       - Reference specific guideline sections
       - Ensure compliance with all previous evaluation recommendations
       - Strengthen areas where compliance was questioned
       - Add missing compliance elements identified in previous evaluation

    6. Additional Considerations:
       - Include historical development context
       - Address potential future considerations
       - Discuss risk management approaches
       - Include any post-approval commitments
       - Provide references to supporting documentation
       - Incorporate all additional recommendations from previous evaluation
       - Add missing context identified in previous evaluation
       - Enhance areas where depth was insufficient

    7. Structure and Organization:
       - Make sure that there are no section heading and introductions present in the output as we'll be adding them later manually
       - Use clear headings and subheadings
       - Include executive summaries where appropriate
       - Provide clear section transitions
       - Use bullet points and tables for clarity
       - Include appendices for detailed data
       - Address any structural issues identified in previous evaluation
       - Improve organization based on previous feedback
       - Ensure logical flow between sections

    8. Completeness Checklist:
       - All required elements from section description
       - All quality guideline requirements
       - All technical specifications
       - All validation and verification details
       - All supporting documentation references
       - All cross-references
       - All justifications and rationales
       - All risk assessments
       - All future considerations
       - All improvements suggested in previous evaluation
       - All critical issues addressed
       - All additional recommendations implemented
       - All missing elements from previous output added
       - All identified gaps filled
       - All previous weaknesses strengthened

    9. Narrative Style and Descriptiveness:
       - Write in a narrative, story-like manner that flows naturally
       - Use descriptive language that paints a complete picture
       - Organize information into well-structured paragraphs
       - Connect ideas with transitional phrases
       - Provide context and background for technical information
       - Explain the significance and implications of findings
       - Use examples and analogies to illustrate complex concepts
       - Include detailed explanations rather than just listing facts
       - Write as if explaining to a knowledgeable but non-specialist reviewer
       - Be thorough and comprehensive in descriptions
       - Remember that more information is better than less in NDA documentation
       - Aim for a document that reads like a professional narrative, not just a technical report

    10. Decision Tree Handling (if applicable):
       - Carefully analyze any decision trees present in the quality guideline chunks
       - Identify all decision points, conditions, and outcomes in the decision trees
       - Document the complete decision flow with all possible paths
       - Explain the rationale behind each decision point
       - Include all relevant criteria and thresholds for each decision
       - Address how the drug or process fits within the decision tree framework
       - Provide justification for the specific path taken through the decision tree
       - Document any deviations from standard decision paths with proper rationale
       - Ensure all decision tree outcomes are properly addressed in the documentation
       - Include references to specific decision tree elements from the quality guidelines

    11. Table and Section Handling:
       - For any tables present in the input or mentioned in descriptions:
         * Include the complete table with all its data
         * Add a detailed description explaining the table's purpose and contents
         * Explain how the table relates to the section's content
         * Reference the table appropriately in the text
       - For multiple sections in the reference text:
         * Include all sections with their complete descriptions
         * Maintain the relationship between different sections
         * Explain how sections relate to each other
         * Ensure no section is omitted or only partially included
       - For section linking:
         * Add clear links between related sections
         * Explain the relationship between linked sections
         * Ensure cross-references are accurate and complete
         * Maintain logical flow between linked sections

    Format your response in a clear, professional manner suitable for regulatory submission. Include all necessary details, even if they seem obvious or standard. Remember that regulatory reviewers prefer comprehensive documentation over concise summaries.

    Return the complete NDA section content.
    Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. Donot include any other formatting like xml, html, plaintext etc.
    """

    # Prepare input document chunks summary
    input_docs_summary = ""
    if "relevant_input_chunks" in section_info and section_info["relevant_input_chunks"]:
        input_chunks = section_info["relevant_input_chunks"]
        input_docs_summary = "Input Document Chunks:\n\n"
        
        for chunk in input_chunks:
            content = chunk.get("content", "")
            if content:
                input_docs_summary += f"Chunk from {chunk.get('input_doc_tag', 'unknown')}:\n{content}\n\n"
            else:
                input_docs_summary += f"Chunk from {chunk.get('input_doc_tag', 'unknown')}: [Content not available]\n\n"
    
    # # Prepare quality guideline chunks summary
    # quality_guideline_chunks_summary = ""
    # if "relevant_chunks" in section_info and section_info["relevant_chunks"]:
    #     quality_chunks = section_info["relevant_chunks"]
    #     quality_guideline_chunks_summary = "Quality Guideline Detailed Information:\n\n"
        
    #     # Get MongoDB client
    #     client = get_mongodb_client()
    #     db = client['mednova']
    #     chunk_collection = db['quality_docs_chunks']
        
    #     for chunk in quality_chunks:
    #         # Fetch the chunk content from MongoDB
    #         chunk_id = chunk.get('_id')
    #         if chunk_id:
    #             chunk_doc = chunk_collection.find_one({"_id": ObjectId(chunk_id)})
    #             if chunk_doc and "content" in chunk_doc:
    #                 quality_guideline_chunks_summary += f"Chunk from {chunk.get('quality_guideline', 'unknown')}:\n{chunk_doc['content']}\n\n"
    #             else:
    #                 quality_guideline_chunks_summary += f"Chunk from {chunk.get('quality_guideline', 'unknown')}: [Content not available]\n\n"
    #         else:
    #             quality_guideline_chunks_summary += f"Chunk from {chunk.get('quality_guideline', 'unknown')}: [ID not available]\n\n"
        
    #     # Close MongoDB connection
    #     client.close()
    

    # Prepare quality guideline summary
    quality_guideline_summary = ""
    if "quality_guideline_combined_summary" in section_info and section_info["quality_guideline_combined_summary"]:
        quality_guideline_summary = f"Quality Guideline Summary:\n\n{section_info['quality_guideline_combined_summary']}\n\n"
    
    user_prompt = f"""
    Section Information:
    - Section: {section_info.get('section', '')}
    - Title: {section_info.get('title', '')}
    - Description: {section_info.get('description', '')}
    - Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
    - Formatting Instructions: {section_info.get("formatting_instructions", "")}
    
    Quality Guideline Summary:
    {quality_guideline_summary}
    
    Input Document Context:
    {input_docs_summary}

    {evaluation_feedback}
    """
    
    return system_prompt, user_prompt

# %%
def create_nda_output_critique_prompt(section_info, nda_output):
    """
    Create a prompt for critiquing NDA output.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        Tuple[str, str]: System prompt and user prompt for NDA output critique
    """
    system_prompt = f"""
    You are a strict regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate the provided NDA section output against the quality guidelines summary and ICH eCTD v3.2.2 requirements.
    You'll be given a section description, reference text, quality guideline summary and NDA output.

    Instructions for Critical Evaluation:

    0. Golden Instructions and Formatting Compliance (HIGHEST PRIORITY):
       - STRICTLY evaluate adherence to Golden Instructions and Checkpoints
       - STRICTLY verify compliance with Formatting Instructions
       - These evaluations take precedence over all other assessments
       - Check if every point in Golden Instructions is addressed
       - Verify exact compliance with Formatting Instructions
       - Consider non-compliance with these instructions as critical issues
       - Prioritize these evaluations over all other checks

    1. ICH eCTD v3.2.2 Compliance Assessment (HIGH PRIORITY):
       - Evaluate adherence to eCTD structure and format
       - Check for proper section organization
       - Verify required elements are present
       - Assess document hierarchy
       - Review cross-referencing accuracy
       - STRICTLY verify compliance with section description
       - STRICTLY verify compliance with reference text
       - Ensure all requirements from the ICH eCTD guidelines are met

    2. Quality Guidelines Summary Coverage:
       - Verify all key requirements from the quality guideline summary are addressed
       - Check if critical considerations are covered
       - Assess documentation completeness
       - Evaluate compliance with the quality guideline summary
       - Review implementation of recommendations

    3. Technical Accuracy and Completeness:
       - Verify technical specifications
       - Check parameter accuracy
       - Assess validation details
       - Review analytical methods
       - Evaluate data presentation

    4. Content Quality Assessment:
       - Check for clarity and precision
       - Evaluate professional language
       - Assess logical flow
       - Review completeness of explanations
       - Verify data consistency

    5. Hallucination Detection:
       - Identify any unsupported claims
       - Check for fabricated data
       - Verify guideline references
       - Assess technical accuracy
       - Review consistency with source material

    6. Regulatory Compliance:
       - Evaluate adherence to guidelines
       - Check for regulatory requirements
       - Assess justification quality
       - Review risk management
       - Verify post-approval considerations

    7. Documentation Standards:
       - Check formatting consistency. Make sure that the output is markdown formatted and shouldnot have any backticks or code blocks. The output should not include any other formatting like xml, html, plaintext etc.
       - Check if there are section heading present, we dont want markdown headings in the output as we'll be adding them later manually
       - Evaluate table and figure quality
       - Assess reference accuracy
       - Review appendix completeness
       - Verify document structure

    8. Improvement Areas:
       - Identify missing elements
       - Note unclear sections
       - Flag incomplete information
       - Highlight inconsistencies
       - Suggest specific enhancements

    9. Decision Tree Handling (if applicable):
       - If decision trees are present in the quality guidelines, assess how well they are addressed
       - Verify that decision points and outcomes are properly documented
       - Check if the rationale for chosen paths is clearly explained
       - Note any missing elements related to decision trees

    10. Table and Section Handling Evaluation:
        - For tables:
          * Verify that all tables are included with complete data
          * Check if each table has a detailed description explaining its purpose and contents
          * Assess if the relationship between tables and section content is clearly explained
          * Verify that tables are properly referenced in the text
        - For multiple sections:
          * Confirm that all sections from the reference text are included
          * Check if section relationships are properly maintained
          * Verify that no sections are omitted or partially included
          * Assess if section connections are clearly explained
        - For section linking:
          * Verify that all necessary links between sections are present
          * Check if the relationships between linked sections are clearly explained
          * Assess the accuracy and completeness of cross-references
          * Verify that the logical flow between linked sections is maintained

    Make sure that if there are any tables present then their descriptions along with the table are present in the output.
    Also, if there are multiple sections present in the reference text then make sure that all of them along with their descriptions are present in the output.

    Rating Criteria (1-10):
    1-3: Major deficiencies, significant non-compliance
    4-6: Moderate issues, partial compliance
    7-8: Minor issues, mostly compliant
    9-10: Excellent compliance, minimal improvements needed

    Provide your evaluation in the following format:

    1. Overall Rating:
       [Provide numerical rating]

    2. Key Strengths:
       - [List main strengths]
       - [Highlight positive aspects]
       - [Note well-executed elements]

    3. Critical Issues:
       - [List major problems]
       - [Highlight compliance gaps]
       - [Note significant deficiencies]

    4. Required Improvements:
       - [Specific changes needed]
       - [Missing elements to add]
       - [Areas needing enhancement]

    5. Additional Recommendations:
       - [Best practices to implement]
       - [Quality enhancement suggestions]
       - [Future considerations]

    Be thorough and critical in your evaluation. Focus on regulatory compliance and completeness. Provide specific, actionable feedback for improvement.

    Return your evaluation in the specified format without any additional text or formatting.
    """

    # Prepare quality guideline summary
    quality_guideline_summary = ""
    if "quality_guideline_combined_summary" in section_info and section_info["quality_guideline_combined_summary"]:
        quality_guideline_summary = f"Quality Guideline Summary:\n\n{section_info['quality_guideline_combined_summary']}\n\n"

    user_prompt = f"""
    Section Information:
    - Section: {section_info.get('section', '')}
    - Title: {section_info.get('title', '')}
    - Description: {section_info.get('description', '')}
    - Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
    - Formatting Instructions: {section_info.get("formatting_instructions", "")}

    {quality_guideline_summary}

    NDA Output to Evaluate:
    {nda_output}"""

    return system_prompt, user_prompt

# %%
def extract_evaluation_into_json(evaluation_text):
    """
    Extracts the numerical rating and structured feedback from the evaluation text into a JSON format.
    
    Args:
        evaluation_text: The evaluation text to parse
        
    Returns:
        Dict: JSON object containing the evaluation results
    """
    # Initialize the result dictionary
    result = {
        "overall_rating": None,
        "key_strengths": [],
        "critical_issues": [],
        "required_improvements": [],
        "additional_recommendations": []
    }
    
    # Split the text into sections
    sections = evaluation_text.split('\n\n')
    
    for section in sections:
        section = section.strip()
        if not section:
            continue
            
        # Extract overall rating
        if section.startswith("1. Overall Rating"):
            try:
                # Extract the number from the rating line
                rating_text = section.split(":")[1].strip()
                result["overall_rating"] = float(rating_text.split('/')[0])
            except:
                result["overall_rating"] = None
                
        # Extract key strengths
        elif section.startswith("2. Key Strengths"):
            points = section.split('\n')[1:]
            result["key_strengths"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract critical issues
        elif section.startswith("3. Critical Issues"):
            points = section.split('\n')[1:]
            result["critical_issues"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract required improvements
        elif section.startswith("4. Required Improvements"):
            points = section.split('\n')[1:]
            result["required_improvements"] = [point.strip('- ').strip() for point in points if point.strip()]
            
        # Extract additional recommendations
        elif section.startswith("5. Additional Recommendations"):
            points = section.split('\n')[1:]
            result["additional_recommendations"] = [point.strip('- ').strip() for point in points if point.strip()]
    
    return result

# %%
async def evaluate_nda_output(section_info, nda_output):
    """
    Evaluate NDA output against quality guidelines and section information.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to evaluate
        
    Returns:
        str: Evaluation text
    """
    system_prompt, user_prompt = create_nda_output_critique_prompt(section_info, nda_output)
    
    async def make_api_call():
        response = await anthropic_client.chat.completions.create(
            model=CRITIQUE_MODEL_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
             {"role": "user", "content": user_prompt}
         ],
            temperature=0.2
        )
        return response.choices[0].message.content
    
    evaluation = await retry_on_empty_response(make_api_call)
    
    system_tokens = caculate_number_of_tokens(system_prompt)
    user_tokens = caculate_number_of_tokens(user_prompt)
    output_tokens = caculate_number_of_tokens(evaluation)
    total_tokens = system_tokens + user_tokens + output_tokens
    print(f"evaluate_nda_output token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return evaluation

# %%
async def generate_nda_output(section_info):
    """
    Generate NDA output for a section.
    
    Args:
        section_info: Dictionary containing section information
        
    Returns:
        str: Generated NDA output
    """
    system_prompt, user_prompt = create_nda_output_prompt(section_info)
    
    async def make_api_call():
        response = await openai_async_client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": system_prompt}, 
                {"role": "user", "content": user_prompt}
        ],
            temperature=0.3
        )
        return response.choices[0].message.content
    
    nda_output = await retry_on_empty_response(make_api_call)
    
    # Calculate token counts
    system_tokens = caculate_number_of_tokens(system_prompt)
    user_tokens = caculate_number_of_tokens(user_prompt)
    output_tokens = caculate_number_of_tokens(nda_output)
    total_tokens = system_tokens + user_tokens + output_tokens
    print(f"generate_nda_output token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return nda_output

# %%
async def rate_nda_output_and_self_improve(section_info):
    """
    Generate NDA output, evaluate it, and improve it based on feedback.
    
    Args:
        section_info: Dictionary containing section information
        
    Returns:
        Dict: Updated section information with NDA output and evaluation
    """
    overall_rating = 0
    counter = 0
    nda_output_evaluation_json = None

    answers_list = []

    while(overall_rating < 9 and counter < 3):
        # Generate and evaluate in parallel
        nda_output = await generate_nda_output(section_info)
        
        nda_output_evaluation = await evaluate_nda_output(section_info, nda_output)
        nda_output_evaluation_json = extract_evaluation_into_json(nda_output_evaluation)
        section_info["nda_output_evaluation_json"] = nda_output_evaluation_json
        overall_rating = nda_output_evaluation_json["overall_rating"]
        counter += 1

        answers_list.append(
            {
                "nda_output": nda_output,
                "nda_output_evaluation_json": nda_output_evaluation_json,
                "overall_rating": overall_rating
            }
        )

        print(f"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}")

    # Select best answer based on overall rating
    best_answer = max(answers_list, key=lambda x: x["overall_rating"])
    section_info["nda_output"] = best_answer["nda_output"]
    section_info["nda_output_evaluation_json"] = best_answer["nda_output_evaluation_json"]

    print(f"Selecting best answer for section {section_info.get('section', 'unknown')} with rating {best_answer['overall_rating']}")

    return section_info

# %%
async def generate_nda_for_each_section(json_obj):
    """
    Recursively process a JSON object and generate NDA output for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with NDA outputs
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs NDA output generation
            if (obj and "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Generating NDA output for section {section.get('section', 'unknown')}")
        
        # Generate NDA output for this section
        processed_section = await rate_nda_output_and_self_improve(section)
        
        print(f"Completed NDA output generation for section {section.get('section', 'unknown')}")
        
        return processed_section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj

# %%
# Process the document to find relevant input documents
nda_output_json = await generate_nda_for_each_section(processed_doc_with_input_docs)

# Save the results to a JSON file
write_to_json(nda_output_json, "nda_output_json.json")

# %%
import pypandoc

def format_answer(json_obj, formatted_text="", depth=1):
    """
    Recursively formats the answer JSON into a structured Markdown document.
    Uses '#' for headings based on depth.
    """
    if isinstance(json_obj, dict):
        if "section" in json_obj and "title" in json_obj:
            formatted_text += f"{'#' * (depth)} {json_obj['section']} {json_obj['title']}\n\n"
            if "nda_output" in json_obj:
                formatted_text += f"{json_obj['nda_output']}\n\n"

        for key, value in json_obj.items():
            if isinstance(value, dict):
                formatted_text = format_answer(value, formatted_text, depth + 1)  # Increase depth

    return formatted_text

def save_answer_file(json_obj, filename="formatted_answer.md"):
    """
    Saves the formatted answer text to a file.
    """
    formatted_text = format_answer(json_obj)
    with open(filename, "w", encoding="utf-8") as file:
        file.write(formatted_text)
    print(f"Formatted answer saved to {filename}")

save_answer_file(nda_output_json)

# %%
pypandoc.convert_file("formatted_answer.md", 'docx', outputfile="formatted_answer.docx")

# %%
from docx import Document
from docx.shared import Inches
from copy import deepcopy
import os
import shutil
import zipfile
from docx.oxml.ns import qn

def remove_all_tables(doc):
    body = doc.element.body
    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]
    for tbl in tables_to_remove:
        body.remove(tbl)
        
def extract_images(docx_path, extract_folder):
    with zipfile.ZipFile(docx_path, 'r') as zip_ref:
        zip_ref.extractall(extract_folder)

def find_all_paragraphs_by_text(doc, search_text):
    return [para for para in doc.paragraphs if search_text in para.text]

def copy_table_with_break(source_table, insert_after_paragraph):
    tbl_element = source_table._tbl
    tbl_copy = deepcopy(tbl_element)
    insert_after_paragraph._element.addnext(tbl_copy)
    
    # Insert an empty paragraph after the table (acts as a line break)
    from docx.oxml import OxmlElement
    p = OxmlElement('w:p')
    tbl_copy.addnext(p)


def insert_image_before_paragraph(doc, image_path, paragraph):
    run = paragraph.insert_paragraph_before().add_run()
    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed

def main():
    source_docx = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report.docx'
    target_docx = 'formatted_answer.docx'
    extract_folder = 'source_extracted'

    # Step 1: Extract images from source.docx
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
    extract_images(source_docx, extract_folder)

    media_folder = os.path.join(extract_folder, 'word', 'media')
    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []

    source_doc = Document(source_docx)
    target_doc = Document(target_docx)

    # Step 1.5: Remove all existing tables from target doc
    remove_all_tables(target_doc)
    print("🗑️ Removed all existing tables from target document")

    # Step 2: Process tables with line break after
    for idx, source_table in enumerate(source_doc.tables, start=1):
        reference_text = f"Table {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        if matched_paragraphs:
            for para in matched_paragraphs:
                copy_table_with_break(source_table, para)
            print(f"✅ Inserted Table {idx} + line break after all '{reference_text}' occurrences")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 3: Process figures/images
    for idx, image_file in enumerate(media_files, start=1):
        reference_text = f"Figure {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        image_path = os.path.join(media_folder, image_file)
        if matched_paragraphs:
            for para in matched_paragraphs:
                insert_image_before_paragraph(target_doc, image_path, para)
            print(f"✅ Inserted Figure {idx} before all '{reference_text}' occurrences")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 4: Save output
    target_doc.save('table_and_image_formatted_answer.docx')
    print("✅ All done. Saved as 'table_and_image_formatted_answer.docx'")
    
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
        print(f"🗑️ Cleaned up temporary folder: {extract_folder}")

if __name__ == "__main__":
    main()



