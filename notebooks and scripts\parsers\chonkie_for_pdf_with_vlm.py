#!/usr/bin/env python3
"""
Simple chunking script using <PERSON><PERSON>ie
Reads a text file, chunks it, and saves chunks to a folder
"""

from chonkie import NeuralChunker, RecursiveChunker
import os
import sys
import argparse


def chunk_text_file(input_file: str, output_dir: str, chunker_type: str = "neural"):
    """
    Simple function to chunk a text file and save chunks to a directory.
    
    Args:
        input_file (str): Path to input text file
        output_dir (str): Directory to save chunks
        chunker_type (str): Type of chunker ("neural" or "recursive")
    """
    
    # Initialize the chunker
    if chunker_type == "neural":
        chunker = NeuralChunker(
            model="mirth/chonky_modernbert_base_1",
            device_map="cpu",
            min_characters_per_chunk=1000,
            return_type="chunks"
        )
    elif chunker_type == "recursive":
        chunker = RecursiveChunker(chunk_size=2000)
    else:
        raise ValueError(f"Unknown chunker type: {chunker_type}")
    
    print(f"Using {chunker_type} chunker")
    
    # Validate input file
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found.")
        return False
    
    # Read the text file
    with open(input_file, "r", encoding="utf-8") as f:
        document_text = f.read()
    
    print(f"Document length: {len(document_text)} characters")
    
    # Chunk the text
    print("Chunking text...")
    chunks = chunker(document_text)
    print(f"Created {len(chunks)} chunks")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save chunks to files
    for i, chunk in enumerate(chunks):
        chunk_file = os.path.join(output_dir, f"chunk_{i}.txt")
        with open(chunk_file, "w", encoding="utf-8") as f:
            f.write(chunk.text)
        
        print(f"Chunk {i}: {len(chunk.text)} characters")
    
    print(f"\nSaved {len(chunks)} chunks to '{output_dir}'")
    return True


def main():
    parser = argparse.ArgumentParser(
        description="Simple text chunking with Chonkie",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python chonie_for_pdf_with_vlm.py document.txt chunks/
    python chonie_for_pdf_with_vlm.py document.txt chunks/ --chunker recursive
        """
    )
    
    parser.add_argument(
        'input_file',
        help='Path to the input text file'
    )
    
    parser.add_argument(
        'output_dir',
        help='Directory to save chunks'
    )
    
    parser.add_argument(
        '--chunker',
        choices=['neural', 'recursive'],
        default='neural',
        help='Type of chunker to use (default: neural)'
    )
    
    args = parser.parse_args()
    
    # Validate input
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found.")
        sys.exit(1)
    
    print("=" * 50)
    print("SIMPLE CHONKIE TEXT CHUNKING")
    print("=" * 50)
    print(f"Input file: {args.input_file}")
    print(f"Output directory: {args.output_dir}")
    print(f"Chunker type: {args.chunker}")
    print()
    
    # Run chunking
    success = chunk_text_file(
        input_file=args.input_file,
        output_dir=args.output_dir,
        chunker_type=args.chunker
    )
    
    if success:
        print("\n" + "=" * 50)
        print("✓ CHUNKING COMPLETED SUCCESSFULLY!")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("✗ CHUNKING FAILED!")
        print("=" * 50)
        sys.exit(1)


if __name__ == "__main__":
    main()
