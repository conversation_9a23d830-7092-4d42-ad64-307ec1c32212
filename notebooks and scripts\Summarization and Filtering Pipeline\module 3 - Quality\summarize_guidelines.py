import os
import json
import asyncio
from openai import AsyncOpenAI
from dotenv import load_dotenv
import glob
from db_utils import insert_chunks_in_db_for_quality_guideline, get_mongodb_client
from bson.objectid import ObjectId
from typing import List, Dict, Any
from transformers import AutoTokenizer
from retry_on_empty_response import retry_on_empty_response

# Load environment variables
load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

print(GENERATOR_MODEL_NAME, GENERATOR_MODEL_BASE_URL, GENERATOR_MODEL_API_KEY)

# Maximum token limit for input (keeping some buffer for the prompt and output)
MAX_TOKEN_LIMIT = 20000

# Initialize AsyncOpenAI client
openai_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL,
    api_key=GENERATOR_MODEL_API_KEY
)

# Initialize tokenizer
tokenizer = None

def extract_final_summary_from_tags(content: str) -> str:
    """
    Extract final summary from <final_summary> tags if present, otherwise return content as is.
    
    Args:
        content: The raw content from the LLM response
        
    Returns:
        Extracted final summary or original content if tags not found
    """
    if not content:
        return content
        
    # Look for <final_summary> tags
    import re
    summary_match = re.search(r'<final_summary>(.*?)</final_summary>', content, re.DOTALL | re.IGNORECASE)
    
    if summary_match:
        # Extract and clean the summary content
        summary = summary_match.group(1).strip()
        return summary
    else:
        # Return original content if no tags found
        return content

def calculate_number_of_tokens(text):
    """Calculate the number of tokens in a text using Mistral tokenizer."""
    global tokenizer
    if tokenizer is None:
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")
    
    tokens = tokenizer.encode(text, add_special_tokens=False)
    return len(tokens)

def read_chunk(file_path):
    """Read a chunk file and return its contents."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

async def get_individual_summary(chunk_text: str) -> str:
    """Get summary of a single chunk using the LLM."""
    system_prompt = """You are an expert AI assistant specializing in pharmaceutical quality guidelines and regulatory compliance. Your task is to provide comprehensive and concise summaries of technical documents while maintaining accuracy and clarity."""
    
    user_prompt = f"""Here is the text you need to summarize:

<chunk_text>
{chunk_text}
</chunk_text>

Please analyze this text and provide a summary following these steps:

1. Carefully read and analyze the entire text.
2. In your analysis, consider the following:
   - Identify all critical information, including technical specifications, numerical values, and regulatory requirements.
   - Note any decision trees or complex logic structures.
   - Recognize key methods, criteria, and validation requirements.

3. Break down the document inside <document_breakdown> tags in your thinking block. Within these tags:
   - List key topics and subtopics in the text.
   - Quote critical information for each topic.
   - Identify and outline any decision trees or complex logic.
   - Note important numerical values and units.
   - Break down the main components of the text.
   - List all critical information you've identified.
   - Explain how you'll structure decision trees (if any) in a linear format.
   - Describe how you'll ensure technical accuracy and efficiency in your summary.
   It's OK for this section to be quite long.

4. After your analysis, create a summary that adheres to these requirements:
   - Capture 100% of all critical information without exception.
   - Use precise, clear language to minimize verbosity.
   - Avoid redundant or repetitive statements.
   - Present decision trees (if any) in a clear, linear text format using indentation and If/Then statements.
   - Use exact technical terminology, specifications, and numerical values with units.
   - Document methods, criteria, and validation requirements efficiently.

5. Format your summary as follows:
   - Use plain text only - no special formatting or markdown.
   - Organize information into clear numbered sections.
   - Use simple indentation for hierarchy when needed.
   - Use hyphens (-) for bullet points.
   - Separate paragraphs with blank lines.

6. Before finalizing, review your summary to ensure:
   - All critical information is included.
   - The language is as concise as possible without losing meaning.
   - Technical accuracy is maintained throughout.
   - The formatting guidelines are followed correctly.

Here's an example of how your summary structure should look (content is placeholder):

1. Introduction
   - Brief overview of the document's purpose

2. Key Specifications
   - Specification A: [precise value and unit]
   - Specification B: [precise value and unit]

3. Regulatory Requirements
   - Requirement 1: [concise description]
   - Requirement 2: [concise description]

4. Decision Tree (if applicable)
   If [Condition A]:
     Then [Outcome 1]
   Else If [Condition B]:
     Then [Outcome 2]
   Else:
     [Outcome 3]

5. Validation Criteria
   - Criterion 1: [efficient description]
   - Criterion 2: [efficient description]

6. Conclusion
   - Summary of critical points

Remember, this is just a structure example. Your actual summary should be tailored to the specific content of the provided text, ensuring all critical information is captured accurately and concisely.

Please proceed with your analysis and summary. Your final output should consist only of the summary and should not duplicate or rehash any of the work you did in the document breakdown section."""

    async def make_api_call():
        response = await openai_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            temperature=0.2,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        )
        
        return response.choices[0].message.content
    
    raw_summary = await retry_on_empty_response(make_api_call)
    
    # Extract summary from tags if present
    if raw_summary is None:
        summary = ""
    else:
        summary = extract_final_summary_from_tags(raw_summary)
    
    return summary

async def create_final_summary(individual_summaries: List[str]) -> str:
    """Create a final summary from all individual summaries by combining multiple summaries at once while respecting token limits."""
    
    print(f"\nStarting final summary creation with {len(individual_summaries)} individual summaries")
    
    # Handle empty input
    if not individual_summaries:
        print("No summaries provided, returning empty string")
        return ""
        
    # Handle single summary case
    if len(individual_summaries) == 1:
        print("Only one summary provided, returning it as is")
        return individual_summaries[0]
    
    async def combine_summaries(summaries_to_combine):
        """Helper function to combine multiple summaries while preserving all information."""
        total_summaries = len(summaries_to_combine)
        print(f"\nCombining {total_summaries} summaries:")
        
        total_tokens = sum(calculate_number_of_tokens(summary) for summary in summaries_to_combine)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are an expert in pharmaceutical quality guidelines and regulatory compliance. Your task is to combine multiple summaries into one comprehensive, combined summary. This is a critical task where preserving ALL information is of utmost importance."""

        user_prompt = f"""Here are the summaries you need to combine:
<summaries>
        """
        
        # Add each summary with a header
        for i, summary in enumerate(summaries_to_combine, 1):
            user_prompt += f"""
        Summary {i}:
        <summary_{i}>
        {summary}
        </summary_{i}>
        """
        
        user_prompt += f"""
</summaries>

Here is the total number of summaries you will be combining:
<total_summaries>
{total_summaries}
</total_summaries>

I will provide you with each summary one by one. For each summary, you will analyze its content and prepare it for integration into the final combined summary. After all summaries have been analyzed, you will create the final combined summary.

Please follow these steps:

1. Analysis:
   Conduct your analysis inside <summary_analysis> tags within your thinking block. For each summary:
   a) List key points
   b) Identify cross-references
   c) Note unique information
   d) Count the number of specific details, numbers, and measurements
   Extract all key information, identify cross-references, and note any potential duplications with previously analyzed summaries.

2. Integration:
   After analyzing all summaries, use <integration> tags to describe how you will merge the information from all summaries into a cohesive document. Focus on maintaining logical flow while preserving all details.

3. Final Summary:
   Create the final combined summary in <final_summary> tags. Adhere to these formatting guidelines:
   - Use plain text format only
   - Use simple bullet points with hyphens (-) when needed
   - Separate paragraphs with blank lines
   - Use parentheses for references or additional information

4. Verification:
   Use <verification> tags to check your final summary against this checklist:
   1. Does the combined summary include ALL information from all summaries?
   2. Are ALL specific details, numbers, and measurements preserved?
   3. Are ALL technical specifications maintained?
   4. Are ALL references and citations included?
   5. Are ALL examples and case studies preserved?
   6. Are ALL requirements and recommendations kept?
   7. Are ALL validation and verification details included?
   8. Are ALL risk assessments preserved?
   9. Are ALL cross-references maintained?
   10. Is ALL contextual information preserved?

If any item on the checklist is not met, revise your final summary accordingly.

Remember:
- Preserve ALL information from all summaries
- Do not omit or summarize any details
- Include all specific numbers, measurements, and criteria
- Maintain all technical specifications
- Keep all references and citations
- Preserve all examples and case studies
- Include all requirements and recommendations
- Maintain all validation and verification details
- Keep all risk assessments and considerations
- Preserve all cross-references and dependencies
- Ensure logical flow and organization
- Preserve technical accuracy and precision
- Length is not a concern - include ALL details

Example output structure:

<summary_analysis>
Summary 1:
- Key points: [List of key points]
- Cross-references: [List of cross-references]
- Unique information: [List of information unique to this summary]
- Number of specific details, numbers, and measurements: [Count]

Summary 2:
[Similar structure to Summary 1]

[Continue for all summaries]
</summary_analysis>

<integration>
[Description of integration strategy]
</integration>

<final_summary>
[Combined summary content]

- Bullet point 1
- Bullet point 2

Paragraph 1

Paragraph 2 (with a reference)
</final_summary>

<verification>
[Checklist verification results]
</verification>

Please proceed with your analysis of the first summary. Your final output should consist only of the combined summary in <final_summary> tags and should not duplicate or rehash any of the work you did in the thinking block."""

        print(f"Sending request to LLM to combine {total_summaries} summaries...")
        
        async def make_api_call():
            response = await openai_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
                ],
                temperature=0.2
            )
            return response.choices[0].message.content
        
        raw_combined_summary = await retry_on_empty_response(make_api_call)
        
        # Extract final summary from tags if present
        if raw_combined_summary is None:
            combined_summary = ""
        else:
            combined_summary = extract_final_summary_from_tags(raw_combined_summary)
        
        print(f"Combined summary length: {len(combined_summary)} characters")
        print(f"Combined summary contains {len(combined_summary.split())} words")
        print(f"Combined summary token count: {calculate_number_of_tokens(combined_summary)}")
        
        return combined_summary

    # Process summaries iteratively, combining optimal batches in parallel
    current_summaries = individual_summaries.copy()
    iteration = 1
    
    while len(current_summaries) > 1:
        print(f"\nIteration {iteration}:")
        print(f"Number of summaries to process: {len(current_summaries)}")
        
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for summary in current_summaries:
            summary_tokens = calculate_number_of_tokens(summary)
            
            # If adding this summary would exceed the token limit, start a new batch
            if current_batch and current_token_count + summary_tokens > MAX_TOKEN_LIMIT:
                batches.append(current_batch)
                current_batch = [summary]
                current_token_count = summary_tokens
            else:
                current_batch.append(summary)
                current_token_count += summary_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        print(f"Created {len(batches)} batches for processing")
        for i, batch in enumerate(batches):
            print(f"Batch {i+1} contains {len(batch)} summaries with {sum(calculate_number_of_tokens(s) for s in batch)} tokens")
        
        # Process all batches in parallel
        tasks = [combine_summaries(batch) for batch in batches]
        
        # Wait for all combinations to complete
        print("Waiting for all batch combinations to complete...")
        new_summaries = await asyncio.gather(*tasks)
        current_summaries = new_summaries
        
        print(f"Iteration {iteration} complete. {len(current_summaries)} summaries remaining")
        iteration += 1
    
    print(f"\nFinal summary creation complete. Final summary length: {len(current_summaries[0])} characters")
    print(f"Final summary contains {len(current_summaries[0].split())} words")
    print(f"Final summary token count: {calculate_number_of_tokens(current_summaries[0])}")
    
    return current_summaries[0]

async def process_chunk(chunk_file: str, quality_guideline: str) -> Dict[str, Any]:
    """Process a single chunk and return its summary and metadata."""
    print(f"Processing individual summary for {os.path.basename(chunk_file)}...")
    
    # Read the chunk
    chunk_text = read_chunk(chunk_file)
    
    # Store original chunk in MongoDB
    chunk_id = insert_chunks_in_db_for_quality_guideline(
        chunks_data=chunk_text,
        chunk_filename=os.path.basename(chunk_file),
        quality_guideline=quality_guideline,
        is_summary=False
    )
    
    # Get individual summary
    summary = await get_individual_summary(chunk_text)
    
    # Store the individual summary in MongoDB
    summary_id = insert_chunks_in_db_for_quality_guideline(
        chunks_data=summary,
        chunk_filename=None,
        quality_guideline=quality_guideline,
        is_summary=True,
        summary_level=2,
        chunk_reference_ids=[str(chunk_id)]
    )
    
    return {
        "chunk": os.path.basename(chunk_file),
        "individual_summary": summary,
        "chunk_id": str(chunk_id),
        "summary_id": str(summary_id)
    }

async def process_chunks(chunks_dir: str) -> Dict[str, Any]:
    """Process all chunks in parallel and create a final summary."""
    # Get all chunk files and sort them numerically
    chunk_files = sorted(
        glob.glob(os.path.join(chunks_dir, "chunk_*.txt")),
        key=lambda x: int(os.path.basename(x).split('_')[1].split('.')[0])
    )
    
    # Extract quality guideline from directory path
    quality_guideline = chunks_dir.split("/")[-1].split("chunks")[0].strip()
    
    # Process all chunks in parallel
    tasks = [process_chunk(chunk_file, quality_guideline) for chunk_file in chunk_files]
    all_summaries = await asyncio.gather(*tasks)
    
    # Extract individual summaries for final summary creation
    individual_summaries = [summary["individual_summary"] for summary in all_summaries]
    
    # Create final summary
    print("\nCreating final summary...")
    final_summary = await create_final_summary(individual_summaries)
    
    # Store final summary in MongoDB
    final_summary_id = insert_chunks_in_db_for_quality_guideline(
        chunks_data=final_summary,
        chunk_filename=None,
        quality_guideline=quality_guideline,
        is_summary=True,
        summary_level=1,
        summary_reference_ids=[summary["summary_id"] for summary in all_summaries]
    )
    
    # Save all summaries to a JSON file
    output_file = os.path.join(chunks_dir, "all_summaries.json")
    final_data = {
        "individual_summaries": all_summaries,
        "final_summary": final_summary,
        "final_summary_id": str(final_summary_id)
    }
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(final_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nAll summaries saved to {output_file}")
    
    # Save final summary to a separate file
    with open(os.path.join(chunks_dir, "final_summary.txt"), 'w', encoding='utf-8') as f:
        f.write(final_summary)
    
    print(f"Final summary saved to {os.path.join(chunks_dir, 'final_summary.txt')}")
    
    return final_data

def get_missing_guidelines(base_dir: str) -> List[str]:
    """Check for quality guidelines that exist in local folders but are missing from MongoDB."""
    # Get all directories that end with 'chunks' and start with 'Q'
    local_guidelines = [d[:-7] for d in os.listdir(base_dir) 
                       if os.path.isdir(os.path.join(base_dir, d)) 
                       and d.startswith('Q') 
                       and d.endswith('chunks')]
    
    # Get all unique quality guidelines from MongoDB
    client = get_mongodb_client()
    db = client['mednova']
    chunks_collection = db['quality_docs_chunks']
    summaries_collection = db['quality_docs_summaries']
    
    # Get unique guidelines from both collections
    mongo_guidelines = set()
    for doc in chunks_collection.find({}, {"quality_guideline": 1}):
        mongo_guidelines.add(doc["quality_guideline"])
    for doc in summaries_collection.find({}, {"quality_guideline": 1}):
        mongo_guidelines.add(doc["quality_guideline"])
    
    client.close()
    
    # Find guidelines that are in local folders but not in MongoDB
    missing_guidelines = list(set(local_guidelines) - mongo_guidelines)
    
    return missing_guidelines

async def main():
    base_dir = "Input Docs/Quality Guidelines"
    
    # Check for missing guidelines
    missing_guidelines = get_missing_guidelines(base_dir)
    if missing_guidelines:
        print("\nThe following quality guidelines exist in local folders but are missing from MongoDB:")
        for guideline in sorted(missing_guidelines):
            print(f"- {guideline}")
        print("\nProcessing these missing guidelines...")
        
        # Process only the missing guidelines
        for guideline in sorted(missing_guidelines):
            chunks_dir = os.path.join(base_dir, f"{guideline} chunks")
            if not os.path.exists(chunks_dir):
                print(f"Warning: Directory {chunks_dir} not found. Skipping {guideline}")
                continue
                
            print(f"\nProcessing quality guideline: {guideline}")
            try:
                await process_chunks(chunks_dir)
                print(f"Successfully processed {guideline}")
            except Exception as e:
                print(f"Error processing {guideline}: {str(e)}")
                continue
        
        print("\nAll missing guidelines have been processed.")
        return
    
    # If no missing guidelines, ask user before processing all directories
    print("\nNo missing guidelines found. All guidelines are up to date in MongoDB.")
    print("Would you like to process all guidelines anyway? (y/n)")
    response = input().strip().lower()
    if response != 'y':
        print("Exiting...")
        return
    
    # Get all directories that end with 'chunks' and start with 'Q'
    chunks_dirs = [d for d in os.listdir(base_dir) 
                  if os.path.isdir(os.path.join(base_dir, d)) 
                  and d.startswith('Q') 
                  and d.endswith('chunks')]
    
    print(f"\nFound {len(chunks_dirs)} quality guideline directories to process")
    
    for chunks_dir_name in chunks_dirs:
        # Extract the quality guideline name by removing ' chunks' from the end
        quality_guideline = chunks_dir_name[:-7]  # Remove ' chunks' (7 characters)
        chunks_dir = os.path.join(base_dir, chunks_dir_name)
        
        print(f"\nProcessing quality guideline: {quality_guideline}")
        try:
            await process_chunks(chunks_dir)
            print(f"Successfully processed {quality_guideline}")
        except Exception as e:
            print(f"Error processing {quality_guideline}: {str(e)}")
            continue

if __name__ == "__main__":
    asyncio.run(main()) 