# %%
import json
import os
import traceback
import uuid
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, Tuple
import asyncio
from transformers import AutoTokenizer
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider

load_dotenv()

os.environ["TOKENIZERS_PARALLELISM"] = "true"

GENERATOR_MODEL_NAME="agentic-large"
GENERATOR_MODEL_BASE_URL="https://api.theagentic.ai/v1"
GENERATOR_MODEL_API_KEY="kYElszzLTUKUT16jsdCPcamIcpABo7D3"
MONGO_DB_URL="mongodb+srv://prakhar:<EMAIL>/?retryWrites=true&w=majority&appName=magic-pro"

MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000
MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000

NUMBER_OF_QUALITY_GUIDELINES_TO_GENERATE_CHECKPOINTS_FOR = 27 # Max is 27

generator_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL, 
    api_key=GENERATOR_MODEL_API_KEY,     
    max_retries=1,
    timeout=10000000
)

generator_model = OpenAIModel(
    model_name=GENERATOR_MODEL_NAME,
    provider=OpenAIProvider(
        base_url=GENERATOR_MODEL_BASE_URL,
        api_key=GENERATOR_MODEL_API_KEY
    )
)

# Pydantic schema for structured output
class CheckpointList(BaseModel):
    """Schema for checkpoint list generation"""
    checkpoints: List[str] = Field(
        description="List of specific checkpoint questions that should be answered in summaries for this section",
        min_items=2
    )

def write_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"JSON saved to {filename}")

def read_json(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def get_mongodb_client():
    """Get MongoDB client connection."""
    return MongoClient(MONGO_DB_URL)
    
def calculate_number_of_tokens(text):
    # Load tokenizer for Mistral model
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    return token_count

# %%

doc_to_work_on = {
    "section": "3.2.P.8.1",
    "title": "Stability Summary and Conclusion (name, dosage form)",
    "description": "The types of studies conducted, protocols used, and the results of the studies should be\nsummarized. The summary should include, for example, conclusions with respect to storage\nconditions and shelf-life, and, if applicable, in-use storage conditions and shelf-life.\nReference ICH Guidelines: Q1A, Q1B, Q3B, and Q5C, Q6A",
    "enhanced_instructions_and_checkpoints": "1. OVERVIEW\n\nDescription Content:\n    Purpose of stability studies (evaluation under ICH guidance).\n    Brief description of registration batches:\n        Manufactured under GMPs.\n        Scaled appropriately (≥1/10th of commercial scale, stating blend size and bracketing of tablet strengths per ICH Q1D).\n        Description of process performance qualification (PPQ) batches.\n        Confirmation if batches are packaged in intended commercial primary container closure system.\n    Cross-reference to relevant sections for:\n        Container closure system (e.g., Section 3.2.P.7).\n        Presentation of stability data (Section 3.2.P.8.3).\n        Photostability/forced degradation testing (this section).\n        Clinical capsule stability (Section 3.2.P.2.2).\n    Status update on stability testing for PPQ batches.\n\n2. STABILITY STUDIES\n2.1 Registration Stability\n\n    Textual Summary:\n        Description of registration stability protocol.\n        Statement that protocol supports intended long-term storage conditions.\n        Note that quality attributes and methods align with proposed specification.\n    Required Table: Registration Stability Protocol.\n\n2.2 Process Performance Qualification Stability\n\n    Textual Summary:\n        Description of PPQ stability protocol (aligns with registration protocol).\n    Required Table: Process Performance Qualification Stability Protocol.\n\n2.3 Supporting Stability\n\n    Textual Summary:\n        Statement that supportive studies are complete.\n        Engineering batch info (use of intended formulation/equipment).\n        Reference to corresponding sections for formulation and equipment.\n\n2.4 Confirmatory Photostability Study\n\n    Textual Summary:\n        Summary of test design: strengths tested, conditions (temperature/humidity/light), packaging states, controls, exposure parameters.\n        Methods used (e.g., HPLC-UV, appearance, friability, disintegration, dissolution).\n        Mention of photostress test on powder blend and test parameters.\n\n2.5 Forced Degradation\n\n    Textual Summary:\n        Summary of forced degradation study types (thermal, oxidative, photodegradation).\n        Test conditions for each (e.g., temperature, duration, H₂O₂ conc., light exposure).\n        Methods/parameters measured (assay, purity, degradants, appearance).\n\n3. RESULTS AND DISCUSSION\n3.1 Registration Stability\n\n    Summary of available stability data and duration covered (mention timepoints).\n    Reference to tabulated data in Section 3.2.P.8.3.\n    Summary statement regarding change/variability and statistical analysis per ICH Q1E.\n\n3.2 Process Performance Qualification Stability\n\n    Indicate status (initiated/completed) and where release data is provided.\n\n3.3 Supporting Stability\n\n    Summary of available data with timepoints.\n    Reference to section where data are located.\n    Statement on change/variability and statistical analysis.\n\n3.4 Confirmatory Photostability\n\n    Indicate protocol followed/results summary (little to no change/variability).\n    Reference to section for data.\n    Mention of the outcome for blend testing and effect on manufacturing precautions.\n\n3.5 Forced Degradation\n\n    Brief summary: pathways/profiling methods used; list analysis tech (HPLC-UV-MS, NMR, visual).\n        Include subsections for each stress test (thermolytic, oxidative, photolytic):\n            Textual Results: Test conditions, principal degradants identified.\n            Table: Comparative summary of CQA (appearance, assay, purity, impurity profiles, degradants) between control and stressed samples.\n            Figure: Degradation pathways with chemical structures.\n\n4. PROPOSED SHELF LIFE AND CONCLUSIONS\n\n    Statement regarding product stability under normal conditions.\n    Proposal of expiration period based on real-time data provided.\n    Long-term storage condition(s) listed, with packaging information.",
    "formatting_instructions": "General:\n\n    Use consistent numbering for sections and subsections as shown\n    Use bold and/or larger font for main section headings.\n    Subsection headings should be clear and hierarchical.\n    For references to other sections, highlight in color (red in the example) or with visible formatting for cross-referencing.\n\nFigures:\n\n    Chemical structure/pathways figure required for each degradation type, clearly labeled as “Figure X: [Descriptive Title]”.\n    Place the figure caption immediately below the figure.\n    Chemical structures should be clear, labeled consistently (e.g., Deg A, Deg B), and all referenced in corresponding results text.\n\nText:\n\n    Provide sufficient description before and after all tables/figures, drawing conclusions as appropriate.\n    Link all claims to data or referenced sections.\n    Units should be consistent and should be included for all test conditions.\n\nCross-references:\n\n    Cite all related sections for container closure, batch/process description, test methods, etc.\n\nResults and Conclusions:\n\n    Explicitly state if statistical analysis is or isn't required, justifying per guidelines.\n    State explicitly the shelf life supported, storage conditions, and any data-driven limitations.\n",
    "have_to_generate_output_for_this_section": True,
    "referenced_keys": [],
    "referenced_quality_guidelines": [
        "Q1A(R2)",
        "Q1B",
        "Q3B(R2)",
        "Q5C",
        "Q6A",
        "Q1D",
        "Q1E"
    ]
}

# %%
async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:
    """
    Generate checkpoint questions from a single individual summary.
    
    Args:
        summary_content: The content of the individual summary
        section_info: Dictionary containing section information
        
    Returns:
        List[str]: A list of checkpoint questions generated from the individual summary
    """
    try:
        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive checklist of specific questions based on the provided summary for a section of ICH eCTD guidelines."
        
        prompt = f"""
First, review the following information:

Golden Instructions and Checkpoints:
<golden_instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</golden_instructions_and_checkpoints>

Summary Content:
<summary_content>
{summary_content}
</summary_content>

Section Number:
<section_number>
{section_info.get('section', '')}
</section_number>

Section Title:
<section_title>
{section_info.get('title', '')}
</section_title>

Before creating the checklist, analyze the provided information and formulate your approach inside your thinking block using <checklist_preparation> tags:

1. Summarize the key points from the summary content.
2. Quote relevant phrases from the summary content and golden instructions.
3. Identify the main topics and requirements mentioned in the golden instructions and checkpoints.
4. List any specific technical or regulatory aspects that need to be addressed.
5. Note any particular areas of focus (e.g., physical characteristics, chemical properties, manufacturing processes, quality control measures, stability data, packaging specifications, compatibility information).
6. Brainstorm potential questions based on the quoted phrases and identified topics.
7. Ensure alignment with ICH guidelines.
8. Consider how to phrase each point as a specific, answerable question.

Now, create a detailed checklist of specific questions based on your analysis. The checklist should:

1. Include all key requirements and recommendations from the summary expressed as specific questions.
2. Cover all aspects mentioned in the summary.
3. Strictly adhere to the golden instructions and checkpoints provided.
4. Be specific enough to clearly determine if an input document addresses each point.
5. Format EACH checkpoint as a question that can be answered with yes/no or specific information.
6. Focus on technical and regulatory content needed for NDA documentation.
7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.
8. Cover stability data, packaging specifications, and compatibility information where appropriate.
9. Include questions about the validation and verification methods used.
10. Use the material prepared in the thinking block to ensure comprehensive coverage.

After creating the checklist, review it to ensure:
- All points from the summary are covered
- Questions align with the golden instructions and checkpoints
- Each question is specific and answerable
- The checklist is comprehensive and accurate

Present your final checklist after this review. Your output should consist only of the checklist and should not duplicate or rehash any of the work you did in the thinking block.
"""
        
        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(prompt)

        checkpoint_list_agent = Agent(
            model=generator_model,
            system_prompt=system_prompt,
            output_type=CheckpointList,
            model_settings={
                "temperature": 0.1,
                "extra_body": {
                    # "return_reasoning": True
                }
            },
            retries=1
        )

        response = await checkpoint_list_agent.run(prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        # print(f"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        checkpoints = response.output.checkpoints
        
        # Validate the output
        if len(checkpoints) < 2:
            raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
        
        return checkpoints
        
    except Exception as e:
        print(f"Error in _generate_checkpoints_from_one_summary: {e}")
        traceback.print_exc()
    
    return []

async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:
    """
    Generate checkpoints incrementally from a list of individual summaries.
    
    Args:
        section_info: Dictionary containing section information
        list_of_individual_summary_contents: List of individual summary contents
        
    Returns:
        List[str]: A comprehensive list of checkpoints generated from all individual summaries
    """
    # Handle empty input
    if not list_of_individual_summary_contents:
        return []
        
    # Handle single summary case
    if len(list_of_individual_summary_contents) == 1:
        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)
    
    # Maximum token limit for checkpoint list batches
    
    # Generate initial checkpoints from each summary in parallel
    tasks = [_generate_checkpoints_from_one_summary(summary, section_info) for summary in list_of_individual_summary_contents]
    current_checkpoints = await asyncio.gather(*tasks)
    checkpoints_text = ""
    for checkpoint in current_checkpoints:
        checkpoints_text += "\n".join(checkpoint)

    print(f"Generated {len(current_checkpoints)} individual checkpoint lists incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(checkpoints_text)}")
    
    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):
        """Helper function to combine multiple checkpoint lists while preserving all unique checkpoints."""
        total_lists = len(checkpoint_lists)
        print(f"\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints")
        
        # Calculate token count for all lists combined
        combined_text = "\n".join(["\n".join(cp) for cp in checkpoint_lists])
        total_tokens = calculate_number_of_tokens(combined_text)
        print(f"Total input tokens for checkpoint merging: {total_tokens}")
        
        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive merged list of checkpoint questions for a specific section of ICH eCTD guidelines."
        
        user_prompt = f"""
First, review the following information:

Section:
<section_info>
{section_info.get('section', '')}
</section_info>

Title:
<section_title>
{section_info.get('title', '')}
</section_title>

Golden Instructions and Checkpoints:
<enhanced_instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</enhanced_instructions_and_checkpoints>

You will be processing {total_lists} lists of checkpoint questions. 

"""
        
        # Add each checkpoint list
        for i, checkpoints in enumerate(checkpoint_lists, 1):
            user_prompt += f"""
Checkpoint List {i}:
<checkpoints_{i}>
{json.dumps(checkpoints, indent=2)}
</checkpoints_{i}>

"""

        user_prompt += f"""
Your goal is to merge these lists into a single comprehensive list while adhering to the following requirements:

1. Preserve ALL unique questions from all lists
2. Remove any duplicate or redundant questions
3. Ensure the merged list is comprehensive and covers all aspects
4. Maintain the specificity and clarity of each question
5. Keep the question format consistent (e.g., "Does the document mention...")
6. Ensure each question focuses on a single specific point
7. Group related questions together when possible
8. Strictly adhere to the referenced list of golden instructions and checkpoints
9. Keep all questions focused on technical regulatory content for NDA documentation

Before producing the final merged list, wrap your analysis inside <checkpoint_analysis> tags in your thinking block. In this analysis:
1. Summarize the key points from the section_info, section_title, and enhanced_instructions_and_checkpoints.
2. Analyze the first list of checkpoints, noting any patterns or themes.
3. Plan how you will approach merging subsequent lists (even though we only have the first list now).
4. Ensure all requirements are met and pay special attention to accuracy and best practices in regulatory documentation.

After your analysis, provide the merged list of checkpoint questions. 

Your final output should consist only of the merged list of checkpoint questions and should not duplicate or rehash any of the work you did in the checkpoint analysis section.
        """
        
        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)

        checkpoint_list_agent = Agent(
            model=generator_model,
            system_prompt=system_prompt,
            output_type=CheckpointList,
            model_settings={
                "temperature": 0.1,
                "extra_body": {
                    # "return_reasoning": True
                }
            },
            retries=1
        )

        response = await checkpoint_list_agent.run(user_prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        print(f"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        merged_checkpoints = response.output.checkpoints

        # Save the system and user prompts to a file in openai chat format
        chat_history = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        json_to_save = {
            "system_tokens": system_tokens,
            "user_tokens": user_tokens,
            "total_tokens": total_tokens,
            "output_tokens": output_tokens,
            "number_of_output_checkpoints": len(merged_checkpoints),
            "merged_checkpoints": merged_checkpoints,
            "chat_history": chat_history,
        }

        unique_id = str(uuid.uuid4())

        # mkdir chat_history
        os.makedirs("chat_history", exist_ok=True)

        with open(f"chat_history/chat_history_{unique_id}.json", "w") as f:
            json.dump(json_to_save, f)
        
        # Validate the output
        if len(merged_checkpoints) < 2:
            raise ValueError(f"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}")
        
        print(f"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints")
        
        return merged_checkpoints
    
    # Iteratively merge checkpoint lists in optimal batches
    while len(current_checkpoints) > 1:
        # Create optimal batches based on token count and number of checkpoints
        batches = []
        current_batch = []
        current_token_count = 0
        current_checkpoint_count = 0
        
        for checkpoint_list in current_checkpoints:
            # Calculate tokens for this checkpoint list
            checkpoint_text = "\n".join(checkpoint_list)
            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)
            
            # Check if this single checkpoint list exceeds the token limit
            if checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT:
                print(f"Warning: Single checkpoint list has {checkpoint_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT}")
                # Force this large list to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(checkpoint_list)
                    current_token_count += checkpoint_tokens
                    current_checkpoint_count += len(checkpoint_list)
                    print(f"Forcing merge of oversized checkpoint list with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                    current_checkpoint_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next checkpoint list
                    # to avoid it being processed alone repeatedly
                    current_batch = [checkpoint_list]
                    current_token_count = checkpoint_tokens
                    current_checkpoint_count = len(checkpoint_list)
                    print(f"Starting new batch with oversized checkpoint list ({checkpoint_tokens} tokens)")
            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch
            elif (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):
                batches.append(current_batch)
                current_batch = [checkpoint_list]
                current_token_count = checkpoint_tokens
                current_checkpoint_count = len(checkpoint_list)
            else:
                current_batch.append(checkpoint_list)
                current_token_count += checkpoint_tokens
                current_checkpoint_count += len(checkpoint_list)
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        # Ensure no batch has only one checkpoint list to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one checkpoint list left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        print(f"Created {len(batches)} batches for checkpoint list merging")
        for i, batch in enumerate(batches):
            total_checkpoints = sum(len(cp) for cp in batch)
            total_tokens = sum(calculate_number_of_tokens("\n".join(cp)) for cp in batch)
            print(f"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints and {total_tokens} total tokens")
        
        # Process all batches in parallel
        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]
        
        # Wait for all merges to complete
        current_checkpoints = await asyncio.gather(*tasks)

    return current_checkpoints[0]

async def search_summaries_by_llm(section_info, quality_guidelines, summary_level=1):
    """
    Search for summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        quality_guidelines: List of quality guidelines to search in (e.g., ["Q6A", "Q6B"])
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['quality_docs_summaries']
    
    # Find all summaries matching the criteria
    if len(quality_guidelines) > 0:
        summaries = list(collection.find({
            "quality_guideline": {"$in": quality_guidelines},
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))

    client.close()
    return summaries

def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['quality_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries

# %%
async def extract_relevant_chunks(json_obj):
    """
    Recursively iterates through the nested JSON structure, extracts titles and descriptions,
    generates checkpoint lists, and finds relevant summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with added relevant summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs processing
            if (obj and "title" in obj and "description" in obj and 
                "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        all_quality_guidelines = ["Q10", "Q11", "Q12", "Q13", "Q14", "Q1A(R2)", "Q1B", "Q1D", "Q1E", "Q1F", "Q2(R2)", "Q3A(R2)", "Q3B(R2)", "Q3C(R9)", "Q3D(R2)", "Q4B", "Q4B(R1)", "Q5A(R2)", "Q5B", "Q5C", "Q5D", "Q5E", "Q6A", "Q6B", "Q7", "Q8(R2)", "Q9(R1)"]

        final_summaries = await search_summaries_by_llm(
            section_info=section,
            quality_guidelines=all_quality_guidelines[:NUMBER_OF_QUALITY_GUIDELINES_TO_GENERATE_CHECKPOINTS_FOR], # Vary this number to increase or decrease the number of quality guidelines to search for
            summary_level=1
        )
        
        # Step 2: For each final summary, get relevant individual summaries (level 2)
        individual_summaries = []
        for final_summary in final_summaries:
            summaries = get_summaries_for_final_summary(final_summary["_id"])
            individual_summaries.extend(summaries)

        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, total tokens for input summaries before creating checkpoints: {calculate_number_of_tokens(str("".join([summary['content'] for summary in individual_summaries])))}")

        # Generate checkpoints incrementally from individual summaries
        if len(individual_summaries) > 0:
            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(
                section,
                [summary["content"] for summary in individual_summaries]
            )
        
        print(f"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}, total tokens for output checkpoints: {calculate_number_of_tokens(str("".join(checkpoints)))}")
        
        return checkpoints
    
    # Process all sections in parallel
    if sections_to_process:
        checkpoints = await asyncio.gather(*[process_section(section) for section in sections_to_process])

    return checkpoints[0]

# %%
if __name__ == "__main__":
    checkpoints = asyncio.run(extract_relevant_chunks(doc_to_work_on))
    write_to_json(checkpoints, "checkpoints.json")