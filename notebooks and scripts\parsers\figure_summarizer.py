import os
import json
import asyncio
import shutil
import base64
from pathlib import Path

from dotenv import load_dotenv

from openai import OpenAI, AsyncOpenAI

load_dotenv(override=True)
# Environment variables for OpenAI GPT-4o
GENERATOR_MODEL_NAME = os.getenv("OPENAI_MODEL_NAME", "gpt-4o")
GENERATOR_MODEL_BASE_URL = os.getenv("OPENAI_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("OPENAI_MODEL_API_KEY")

generator_client = OpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)
async_generator_client = AsyncOpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)

# ------------------------------------------------------------
# OPENAI CALL
# ------------------------------------------------------------

async def create_summary_of_figure_async(figure_heading: str, figure_description: str, image_block=None) -> str:
    """Create a concise, regulatory-ready summary of a figure."""
    try:
        response = await async_generator_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            max_tokens=512,
            messages=[
                {
                    "role": "system",
                    "content": (
                        """
                        You are an expert system designed to meticulously analyze images, figures, and diagrams from drug developmental reports.
                        Your primary goal is to extract ALL information present in the provided image and present it in a detailed, comprehensive plaintext format.
                        This includes, but is not limited to:
                        - All visible text, including titles, labels, annotations, legends, and any text within the image itself.
                        - Descriptions of graphical elements such as charts (bar, line, pie, scatter plots, etc.), diagrams, flowcharts, chemical structures, and schematics.
                        - For charts and graphs, identify trends, patterns, data points, axes labels, units, and any statistical information presented.
                        - For diagrams and flowcharts, describe the components, connections, and the process or system they represent.
                        - For chemical structures, provide the name of the compound if identifiable, and describe its structure.
                        - If there are any human figures or specific visual details (e.g., equipment, experimental setup), describe them, including relevant characteristics like skin color if applicable, but prioritize the scientific and technical information.
                        The output should be a thorough and exhaustive textual representation of the image, suitable for downstream processing and inclusion in a New Drug Application (NDA).
                        Do not summarize. Provide a long, detailed, and structured description.
                        """
                    )
                },
                {
                    "role": "user",
                    "content": (
                        [
                            {
                                "type": "text",
                                "text": (
                                    "Figure heading/title:\n" + figure_heading + "\n\n" +
                                    "Figure description / extracted text:\n" + figure_description
                                )
                            }
                        ] + ([image_block] if image_block else [])
                    )
                }
            ]
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error creating figure summary: {e}")
        return None

# ------------------------------------------------------------
# IO HELPERS
# ------------------------------------------------------------

def load_figure_index(figures_dir: Path) -> dict:
    """Load figure_index.json."""
    index_path = figures_dir / "figure_index.json"
    try:
        with open(index_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading figure index: {e}")
        return {}

def read_figure_text(figures_dir: Path, figure_file: str) -> str:
    path = figures_dir / figure_file
    try:
        with open(path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(f"Error reading figure file {figure_file}: {e}")
        return ""

def save_summary_to_file(output_dir: Path, figure_id: str, summary: str):
    output_file = output_dir / f"{figure_id}.txt"
    figure_number = figure_id.split("_")[1]
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"Figure Summary: {summary}\n\n")
            f.write(f"`<FIGURE_TAG_{figure_number}>`")
        print(f"✓ Saved summary for {figure_id}")
        return True
    except Exception as e:
        print(f"✗ Error saving summary for {figure_id}: {e}")
        return False

def copy_figure_index(figures_dir: Path, output_dir: Path) -> bool:
    src = figures_dir / "figure_index.json"
    dst = output_dir / "figure_index.json"
    try:
        shutil.copy2(src, dst)
        print(f"✓ Copied figure_index.json")
        return True
    except Exception as e:
        print(f"✗ Error copying figure_index.json: {e}")
        return False

# ------------------------------------------------------------
# PROCESSING
# ------------------------------------------------------------

async def process_single_figure(figures_dir: Path, output_dir: Path, figure_id: str, metadata: dict) -> bool:
    heading = metadata.get("heading", "")
    figure_file = metadata.get("file", "")
    if not figure_file:
        print(f"✗ No file for {figure_id}")
        return False

    description = read_figure_text(figures_dir, figure_file)

    # Get first image file from metadata if available
    image_files = metadata.get("images", [])
    image_b64_block = None
    if image_files:
        image_path = figures_dir / image_files[0]
        try:
            with open(image_path, "rb") as img_f:
                img_bytes = img_f.read()
                b64_data = base64.b64encode(img_bytes).decode("utf-8")
                media_type = "image/png" if image_path.suffix.lower() != ".jpg" else "image/jpeg"
                image_b64_block = {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{media_type};base64,{b64_data}"
                    }
                }
        except Exception as e:
            print(f"⚠️  Could not read image for {figure_id}: {e}")

    if not description and not image_b64_block:
        print(f"✗ No textual description or image for {figure_id}")
        return False

    print(f"🖼️  Processing {figure_id}: {heading[:60]}...")
    summary = await create_summary_of_figure_async(heading, description, image_b64_block)
    if not summary:
        print(f"✗ Failed summary for {figure_id}")
        return False

    return save_summary_to_file(output_dir, figure_id, summary)

async def process_all_figures():
    """Main orchestration similar to table_summarizer."""
    # Default directories (change as needed)
    figures_dir = Path("test/Stability Section - Developmental Report_figures")
    output_dir = Path("test/Stability Section - Developmental Report_figures_with_summaries_and_tags")

    output_dir.mkdir(parents=True, exist_ok=True)

    print("📋 Copying figure index…")
    copy_figure_index(figures_dir, output_dir)

    print("📋 Loading figure index…")
    figure_index = load_figure_index(figures_dir)
    if not figure_index:
        print("✗ No figures found in index")
        return

    print(f"🖼️  Found {len(figure_index)} figures to process")
    batch_size = 10
    items = list(figure_index.items())
    success = fail = 0

    for i in range(0, len(items), batch_size):
        batch = items[i : i + batch_size]
        print(f"\n🔄 Processing batch {i//batch_size + 1}/{(len(items)+batch_size-1)//batch_size}")
        tasks = [process_single_figure(figures_dir, output_dir, md.get("id", key), md) for key, md in batch]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        for r in results:
            if isinstance(r, Exception):
                print(f"✗ Exception: {r}")
                fail += 1
            elif r:
                success += 1
            else:
                fail += 1
        if i + batch_size < len(items):
            await asyncio.sleep(1)

    print("\n" + "="*60)
    print("🖼️  FIGURE SUMMARIZATION COMPLETE")
    print("="*60)
    print(f"✓ Successfully processed: {success}")
    print(f"✗ Failed to process: {fail}")
    print(f"📁 Output directory: {output_dir}")
    print("="*60)

if __name__ == "__main__":
    print("🚀 Starting figure summarization process…")
    asyncio.run(process_all_figures()) 