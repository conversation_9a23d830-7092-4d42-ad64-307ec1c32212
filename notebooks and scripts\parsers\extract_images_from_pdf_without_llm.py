#!/usr/bin/env python3
"""
PDF Image Extractor

This script extracts all images from a PDF file and saves them to a specified output directory.
Supports various image formats (JPEG, PNG, etc.) and provides progress feedback.
Can also extract text context around images (headings, captions, etc.) with coordinate information.
Additionally detects and extracts text-based figures (LaTeX diagrams, process flows) as PNG images
with structured text content extraction.

Dependencies:
    pip install PyMuPDF Pillow pdfplumber

Usage:
    python extract_images_from_pdf.py input.pdf -o output_directory --metadata --text-context --text-figures
"""

import argparse
import os
import sys
import json
import re
from pathlib import Path
from typing import List, Tuple, Optional
from datetime import datetime
import fitz  # PyMuPDF
import pdfplumber
from PIL import Image
import io
import traceback


class PDFImageExtractor:
    """Class to handle PDF image extraction operations."""
    
    def __init__(self, pdf_path: str, output_dir: str, min_width: int = 50, min_height: int = 50):
        """
        Initialize the PDF image extractor.
        
        Args:
            pdf_path: Path to the input PDF file
            output_dir: Directory to save extracted images
            min_width: Minimum width of images to extract (filters out small icons)
            min_height: Minimum height of images to extract (filters out small icons)
        """
        self.pdf_path = Path(pdf_path)
        self.output_dir = Path(output_dir)
        self.min_width = min_width
        self.min_height = min_height
        
        # Validate inputs
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Supported image extensions
        self.image_extensions = {
            "jpeg": "jpg",
            "png": "png",
            "bmp": "bmp",
            "tiff": "tiff",
            "gif": "gif"
        }
    
    def extract_images(self, save_metadata: bool = False, extract_text_context: bool = False, extract_text_figures: bool = False, skip_black_images: bool = True, fix_image_issues: bool = False) -> List[str]:
        """
        Extract all images from the PDF file.
        
        Args:
            save_metadata: Whether to save image metadata to JSON file
            extract_text_context: Whether to extract text context around images
            extract_text_figures: Whether to detect and extract text-based figures
            skip_black_images: Whether to skip images that are mostly black
            fix_image_issues: Whether to apply processing to fix transparency/color issues
            
        Returns:
            List of paths to extracted images and rendered text figures
        """
        extracted_images = []
        
        try:
            # Open the PDF
            pdf_document = fitz.open(str(self.pdf_path))
            total_pages = len(pdf_document)
            
            print(f"Processing PDF: {self.pdf_path.name}")
            print(f"Total pages: {total_pages}")
            print(f"Output directory: {self.output_dir}")
            print("-" * 50)
            
            image_count = 0
            metadata_info = []
            
            # Iterate through each page
            for page_num in range(total_pages):
                page = pdf_document[page_num]
                image_list = page.get_images()
                
                if image_list:
                    print(f"Page {page_num + 1}: Found {len(image_list)} image(s)")
                
                # Extract each image
                for img_index, img in enumerate(image_list):
                    try:
                        # Get image data
                        xref = img[0]
                        base_image = pdf_document.extract_image(xref)
                        image_bytes = base_image["image"]
                        image_ext = base_image["ext"]
                        width = base_image["width"]
                        height = base_image["height"]
                        
                        # Get additional image metadata for debugging
                        colorspace = base_image.get("colorspace", "unknown")
                        bpc = base_image.get("bpc", "unknown")  # bits per component
                        
                        # Filter out small images (likely icons or artifacts)
                        if width < self.min_width or height < self.min_height:
                            print(f"  Skipping small image: {width}x{height}")
                            continue
                        
                        # Generate temporary filename (will be renamed after figure mapping)
                        temp_filename = f"temp_page_{page_num + 1:03d}_img_{img_index + 1:03d}.{image_ext}"
                        image_path = self.output_dir / temp_filename
                        
                        # Save the image with optional enhanced processing
                        if fix_image_issues:
                            extraction_success = self._save_image_with_processing(
                                image_bytes, image_path, image_ext, colorspace, bpc, width, height
                            )
                        else:
                            # Standard save
                            try:
                                with open(image_path, "wb") as img_file:
                                    img_file.write(image_bytes)
                                extraction_success = True
                            except Exception as e:
                                traceback.print_exc()
                                print(f"  Error saving {temp_filename}: {str(e)}")
                                extraction_success = False
                        
                        if extraction_success:
                            extracted_images.append(str(image_path))
                            image_count += 1
                            
                            print(f"  Extracted: {temp_filename} ({width}x{height}, {len(image_bytes)} bytes)")
                            if colorspace not in ["DeviceRGB", "RGB"]:
                                print(f"    → Colorspace: {colorspace}, BPC: {bpc}")
                            
                            # Check if image is mostly black and should be skipped
                            is_black_image = False
                            if skip_black_images:
                                is_black_image = self._is_black_image(image_path)
                                if is_black_image:
                                    print(f"    → Detected black-only image, will be marked for removal")
                        else:
                            print(f"  Failed to extract: {temp_filename} - trying alternative method")
                            continue
                        
                                                 # Collect metadata
                        if save_metadata:
                            metadata_item = {
                                "temp_filename": temp_filename,
                                "final_filename": None,  # Will be set after figure mapping
                                "page": page_num + 1,
                                "type": "regular_image",
                                "width": width,
                                "height": height,
                                "format": image_ext,
                                "size_bytes": len(image_bytes),
                                "is_black_image": is_black_image,
                                "temp_image_path": str(image_path)
                            }
                            
                            # Try to get image position (needed for mapping to figure headings)
                            img_bbox = None
                            try:
                                # Get image position for text context extraction and figure mapping
                                image_list_with_transforms = page.get_images(full=True)
                                if img_index < len(image_list_with_transforms):
                                    img_with_transform = image_list_with_transforms[img_index]
                                    if len(img_with_transform) > 1:
                                        # Try to get image rectangle from the page
                                        img_rects = page.get_image_rects(img_with_transform[0])
                                        if img_rects:
                                            img_bbox = img_rects[0]
                                            metadata_item["bbox"] = list(img_bbox)  # Store bbox for figure mapping
                                            
                                            if extract_text_context:
                                                text_context = self._get_text_context_around_image(page, img_bbox)
                                                metadata_item["text_context"] = text_context
                            except Exception as e:
                                traceback.print_exc()
                                if extract_text_context:
                                    metadata_item["text_context"] = {"error": f"Could not extract text context: {str(e)}"}
                            
                            # Always append to metadata for potential figure mapping
                            metadata_info.append(metadata_item)
                            
                    except Exception as e:
                        traceback.print_exc()
                        print(f"  Error extracting image {img_index + 1}: {str(e)}")
                        continue
            
            # Always try to find figure headings for better naming, even if not extracting text figures
            print("\nAnalyzing document structure for figure headings...")
            figure_headings = self._find_figure_headings(pdf_document)
            
            if figure_headings:
                # Map regular images to figure headings for better naming
                regular_images_metadata = [item for item in metadata_info if item.get("type") == "regular_image"]
                print(f"\nMapping {len(regular_images_metadata)} regular images to {len(figure_headings)} figure headings...")
                
                self._map_images_to_headings(figure_headings, regular_images_metadata)
                
                # Rename images based on figure mappings
                self._rename_images_by_figure_headings(extracted_images, metadata_info, figure_headings)
            else:
                # No figure headings found, use generic naming for all images
                print("\nNo figure headings found, using generic naming...")
                self._rename_images_generic(extracted_images, metadata_info)

            # Extract text-based figures if requested
            text_figure_count = 0
            if extract_text_figures and figure_headings:
                
                # Show mapping results and categorize headings
                matched_headings = [h for h in figure_headings if h["has_image"]]
                unmatched_headings = [h for h in figure_headings if not h["has_image"]]
                text_extraction_headings = [h for h in figure_headings if not h["has_image"] or h.get("needs_text_extraction", False)]
                
                print(f"\nMapping results:")
                print(f"  - {len(matched_headings)} headings matched to regular images")
                print(f"  - {len(unmatched_headings)} headings without regular images")
                print(f"  - {len(text_extraction_headings)} headings need text-based extraction")
                
                if matched_headings:
                    print("\nHeadings with regular images:")
                    for heading in matched_headings:
                        matched_files = [img["filename"] for img in heading["matched_images"]]
                        if heading.get("needs_text_extraction", False):
                            analysis = heading.get("text_figure_analysis", {})
                            reason = analysis.get("reason", "unknown")
                            print(f"  🔍 Page {heading['page']}: {heading['text'][:50]}... → {', '.join(matched_files)} (ALSO text extraction: {reason})")
                        else:
                            print(f"  ✓ Page {heading['page']}: {heading['text'][:50]}... → {', '.join(matched_files)} (images only)")
                
                if text_extraction_headings:
                    print("\nHeadings that will be extracted as text figures:")
                    for heading in text_extraction_headings:
                        if heading.get("needs_text_extraction", False):
                            analysis = heading.get("text_figure_analysis", {})
                            stats = analysis.get("stats", {})
                            print(f"  📝🖼️ Page {heading['page']}: {heading['text'][:50]}... (has {stats.get('total_images', 0)} small images)")
                        else:
                            print(f"  📝 Page {heading['page']}: {heading['text'][:50]}... (no images)")
                    
                    # Step 3: Extract text-based figures for headings that need it
                    print(f"\nExtracting {len(text_extraction_headings)} text-based figures...")
                    for heading in text_extraction_headings:
                        try:
                            page_num = heading["page"] - 1  # Convert to 0-based
                            page = pdf_document[page_num]
                            
                            # Find the next heading on the same page to limit boundaries
                            next_heading_y = None
                            for other_heading in figure_headings:
                                if (other_heading["page"] == heading["page"] and 
                                    other_heading["y_position"] > heading["y_position"]):
                                    next_heading_y = other_heading["y_position"]
                                    break
                            
                            # Detect figure boundaries
                            figure_bbox = self._detect_text_figure_boundaries(
                                page, heading["bbox"], next_heading_y
                            )
                            
                            # Generate filename for text figure
                            filename = f"page_{heading['page']:03d}_textfig_{heading['text'][:20].replace(' ', '_').replace('.', '').replace(':', '')}.png"
                            image_path = self.output_dir / filename
                            
                            # Render the text figure as PNG
                            if self._render_figure_as_image(page, figure_bbox, str(image_path)):
                                extracted_images.append(str(image_path))
                                text_figure_count += 1
                                
                                # Extract structured text content
                                text_content = self._extract_text_figure_content(page, figure_bbox)
                                
                                width = figure_bbox[2] - figure_bbox[0]
                                height = figure_bbox[3] - figure_bbox[1]
                                
                                print(f"    Extracted: {filename} ({width:.0f}x{height:.0f})")
                                print(f"    Title: {heading['text'][:60]}...")
                                
                                # Add to metadata if requested
                                if save_metadata:
                                    metadata_item = {
                                        "filename": filename,
                                        "page": heading["page"],
                                        "type": "text_figure",
                                        "width": round(width, 1),
                                        "height": round(height, 1),
                                        "bbox": list(figure_bbox),
                                        "figure_heading": heading["text"],
                                        "text_blocks_count": text_content["total_blocks"],
                                        "text_content": text_content
                                    }
                                    
                                    # Add information about hybrid extraction (text + images)
                                    if heading.get("needs_text_extraction", False):
                                        metadata_item["extraction_type"] = "hybrid_text_and_images"
                                        metadata_item["associated_images"] = heading.get("matched_images", [])
                                        metadata_item["analysis"] = heading.get("text_figure_analysis", {})
                                    else:
                                        metadata_item["extraction_type"] = "text_only"
                                    
                                    metadata_info.append(metadata_item)
                            
                        except Exception as e:
                            traceback.print_exc()
                            print(f"    Error extracting text figure '{heading['text'][:30]}...': {str(e)}")
                            continue
                else:
                    print("\nNo headings need text-based figure extraction")

            # Filter out skipped images and black images from final results and file system
            original_count = len(extracted_images)
            
            # Clean up images that are part of text figures (if text figure extraction was used)
            skipped_count = 0
            if extract_text_figures:
                skipped_count = self._cleanup_skipped_images(extracted_images, metadata_info)
                if skipped_count > 0:
                    print(f"\nCleaned up {skipped_count} small images that are part of text-based figures")
            
            # Clean up black images
            black_count = 0
            if skip_black_images:
                black_count = self._cleanup_black_images(extracted_images, metadata_info)
                if black_count > 0:
                    print(f"Cleaned up {black_count} black/artifact images")
            
            total_removed = skipped_count + black_count
            if total_removed > 0:
                print(f"Final image count: {len(extracted_images)} (was {original_count})")
            
            # Save metadata if requested
            if save_metadata and metadata_info:
                self._save_metadata(metadata_info)
            
            print("-" * 50)
            
            # Calculate final statistics
            final_regular_images = len([img for img in metadata_info 
                                      if img.get("type") == "regular_image" 
                                      and not img.get("skip_extraction", False)
                                      and not img.get("is_black_image", False)])
            skipped_images = len([img for img in metadata_info if img.get("skip_extraction", False)])
            black_images = len([img for img in metadata_info if img.get("is_black_image", False)])
            
            print(f"Extraction complete!")
            print(f"  Regular images: {final_regular_images}")
            print(f"  Text figures: {text_figure_count}")
            if skipped_images > 0:
                print(f"  Skipped images (part of text figures): {skipped_images}")
            if black_images > 0:
                print(f"  Black/artifact images removed: {black_images}")
            print(f"  Total items extracted: {len(extracted_images)}")
            
            # Close the PDF
            pdf_document.close()
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing PDF: {str(e)}")
            sys.exit(1)
        
        return extracted_images
    
    def extract_images_with_coordinates(self, extract_text_context: bool = False) -> List[Tuple[str, dict]]:
        """
        Extract images with their coordinate information from the PDF.
        
        Returns:
            List of tuples containing (image_path, coordinate_info)
        """
        extracted_images = []
        
        try:
            pdf_document = fitz.open(str(self.pdf_path))
            total_pages = len(pdf_document)
            
            print(f"Extracting images with coordinates from: {self.pdf_path.name}")
            print("-" * 50)
            
            image_count = 0
            
            for page_num in range(total_pages):
                page = pdf_document[page_num]
                
                # Get image blocks with coordinates
                image_blocks = [block for block in page.get_text("dict")["blocks"] if "image" in block]
                
                for img_index, block in enumerate(image_blocks):
                    try:
                        # Get image data
                        img_ref = block["image"]
                        base_image = pdf_document.extract_image(img_ref)
                        image_bytes = base_image["image"]
                        image_ext = base_image["ext"]
                        width = base_image["width"]
                        height = base_image["height"]
                        
                        # Get coordinates
                        bbox = block["bbox"]  # (x0, y0, x1, y1)
                        
                        # Filter out small images
                        if width < self.min_width or height < self.min_height:
                            continue
                        
                        # Generate filename
                        filename = f"page_{page_num + 1:03d}_img_figure_{img_index + 1:03d}_coord.{image_ext}"
                        image_path = self.output_dir / filename
                        
                        # Save the image
                        with open(image_path, "wb") as img_file:
                            img_file.write(image_bytes)
                        
                                                 # Coordinate information
                        coord_info = {
                            "page": page_num + 1,
                            "bbox": bbox,
                            "width": width,
                            "height": height,
                            "x0": bbox[0],
                            "y0": bbox[1],
                            "x1": bbox[2],
                            "y1": bbox[3]
                        }
                        
                        context_summary = ""
                        
                        # Get text context around image if requested
                        if extract_text_context:
                            text_context = self._get_text_context_around_image(page, bbox)
                            coord_info["text_context"] = text_context
                            
                            # Show text context summary
                            if text_context.get("figure_headings"):
                                context_summary += f", figure: '{text_context['figure_headings'][0]['text'][:50]}...'"
                            elif text_context.get("headings_above"):
                                context_summary += f", heading: '{text_context['headings_above'][0]['text'][:50]}...'"
                            if text_context.get("captions_below"):
                                context_summary += f", caption: '{text_context['captions_below'][0]['text'][:50]}...'"
                        
                        extracted_images.append((str(image_path), coord_info))
                        image_count += 1
                        
                        print(f"  Page {page_num + 1}: {filename} at ({bbox[0]:.1f}, {bbox[1]:.1f}){context_summary}")
                        
                    except Exception as e:
                        traceback.print_exc()
                        print(f"  Error extracting image with coordinates: {str(e)}")
                        continue
            
            print(f"Total images with coordinates extracted: {image_count}")
            pdf_document.close()
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing PDF for coordinates: {str(e)}")
            sys.exit(1)
        
        return extracted_images
    
    def _save_metadata(self, metadata_info: List[dict]):
        """Save image metadata to a JSON file."""
        metadata_file = self.output_dir / "extraction_metadata.json"
        
        metadata = {
            "extraction_info": {
                "source_pdf": self.pdf_path.name,
                "extraction_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "total_images": len(metadata_info),
                "min_dimensions": {
                    "width": self.min_width,
                    "height": self.min_height
                }
            },
            "images": metadata_info
        }
        
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"Metadata saved to: {metadata_file}")
    
    def _get_text_context_around_image(self, page, image_bbox: tuple, context_distance: float = 500) -> dict:
        """
        Extract text context around an image (headings, captions, etc.).
        
        Args:
            page: PDF page object
            image_bbox: Image bounding box (x0, y0, x1, y1)
            context_distance: Maximum distance to look for text context
            
        Returns:
            Dictionary with text context information
        """
        try:
            # Get all text blocks with formatting information
            text_dict = page.get_text("dict")
            
            img_x0, img_y0, img_x1, img_y1 = image_bbox
            img_center_x = (img_x0 + img_x1) / 2
            img_center_y = (img_y0 + img_y1) / 2
            
            text_context = {
                "headings_above": [],
                "captions_below": [],
                "surrounding_text": [],
                "text_blocks_near": [],
                "figure_headings": []
            }
            
            for block in text_dict.get("blocks", []):
                if block.get("type") == 0:  # Text block
                    block_bbox = block.get("bbox", [0, 0, 0, 0])
                    block_x0, block_y0, block_x1, block_y1 = block_bbox
                    
                    # Calculate distance from image center to text block center
                    text_center_x = (block_x0 + block_x1) / 2
                    text_center_y = (block_y0 + block_y1) / 2
                    
                    distance = ((img_center_x - text_center_x) ** 2 + (img_center_y - text_center_y) ** 2) ** 0.5
                    
                    if distance <= context_distance:
                        # Extract text from the block
                        block_text = ""
                        font_sizes = []
                        is_bold = False
                        
                        for line in block.get("lines", []):
                            for span in line.get("spans", []):
                                span_text = span.get("text", "").strip()
                                if span_text:
                                    block_text += span_text + " "
                                    font_sizes.append(span.get("size", 12))
                                    if span.get("flags", 0) & 2**4:  # Bold flag
                                        is_bold = True
                        
                        block_text = block_text.strip()
                        if not block_text:
                            continue
                        
                        avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 12
                        
                        text_info = {
                            "text": block_text,
                            "bbox": block_bbox,
                            "distance": round(distance, 2),
                            "font_size": round(avg_font_size, 1),
                            "is_bold": is_bold,
                            "position_relative_to_image": self._get_relative_position(block_bbox, image_bbox)
                        }
                        
                        # Check if text starts with "figure" (case insensitive)
                        is_figure_heading = block_text.lower().startswith("figure")
                        
                        # Add to figure headings if it starts with "figure"
                        if is_figure_heading:
                            text_context["figure_headings"].append(text_info)
                        
                        # Categorize text based on position and formatting
                        if block_y1 < img_y0:  # Text above image
                            if is_bold or avg_font_size > 14:  # Likely heading
                                text_context["headings_above"].append(text_info)
                            else:
                                text_context["surrounding_text"].append(text_info)
                        elif block_y0 > img_y1:  # Text below image
                            if distance < 50:  # Close to image, likely caption
                                text_context["captions_below"].append(text_info)
                            else:
                                text_context["surrounding_text"].append(text_info)
                        else:  # Text at same level as image
                            text_context["surrounding_text"].append(text_info)
                        
                        text_context["text_blocks_near"].append(text_info)
            
            # Sort by distance
            for key in text_context:
                if isinstance(text_context[key], list):
                    text_context[key].sort(key=lambda x: x.get("distance", float('inf')))
            
            return text_context
            
        except Exception as e:
            traceback.print_exc()
            return {
                "error": f"Failed to extract text context: {str(e)}",
                "headings_above": [],
                "captions_below": [],
                "surrounding_text": [],
                "text_blocks_near": [],
                "figure_headings": []
            }
    
    def _get_relative_position(self, text_bbox: tuple, image_bbox: tuple) -> str:
        """Determine relative position of text to image."""
        text_x0, text_y0, text_x1, text_y1 = text_bbox
        img_x0, img_y0, img_x1, img_y1 = image_bbox
        
        # Check vertical position
        if text_y1 < img_y0:
            vertical = "above"
        elif text_y0 > img_y1:
            vertical = "below"
        else:
            vertical = "aligned"
        
        # Check horizontal position
        if text_x1 < img_x0:
            horizontal = "left"
        elif text_x0 > img_x1:
            horizontal = "right"
        else:
            horizontal = "overlapping"
        
        return f"{vertical}_{horizontal}"
    
    def _extract_text_and_find_figures(self) -> List[dict]:
        """
        Extract all text from PDF using pdfplumber and find figure headings.
        Uses the same clean approach as the table extraction script.
        
        Returns:
            List of figure headings found in the document
        """
        figure_headings = []
        
        try:
            print("Extracting text and analyzing for figure headings...")
            
            with pdfplumber.open(str(self.pdf_path)) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract clean text from the page
                    page_text = page.extract_text()
                    
                    if not page_text:
                        continue
                    
                    # Split into lines for analysis
                    lines = page_text.split('\n')
                    
                    # Check if this page contains a TOC section
                    is_toc_page = self._is_toc_page(lines)
                    if is_toc_page:
                        print(f"  Page {page_num + 1} appears to be a Table of Contents - skipping figure detection")
                        continue
                    
                    page_figure_headings = []  # Track figure headings on this page
                    
                    for line_num, line in enumerate(lines):
                        line = line.strip()
                        if not line:
                            continue
                        
                        # Check if this line is a figure heading
                        if self._is_figure_heading(line):
                            # Estimate position based on line number
                            # This is approximate but sufficient for mapping
                            estimated_y = (line_num / len(lines)) * page.height
                            
                            # Try to get more precise coordinates using character-level search
                            bbox = self._find_text_bbox_in_page(page, line)
                            if not bbox:
                                # Fallback to estimated position
                                bbox = [50, estimated_y, page.width - 50, estimated_y + 20]
                            
                            confidence = self._calculate_heading_confidence(line, 12, False)  # Default values
                            
                            heading_info = {
                                "text": line,
                                "page": page_num + 1,
                                "bbox": bbox,
                                "y_position": bbox[1],
                                "font_size": 12,  # We don't have this info from simple text extraction
                                "is_bold": False,  # We don't have this info from simple text extraction
                                "has_image": False,
                                "matched_images": [],
                                "confidence": confidence,
                                "extraction_method": "pdfplumber_text"
                            }
                            
                            page_figure_headings.append(heading_info)
                            print(f"  Found potential figure heading on page {page_num + 1}: {line[:60]}...")
                    
                    # Filter out if too many figure headings on one page (likely TOC)
                    if len(page_figure_headings) > 5:
                        print(f"  Page {page_num + 1} has {len(page_figure_headings)} figure headings - likely TOC, filtering out")
                        # Still add them but with lower confidence
                        for heading in page_figure_headings:
                            heading["confidence"] *= 0.3  # Reduce confidence significantly
                            heading["likely_toc"] = True
                    
                    figure_headings.extend(page_figure_headings)
            
            # Sort by page and position
            figure_headings.sort(key=lambda x: (x["page"], x["y_position"]))
            
            return figure_headings
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error extracting text with pdfplumber: {str(e)}")
            return []
    
    def _find_text_bbox_in_page(self, page, target_text: str) -> List[float]:
        """
        Find the bounding box of specific text within a page.
        
        Args:
            page: pdfplumber page object
            target_text: Text to find
            
        Returns:
            Bounding box [x0, y0, x1, y1] or None if not found
        """
        try:
            # Search for the text in characters
            chars = page.chars
            target_words = target_text.lower().split()
            
            if not target_words:
                return None
            
            # Look for the first word of the target text
            first_word = target_words[0]
            
            for i, char in enumerate(chars):
                if char.get('text', '').lower().startswith(first_word[0]):
                    # Found potential start, check if full text matches
                    text_chars = []
                    for j in range(i, min(i + len(target_text) + 10, len(chars))):
                        if j < len(chars):
                            text_chars.append(chars[j])
                    
                    if text_chars:
                        extracted_text = ''.join(c.get('text', '') for c in text_chars[:len(target_text)])
                        if target_text.lower().startswith(extracted_text.lower()[:len(target_text)//2]):
                            # Found it! Calculate bounding box
                            min_x0 = min(c['x0'] for c in text_chars if 'x0' in c)
                            min_y0 = min(c['top'] for c in text_chars if 'top' in c)
                            max_x1 = max(c['x1'] for c in text_chars if 'x1' in c)
                            max_y1 = max(c['bottom'] for c in text_chars if 'bottom' in c)
                            
                            return [min_x0, min_y0, max_x1, max_y1]
            
            return None
            
        except Exception:
            traceback.print_exc()
            return None
    
    def _find_figure_headings(self, pdf_document) -> List[dict]:
        """
        Find all figure headings using the clean pdfplumber approach.
        
        Args:
            pdf_document: PyMuPDF PDF document object (kept for compatibility)
            
        Returns:
            List of figure headings with their positions and page numbers
        """
        try:
            # Use the new clean text extraction approach
            figure_headings = self._extract_text_and_find_figures()
            
            # Filter by confidence if we have too many matches
            if len(figure_headings) > 20:  # Reasonable threshold
                high_confidence = [h for h in figure_headings if h["confidence"] > 0.7]
                if high_confidence:
                    figure_headings = high_confidence
            
            print(f"Found {len(figure_headings)} figure headings in document")
            for i, heading in enumerate(figure_headings):
                confidence = heading.get("confidence", 0)
                print(f"  {i+1}. Page {heading['page']}: {heading['text'][:60]}... (confidence: {confidence:.2f})")
            
            return figure_headings
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error finding figure headings with pdfplumber: {str(e)}")
            print("Falling back to PyMuPDF text extraction...")
            return self._find_figure_headings_fallback(pdf_document)
    
    def _is_figure_heading(self, text: str) -> bool:
        """
        Determine if a text line is likely a figure heading using improved pattern matching.
        Excludes Table of Contents entries.
        
        Args:
            text: Text content to analyze
            
        Returns:
            True if likely a figure heading (not TOC entry)
        """
        text_clean = text.strip().lower()
        original_text = text.strip()
        
        # First, check if this looks like a TOC entry and exclude it
        if self._is_toc_entry(original_text):
            return False
        
        # More specific patterns for figure headings
        figure_patterns = [
            r'^figure\s+\d+[\.\:\-\s]',  # Figure 1. or Figure 1: or Figure 1-
            r'^fig\s+\d+[\.\:\-\s]',     # Fig 1. or Fig. 1:
            r'^figure\s+[a-z][\.\:\-\s]', # Figure A. or Figure a:
            r'^fig\s+[a-z][\.\:\-\s]',    # Fig A. or Fig a:
            r'^figure\s+\d+[a-z][\.\:\-\s]', # Figure 1a. or Figure 2b:
            r'^figure\s+[ivxlcdm]+[\.\:\-\s]'  # Figure i. or Figure II:
        ]
        
        # Check against patterns
        for pattern in figure_patterns:
            if re.match(pattern, text_clean):
                return True
        
        return False
    
    def _is_toc_entry(self, text: str) -> bool:
        """
        Determine if a text line is likely a Table of Contents entry.
        
        Args:
            text: Text content to analyze
            
        Returns:
            True if likely a TOC entry
        """
        # Common TOC patterns
        toc_patterns = [
            r'.*\.{3,}.*\d+\s*$',           # Text followed by dots and page number: "Figure 1....23"
            r'.*\-{3,}.*\d+\s*$',           # Text followed by dashes and page number: "Figure 1---23"
            r'.*\s+\d+\s*$',                # Text ending with just a page number: "Figure 1 Description  23"
            r'^figure\s+\d+.*\s+\d{1,3}\s*$', # Figure + number + description + page number
        ]
        
        for pattern in toc_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True
        
        # Check for excessive dots or dashes (common in TOC formatting)
        dot_count = text.count('.')
        dash_count = text.count('-')
        underscore_count = text.count('_')
        
        if dot_count > 5 or dash_count > 5 or underscore_count > 5:
            return True
        
        # Check if text ends with a number that could be a page reference
        words = text.split()
        if len(words) > 2 and words[-1].isdigit():
            page_num = int(words[-1])
            # If it's a reasonable page number (1-999), likely TOC
            if 1 <= page_num <= 999:
                return True
        
        return False
    
    def _is_toc_page(self, lines: List[str]) -> bool:
        """
        Determine if a page appears to be a Table of Contents.
        
        Args:
            lines: List of text lines from the page
            
        Returns:
            True if page appears to be a TOC
        """
        # Look for TOC section headers
        toc_headers = [
            'table of contents', 'contents', 'list of figures', 'list of tables',
            'table of figure', 'figures and tables', 'index of figures'
        ]
        
        for line in lines[:10]:  # Check first 10 lines for headers
            line_lower = line.strip().lower()
            if any(header in line_lower for header in toc_headers):
                return True
        
        # Count figure-like entries and see if there are too many
        figure_count = 0
        toc_pattern_count = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Count potential figure references
            if re.match(r'^figure\s+\d+', line.lower()):
                figure_count += 1
            
            # Count TOC formatting patterns
            if (re.search(r'\.{3,}', line) or  # Multiple dots
                re.search(r'\-{3,}', line) or  # Multiple dashes
                re.match(r'.*\s+\d{1,3}\s*$', line)):  # Ends with page number
                toc_pattern_count += 1
        
        # If we have many figure references with TOC formatting, likely a TOC
        if figure_count >= 3 and toc_pattern_count >= 3:
            return True
        
        # If more than half the lines have TOC patterns, likely a TOC
        non_empty_lines = [line for line in lines if line.strip()]
        if len(non_empty_lines) > 0 and toc_pattern_count / len(non_empty_lines) > 0.5:
            return True
        
        return False
    
    def _calculate_heading_confidence(self, text: str, font_size: float, is_bold: bool) -> float:
        """
        Calculate confidence score for figure heading detection.
        
        Args:
            text: Text content
            font_size: Average font size
            is_bold: Whether text is bold
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        confidence = 0.0
        text_lower = text.lower().strip()
        
        # Base confidence for pattern match
        if re.match(r'^figure\s+\d+[\.\:\-\s]', text_lower):
            confidence += 0.6
        elif re.match(r'^fig\s+\d+[\.\:\-\s]', text_lower):
            confidence += 0.5
        elif text_lower.startswith('figure'):
            confidence += 0.3
        
        # Font size bonus
        if font_size > 14:
            confidence += 0.2
        elif font_size > 12:
            confidence += 0.1
        
        # Bold text bonus
        if is_bold:
            confidence += 0.1
        
        # Length penalty (very long text is less likely to be a heading)
        if len(text) > 100:
            confidence -= 0.2
        elif len(text) > 200:
            confidence -= 0.4
        
        # Common figure heading words bonus
        heading_words = ['diagram', 'chart', 'graph', 'illustration', 'schematic', 'flowchart', 'overview']
        if any(word in text_lower for word in heading_words):
            confidence += 0.1
        
        return min(1.0, max(0.0, confidence))
    
    def _find_figure_headings_fallback(self, pdf_document) -> List[dict]:
        """
        Fallback method using PyMuPDF if pdfplumber fails.
        """
        figure_headings = []
        
        try:
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                text_dict = page.get_text("dict")
                
                for block in text_dict.get("blocks", []):
                    if block.get("type") == 0:  # Text block
                        bbox = block.get("bbox", [0, 0, 0, 0])
                        block_text = ""
                        font_sizes = []
                        is_bold = False
                        
                        for line in block.get("lines", []):
                            for span in line.get("spans", []):
                                span_text = span.get("text", "").strip()
                                if span_text:
                                    block_text += span_text + " "
                                    font_sizes.append(span.get("size", 12))
                                    if span.get("flags", 0) & 2**4:
                                        is_bold = True
                        
                        block_text = block_text.strip()
                        
                        if self._is_figure_heading(block_text):
                            avg_font_size = sum(font_sizes) / len(font_sizes) if font_sizes else 12
                            
                            figure_headings.append({
                                "text": block_text,
                                "page": page_num + 1,
                                "bbox": bbox,
                                "y_position": bbox[1],
                                "font_size": round(avg_font_size, 1),
                                "is_bold": is_bold,
                                "has_image": False,
                                "matched_images": [],
                                "confidence": self._calculate_heading_confidence(block_text, avg_font_size, is_bold)
                            })
            
            figure_headings.sort(key=lambda x: (x["page"], x["y_position"]))
            return figure_headings
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error in fallback figure heading detection: {str(e)}")
            return []
    
    def _map_images_to_headings(self, figure_headings: List[dict], extracted_images_info: List[dict], context_distance: float = 200) -> List[dict]:
        """
        Map extracted images to figure headings based on proximity.
        Detects text-based figures with embedded small images.
        
        Args:
            figure_headings: List of figure headings found in document
            extracted_images_info: List of extracted image information with positions
            context_distance: Maximum distance to consider image as belonging to heading
            
        Returns:
            Updated figure_headings with matched images marked
        """
        print(f"  Attempting to map {len(extracted_images_info)} images to {len(figure_headings)} headings...")
        
        for heading in figure_headings:
            heading_page = heading["page"]
            heading_y = heading["y_position"]
            
            # Look for images on the same page or nearby pages
            for img_info in extracted_images_info:
                if "bbox" in img_info and abs(img_info["page"] - heading_page) <= 1:  # Same page or adjacent page
                    # Calculate distance from heading to image
                    img_y = img_info["bbox"][1]  # Top of image
                    distance = abs(img_y - heading_y)
                    
                    if distance <= context_distance:
                        heading["has_image"] = True
                        heading["matched_images"].append({
                            "filename": img_info["temp_filename"],
                            "distance": round(distance, 1),
                            "image_bbox": img_info["bbox"],
                            "width": img_info.get("width", 0),
                            "height": img_info.get("height", 0),
                            "size_bytes": img_info.get("size_bytes", 0)
                        })
                        print(f"    ✓ Matched '{heading['text'][:40]}...' to {img_info['temp_filename']} (distance: {distance:.1f}px)")
                
                # Also try to match if image doesn't have bbox but is on same page
                elif "bbox" not in img_info and img_info["page"] == heading_page:
                    heading["has_image"] = True
                    heading["matched_images"].append({
                        "filename": img_info["temp_filename"],
                        "distance": "unknown",
                        "image_bbox": [],
                        "width": img_info.get("width", 0),
                        "height": img_info.get("height", 0),
                        "size_bytes": img_info.get("size_bytes", 0)
                    })
                    print(f"    ✓ Matched '{heading['text'][:40]}...' to {img_info['temp_filename']} (same page, no bbox)")
        
        # Analyze matched images to detect text-based figures with embedded images
        images_to_skip = []
        for heading in figure_headings:
            if heading["has_image"]:
                analysis = self._analyze_matched_images(heading)
                if analysis["likely_text_figure"]:
                    heading["needs_text_extraction"] = True
                    heading["text_figure_analysis"] = analysis
                    
                    # Mark associated small images for skipping since they'll be in the text figure
                    for img in heading["matched_images"]:
                        images_to_skip.append(img["filename"])
                    
                    print(f"    🔍 '{heading['text'][:40]}...' appears to be text-based figure with embedded images")
                    print(f"      → Will skip {len(heading['matched_images'])} small images: {', '.join(img['filename'] for img in heading['matched_images'])}")
        
        # Mark images for skipping in the metadata
        self._mark_images_for_skipping(extracted_images_info, images_to_skip)
        
        return figure_headings
    
    def _mark_images_for_skipping(self, extracted_images_info: List[dict], images_to_skip: List[str]):
        """
        Mark images that should be skipped because they're part of text-based figures.
        
        Args:
            extracted_images_info: List of extracted image metadata
            images_to_skip: List of filenames to mark for skipping
        """
        for img_info in extracted_images_info:
            if img_info["temp_filename"] in images_to_skip:
                img_info["skip_extraction"] = True
                img_info["skip_reason"] = "part_of_text_figure"
    
    def _cleanup_skipped_images(self, extracted_images: List[str], metadata_info: List[dict]) -> int:
        """
        Remove skipped images from file system and update tracking lists.
        
        Args:
            extracted_images: List of extracted image paths (will be modified)
            metadata_info: List of image metadata (will be updated)
            
        Returns:
            Number of images that were cleaned up
        """
        skipped_count = 0
        images_to_remove = []
        
        # Find images marked for skipping
        for img_info in metadata_info:
            if img_info.get("skip_extraction", False):
                filename = img_info.get("final_filename") or img_info.get("temp_filename", "unknown")
                image_path = self.output_dir / filename
                
                # Remove the physical file
                try:
                    if image_path.exists():
                        # image_path.unlink()
                        print(f"    Removed: {filename} (part of text figure)")
                except Exception as e:
                    traceback.print_exc()
                    print(f"    Warning: Could not remove {filename}: {str(e)}")
                
                # Mark for removal from extracted_images list
                full_path = str(image_path)
                if full_path in extracted_images:
                    images_to_remove.append(full_path)
                
                # Update metadata to show it was skipped rather than extracted
                img_info["status"] = "skipped"
                img_info["file_removed"] = True
                skipped_count += 1
        
        # Remove from extracted_images list
        for img_path in images_to_remove:
            extracted_images.remove(img_path)
        
        return skipped_count
    
    def _cleanup_black_images(self, extracted_images: List[str], metadata_info: List[dict]) -> int:
        """
        Remove black/artifact images from file system and update tracking lists.
        
        Args:
            extracted_images: List of extracted image paths (will be modified)
            metadata_info: List of image metadata (will be updated)
            
        Returns:
            Number of black images that were cleaned up
        """
        black_count = 0
        images_to_remove = []
        
        # Find images marked as black
        for img_info in metadata_info:
            if img_info.get("is_black_image", False) and img_info.get("type") == "regular_image":
                filename = img_info.get("final_filename") or img_info.get("temp_filename", "unknown")
                image_path = self.output_dir / filename
                
                # Remove the physical file
                try:
                    if image_path.exists():
                        image_path.unlink()
                        print(f"    Removed: {filename} (black/artifact image)")
                except Exception as e:
                    traceback.print_exc()
                    print(f"    Warning: Could not remove {filename}: {str(e)}")
                
                # Mark for removal from extracted_images list
                full_path = str(image_path)
                if full_path in extracted_images:
                    images_to_remove.append(full_path)
                
                # Update metadata to show it was skipped rather than extracted
                img_info["status"] = "removed_black"
                img_info["file_removed"] = True
                black_count += 1
        
        # Remove from extracted_images list
        for img_path in images_to_remove:
            extracted_images.remove(img_path)
        
        return black_count
    
    def _analyze_matched_images(self, heading: dict) -> dict:
        """
        Analyze matched images to determine if this is a text-based figure.
        
        Args:
            heading: Figure heading with matched images
            
        Returns:
            Analysis results with recommendations
        """
        matched_images = heading.get("matched_images", [])
        
        if not matched_images:
            return {"likely_text_figure": False, "reason": "no_images"}
        
        # Calculate statistics about matched images
        total_images = len(matched_images)
        small_images = 0
        total_area = 0
        max_dimension = 0
        
        for img in matched_images:
            width = img.get("width", 0)
            height = img.get("height", 0)
            area = width * height
            total_area += area
            max_dimension = max(max_dimension, width, height)
            
            # Consider "small" if either dimension is less than 200px or area < 40000
            if width < 200 or height < 200 or area < 40000:
                small_images += 1
        
        avg_area = total_area / total_images if total_images > 0 else 0
        small_image_ratio = small_images / total_images if total_images > 0 else 0
        
        # Decision logic for text-based figure detection
        likely_text_figure = False
        reason = ""
        
        if total_images >= 3 and small_image_ratio >= 0.7:
            # Multiple small images - likely icons/elements in a text diagram
            likely_text_figure = True
            reason = f"multiple_small_images ({total_images} images, {small_image_ratio:.1%} small)"
        
        elif total_images >= 2 and avg_area < 30000:
            # Multiple images with small average area
            likely_text_figure = True
            reason = f"small_average_area ({total_images} images, avg area: {avg_area:.0f})"
        
        elif total_images >= 4:
            # Many images regardless of size - likely composite figure
            likely_text_figure = True
            reason = f"many_images ({total_images} images)"
        
        elif total_images == 2 and max_dimension < 150:
            # Two very small images - likely symbols/icons
            likely_text_figure = True
            reason = f"two_tiny_images (max dimension: {max_dimension}px)"
        
        return {
            "likely_text_figure": likely_text_figure,
            "reason": reason,
            "stats": {
                "total_images": total_images,
                "small_images": small_images,
                "small_image_ratio": small_image_ratio,
                "avg_area": round(avg_area),
                "max_dimension": max_dimension
            }
        }
    
    def _detect_text_figure_boundaries(self, page, heading_bbox: tuple, next_heading_y: float = None) -> tuple:
        """
        Detect the boundaries of a text-based figure using a comprehensive, relaxed approach.
        
        Args:
            page: PDF page object
            heading_bbox: Bounding box of the figure heading
            next_heading_y: Y position of next heading (to limit search area)
            
        Returns:
            Tuple of (x0, y0, x1, y1) representing figure boundaries
        """
        page_bbox = page.rect
        heading_x0, heading_y0, heading_x1, heading_y1 = heading_bbox
        
        print(f"      Detecting boundaries for figure starting at y={heading_y1:.1f}")
        
        # Strategy 1: Use embedded images as boundary hints if available
        embedded_boundaries = self._get_embedded_image_boundaries(page, heading_y1, next_heading_y)
        
        # Strategy 2: Relaxed text-based boundary detection
        text_boundaries = self._get_relaxed_text_boundaries(page, heading_bbox, next_heading_y)
        
        # Strategy 3: Visual layout analysis (whitespace detection)
        layout_boundaries = self._analyze_visual_layout(page, heading_y1, next_heading_y)
        
        # Strategy 4: Generous fallback area
        fallback_boundaries = self._get_generous_fallback_area(page_bbox, heading_bbox, next_heading_y)
        
        # Combine all strategies - take the most generous bounds
        all_boundaries = [embedded_boundaries, text_boundaries, layout_boundaries, fallback_boundaries]
        valid_boundaries = [b for b in all_boundaries if b is not None]
        
        if valid_boundaries:
            # Take the most generous (largest) boundaries
            min_x0 = min(b[0] for b in valid_boundaries)
            min_y0 = min(b[1] for b in valid_boundaries)  
            max_x1 = max(b[2] for b in valid_boundaries)
            max_y1 = max(b[3] for b in valid_boundaries)
            
            # Ensure minimum reasonable size
            min_width = 300
            min_height = 150
            
            if max_x1 - min_x0 < min_width:
                center_x = (min_x0 + max_x1) / 2
                min_x0 = max(0, center_x - min_width/2)
                max_x1 = min(page_bbox.width, center_x + min_width/2)
            
            if max_y1 - min_y0 < min_height:
                max_y1 = min(page_bbox.height, min_y0 + min_height)
            
            final_bounds = (min_x0, min_y0, max_x1, max_y1)
            width = max_x1 - min_x0
            height = max_y1 - min_y0
            
            print(f"      Final boundaries: {width:.0f}x{height:.0f} at ({min_x0:.0f}, {min_y0:.0f})")
            return final_bounds
        
        # Last resort: very generous fallback
        return fallback_boundaries
    
    def _get_embedded_image_boundaries(self, page, start_y: float, end_y: float = None) -> tuple:
        """Use embedded images to help determine figure boundaries."""
        try:
            image_list = page.get_images(full=True)
            if not image_list:
                return None
            
            relevant_images = []
            for img in image_list:
                try:
                    img_rects = page.get_image_rects(img[0])
                    for rect in img_rects:
                        img_y = rect[1]  # Top y coordinate
                        if img_y > start_y and (end_y is None or img_y < end_y):
                            relevant_images.append(rect)
                except:
                    continue
            
            if relevant_images:
                min_x = min(rect[0] for rect in relevant_images)
                min_y = min(rect[1] for rect in relevant_images)
                max_x = max(rect[2] for rect in relevant_images)
                max_y = max(rect[3] for rect in relevant_images)
                
                # Expand around images with generous padding
                padding = 50
                return (
                    max(0, min_x - padding),
                    max(0, min_y - padding),
                    min(page.rect.width, max_x + padding),
                    min(page.rect.height, max_y + padding)
                )
        except:
            pass
        
        return None
    
    def _get_relaxed_text_boundaries(self, page, heading_bbox: tuple, next_heading_y: float = None) -> tuple:
        """Get boundaries using much more relaxed text analysis."""
        heading_x0, heading_y0, heading_x1, heading_y1 = heading_bbox
        text_dict = page.get_text("dict")
        
        figure_blocks = []
        
        for block in text_dict.get("blocks", []):
            if block.get("type") == 0:  # Text block
                bbox = block.get("bbox", [0, 0, 0, 0])
                block_y0 = bbox[1]
                
                # Much more relaxed criteria
                if block_y0 > heading_y1:
                    if next_heading_y and block_y0 >= next_heading_y:
                        break
                    
                    # Extract text
                    block_text = ""
                    for line in block.get("lines", []):
                        for span in line.get("spans", []):
                            span_text = span.get("text", "").strip()
                            if span_text:
                                block_text += span_text + " "
                    
                    block_text = block_text.strip()
                    
                    # Very relaxed filtering - include almost everything
                    if self._is_relaxed_figure_content(block_text, bbox, heading_bbox):
                        figure_blocks.append(bbox)
        
        if figure_blocks:
            min_x = min(bbox[0] for bbox in figure_blocks)
            min_y = min(bbox[1] for bbox in figure_blocks)
            max_x = max(bbox[2] for bbox in figure_blocks)
            max_y = max(bbox[3] for bbox in figure_blocks)
            
            # Add generous padding
            padding = 30
            return (
                max(0, min_x - padding),
                heading_y0,  # Start from heading
                min(page.rect.width, max_x + padding),
                min(page.rect.height, max_y + padding)
            )
        
        return None
    
    def _is_relaxed_figure_content(self, text: str, block_bbox: tuple, heading_bbox: tuple) -> bool:
        """Much more relaxed criteria for figure content."""
        if len(text) < 1:  # Only skip completely empty
            return False
        
        # Skip only obvious non-figure content
        text_lower = text.lower().strip()
        
        # Skip other headings
        if (text_lower.startswith("figure ") or 
            text_lower.startswith("table ") or
            text_lower.startswith("chapter ") or
            text_lower.startswith("section ")):
            return False
        
        # Skip very long paragraphs (>500 chars instead of 200)
        if len(text) > 500:
            return False
        
        # Much more generous horizontal positioning (500px instead of 200px)
        block_x0 = block_bbox[0]
        heading_x0 = heading_bbox[0]
        x_offset = abs(block_x0 - heading_x0)
        
        if x_offset > 500:  # Very generous horizontal range
            return False
        
        return True  # Include everything else
    
    def _analyze_visual_layout(self, page, start_y: float, end_y: float = None) -> tuple:
        """Analyze visual layout to find natural figure boundaries."""
        try:
            # Get all text positions to find content density
            chars = page.chars
            if not chars:
                return None
            
            # Filter chars to relevant area
            relevant_chars = [
                char for char in chars 
                if char.get('top', 0) > start_y and 
                   (end_y is None or char.get('top', 0) < end_y)
            ]
            
            if not relevant_chars:
                return None
            
            # Find content boundaries with generous padding
            min_x = min(char.get('x0', 0) for char in relevant_chars)
            min_y = min(char.get('top', 0) for char in relevant_chars) 
            max_x = max(char.get('x1', 0) for char in relevant_chars)
            max_y = max(char.get('bottom', 0) for char in relevant_chars)
            
            # Very generous padding for visual elements we might miss
            padding = 75
            return (
                max(0, min_x - padding),
                max(0, min_y - padding),
                min(page.rect.width, max_x + padding),
                min(page.rect.height, max_y + padding)
            )
            
        except:
            return None
    
    def _get_generous_fallback_area(self, page_bbox, heading_bbox: tuple, next_heading_y: float = None) -> tuple:
        """Generate a generous fallback area when other methods fail."""
        heading_x0, heading_y0, heading_x1, heading_y1 = heading_bbox
        
        # Much more generous fallback
        fallback_height = 400  # Increased from 200
        
        # Determine end boundary
        if next_heading_y:
            max_y = min(next_heading_y - 20, heading_y1 + fallback_height)
        else:
            max_y = min(page_bbox.height - 50, heading_y1 + fallback_height)
        
        # Use most of page width with reasonable margins
        margin = 30
        
        return (
            margin,  # Start near left margin
            heading_y0,  # Include heading
            page_bbox.width - margin,  # Go to near right margin
            max_y
        )
    
    def _is_figure_content_block(self, text: str, block_bbox: tuple, heading_bbox: tuple) -> bool:
        """
        Determine if a text block is likely part of a figure.
        
        Args:
            text: Text content of the block
            block_bbox: Bounding box of the text block
            heading_bbox: Bounding box of the figure heading
            
        Returns:
            True if block is likely figure content
        """
        # Skip empty or very short text
        if len(text) < 3:
            return False
        
        # Skip very long paragraphs
        if len(text) > 200:
            return False
        
        # Skip if it looks like another figure heading
        if text.lower().startswith("figure"):
            return False
        
        # Skip if it looks like a table heading
        if text.lower().startswith("table"):
            return False
        
        # Check horizontal alignment - figure content often has varied x-positions
        block_x0 = block_bbox[0]
        heading_x0 = heading_bbox[0]
        
        # Allow some horizontal variation (suggesting structured layout)
        x_offset = abs(block_x0 - heading_x0)
        
        # Figure content characteristics:
        # 1. Reasonable horizontal position (not too far from heading)
        # 2. Short to medium length text
        # 3. Contains typical figure elements
        figure_keywords = ["materials", "equipment", "process", "step", "procedure", 
                          "components", "system", "flow", "diagram", "method"]
        
        has_figure_keywords = any(keyword in text.lower() for keyword in figure_keywords)
        
        return (x_offset < 200 and  # Not too far horizontally
                len(text) <= 100 and  # Reasonably short
                (has_figure_keywords or x_offset > 50))  # Either has keywords or is indented/offset
    
    def _extract_text_figure_content(self, page, figure_bbox: tuple) -> dict:
        """
        Extract structured text content from a text-based figure.
        
        Args:
            page: PDF page object
            figure_bbox: Bounding box of the figure (x0, y0, x1, y1)
            
        Returns:
            Dictionary with structured text content
        """
        x0, y0, x1, y1 = figure_bbox
        
        # Extract text within the figure bounds
        text_dict = page.get_text("dict")
        figure_text_blocks = []
        
        for block in text_dict.get("blocks", []):
            if block.get("type") == 0:  # Text block
                bbox = block.get("bbox", [0, 0, 0, 0])
                block_x0, block_y0, block_x1, block_y1 = bbox
                
                # Check if block is within figure bounds
                if (block_x0 >= x0 and block_y0 >= y0 and 
                    block_x1 <= x1 and block_y1 <= y1):
                    
                    block_text = ""
                    font_info = []
                    
                    for line in block.get("lines", []):
                        for span in line.get("spans", []):
                            span_text = span.get("text", "").strip()
                            if span_text:
                                block_text += span_text + " "
                                font_info.append({
                                    "size": span.get("size", 12),
                                    "font": span.get("font", ""),
                                    "flags": span.get("flags", 0)
                                })
                    
                    if block_text.strip():
                        figure_text_blocks.append({
                            "text": block_text.strip(),
                            "bbox": bbox,
                            "position": {
                                "x": round(block_x0, 1),
                                "y": round(block_y0, 1),
                                "width": round(block_x1 - block_x0, 1),
                                "height": round(block_y1 - block_y0, 1)
                            },
                            "font_info": font_info
                        })
        
        # Sort by position (top to bottom, left to right)
        figure_text_blocks.sort(key=lambda x: (x["position"]["y"], x["position"]["x"]))
        
        # Try to identify title/heading
        title = None
        if figure_text_blocks:
            # Look for title above the figure or as first large text
            for block in figure_text_blocks:
                if (block["text"].lower().startswith("figure") or 
                    any(f["size"] > 14 for f in block["font_info"])):
                    title = block["text"]
                    break
        
        return {
            "title": title,
            "text_blocks": figure_text_blocks,
            "total_blocks": len(figure_text_blocks),
            "extracted_text": "\n".join(block["text"] for block in figure_text_blocks)
        }
    
    def _render_figure_as_image(self, page, figure_bbox: tuple, output_path: str, dpi: int = 150) -> bool:
        """
        Render a specific region of the PDF page as a PNG image.
        
        Args:
            page: PDF page object
            figure_bbox: Bounding box to render (x0, y0, x1, y1)
            output_path: Path to save the rendered image
            dpi: Resolution for rendering
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create a transformation matrix for the specific region
            rect = fitz.Rect(figure_bbox)
            
            # Calculate zoom factor based on DPI
            zoom = dpi / 72.0
            mat = fitz.Matrix(zoom, zoom)
            
            # Render the specific region
            pix = page.get_pixmap(matrix=mat, clip=rect)
            
            # Save as PNG
            pix.save(output_path)
            pix = None  # Free memory
            
            return True
            
        except Exception as e:
            traceback.print_exc()
            print(f"Error rendering figure as image: {str(e)}")
            return False
    
    def convert_images_to_format(self, target_format: str = "png") -> List[str]:
        """
        Convert all extracted images to a specific format.
        
        Args:
            target_format: Target image format (png, jpg, etc.)
            
        Returns:
            List of converted image paths
        """
        converted_images = []
        
        # Get all image files in output directory
        image_files = []
        for ext in ["jpg", "jpeg", "png", "bmp", "tiff", "gif"]:
            image_files.extend(self.output_dir.glob(f"*.{ext}"))
        
        print(f"Converting {len(image_files)} images to {target_format} format...")
        
        for img_path in image_files:
            try:
                # Open and convert image
                with Image.open(img_path) as img:
                    # Create new filename with target format
                    new_name = img_path.stem + f"_converted.{target_format}"
                    new_path = self.output_dir / new_name
                    
                    # Convert and save
                    if target_format.lower() == "jpg" or target_format.lower() == "jpeg":
                        # Convert RGBA to RGB for JPEG
                        if img.mode in ("RGBA", "LA", "P"):
                            img = img.convert("RGB")
                    
                    img.save(new_path, target_format.upper())
                    converted_images.append(str(new_path))
                    
                    print(f"  Converted: {img_path.name} -> {new_name}")
                    
            except Exception as e:
                traceback.print_exc()
                print(f"  Error converting {img_path.name}: {str(e)}")
                continue
        
        print(f"Conversion complete! {len(converted_images)} images converted.")
        return converted_images
    
    def _make_safe_filename(self, text: str, max_length: int = 50) -> str:
        """
        Convert figure heading text to a safe filename.
        
        Args:
            text: Figure heading text
            max_length: Maximum length of resulting filename part
            
        Returns:
            Safe filename string
        """
        import re
        
        # Remove "Figure" prefix and clean up
        text = re.sub(r'^figure\s*\d*[.\-:\s]*', '', text, flags=re.IGNORECASE)
        
        # Replace problematic characters with underscores
        safe_text = re.sub(r'[<>:"/\\|?*\[\]{}().,;!\s]+', '_', text)
        
        # Remove multiple consecutive underscores
        safe_text = re.sub(r'_+', '_', safe_text)
        
        # Remove leading/trailing underscores
        safe_text = safe_text.strip('_')
        
        # Truncate if too long
        if len(safe_text) > max_length:
            safe_text = safe_text[:max_length].rstrip('_')
        
        # Ensure it's not empty
        if not safe_text:
            safe_text = "unknown_figure"
        
        return safe_text
    
    def _rename_images_by_figure_headings(self, extracted_images: List[str], metadata_info: List[dict], figure_headings: List[dict]):
        """
        Rename extracted images based on their mapped figure headings.
        
        Args:
            extracted_images: List of extracted image paths (will be modified)
            metadata_info: List of metadata items (will be updated)
            figure_headings: List of figure headings with mapped images
        """
        print("\nRenaming images based on figure headings...")
        
        # Create a mapping of temp_filename to heading text
        temp_to_heading = {}
        heading_usage_count = {}  # Track how many images use each heading
        
        for heading in figure_headings:
            if heading.get("has_image", False):
                heading_text = heading.get("text", "unknown_figure")
                safe_heading = self._make_safe_filename(heading_text)
                
                # Initialize count for this heading
                if safe_heading not in heading_usage_count:
                    heading_usage_count[safe_heading] = 0
                
                for matched_img in heading.get("matched_images", []):
                    temp_filename = matched_img.get("filename", "")
                    if temp_filename:
                        heading_usage_count[safe_heading] += 1
                        img_index = heading_usage_count[safe_heading]
                        temp_to_heading[temp_filename] = {
                            "safe_heading": safe_heading,
                            "index": img_index,
                            "original_heading": heading_text
                        }
        
        # Rename files and update metadata
        renamed_count = 0
        for metadata_item in metadata_info:
            if metadata_item.get("type") == "regular_image":
                temp_filename = metadata_item.get("temp_filename", "")
                temp_path = metadata_item.get("temp_image_path", "")
                
                if temp_filename in temp_to_heading:
                    # This image is mapped to a figure heading
                    heading_info = temp_to_heading[temp_filename]
                    safe_heading = heading_info["safe_heading"]
                    img_index = heading_info["index"]
                    image_ext = metadata_item.get("format", "png")
                    page_num = metadata_item.get("page", 0)
                    
                    # Generate new filename
                    if heading_info["index"] == 1 and heading_usage_count[safe_heading] == 1:
                        # Single image for this heading - no index needed
                        new_filename = f"page_{page_num:03d}_figure_{safe_heading}.{image_ext}"
                    else:
                        # Multiple images for this heading - add index
                        new_filename = f"page_{page_num:03d}_figure_{safe_heading}_{img_index:02d}.{image_ext}"
                    
                    new_path = self.output_dir / new_filename
                    
                    # Rename the file
                    try:
                        if Path(temp_path).exists():
                            Path(temp_path).rename(new_path)
                            
                            # Update the extracted_images list
                            if temp_path in extracted_images:
                                extracted_images[extracted_images.index(temp_path)] = str(new_path)
                            
                            # Update metadata
                            metadata_item["final_filename"] = new_filename
                            metadata_item["filename"] = new_filename  # For backward compatibility
                            metadata_item["mapped_to_heading"] = heading_info["original_heading"]
                            
                            print(f"  ✓ Renamed: {temp_filename} → {new_filename}")
                            print(f"    Mapped to: {heading_info['original_heading'][:60]}...")
                            renamed_count += 1
                            
                    except Exception as e:
                        traceback.print_exc()
                        print(f"  ✗ Failed to rename {temp_filename}: {str(e)}")
                        # Keep temp filename as final
                        metadata_item["final_filename"] = temp_filename
                        metadata_item["filename"] = temp_filename
                
                else:
                    # Image not mapped to any heading - use generic naming
                    image_ext = metadata_item.get("format", "png")
                    page_num = metadata_item.get("page", 0)
                    
                    # Extract index from temp filename
                    import re
                    match = re.search(r'img_(\d+)', temp_filename)
                    img_index = int(match.group(1)) if match else 1
                    
                    generic_filename = f"page_{page_num:03d}_figure_unmapped_{img_index:03d}.{image_ext}"
                    generic_path = self.output_dir / generic_filename
                    
                    # Rename to generic naming
                    try:
                        if Path(temp_path).exists():
                            Path(temp_path).rename(generic_path)
                            
                            # Update the extracted_images list
                            if temp_path in extracted_images:
                                extracted_images[extracted_images.index(temp_path)] = str(generic_path)
                            
                            # Update metadata
                            metadata_item["final_filename"] = generic_filename
                            metadata_item["filename"] = generic_filename
                            
                            print(f"  ○ Renamed unmapped: {temp_filename} → {generic_filename}")
                            renamed_count += 1
                            
                    except Exception as e:
                        traceback.print_exc()
                        print(f"  ✗ Failed to rename unmapped {temp_filename}: {str(e)}")
                        # Keep temp filename as final
                        metadata_item["final_filename"] = temp_filename
                        metadata_item["filename"] = temp_filename
        
        print(f"Renamed {renamed_count} images based on figure headings")
    
    def _rename_images_generic(self, extracted_images: List[str], metadata_info: List[dict]):
        """
        Rename extracted images using generic naming when no figure headings are found.
        
        Args:
            extracted_images: List of extracted image paths (will be modified)
            metadata_info: List of metadata items (will be updated)
        """
        print("Applying generic naming to images...")
        
        renamed_count = 0
        for metadata_item in metadata_info:
            if metadata_item.get("type") == "regular_image":
                temp_filename = metadata_item.get("temp_filename", "")
                temp_path = metadata_item.get("temp_image_path", "")
                
                if temp_path:
                    image_ext = metadata_item.get("format", "png")
                    page_num = metadata_item.get("page", 0)
                    
                    # Extract index from temp filename
                    import re
                    match = re.search(r'img_(\d+)', temp_filename)
                    img_index = int(match.group(1)) if match else 1
                    
                    generic_filename = f"page_{page_num:03d}_figure_{img_index:03d}.{image_ext}"
                    generic_path = self.output_dir / generic_filename
                    
                    # Rename to generic naming
                    try:
                        if Path(temp_path).exists():
                            Path(temp_path).rename(generic_path)
                            
                            # Update the extracted_images list
                            if temp_path in extracted_images:
                                extracted_images[extracted_images.index(temp_path)] = str(generic_path)
                            
                            # Update metadata
                            metadata_item["final_filename"] = generic_filename
                            metadata_item["filename"] = generic_filename
                            
                            print(f"  ○ Renamed: {temp_filename} → {generic_filename}")
                            renamed_count += 1
                            
                    except Exception as e:
                        traceback.print_exc()
                        print(f"  ✗ Failed to rename {temp_filename}: {str(e)}")
                        # Keep temp filename as final
                        metadata_item["final_filename"] = temp_filename
                        metadata_item["filename"] = temp_filename
        
        print(f"Applied generic naming to {renamed_count} images")
    
    def _save_image_with_processing(self, image_bytes: bytes, image_path: str, image_ext: str, 
                                   colorspace: str, bpc: int, width: int, height: int) -> bool:
        """
        Save image with enhanced processing to handle transparency, color space, and other issues.
        
        Args:
            image_bytes: Raw image bytes from PDF
            image_path: Path to save the image
            image_ext: Original image extension
            colorspace: PDF colorspace information
            bpc: Bits per component
            width: Image width
            height: Image height
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # First, save the original
            with open(image_path, "wb") as img_file:
                img_file.write(image_bytes)
            
            # Try to process and fix common issues
            try:
                with Image.open(image_path) as img:
                    original_mode = img.mode
                    needs_processing = False
                    processed_img = img.copy()
                    
                    # Handle transparency issues
                    if img.mode in ('RGBA', 'LA') or 'transparency' in img.info:
                        print(f"    → Processing transparency ({img.mode})")
                        if img.mode == 'RGBA':
                            # Create white background for transparency
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[-1])  # Use alpha channel as mask
                            processed_img = background
                            needs_processing = True
                        elif img.mode == 'LA':
                            # Convert LA to RGB with white background
                            rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                            rgb_img.paste(img.convert('L'), mask=img.split()[-1] if len(img.split()) > 1 else None)
                            processed_img = rgb_img
                            needs_processing = True
                    
                    # Handle different color modes
                    if img.mode in ('CMYK', 'L', 'P'):
                        print(f"    → Converting colorspace ({img.mode} → RGB)")
                        if img.mode == 'CMYK':
                            processed_img = img.convert('RGB')
                        elif img.mode == 'L':
                            processed_img = img.convert('RGB')
                        elif img.mode == 'P':
                            processed_img = img.convert('RGB')
                        needs_processing = True
                    
                    # Check for inverted colors (common issue)
                    if colorspace in ['DeviceGray', 'CalGray'] or original_mode == 'L':
                        # For grayscale images, check if they might be inverted
                        test_pixels = list(processed_img.convert('L').getdata())[:1000]  # Sample first 1000 pixels
                        avg_brightness = sum(test_pixels) / len(test_pixels)
                        
                        if avg_brightness < 50:  # Very dark image, might be inverted
                            print(f"    → Detected potentially inverted image (avg brightness: {avg_brightness:.1f})")
                            inverted_img = Image.eval(processed_img.convert('L'), lambda x: 255 - x).convert('RGB')
                            
                            # Save both versions for comparison
                            base_path = str(image_path).rsplit('.', 1)[0]
                            inverted_path = f"{base_path}_inverted.{image_ext}"
                            inverted_img.save(inverted_path)
                            print(f"    → Saved inverted version: {inverted_path.split('/')[-1]}")
                    
                    # Save processed version if needed
                    if needs_processing:
                        processed_img.save(image_path)
                        print(f"    → Applied processing fixes")
                    
                    return True
                    
            except Exception as processing_error:
                traceback.print_exc()
                print(f"    → Processing failed: {str(processing_error)}, keeping original")
                return True  # Original save succeeded
                
        except Exception as e:
            traceback.print_exc()
            print(f"    → Save failed: {str(e)}")
            return False
    
    def _is_black_image(self, image_path: str, black_threshold: float = 0.95) -> bool:
        """
        Determine if an image is mostly black pixels.
        
        Args:
            image_path: Path to the image file
            black_threshold: Percentage of pixels that must be black (0.0-1.0)
            
        Returns:
            True if image is mostly black
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if not already
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Get image dimensions
                width, height = img.size
                total_pixels = width * height
                
                # For very small images, be more lenient (might be artifacts)
                if total_pixels < 1000:  # Less than ~32x32
                    black_threshold = 0.8
                
                # Sample pixels for large images to improve performance
                if total_pixels > 50000:  # Sample large images
                    # Sample every nth pixel
                    sample_rate = max(1, total_pixels // 10000)
                    pixels = list(img.getdata())[::sample_rate]
                else:
                    pixels = list(img.getdata())
                
                if not pixels:
                    return False
                
                # Count black and very dark pixels
                black_pixels = 0
                dark_threshold = 30  # RGB values below this are considered "black"
                
                for pixel in pixels:
                    r, g, b = pixel[:3]  # Handle RGBA by taking only RGB
                    # Consider pixel black if all RGB values are very low
                    if r <= dark_threshold and g <= dark_threshold and b <= dark_threshold:
                        black_pixels += 1
                
                black_ratio = black_pixels / len(pixels)
                
                # Additional check: if image is very small and mostly black, likely artifact
                if total_pixels < 5000 and black_ratio > 0.7:
                    return True
                
                is_black = black_ratio >= black_threshold
                
                if is_black:
                    print(f"      Black image analysis: {black_ratio:.1%} black pixels")
                
                return is_black
                
        except Exception as e:
            traceback.print_exc()
            print(f"      Warning: Could not analyze image {image_path}: {str(e)}")
            return False


def main():
    """Main function to handle command line arguments and execute extraction."""
    parser = argparse.ArgumentParser(
        description="Extract images from PDF files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python extract_images_from_pdf.py document.pdf
  python extract_images_from_pdf.py document.pdf -o images_folder
  python extract_images_from_pdf.py document.pdf -o images_folder --min-size 100 100
  python extract_images_from_pdf.py document.pdf --coordinates --metadata --text-context
  python extract_images_from_pdf.py document.pdf --metadata --text-context --text-figures
  python extract_images_from_pdf.py document.pdf --fix-image-issues --skip-black-images
  python extract_images_from_pdf.py document.pdf --text-figures --fix-image-issues --convert png
        """
    )
    
    parser.add_argument("pdf_file", help="Path to the PDF file")
    parser.add_argument("-o", "--output", default="extracted_images", 
                       help="Output directory for extracted images (default: extracted_images)")
    parser.add_argument("--min-size", nargs=2, type=int, default=[50, 50],
                       metavar=("WIDTH", "HEIGHT"),
                       help="Minimum image size to extract (default: 50x50)")
    parser.add_argument("--coordinates", action="store_true",
                       help="Extract images with coordinate information")
    parser.add_argument("--metadata", action="store_true",
                       help="Save extraction metadata to JSON file")
    parser.add_argument("--text-context", action="store_true",
                       help="Extract text context around images (headings, captions)")
    parser.add_argument("--text-figures", action="store_true",
                       help="Detect and extract text-based figures (LaTeX diagrams, process flows)")
    parser.add_argument("--skip-black-images", action="store_true", default=True,
                       help="Skip images that are mostly black (artifacts, borders) (default: True)")
    parser.add_argument("--fix-image-issues", action="store_true",
                       help="Apply processing to fix transparency, color space, and inversion issues")
    parser.add_argument("--convert", choices=["png", "jpg", "jpeg", "bmp"],
                       help="Convert extracted images to specified format")
    
    args = parser.parse_args()
    
    try:
        # Initialize extractor
        extractor = PDFImageExtractor(
            pdf_path=args.pdf_file,
            output_dir=args.output,
            min_width=args.min_size[0],
            min_height=args.min_size[1]
        )
        
        # Extract images
        if args.coordinates:
            extracted_images = extractor.extract_images_with_coordinates(extract_text_context=args.text_context)
            print(f"\nExtracted {len(extracted_images)} images with coordinates.")
        else:
            extracted_images = extractor.extract_images(
                save_metadata=args.metadata, 
                extract_text_context=args.text_context,
                extract_text_figures=args.text_figures,
                skip_black_images=args.skip_black_images,
                fix_image_issues=args.fix_image_issues
            )
            print(f"\nExtracted {len(extracted_images)} total items.")
        
        # Convert images if requested
        if args.convert and extracted_images:
            extractor.convert_images_to_format(args.convert)
        
        print(f"\nAll images saved to: {args.output}")
        
    except Exception as e:
        traceback.print_exc()
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
