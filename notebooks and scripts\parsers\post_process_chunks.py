#!/usr/bin/env python3
"""
Post-process chunks to handle tables at the beginning of chunks.
- Move tables that start a chunk to the previous chunk
- Remove empty chunks
- Rename chunks sequentially
"""
import os
import re
import shutil
import argparse
from typing import List, <PERSON><PERSON>

def detect_table_at_start(content: str) -> Tuple[bool, int]:
    """
    Detect if content starts with a table (Table Summary format with table tag).
    
    Returns:
        Tuple of (is_table_at_start, end_position_of_table)
    """
    # Skip leading whitespace but keep track of it
    original_content = content
    content = content.lstrip()
    leading_whitespace_len = len(original_content) - len(content)
    
    if not content:
        return False, 0
    
    # Check if content starts with "Table Summary:"
    if content.startswith("Table Content:"):
        print(f"    Found Table Content at start")
        
        # Look for the table tag pattern at the end: `<TABLE_TAG_table_X>`
        table_tag_pattern = r'`<TABLE_TAG_table_\d+>`'
        
        # Find the table tag
        match = re.search(table_tag_pattern, content)
        if match:
            # Calculate the end position (including the backtick after the tag)
            table_end_pos = leading_whitespace_len + match.end()
            print(f"    Table tag found at position {match.start()}-{match.end()}")
            print(f"    Table ends at position {table_end_pos}")
            return True, table_end_pos
        else:
            print(f"    Table Content found but no closing table tag")
            return False, 0
    
    return False, 0

def extract_table_from_start(content: str, table_end_pos: int) -> Tuple[str, str]:
    """
    Extract table from the start of content.
    
    Returns:
        Tuple of (table_content, remaining_content)
    """
    table_content = content[:table_end_pos].rstrip()
    remaining_content = content[table_end_pos:].lstrip()
    
    return table_content, remaining_content

def remove_table_content_prefix(content: str) -> str:
    """
    Remove 'Table Content: ' prefix from content.
    
    Args:
        content: The content string to clean
    
    Returns:
        Content string with 'Table Content: ' prefix removed
    """
    # Remove 'Table Content: ' prefix (case-insensitive)
    cleaned_content = re.sub(r'^Table Content:\s*', '', content, flags=re.MULTILINE | re.IGNORECASE)
    return cleaned_content

def process_chunks_sequentially(chunks_dir: str, output_dir: str = None):
    """
    Process chunks sequentially to move tables at chunk start to previous chunk.
    
    Args:
        chunks_dir: Directory containing the chunks
        output_dir: Output directory for processed chunks (default: chunks_dir + "_processed")
    """
    if output_dir is None:
        output_dir = chunks_dir + "_processed"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all chunk files and sort them by chunk number
    chunk_files = [f for f in os.listdir(chunks_dir) if f.startswith('chunk_') and f.endswith('.txt')]
    
    # Sort by chunk number
    def get_chunk_number(filename):
        return int(filename.replace('chunk_', '').replace('.txt', ''))
    
    chunk_files.sort(key=get_chunk_number)
    
    if not chunk_files:
        print("No chunk files found!")
        return
    
    print(f"Found {len(chunk_files)} chunks to process")
    print(f"Chunk order: {chunk_files}")
    
    # Read all chunks into memory
    chunks = []
    for chunk_file in chunk_files:
        chunk_path = os.path.join(chunks_dir, chunk_file)
        with open(chunk_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        chunk_num = get_chunk_number(chunk_file)
        chunks.append({
            'number': chunk_num,
            'filename': chunk_file,
            'content': content
        })
        print(f"  Loaded {chunk_file}: {len(content)} characters")
    
    # Process chunks sequentially starting from chunk 1 (index 1)
    tables_moved = 0
    
    for i in range(1, len(chunks)):  # Start from index 1 (second chunk)
        current_chunk = chunks[i]
        previous_chunk = chunks[i-1]
        
        print(f"\n--- Processing {current_chunk['filename']} ---")
        
        # Check if current chunk starts with a table
        starts_with_table, table_end_pos = detect_table_at_start(current_chunk['content'])
        
        if starts_with_table:
            print(f"  ✓ {current_chunk['filename']} starts with a table")
            
            # Extract table and remaining content
            table_content, remaining_content = extract_table_from_start(
                current_chunk['content'], table_end_pos
            )
            
            print(f"  Table content length: {len(table_content)} characters")
            print(f"  Remaining content length: {len(remaining_content)} characters")
            
            # Move table to previous chunk
            previous_chunk['content'] = previous_chunk['content'].rstrip() + '\n\n' + table_content
            
            # Update current chunk with remaining content
            current_chunk['content'] = remaining_content
            
            print(f"  ✓ Moved table from {current_chunk['filename']} to {previous_chunk['filename']}")
            tables_moved += 1
        else:
            print(f"  - {current_chunk['filename']} does not start with a table")
    
    print(f"\n=== Processing Summary ===")
    print(f"Tables moved: {tables_moved}")
    
    # Remove empty chunks
    non_empty_chunks = []
    removed_chunks = []
    
    for chunk in chunks:
        if chunk['content'].strip():  # Keep non-empty chunks
            non_empty_chunks.append(chunk)
        else:
            removed_chunks.append(chunk['filename'])
            print(f"  Will remove empty chunk: {chunk['filename']}")
    
    print(f"Empty chunks to remove: {len(removed_chunks)}")
    
    # Clean up 'Table Content: ' prefix from all chunks
    print(f"\n=== Cleaning 'Table Content: ' prefix ===")
    table_content_removals = 0
    
    for chunk in non_empty_chunks:
        original_content = chunk['content']
        cleaned_content = remove_table_content_prefix(original_content)
        
        if len(cleaned_content) != len(original_content):
            table_content_removals += 1
            chunk['content'] = cleaned_content
            print(f"  ✓ Removed 'Table Content: ' prefix from {chunk['filename']}")
    
    print(f"'Table Content: ' prefix removed from {table_content_removals} chunks")
    
    # Save processed chunks with sequential numbering
    print(f"\n=== Saving Processed Chunks ===")
    
    for new_index, chunk in enumerate(non_empty_chunks):
        new_filename = f"chunk_{new_index}.txt"
        output_path = os.path.join(output_dir, new_filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(chunk['content'])
        
        print(f"  Saved {new_filename} (was {chunk['filename']}, {len(chunk['content'])} chars)")
    
    # Create a mapping file for reference
    # mapping_file = os.path.join(output_dir, "chunk_mapping.txt")
    # with open(mapping_file, 'w', encoding='utf-8') as f:
    #     f.write("Chunk Processing Summary\n")
    #     f.write("=" * 40 + "\n\n")
    #     f.write("Tables moved between chunks:\n")
    #     f.write(f"Total tables moved: {tables_moved}\n\n")
    #     f.write("Chunk mapping (new -> original):\n")
    #     for new_index, chunk in enumerate(non_empty_chunks):
    #         f.write(f"chunk_{new_index}.txt -> {chunk['filename']}\n")
        
    #     if removed_chunks:
    #         f.write(f"\nRemoved empty chunks:\n")
    #         for removed in removed_chunks:
    #             f.write(f"  {removed}\n")
    
    print(f"\n=== Final Summary ===")
    print(f"Original chunks: {len(chunks)}")
    print(f"Final chunks: {len(non_empty_chunks)}")
    print(f"Tables moved: {tables_moved}")
    print(f"Empty chunks removed: {len(removed_chunks)}")
    print(f"'Table Content: ' prefixes removed: {table_content_removals}")
    print(f"Output directory: {output_dir}")
    # print(f"Mapping saved to: {mapping_file}")

def main():
    parser = argparse.ArgumentParser(
        description="Post-process chunks to handle tables at chunk boundaries",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python post_process_chunks.py "chunks_with_tables"
    python post_process_chunks.py "chunks_with_tables" --output "final_chunks"
    python post_process_chunks.py "/path/to/chunks" --output "/path/to/output"
        """
    )
    
    parser.add_argument(
        'chunks_dir',
        help='Directory containing the chunks to process'
    )
    
    parser.add_argument(
        '--output',
        help='Output directory for processed chunks (default: input_dir + "_processed")'
    )
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.chunks_dir):
        print(f"Error: Chunks directory '{args.chunks_dir}' not found!")
        return 1
    
    if not os.path.isdir(args.chunks_dir):
        print(f"Error: '{args.chunks_dir}' is not a directory!")
        return 1
    
    # Run processing
    print("=" * 60)
    print("POST-PROCESSING CHUNKS - SEQUENTIAL TABLE MOVEMENT")
    print("=" * 60)
    print(f"Input directory: {args.chunks_dir}")
    print(f"Output directory: {args.output or 'auto-generated'}")
    print()
    
    try:
        process_chunks_sequentially(args.chunks_dir, args.output)
        print("\n✓ Post-processing completed successfully!")
        return 0
    except Exception as e:
        print(f"\n✗ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main()) 