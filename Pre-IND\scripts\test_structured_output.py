#!/usr/bin/env python3
"""
Sample script demonstrating Pydantic AI agents with structured output.
This script shows how to use Pydantic models as schemas for structured LLM responses,
similar to the patterns used in gap_analysis.py.
"""

import asyncio
import os
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.providers.openai import OpenAIProvider
from dotenv import load_dotenv

# Load environment variables
# load_dotenv()

# Configuration
MAX_CONCURRENT_REQUESTS = 5
api_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

# Model configuration
# MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL", "https://api.openai.com/v1")
MODEL_BASE_URL = "http://*************:9000/v1"
MODEL_API_KEY = ""
MODEL_NAME = "agentic-large"

# =============================================================================
# PYDANTIC SCHEMAS FOR STRUCTURED OUTPUT
# =============================================================================

class DocumentAnalysis(BaseModel):
    """Schema for analyzing document content"""
    main_topic: str = Field(
        description="Primary topic or subject matter of the document"
    )
    key_findings: List[str] = Field(
        description="List of important findings or insights from the document",
        min_items=1,
        max_items=10
    )
    confidence_score: float = Field(
        description="Confidence score from 0-10 for the analysis quality",
        ge=0.0,
        le=10.0
    )
    
class RiskAssessment(BaseModel):
    """Schema for risk assessment with categorized risks"""
    high_risks: List[str] = Field(
        description="List of high-priority risks identified",
        default=[]
    )
    medium_risks: List[str] = Field(
        description="List of medium-priority risks identified", 
        default=[]
    )
    low_risks: List[str] = Field(
        description="List of low-priority risks identified",
        default=[]
    )
    overall_risk_level: str = Field(
        description="Overall risk level: 'low', 'medium', 'high', or 'critical'"
    )
    mitigation_strategies: List[str] = Field(
        description="Recommended strategies to mitigate identified risks",
        default=[]
    )

class ComplianceEvaluation(BaseModel):
    """Schema for regulatory compliance evaluation"""
    compliance_score: float = Field(
        description="Compliance score from 0-100 indicating regulatory adherence",
        ge=0.0,
        le=100.0
    )
    compliant_areas: List[str] = Field(
        description="Areas where compliance requirements are met",
        default=[]
    )
    non_compliant_areas: List[str] = Field(
        description="Areas where compliance requirements are not met",
        default=[]
    )
    required_actions: List[str] = Field(
        description="Specific actions required to achieve full compliance",
        default=[]
    )
    regulatory_references: List[str] = Field(
        description="Relevant regulatory guidelines or standards referenced",
        default=[]
    )

class QuestionAnswerPair(BaseModel):
    """Schema for individual question-answer pairs"""
    question: str = Field(description="The specific question asked")
    answer: str = Field(description="Detailed answer to the question")
    confidence: float = Field(
        description="Confidence in the answer from 0-10",
        ge=0.0,
        le=10.0
    )
    sources: List[str] = Field(
        description="Sources or evidence supporting the answer",
        default=[]
    )

class MultiQuestionResponse(BaseModel):
    """Schema for handling multiple questions at once"""
    responses: List[QuestionAnswerPair] = Field(
        description="List of question-answer pairs",
        min_items=1
    )
    overall_summary: str = Field(
        description="Overall summary connecting all the answers"
    )

# =============================================================================
# MODEL SETUP FUNCTIONS
# =============================================================================

def get_model_instance(base_url: str, api_key: str, model_name: str):
    """
    Create and return appropriate model instance based on base URL.
    Similar to get_generator_and_critique_model in gap_analysis.py
    """
    generator_model = OpenAIModel(
        model_name=MODEL_NAME,
        provider=OpenAIProvider(
            base_url=MODEL_BASE_URL,
            api_key=MODEL_API_KEY
        )
    )

    return generator_model

async def rate_limited_agent_call(agent, prompt, semaphore):
    """
    Rate-limited wrapper for agent calls.
    Similar to rate_limited_agent_call in gap_analysis.py
    """
    async with semaphore:
        return await agent.run(prompt)

# =============================================================================
# AGENT DEFINITIONS WITH STRUCTURED OUTPUT
# =============================================================================

def create_document_analyzer_agent(model):
    """Create an agent for document analysis with structured output"""
    return Agent(
        model=model,
        result_type=DocumentAnalysis,
        system_prompt="""
        You are an expert document analyst. Your task is to analyze the provided document content 
        and extract key information in a structured format.
        
        Focus on:
        - Identifying the main topic or subject
        - Extracting key findings and insights
        - Providing a confidence assessment
        
        Be thorough but concise in your analysis.
        """
    )

def create_risk_assessment_agent(model):
    """Create an agent for risk assessment with structured output"""
    return Agent(
        model=model,
        result_type=RiskAssessment,
        system_prompt="""
        You are a risk assessment specialist. Analyze the provided content to identify 
        and categorize potential risks.
        
        Categorize risks by priority:
        - High: Immediate threats requiring urgent attention
        - Medium: Significant risks that need planning
        - Low: Manageable risks with standard procedures
        
        Also provide practical mitigation strategies for identified risks.
        """
    )

def create_compliance_evaluator_agent(model):
    """Create an agent for compliance evaluation with structured output"""
    return Agent(
        model=model,
        result_type=ComplianceEvaluation,
        system_prompt="""
        You are a regulatory compliance expert. Evaluate the provided content against 
        relevant regulatory standards and guidelines.
        
        Provide:
        - Quantitative compliance score
        - Clear identification of compliant and non-compliant areas
        - Specific actionable recommendations
        - Relevant regulatory references
        
        Be precise and reference specific regulations where applicable.
        """
    )

def create_qa_agent(model):
    """Create an agent for answering multiple questions with structured output"""
    return Agent(
        model=model,
        result_type=MultiQuestionResponse,
        system_prompt="""
        You are an expert analyst capable of answering multiple questions about 
        provided content with high accuracy and detail.
        
        For each question:
        - Provide a comprehensive answer
        - Include confidence level in your response
        - Cite relevant sources or evidence
        
        Connect your answers in an overall summary that shows relationships 
        between different aspects of the content.
        """
    )

# =============================================================================
# SAMPLE FUNCTIONS DEMONSTRATING STRUCTURED OUTPUT
# =============================================================================

async def analyze_document_content(content: str) -> DocumentAnalysis:
    """
    Analyze document content and return structured analysis.
    """
    model = get_model_instance(MODEL_BASE_URL, MODEL_API_KEY, MODEL_NAME)
    agent = create_document_analyzer_agent(model)
    
    prompt = f"""
    Please analyze the following document content:
    
    {content}
    
    Provide a structured analysis including the main topic, key findings, and your confidence in the analysis.
    """
    
    result = await rate_limited_agent_call(agent, prompt, api_semaphore)
    return result.data

async def assess_risks(content: str, context: str = "general") -> RiskAssessment:
    """
    Perform risk assessment on content and return structured results.
    """
    model = get_model_instance(MODEL_BASE_URL, MODEL_API_KEY, MODEL_NAME)
    agent = create_risk_assessment_agent(model)
    
    prompt = f"""
    Context: {context}
    
    Please assess the risks in the following content:
    
    {content}
    
    Categorize risks by priority level and provide mitigation strategies.
    """
    
    result = await rate_limited_agent_call(agent, prompt, api_semaphore)
    return result.data

async def evaluate_compliance(content: str, regulatory_domain: str = "FDA") -> ComplianceEvaluation:
    """
    Evaluate regulatory compliance and return structured assessment.
    """
    model = get_model_instance(MODEL_BASE_URL, MODEL_API_KEY, MODEL_NAME)
    agent = create_compliance_evaluator_agent(model)
    
    prompt = f"""
    Regulatory Domain: {regulatory_domain}
    
    Please evaluate the compliance of the following content against relevant {regulatory_domain} regulations:
    
    {content}
    
    Provide a comprehensive compliance assessment with specific recommendations.
    """
    
    result = await rate_limited_agent_call(agent, prompt, api_semaphore)
    return result.data

async def answer_multiple_questions(content: str, questions: List[str]) -> MultiQuestionResponse:
    """
    Answer multiple questions about content with structured responses.
    """
    model = get_model_instance(MODEL_BASE_URL, MODEL_API_KEY, MODEL_NAME)
    agent = create_qa_agent(model)
    
    questions_text = "\n".join([f"{i+1}. {q}" for i, q in enumerate(questions)])
    
    prompt = f"""
    Content to analyze:
    {content}
    
    Questions to answer:
    {questions_text}
    
    Please provide detailed answers to each question with confidence scores and supporting evidence.
    """
    
    result = await rate_limited_agent_call(agent, prompt, api_semaphore)
    return result.data

# =============================================================================
# DEMONSTRATION FUNCTIONS
# =============================================================================

async def demo_document_analysis():
    """Demonstrate document analysis with structured output"""
    print("\n" + "="*60)
    print("DEMO: Document Analysis with Structured Output")
    print("="*60)
    
    sample_content = """
    This clinical trial protocol describes a Phase II study investigating the efficacy 
    and safety of a novel cancer immunotherapy agent. The study will enroll 200 patients 
    with advanced melanoma who have failed at least one prior therapy. Primary endpoints 
    include overall response rate and progression-free survival. Secondary endpoints 
    include overall survival, duration of response, and safety profile. The study design 
    includes a lead-in safety run to establish the recommended Phase II dose.
    """
    
    try:
        analysis = await analyze_document_content(sample_content)
        
        print(f"Main Topic: {analysis.main_topic}")
        print(f"Confidence Score: {analysis.confidence_score}/10")
        print("\nKey Findings:")
        for i, finding in enumerate(analysis.key_findings, 1):
            print(f"  {i}. {finding}")
            
    except Exception as e:
        print(f"Error in document analysis: {e}")

async def demo_risk_assessment():
    """Demonstrate risk assessment with structured output"""
    print("\n" + "="*60)
    print("DEMO: Risk Assessment with Structured Output")
    print("="*60)
    
    sample_content = """
    The manufacturing process involves handling cytotoxic compounds at high concentrations. 
    Personnel must work in biosafety level 2 facilities with appropriate PPE. The process 
    includes multiple temperature-sensitive steps and requires precise timing. Raw materials 
    are sourced from a single supplier, and quality control testing is performed at three 
    checkpoints during production.
    """
    
    try:
        assessment = await assess_risks(sample_content, "pharmaceutical manufacturing")
        
        print(f"Overall Risk Level: {assessment.overall_risk_level}")
        
        if assessment.high_risks:
            print("\nHigh Priority Risks:")
            for risk in assessment.high_risks:
                print(f"  • {risk}")
        
        if assessment.medium_risks:
            print("\nMedium Priority Risks:")
            for risk in assessment.medium_risks:
                print(f"  • {risk}")
        
        if assessment.mitigation_strategies:
            print("\nMitigation Strategies:")
            for strategy in assessment.mitigation_strategies:
                print(f"  → {strategy}")
                
    except Exception as e:
        print(f"Error in risk assessment: {e}")

async def demo_compliance_evaluation():
    """Demonstrate compliance evaluation with structured output"""
    print("\n" + "="*60)
    print("DEMO: Compliance Evaluation with Structured Output")
    print("="*60)
    
    sample_content = """
    Our quality management system includes documented procedures for all critical processes. 
    We maintain full traceability of raw materials and conduct regular internal audits. 
    However, our change control procedures may not fully meet ICH Q10 requirements, and 
    we lack a formal risk management framework as outlined in ICH Q9.
    """
    
    try:
        evaluation = await evaluate_compliance(sample_content, "ICH")
        
        print(f"Compliance Score: {evaluation.compliance_score}/100")
        
        if evaluation.compliant_areas:
            print("\nCompliant Areas:")
            for area in evaluation.compliant_areas:
                print(f"  ✓ {area}")
        
        if evaluation.non_compliant_areas:
            print("\nNon-Compliant Areas:")
            for area in evaluation.non_compliant_areas:
                print(f"  ✗ {area}")
        
        if evaluation.required_actions:
            print("\nRequired Actions:")
            for action in evaluation.required_actions:
                print(f"  → {action}")
                
    except Exception as e:
        print(f"Error in compliance evaluation: {e}")

async def demo_multi_question_answering():
    """Demonstrate multi-question answering with structured output"""
    print("\n" + "="*60)
    print("DEMO: Multi-Question Answering with Structured Output")
    print("="*60)
    
    sample_content = """
    MedNova-X1 is an investigational drug for treating Type 2 diabetes. Preclinical studies 
    showed a 40% reduction in glucose levels with minimal side effects. The drug targets 
    the GLP-1 receptor pathway. Phase I trials in 30 healthy volunteers demonstrated good 
    safety profile with no serious adverse events. The recommended starting dose is 5mg 
    once daily, with possible titration to 10mg based on glycemic response.
    """
    
    questions = [
        "What is the mechanism of action of MedNova-X1?",
        "What were the key results from preclinical studies?",
        "What is the recommended dosing regimen?",
        "What safety information is available?"
    ]
    
    try:
        responses = await answer_multiple_questions(sample_content, questions)
        
        print("Individual Question Responses:")
        print("-" * 40)
        
        for i, qa_pair in enumerate(responses.responses, 1):
            print(f"\nQ{i}: {qa_pair.question}")
            print(f"A{i}: {qa_pair.answer}")
            print(f"Confidence: {qa_pair.confidence}/10")
            if qa_pair.sources:
                print(f"Sources: {', '.join(qa_pair.sources)}")
        
        print("\nOverall Summary:")
        print("-" * 40)
        print(responses.overall_summary)
        
    except Exception as e:
        print(f"Error in multi-question answering: {e}")

# =============================================================================
# MAIN EXECUTION
# =============================================================================

async def main():
    """Main function to run all demonstrations"""
    print("Pydantic AI Agents with Structured Output - Sample Demonstrations")
    print("="*80)
    
    if not MODEL_API_KEY:
        print("ERROR: No API key found. Please set GENERATOR_MODEL_API_KEY in your .env file")
        return
    
    print(f"Using Model: {MODEL_NAME}")
    print(f"Base URL: {MODEL_BASE_URL}")
    
    # Run all demonstrations
    try:
        await demo_document_analysis()
        await demo_risk_assessment()
        await demo_compliance_evaluation()
        await demo_multi_question_answering()
        
        print("\n" + "="*80)
        print("All demonstrations completed successfully!")
        print("="*80)
        
    except Exception as e:
        print(f"Error during execution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the demonstration
    asyncio.run(main())
