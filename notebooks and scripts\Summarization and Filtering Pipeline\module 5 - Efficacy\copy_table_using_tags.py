from docx import Document
from docx.document import Document as DocumentObject
from docx.shared import Inches
from copy import deepcopy
import os
import shutil
import zipfile
from docx.oxml.ns import qn
from docx.table import Table
from docx.section import Paragraph
from typing import List
import json
from docx.oxml import OxmlElement

def copy_page_setup(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy page setup (size, orientation, margins) from source to target document"""
    source_section = source_doc.sections[0]
    target_section = target_doc.sections[0]
    
    # Copy page size and orientation
    target_section.page_width = source_section.page_width
    target_section.page_height = source_section.page_height
    target_section.orientation = source_section.orientation
    
    # Copy margins
    target_section.top_margin = source_section.top_margin
    target_section.bottom_margin = source_section.bottom_margin
    target_section.left_margin = source_section.left_margin
    target_section.right_margin = source_section.right_margin
    
    print(f"📏 Copied page setup: {source_section.page_width} x {source_section.page_height}")

def copy_table_styles_completely(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy complete table style definitions including all visual formatting"""
    try:
        # Extract the styles.xml from both documents  
        source_styles_part = source_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        target_styles_part = target_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        
        if source_styles_part and target_styles_part:
            # Get the root elements
            source_styles_root = source_styles_part.element
            target_styles_root = target_styles_part.element
            
            # Find all table styles in source
            source_table_styles = source_styles_root.findall('.//w:style[@w:type="table"]', source_styles_root.nsmap)
            
            for style in source_table_styles:
                style_id = style.get(qn('w:styleId'))
                
                # Check if this style exists in target
                existing_style = target_styles_root.find(f'.//w:style[@w:styleId="{style_id}"]', target_styles_root.nsmap)
                
                if existing_style is not None:
                    # Remove existing style
                    existing_style.getparent().remove(existing_style)
                
                # Add the complete style from source
                target_styles_root.append(deepcopy(style))
            
            print(f"🎨 Copied {len(source_table_styles)} table styles")
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not copy table styles: {e}")
        return False
    
    return False

def apply_table_style_explicitly(table: Table, style_name: str):
    """Apply table style and ensure all conditional formatting is applied"""
    try:
        # Set the table style
        table.style = style_name
        
        # Force conditional formatting by updating tblLook
        tbl_element = table._tbl
        tblPr = tbl_element.find(qn('w:tblPr'))
        
        if tblPr is not None:
            # Find or create tblLook element
            tblLook = tblPr.find(qn('w:tblLook'))
            if tblLook is None:
                tblLook = OxmlElement('w:tblLook')
                tblPr.append(tblLook)
            
            # Set attributes to enable all conditional formatting
            tblLook.set(qn('w:val'), '04A0')
            tblLook.set(qn('w:firstRow'), '1')
            tblLook.set(qn('w:lastRow'), '0')
            tblLook.set(qn('w:firstColumn'), '1')
            tblLook.set(qn('w:lastColumn'), '0')
            tblLook.set(qn('w:noHBand'), '0')
            tblLook.set(qn('w:noVBand'), '1')
            
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply table style: {e}")
        return False
    
    return False

def copy_document_styles(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy styles from source document to target document"""
    try:
        # First copy table styles completely (with full definitions)
        table_styles_copied = copy_table_styles_completely(source_doc, target_doc)
        
        if not table_styles_copied:
            # Fallback to basic style copying
            for style in source_doc.styles:
                if style.name not in [s.name for s in target_doc.styles]:
                    # Add the style to target document
                    target_doc.styles.add_style(style.name, style.type)
        
        print("🎨 Copied document styles")
    except Exception as e:
        print(f"⚠️ Warning: Could not copy all styles: {e}")

def copy_cell_formatting(source_cell, target_cell):
    """Copy complete cell formatting including background, borders, and text formatting"""
    try:
        # Copy cell background/shading
        source_cell_element = source_cell._tc
        target_cell_element = target_cell._tc
        
        # Find and copy cell properties (tcPr)
        source_tcPr = source_cell_element.find(qn('w:tcPr'))
        if source_tcPr is not None:
            # Remove existing tcPr if present
            existing_tcPr = target_cell_element.find(qn('w:tcPr'))
            if existing_tcPr is not None:
                target_cell_element.remove(existing_tcPr)
            
            # Copy the complete cell properties
            target_cell_element.insert(0, deepcopy(source_tcPr))
        
        # Copy paragraph and run formatting more thoroughly
        for i, source_para in enumerate(source_cell.paragraphs):
            if i < len(target_cell.paragraphs):
                target_para = target_cell.paragraphs[i]
                
                # Copy paragraph-level formatting
                source_para_element = source_para._element
                target_para_element = target_para._element
                
                # Copy paragraph properties (pPr)
                source_pPr = source_para_element.find(qn('w:pPr'))
                if source_pPr is not None:
                    existing_pPr = target_para_element.find(qn('w:pPr'))
                    if existing_pPr is not None:
                        target_para_element.remove(existing_pPr)
                    target_para_element.insert(0, deepcopy(source_pPr))
                
                # Copy run formatting more comprehensively
                for j, source_run in enumerate(source_para.runs):
                    if j < len(target_para.runs):
                        target_run = target_para.runs[j]
                        
                        # Copy run properties (rPr) at XML level
                        source_run_element = source_run._element
                        target_run_element = target_run._element
                        
                        source_rPr = source_run_element.find(qn('w:rPr'))
                        if source_rPr is not None:
                            existing_rPr = target_run_element.find(qn('w:rPr'))
                            if existing_rPr is not None:
                                target_run_element.remove(existing_rPr)
                            target_run_element.insert(0, deepcopy(source_rPr))
                        
    except Exception as e:
        print(f"⚠️ Warning: Could not copy cell formatting: {e}")

def copy_table_with_complete_formatting(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with complete formatting preservation including cell shading and text formatting"""
    # Create a new paragraph for the heading before the table tag paragraph
    heading_paragraph = insert_after_paragraph.insert_paragraph_before()
    heading_run = heading_paragraph.add_run(table_heading)
    heading_run.bold = True
    
    # Get the table element and its style
    tbl_element = source_table._tbl
    source_table_style = None
    
    # Extract table style name
    tblPr = tbl_element.find(qn('w:tblPr'))
    if tblPr is not None:
        tblStyle = tblPr.find(qn('w:tblStyle'))
        if tblStyle is not None:
            source_table_style = tblStyle.get(qn('w:val'))
    
    # Create a deep copy of the table element with all formatting
    tbl_copy = deepcopy(tbl_element)
    
    # Insert the copied table after the table tag paragraph
    insert_after_paragraph._element.addnext(tbl_copy)
    
    # Enhanced formatting preservation
    try:
        # Find the newly inserted table in the document
        target_doc = insert_after_paragraph._parent
        target_table = None
        
        # Get the table that was just inserted
        for table in target_doc.tables:
            if table._tbl == tbl_copy:
                target_table = table
                break
        
        if target_table and source_table:
            # Apply the table style explicitly if we found one
            if source_table_style:
                apply_table_style_explicitly(target_table, source_table_style)
            
            # Copy table-level properties more thoroughly
            source_tblPr = source_table._tbl.find(qn('w:tblPr'))
            if source_tblPr is not None:
                target_tblPr = target_table._tbl.find(qn('w:tblPr'))
                if target_tblPr is not None:
                    target_table._tbl.remove(target_tblPr)
                target_table._tbl.insert(0, deepcopy(source_tblPr))
            
            # Copy cell-by-cell formatting
            for i, source_row in enumerate(source_table.rows):
                if i < len(target_table.rows):
                    target_row = target_table.rows[i]
                    for j, source_cell in enumerate(source_row.cells):
                        if j < len(target_row.cells):
                            target_cell = target_row.cells[j]
                            copy_cell_formatting(source_cell, target_cell)
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply enhanced formatting: {e}")
    
    # Insert an empty paragraph after the table (acts as a line break)
    p = OxmlElement('w:p')
    tbl_copy.addnext(p)
    
    # Clear the table tag paragraph content instead of removing it
    insert_after_paragraph.clear()

def remove_all_tables(doc: DocumentObject):
    body = doc.element.body
    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]
    for tbl in tables_to_remove:
        body.remove(tbl)
        
def extract_images(docx_path, extract_folder):
    with zipfile.ZipFile(docx_path, 'r') as zip_ref:
        zip_ref.extractall(extract_folder)

def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:
    return [para for para in doc.paragraphs if search_text in para.text]

def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with heading and line break after - DEPRECATED, use copy_table_with_complete_formatting"""
    copy_table_with_complete_formatting(source_table, insert_after_paragraph, table_heading)

def load_table_headings(json_file_path: str):
    """Load table headings from JSON file"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        table_index = json.load(f)
    
    table_headings = {}
    for table_hash, table_info in table_index.items():
        table_id = table_info['id']
        table_heading = table_info['heading']
        table_headings[table_id] = table_heading
    
    return table_headings

def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):
    run = paragraph.insert_paragraph_before().add_run()
    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed

def main():
    source_docx = '/Users/<USER>/projects/scalegen/MedNova/Input Docs/module 5/adverse_event_source_doc.docx'
    target_docx = '/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/formatted_answer.docx'
    extract_folder = 'source_extracted'
    
    # Path for table headings
    json_file_path = '/Users/<USER>/projects/scalegen/MedNova/Input Docs/module 5/adverse_event_source_doc_tables_with_summaries_and_tags/table_index.json'

    # Step 1: Extract images from source.docx
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
    extract_images(source_docx, extract_folder)

    media_folder = os.path.join(extract_folder, 'word', 'media')
    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []

    # Load table headings from JSON file
    table_headings = load_table_headings(json_file_path)
    print(f"📊 Loaded headings for {len(table_headings)} tables")

    source_doc = Document(source_docx)
    target_doc = Document(target_docx)

    # Step 1.2: Copy page setup and styles from source to target
    copy_page_setup(source_doc, target_doc)
    copy_document_styles(source_doc, target_doc)

    # Step 1.5: Remove all existing tables from target doc
    remove_all_tables(target_doc)
    print("🗑️ Removed all existing tables from target document")

    # Step 2: Process tables with complete formatting preservation
    # First, collect all table tag matches to avoid displacement issues
    table_matches = []
    for idx, source_table in enumerate(source_doc.tables, start=1):
        table_id = f"table_{idx}"
        reference_text = f"<TABLE_TAG_{table_id}>"
        
        # Get the heading for this table from JSON, fallback to generic heading
        table_heading = table_headings.get(table_id, f"Table {idx}")
        
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        if matched_paragraphs:
            for para in matched_paragraphs:
                table_matches.append((para, source_table, table_heading, reference_text, idx))
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")
    
    # Process matches in reverse order to avoid displacement issues
    for para, source_table, table_heading, reference_text, idx in reversed(table_matches):
        copy_table_with_complete_formatting(source_table, para, table_heading)
        print(f"✅ Inserted Table {idx} with complete formatting")

    # Step 3: Process figures/images
    for idx, image_file in enumerate(media_files, start=1):
        reference_text = f"Figure {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        image_path = os.path.join(media_folder, image_file)
        if matched_paragraphs:
            for para in matched_paragraphs:
                insert_image_before_paragraph(target_doc, image_path, para)
            print(f"✅ Inserted Figure {idx}")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 4: Save output
    target_doc.save('/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 5 - Efficacy/table_and_image_formatted_answer.docx')
    print("✅ All done. Saved as 'table_and_image_formatted_answer.docx'")
    print("🎨 Complete visual formatting preserved from source document")
    
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
        print(f"🗑️ Cleaned up temporary folder: {extract_folder}")

if __name__ == "__main__":
    main()