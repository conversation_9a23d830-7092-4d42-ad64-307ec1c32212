import re
import json
import os
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, Tuple
import numpy as np
import asyncio
from transformers import AutoTokenizer
import traceback  # Added import for stack trace information
import time  # Added import for timestamp logging
import inspect    # For frame inspection

async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):
    """
    Wrapper function that retries an async LLM API call when the response is empty.
    
    Args:
        async_func: The async function to call (usually an LLM API call)
        *args: Positional arguments to pass to async_func
        max_retries: Maximum number of retry attempts (default: 3)
        **kwargs: Keyword arguments to pass to async_func
        
    Returns:
        The result of the async_func call, ensuring it's not empty
        
    Raises:
        Exception: If max_retries is reached and the response is still empty
    """
    # Create logs directory if it doesn't exist
    log_dir = "error_logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Extract the function name for logging purposes
    func_name = async_func.__name__ if hasattr(async_func, "__name__") else "unknown_function"
    
    # Try to get the caller's name from the stack
    try:
        caller_frame = inspect.currentframe().f_back
        caller_name = caller_frame.f_code.co_name if caller_frame else "unknown_caller"
    except Exception:
        caller_name = "unknown_caller"
    
    for attempt in range(max_retries):
        try:
            result = await async_func(*args, **kwargs)
            
            # Check if result is None, empty string, just whitespace, or contains tool calls
            is_empty = result is None or (isinstance(result, str) and result.strip() == "")
            contains_tool_call = False
            
            if isinstance(result, str):
                # Check for tool_call patterns
                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)
                contains_tool_call_text = 'tool_call' in result.lower()
                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text
            
            if is_empty or contains_tool_call:
                if is_empty:
                    reason = "empty response"
                    error_type = 'empty_response'
                elif contains_tool_call:
                    reason = "response contains tool calls"
                    error_type = 'tool_call_response'
                
                print(f"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...")
                
                # Get debug information to log
                debug_info = {
                    'error_type': error_type,
                    'function': func_name,
                    'caller': caller_name,
                    'attempt': attempt + 1,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]
                }
                
                # Extract prompt information based on different API patterns
                # For the direct messages pattern in kwargs
                if 'messages' in kwargs:
                    debug_info['messages'] = kwargs['messages']
                    
                # For the pattern where the func is a closure with local make_api_call
                # Try to get source code of the async_func to check for patterns
                try:
                    source = inspect.getsource(async_func)
                    if "chat.completions.create" in source:
                        debug_info['api_pattern'] = "chat_completions_closure"
                except Exception:
                    pass
                
                # Try to extract system_prompt and user_prompt from the caller's frame if available
                try:
                    if caller_frame:
                        caller_locals = caller_frame.f_locals
                        # Capture common patterns in this codebase
                        if 'system_prompt' in caller_locals:
                            debug_info['system_prompt'] = caller_locals['system_prompt']
                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                        # If this is using the OpenAI client pattern, get the model too
                        if 'model' in caller_locals:
                            debug_info['model'] = caller_locals['model']
                        # For the antropic calls
                        if 'CRITIQUE_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                        elif 'GENERATOR_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                        elif 'MODEL' in caller_locals:
                            debug_info['model'] = caller_locals['MODEL']
                except Exception as e:
                    debug_info['frame_inspection_error'] = str(e)
                
                # Save the debug information
                timestamp = int(time.time())
                log_filename = f"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json"
                
                try:
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        json.dump(debug_info, f, indent=2, ensure_ascii=False)
                    print(f"Logged empty response details to {log_filename}")
                except Exception as log_error:
                    print(f"Failed to log empty response details: {str(log_error)}")
                
                # Continue to the next retry attempt
                continue
                
            # If we get here, we have a non-empty response
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            print(f"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}")
            
            # Get debug information to log
            debug_info = {
                'error_type': error_type,
                'error_message': error_msg,
                'function': func_name,
                'caller': caller_name,
                'attempt': attempt + 1,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'stack_trace': traceback.format_exc()
            }
            
            # Extract prompt information based on different API patterns
            # For the direct messages pattern in kwargs
            if 'messages' in kwargs:
                debug_info['messages'] = kwargs['messages']
                
            # For the pattern where the func is a closure with local make_api_call
            # Try to get source code of the async_func to check for patterns
            try:
                source = inspect.getsource(async_func)
                if "chat.completions.create" in source:
                    debug_info['api_pattern'] = "chat_completions_closure"
            except Exception:
                pass
            
            # Try to extract system_prompt and user_prompt from the caller's frame if available
            try:
                if caller_frame:
                    caller_locals = caller_frame.f_locals
                    # Capture common patterns in this codebase
                    if 'system_prompt' in caller_locals:
                        debug_info['system_prompt'] = caller_locals['system_prompt']
                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                    # If this is using the OpenAI client pattern, get the model too
                    if 'model' in caller_locals:
                        debug_info['model'] = caller_locals['model']
                    # For the antropic calls
                    if 'CRITIQUE_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                    elif 'GENERATOR_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                    elif 'MODEL' in caller_locals:
                        debug_info['model'] = caller_locals['MODEL']
            except Exception as frame_error:
                debug_info['frame_inspection_error'] = str(frame_error)
            
            # Save the debug information
            timestamp = int(time.time())
            log_filename = f"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json"
            
            try:
                with open(log_filename, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, indent=2, ensure_ascii=False)
                print(f"Logged error details to {log_filename}")
            except Exception as log_error:
                print(f"Failed to log error details: {str(log_error)}")
            
            if attempt == max_retries - 1:
                # If we've exhausted all retries and still have an error
                print(f"Failed to get non-empty response after {max_retries} attempts")
                return None
            
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** attempt))
    
    # If we've exhausted all retries and still have an empty response
    print(f"Failed to get non-empty response after {max_retries} attempts")
    return None