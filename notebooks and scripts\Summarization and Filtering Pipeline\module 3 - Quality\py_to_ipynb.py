#!/usr/bin/env python3
import json
import sys
import re
import os
import argparse

def py_to_ipynb(py_file_path, ipynb_file_path=None):
    """
    Convert a Python file with "# %%" cell markers to a Jupyter notebook.
    
    Args:
        py_file_path (str): Path to the Python file
        ipynb_file_path (str, optional): Path for the output notebook file. If None,
                                         the same name as py_file will be used with .ipynb extension.
    """
    if ipynb_file_path is None:
        ipynb_file_path = os.path.splitext(py_file_path)[0] + '.ipynb'
    
    # Read the Python file
    with open(py_file_path, 'r', encoding='utf-8') as f:
        py_content = f.read()
    
    # Split the content by "# %%" markers
    cells = []
    current_cell = []
    
    for line in py_content.splitlines():
        if line.strip().startswith('# %%'):
            # If we have content in the current cell, add it to cells list
            if current_cell:
                cells.append('\n'.join(current_cell))
                current_cell = []
            
            # Start a new cell (ignore the marker line)
            continue
        
        current_cell.append(line)
    
    # Add the last cell if it's not empty
    if current_cell:
        cells.append('\n'.join(current_cell))
    
    # Create notebook JSON structure
    notebook = {
        "cells": [
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": cell.splitlines(True),  # Keep line breaks
                "outputs": []
            }
            for cell in cells if cell.strip()  # Skip empty cells
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.8.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Write the notebook to a file
    with open(ipynb_file_path, 'w', encoding='utf-8') as f:
        json.dump(notebook, f, indent=2)
    
    print(f"Converted {py_file_path} to {ipynb_file_path}")
    return ipynb_file_path

if __name__ == "__main__":
    py_to_ipynb("/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/claude_critique.py")
