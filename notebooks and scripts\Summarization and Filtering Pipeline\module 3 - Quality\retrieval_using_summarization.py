# %%
import re
import json
import os
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, <PERSON>ple
import numpy as np
import asyncio
from transformers import AutoTokenizer
import traceback  # Added import for stack trace information
import time  # Added import for timestamp logging
import inspect    # For frame inspection
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings
from pydantic_ai.providers.anthropic import AnthropicProvider
from pydantic_ai.models.openai import OpenAIModel, OpenAIResponsesModelSettings, ReasoningEffort
from pydantic_ai.providers.openai import OpenAIProvider

load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

CRITIQUE_MODEL_NAME = os.getenv("CRITIQUE_MODEL_NAME")
CRITIQUE_MODEL_BASE_URL = os.getenv("CRITIQUE_MODEL_BASE_URL")
CRITIQUE_MODEL_API_KEY = os.getenv("CRITIQUE_MODEL_API_KEY")

INCLUDE_JSON_OUTPUT_TAGS = False

MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 16000
MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 30000

RELEVANCE_THRESHOLD = 8.0

critique_client = AsyncOpenAI(
    base_url=CRITIQUE_MODEL_BASE_URL, 
    api_key=CRITIQUE_MODEL_API_KEY,     
    max_retries=3,
    timeout=10000000
)
generator_client = AsyncOpenAI(
    base_url=GENERATOR_MODEL_BASE_URL, 
    api_key=GENERATOR_MODEL_API_KEY,     
    max_retries=3,
    timeout=10000000
)

critique_model = OpenAIModel(
    model_name=CRITIQUE_MODEL_NAME,
    provider=OpenAIProvider(
        base_url=CRITIQUE_MODEL_BASE_URL,
        api_key=CRITIQUE_MODEL_API_KEY
    )
)

generator_model = OpenAIModel(
    model_name=GENERATOR_MODEL_NAME,
    provider=OpenAIProvider(
        base_url=GENERATOR_MODEL_BASE_URL,
        api_key=GENERATOR_MODEL_API_KEY
    )
)

print("GENERATOR_MODEL_NAME", GENERATOR_MODEL_NAME)
print("GENERATOR_MODEL_BASE_URL", GENERATOR_MODEL_BASE_URL)
print("GENERATOR_MODEL_API_KEY", GENERATOR_MODEL_API_KEY)

print("CRITIQUE_MODEL_NAME", CRITIQUE_MODEL_NAME)
print("CRITIQUE_MODEL_BASE_URL", CRITIQUE_MODEL_BASE_URL)
print("CRITIQUE_MODEL_API_KEY", CRITIQUE_MODEL_API_KEY)

# Pydantic schema for structured output
class CheckpointList(BaseModel):
    """Schema for checkpoint list generation"""
    checkpoints: List[str] = Field(
        description="List of specific checkpoint questions that should be answered in summaries for this section",
        min_items=2
    )

class RelevanceEvaluation(BaseModel):
    """Schema for summary relevance evaluation"""
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well the summary answers the checkpoint questions",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of your score",
        default=""
    )

class CheckpointRelevanceEvaluation(BaseModel):
    """Schema for individual checkpoint relevance evaluation"""
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well the summary answers this specific checkpoint question.",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of your score for this specific checkpoint",
        default=""
    )

class CritiqueEvaluation(BaseModel):
    """Schema for NDA critique evaluation"""
    overall_rating: float = Field(
        description="Overall rating from 1-10 for the NDA section quality",
        ge=1.0,
        le=10.0
    )
    key_strengths: List[str] = Field(
        description="List of main strengths identified in the NDA section",
        default=[]
    )
    critical_issues: List[str] = Field(
        description="List of critical issues that need to be addressed",
        default=[]
    )
    required_improvements: List[str] = Field(
        description="List of specific improvements required",
        default=[]
    )
    additional_recommendations: List[str] = Field(
        description="List of additional recommendations for enhancement",
        default=[]
    )

def get_json_output_tags():
    if INCLUDE_JSON_OUTPUT_TAGS:
        return "Give your json in <text_output> json </text_output> tags."
    else:
        return ""

async def check_if_llm_is_available(client: AsyncOpenAI, model: str):
    try:
        response = await client.chat.completions.create(model=model, messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, world!"}
        ])
        print(f"LLM {model} is available, response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"Error in checking if LLM {model} is available: {e}")
        return False

def calculate_adaptive_threshold(scores, model_name=None, percentile=70):
    """
    Calculate an adaptive threshold based on the distribution of scores for a given model.
    This can help address the LLM-specific threshold problem.
    
    Args:
        scores: List of relevance scores from previous evaluations
        model_name: Name of the LLM model (for future model-specific calibration)
        percentile: Percentile to use for threshold calculation (default: 70th percentile)
        
    Returns:
        float: Calculated adaptive threshold
    """
    if not scores:
        return 5.0  # Default fallback threshold
    
    import numpy as np
    return np.percentile(scores, percentile)

def write_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"JSON saved to {filename}")

def read_json(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def get_mongodb_client():
    """Get MongoDB client connection."""
    return MongoClient(os.getenv("MONGO_DB_URL"))

def parse_json_response(response_text: str) -> Any:
    """
    Parse a JSON response that may be wrapped in backticks.
    
    Args:
        response_text: The response text to parse
        
    Returns:
        Any: The parsed JSON object
    """
    # Remove any markdown code block syntax
    response_text = re.sub(r'```json\n?', '', response_text)
    response_text = re.sub(r'```\n?', '', response_text)
    response_text = response_text.strip()
    
    try:
        # print(f"parse_json_response, response_text: {json.loads(response_text)}")
        return json.loads(response_text)
    except json.JSONDecodeError:
        # If the response is not valid JSON, try to extract a list from the text
        # Look for lines that start with numbers, bullets, or dashes
        lines = re.findall(r'^[\d\-\*\.]+\.?\s*(.+)$', response_text, re.MULTILINE)
        if lines:
            return lines
        # If no lines found, split by newlines and clean up
        # print(f"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\n') if line.strip()]}")
        return [line.strip() for line in response_text.split('\n') if line.strip()]
    
def calculate_number_of_tokens(text):
    # Load tokenizer for Mistral model
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    return token_count

async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):
    """
    Wrapper function that retries an async LLM API call when the response is empty.
    
    Args:
        async_func: The async function to call (usually an LLM API call)
        *args: Positional arguments to pass to async_func
        max_retries: Maximum number of retry attempts (default: 3)
        **kwargs: Keyword arguments to pass to async_func
        
    Returns:
        The result of the async_func call, ensuring it's not empty
        
    Raises:
        Exception: If max_retries is reached and the response is still empty
    """
    # Create logs directory if it doesn't exist
    log_dir = "error_logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Extract the function name for logging purposes
    func_name = async_func.__name__ if hasattr(async_func, "__name__") else "unknown_function"
    
    # Try to get the caller's name from the stack
    try:
        caller_frame = inspect.currentframe().f_back
        caller_name = caller_frame.f_code.co_name if caller_frame else "unknown_caller"
    except Exception:
        caller_name = "unknown_caller"
    
    for attempt in range(max_retries):
        try:
            result = await async_func(*args, **kwargs)
            
            # Check if result is None, empty string, just whitespace, or contains tool calls
            is_empty = result is None or (isinstance(result, str) and result.strip() == "")
            contains_tool_call = False
            
            if isinstance(result, str):
                # Check for tool_call patterns
                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)
                contains_tool_call_text = 'tool_call' in result.lower()
                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text
            
            if is_empty or contains_tool_call:
                if is_empty:
                    reason = "empty response"
                    error_type = 'empty_response'
                elif contains_tool_call:
                    reason = "response contains tool calls"
                    error_type = 'tool_call_response'
                
                print(f"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...")
                
                # Get debug information to log
                debug_info = {
                    'error_type': error_type,
                    'function': func_name,
                    'caller': caller_name,
                    'attempt': attempt + 1,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]
                }
                
                # Extract prompt information based on different API patterns
                # For the direct messages pattern in kwargs
                if 'messages' in kwargs:
                    debug_info['messages'] = kwargs['messages']
                    
                # For the pattern where the func is a closure with local make_api_call
                # Try to get source code of the async_func to check for patterns
                try:
                    source = inspect.getsource(async_func)
                    if "chat.completions.create" in source:
                        debug_info['api_pattern'] = "chat_completions_closure"
                except Exception:
                    pass
                
                # Try to extract system_prompt and user_prompt from the caller's frame if available
                try:
                    if caller_frame:
                        caller_locals = caller_frame.f_locals
                        # Capture common patterns in this codebase
                        if 'system_prompt' in caller_locals:
                            debug_info['system_prompt'] = caller_locals['system_prompt']
                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                        # If this is using the OpenAI client pattern, get the model too
                        if 'model' in caller_locals:
                            debug_info['model'] = caller_locals['model']
                        # For the antropic calls
                        if 'CRITIQUE_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                        elif 'GENERATOR_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                        elif 'MODEL' in caller_locals:
                            debug_info['model'] = caller_locals['MODEL']
                except Exception as e:
                    debug_info['frame_inspection_error'] = str(e)
                
                # Save the debug information
                timestamp = int(time.time())
                log_filename = f"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json"
                
                try:
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        json.dump(debug_info, f, indent=2, ensure_ascii=False)
                    print(f"Logged empty response details to {log_filename}")
                except Exception as log_error:
                    print(f"Failed to log empty response details: {str(log_error)}")
                
                # Continue to the next retry attempt
                continue
                
            # If we get here, we have a non-empty response
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            print(f"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}")
            
            # Get debug information to log
            debug_info = {
                'error_type': error_type,
                'error_message': error_msg,
                'function': func_name,
                'caller': caller_name,
                'attempt': attempt + 1,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'stack_trace': traceback.format_exc()
            }
            
            # Extract prompt information based on different API patterns
            # For the direct messages pattern in kwargs
            if 'messages' in kwargs:
                debug_info['messages'] = kwargs['messages']
                
            # For the pattern where the func is a closure with local make_api_call
            # Try to get source code of the async_func to check for patterns
            try:
                source = inspect.getsource(async_func)
                if "chat.completions.create" in source:
                    debug_info['api_pattern'] = "chat_completions_closure"
            except Exception:
                pass
            
            # Try to extract system_prompt and user_prompt from the caller's frame if available
            try:
                if caller_frame:
                    caller_locals = caller_frame.f_locals
                    # Capture common patterns in this codebase
                    if 'system_prompt' in caller_locals:
                        debug_info['system_prompt'] = caller_locals['system_prompt']
                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                    # If this is using the OpenAI client pattern, get the model too
                    if 'model' in caller_locals:
                        debug_info['model'] = caller_locals['model']
                    # For the antropic calls
                    if 'CRITIQUE_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                    elif 'GENERATOR_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                    elif 'MODEL' in caller_locals:
                        debug_info['model'] = caller_locals['MODEL']
            except Exception as frame_error:
                debug_info['frame_inspection_error'] = str(frame_error)
            
            # Save the debug information
            timestamp = int(time.time())
            log_filename = f"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json"
            
            try:
                with open(log_filename, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, indent=2, ensure_ascii=False)
                print(f"Logged error details to {log_filename}")
            except Exception as log_error:
                print(f"Failed to log error details: {str(log_error)}")
            
            if attempt == max_retries - 1:
                # If we've exhausted all retries and still have an error
                print(f"Failed to get non-empty response after {max_retries} attempts")
                return None
            
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** attempt))
    
    # If we've exhausted all retries and still have an empty response
    print(f"Failed to get non-empty response after {max_retries} attempts")
    return None

await check_if_llm_is_available(generator_client, GENERATOR_MODEL_NAME)
await check_if_llm_is_available(critique_client, CRITIQUE_MODEL_NAME)


# %%
ich_ectd_guideline_referenced_with_quality_guidelines = read_json("/Users/<USER>/projects/scalegen/MedNova/structured jsons/ich_ectd_guideline_referenced_with_quality_guidelines.json")

doc_to_work_on = ich_ectd_guideline_referenced_with_quality_guidelines["module3"]["3.2"]["3.2.P"]["3.2.P.8"]["3.2.P.8.1"]


# %%
async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:
    """
    Generate checkpoint questions from a single individual summary.
    
    Args:
        summary_content: The content of the individual summary
        section_info: Dictionary containing section information
        
    Returns:
        List[str]: A list of checkpoint questions generated from the individual summary
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive checklist of specific questions based on the provided summary for a section of ICH eCTD guidelines."
            
            prompt = f"""
First, review the following information:

Golden Instructions and Checkpoints:
<golden_instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</golden_instructions_and_checkpoints>

Summary Content:
<summary_content>
{summary_content}
</summary_content>

Section Number:
<section_number>
{section_info.get('section', '')}
</section_number>

Section Title:
<section_title>
{section_info.get('title', '')}
</section_title>

Before creating the checklist, analyze the provided information and formulate your approach inside your thinking block using <checklist_preparation> tags:

1. Summarize the key points from the summary content.
2. Quote relevant phrases from the summary content and golden instructions.
3. Identify the main topics and requirements mentioned in the golden instructions and checkpoints.
4. List any specific technical or regulatory aspects that need to be addressed.
5. Note any particular areas of focus (e.g., physical characteristics, chemical properties, manufacturing processes, quality control measures, stability data, packaging specifications, compatibility information).
6. Brainstorm potential questions based on the quoted phrases and identified topics.
7. Ensure alignment with ICH guidelines.
8. Consider how to phrase each point as a specific, answerable question.

Now, create a detailed checklist of specific questions based on your analysis. The checklist should:

1. Include all key requirements and recommendations from the summary expressed as specific questions.
2. Cover all aspects mentioned in the summary.
3. Strictly adhere to the golden instructions and checkpoints provided.
4. Be specific enough to clearly determine if an input document addresses each point.
5. Format EACH checkpoint as a question that can be answered with yes/no or specific information.
6. Focus on technical and regulatory content needed for NDA documentation.
7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.
8. Cover stability data, packaging specifications, and compatibility information where appropriate.
9. Include questions about the validation and verification methods used.
10. Use the material prepared in the thinking block to ensure comprehensive coverage.

After creating the checklist, review it to ensure:
- All points from the summary are covered
- Questions align with the golden instructions and checkpoints
- Each question is specific and answerable
- The checklist is comprehensive and accurate

Present your final checklist after this review. Your output should consist only of the checklist and should not duplicate or rehash any of the work you did in the thinking block.
"""
            
            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)

            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings={
                    "temperature": 0.1,
                    "extra_body": {
                        # "return_reasoning": True
                    }
                },
                retries=3
            )

            response = await checkpoint_list_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            retry_count += 1
            print(f"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Generating basic checkpoints from summary as fallback.")
                # Generate basic checkpoints as fallback
                try:
                    # Extract key sentences from summary as basic checkpoints
                    sentences = [s.strip() + "?" for s in summary_content.split(".") if len(s.strip()) > 20]
                    # Convert statements to questions where possible
                    questions = []
                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload
                        if not sentence.endswith("?"):
                            questions.append(f"Does the document address: {sentence}?")
                        else:
                            questions.append(sentence)
                    return questions
                except Exception:
                    print("Fallback checkpoint generation failed. Returning empty list.")
                    return []
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []

async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:
    """
    Generate checkpoints incrementally from a list of individual summaries.
    
    Args:
        section_info: Dictionary containing section information
        list_of_individual_summary_contents: List of individual summary contents
        
    Returns:
        List[str]: A comprehensive list of checkpoints generated from all individual summaries
    """
    # Handle empty input
    if not list_of_individual_summary_contents:
        return []
        
    # Handle single summary case
    if len(list_of_individual_summary_contents) == 1:
        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)
    
    # Maximum token limit for checkpoint list batches
    
    # Generate initial checkpoints from each summary in parallel
    tasks = [_generate_checkpoints_from_one_summary(summary, section_info) for summary in list_of_individual_summary_contents]
    current_checkpoints = await asyncio.gather(*tasks)
    
    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):
        """Helper function to combine multiple checkpoint lists while preserving all unique checkpoints."""
        total_lists = len(checkpoint_lists)
        print(f"\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints")
        
        # Calculate token count for all lists combined
        combined_text = "\n".join(["\n".join(cp) for cp in checkpoint_lists])
        total_tokens = calculate_number_of_tokens(combined_text)
        print(f"Total input tokens for checkpoint merging: {total_tokens}")
        
        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive merged list of checkpoint questions for a specific section of ICH eCTD guidelines."
        
        user_prompt = f"""
First, review the following information:

Section:
<section_info>
{section_info.get('section', '')}
</section_info>

Title:
<section_title>
{section_info.get('title', '')}
</section_title>

Golden Instructions and Checkpoints:
<enhanced_instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</enhanced_instructions_and_checkpoints>

You will be processing {total_lists} lists of checkpoint questions. 

"""
        
        # Add each checkpoint list
        for i, checkpoints in enumerate(checkpoint_lists, 1):
            user_prompt += f"""
Checkpoint List {i}:
<checkpoints_{i}>
{json.dumps(checkpoints, indent=2)}
</checkpoints_{i}>

"""

        user_prompt += f"""
Your goal is to merge these lists into a single comprehensive list while adhering to the following requirements:

1. Preserve ALL unique questions from all lists
2. Remove any duplicate or redundant questions
3. Ensure the merged list is comprehensive and covers all aspects
4. Maintain the specificity and clarity of each question
5. Keep the question format consistent (e.g., "Does the document mention...")
6. Ensure each question focuses on a single specific point
7. Group related questions together when possible
8. Strictly adhere to the referenced list of golden instructions and checkpoints
9. Keep all questions focused on technical regulatory content for NDA documentation

Before producing the final merged list, wrap your analysis inside <checkpoint_analysis> tags in your thinking block. In this analysis:
1. Summarize the key points from the section_info, section_title, and enhanced_instructions_and_checkpoints.
2. Analyze the first list of checkpoints, noting any patterns or themes.
3. Plan how you will approach merging subsequent lists (even though we only have the first list now).
4. Ensure all requirements are met and pay special attention to accuracy and best practices in regulatory documentation.

After your analysis, provide the merged list of checkpoint questions. 

Your final output should consist only of the merged list of checkpoint questions and should not duplicate or rehash any of the work you did in the checkpoint analysis section.
        """
        
        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)

        checkpoint_list_agent = Agent(
            model=generator_model,
            system_prompt=system_prompt,
            output_type=CheckpointList,
            model_settings={
                "temperature": 0.1,
                "extra_body": {
                    # "return_reasoning": True
                }
            },
            retries=3
        )

        response = await checkpoint_list_agent.run(user_prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        # print(f"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        merged_checkpoints = response.output.checkpoints
        
        # Validate the output
        if len(merged_checkpoints) < 2:
            raise ValueError(f"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}")
        
        print(f"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints")
        
        return merged_checkpoints
    
    # Iteratively merge checkpoint lists in optimal batches
    while len(current_checkpoints) > 1:
        # Create optimal batches based on token count and number of checkpoints
        batches = []
        current_batch = []
        current_token_count = 0
        current_checkpoint_count = 0
        
        for checkpoint_list in current_checkpoints:
            # Calculate tokens for this checkpoint list
            checkpoint_text = "\n".join(checkpoint_list)
            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)
            
            # Check if this single checkpoint list exceeds the token limit
            if checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT:
                print(f"Warning: Single checkpoint list has {checkpoint_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT}")
                # Force this large list to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(checkpoint_list)
                    current_token_count += checkpoint_tokens
                    current_checkpoint_count += len(checkpoint_list)
                    print(f"Forcing merge of oversized checkpoint list with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                    current_checkpoint_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next checkpoint list
                    # to avoid it being processed alone repeatedly
                    current_batch = [checkpoint_list]
                    current_token_count = checkpoint_tokens
                    current_checkpoint_count = len(checkpoint_list)
                    print(f"Starting new batch with oversized checkpoint list ({checkpoint_tokens} tokens)")
            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch
            elif (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):
                batches.append(current_batch)
                current_batch = [checkpoint_list]
                current_token_count = checkpoint_tokens
                current_checkpoint_count = len(checkpoint_list)
            else:
                current_batch.append(checkpoint_list)
                current_token_count += checkpoint_tokens
                current_checkpoint_count += len(checkpoint_list)
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        # Ensure no batch has only one checkpoint list to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one checkpoint list left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        print(f"Created {len(batches)} batches for checkpoint list merging")
        for i, batch in enumerate(batches):
            total_checkpoints = sum(len(cp) for cp in batch)
            total_tokens = sum(calculate_number_of_tokens("\n".join(cp)) for cp in batch)
            print(f"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints and {total_tokens} total tokens")
        
        # Process all batches in parallel
        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]
        
        # Wait for all merges to complete
        current_checkpoints = await asyncio.gather(*tasks)
    
    print(f"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}")
    return current_checkpoints[0]

# %%
async def generate_checkpoint_list(section, title, description, referenced_quality_guidelines, enhanced_instructions_and_checkpoints):
    """
    Generate a checklist of questions that should be answered by summaries for this section.
    
    Args:
        section: The section identifier (e.g., "3.2.P.2.2.1")
        title: The title of the section
        description: The description of the section
        referenced_quality_guidelines: List of quality guidelines referenced
        enhanced_instructions_and_checkpoints: A checkpoint list of golden instructions that must be strictly adhered.
        
    Returns:
        List[str]: A list of checkpoint questions that should be answered in summaries
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Create a prompt for the LLM to generate a checklist
            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive list of checkpoint questions for a specific section of ICH eCTD guidelines."

            prompt = f"""
First, carefully review the following information:

Section:
<section>
{section}
</section>

Title:
<title>
{title}
</title>

Description:
<description>
{description}
</description>

Enhanced Instructions and Checkpoints:
<enhanced_instructions_and_checkpoints>
{enhanced_instructions_and_checkpoints}
</enhanced_instructions_and_checkpoints>

Referenced Quality Guidelines:
<referenced_quality_guidelines>
{', '.join(referenced_quality_guidelines)}
</referenced_quality_guidelines>

Your goal is to create a detailed checklist of specific questions that should be answered in summaries for this section. Follow these guidelines:

1. Include all key requirements and recommendations, expressed as specific questions.
2. Cover all aspects mentioned in the title and description.
3. Strictly adhere to the enhanced instructions and checkpoints provided.
4. Address all points from the referenced quality guidelines.
5. Ensure each question is specific enough to clearly determine if a summary addresses the point.
6. Format each checkpoint as a question that can be answered with yes/no or specific information.
7. Focus on technical and regulatory content needed for NDA documentation.
8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.
9. Cover stability data, packaging specifications, and compatibility information where appropriate.
10. Include questions about the validation and verification methods used.
11. Do not include references to sections/guidelines in the questions themselves.

Before formulating the final list, conduct your analysis within <checkpoint_planning> tags inside your thinking block:
a) Quote key phrases from each input section
b) Break down main themes and requirements
c) List and number potential areas for checkpoint questions

Pay special attention to the enhanced instructions and checkpoints, as these are derived from actual NDA output and provide high-quality guidance.

After your analysis, present the final list of checkpoint questions

Each checkpoint should be a string in the list of checkpoints.

Your final output should consist only of the checkpoint list and should not duplicate or rehash any of the work you did in the thinking block.

Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.
"""

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)
            
            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings={
                    "temperature": 0.1,
                    "extra_body": {
                        # "return_reasoning": True
                    }
                },
                retries=3
            )

            response = await checkpoint_list_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            print(f"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            traceback.print_exc()
            retry_count += 1
            print(f"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning empty list.")
                return []
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []


# %%
async def evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint_question):
    """
    Evaluate if a summary is relevant to a single checkpoint question.
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoint_question: Single checkpoint question to evaluate against
        
    Returns:
        CheckpointRelevanceEvaluation: Structured evaluation result
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and ICH guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate if a given summary addresses a specific checkpoint question for an ICH eCTD section, with a focus on comprehensive information gathering for NDA submissions where having more relevant information is preferred over missing potentially important details."""

            prompt = f"""
Evaluate the relevance of the provided summary to a specific checkpoint question for the following ICH eCTD section:

Summary Content to Evaluate:
<summary_content>
{summary_content}
</summary_content>

Section Information:
<section_info>
{section_info.get('section', '')}
</section_info>

Title Information:
<title_info>
{section_info.get('title', '')}
</title_info>

Please evaluate the relevance of the summary to this specific checkpoint question:
<checkpoint_question>
{checkpoint_question}
</checkpoint_question>

IMPORTANT CONTEXT: This evaluation is for NDA regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.

Evaluation Process:
1. Carefully read the checkpoint question to understand the specific information it's requesting.
2. Analyze the summary content to identify information that directly, indirectly, or partially addresses the checkpoint question.
3. Consider the depth, specificity, and quality of the information provided.
4. Remember that the summary doesn't need to explicitly mention the question itself, only provide relevant information that would help answer it.
5. Give credit to partial relevance - information that addresses part of the question or provides context is valuable.

Conduct your evaluation in the relevance_evaluation thinking block:

1. Quote Extraction and Relevance Assessment:
   - List specific parts of the summary that relate to the checkpoint question.
   - For each quote, explain how well it addresses the question (completely, partially, or provides relevant context).
   - Include information that might be indirectly relevant but still useful for regulatory purposes.

2. Partial Relevance Analysis:
   - Identify content that doesn't fully answer the question but provides relevant background, context, or related information.
   - Consider whether this partial information would be valuable for regulatory professionals working on this section.

3. Depth and Specificity Assessment:
   - Evaluate the level of detail and precision in the relevant information.
   - Consider if the information provides useful context even if not perfectly specific to the checkpoint question.

4. Regulatory Perspective:
   - Elaborate on how a regulatory professional could use this information to address the checkpoint.
   - Consider the value of having comprehensive information for NDA submissions.
   - Assess whether the information contributes to the overall understanding of the regulatory requirements.

5. Scoring Justification:
   - Based on your analysis, assign a preliminary score from 0 to 10 using the following criteria:
     - 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information that would be valuable for regulatory submission
     - 6-7: Partially addresses the question with some relevant information that contributes to understanding the requirements
     - 4-5: Provides limited but potentially useful information related to the question or relevant context
     - 1-3: Provides minimal information that has some connection to the question but is mostly tangential
     - 0: Does not address the question at all or only contains completely irrelevant information
   - Provide a detailed justification for your score, referencing specific elements of your analysis.

IMPORTANT: Be inclusive in your evaluation while maintaining quality standards. For NDA submissions, information that partially addresses a checkpoint or provides valuable context should be scored favorably (8+ range) as comprehensive information gathering is critical for regulatory success. However, still exclude content that is merely tangential or only contains very indirect mentions without substantial relevance.

After your analysis, provide your final evaluation in the following JSON format:

{{
  "relevance_score": <number from 0-10>,
  "justification": "<detailed explanation of your score>"
}}

Your final evaluation should be concise and should not duplicate the detailed work from your relevance evaluation thinking block."""

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(prompt)

            checkpoint_evaluation_agent = Agent(
                model=critique_model,
                system_prompt=system_prompt,
                output_type=CheckpointRelevanceEvaluation,
                model_settings={
                    "temperature": 0.1,
                    "extra_body": {
                        # "return_reasoning": True
                    }
                },
                retries=3
            )

            response = await checkpoint_evaluation_agent.run(prompt)

            output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            total_tokens = system_tokens + user_tokens + output_tokens

            # Uncomment for detailed token tracking
            # print(f"evaluate_individual_checkpoint_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            return response.output
            
        except Exception as e:
            retry_count += 1
            print(f"Error in evaluate_individual_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning default evaluation.")
                # Return default evaluation
                return CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Error occurred during evaluation"
                )
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return CheckpointRelevanceEvaluation(
        relevance_score=0.0,
        justification="Unexpected error"
    )


# %%
async def evaluate_summary_relevance(summary_content, section_info, checkpoints):
    """
    Use an LLM to evaluate if a summary is relevant to a section by evaluating each checkpoint individually.
    This approach provides more accurate relevance determination by:
    1. Evaluating each checkpoint question individually in parallel
    2. Using a clear 8+ threshold for relevance determination
    3. Aggregating individual scores and applying threshold to final average
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoints: List of checkpoint questions that should be answered
        
    Returns:
        Tuple[bool, float, int]: Tuple containing boolean relevance, average score, and number of relevant checkpoints
    """
    # Handle empty checkpoint list
    if not checkpoints:
        print("No checkpoints provided for evaluation")
        return False, 0.0, 0
    
    try:
        print(f"Evaluating summary relevance using {len(checkpoints)} individual checkpoints")
        
        # Create tasks for parallel evaluation of each checkpoint
        tasks = [
            evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint)
            for checkpoint in checkpoints
        ]
        
        # Execute all evaluations in parallel
        checkpoint_evaluations = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle any exceptions
        valid_evaluations = []
        for i, evaluation in enumerate(checkpoint_evaluations):
            if isinstance(evaluation, Exception):
                print(f"Error evaluating checkpoint {i+1}: {evaluation}")
                # Create default evaluation for failed checkpoint
                valid_evaluations.append(CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Evaluation failed"
                ))
            else:
                valid_evaluations.append(evaluation)
        
        # Calculate simple average of all individual scores
        total_checkpoints = len(valid_evaluations)
        total_score = sum(eval.relevance_score for eval in valid_evaluations)
        average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0
        number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)
        
        # Determine if summary is relevant based on average score
        is_relevant = number_of_relevant_checkpoints >= 1

        if not is_relevant:
            print("Summary is not relevant, evaluations:", "\n".join([f"Relevance Score: {eval.relevance_score:.2f}, Justification: {eval.justification}" for eval in valid_evaluations]))

        return is_relevant, average_score, number_of_relevant_checkpoints
        
    except Exception as e:
        print(f"Error in evaluate_summary_relevance: {e}")
        traceback.print_exc()
        return False, 0.0, 0

# %%
async def search_summaries_by_llm(section_info, quality_guidelines, summary_level=1):
    """
    Search for summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        quality_guidelines: List of quality guidelines to search in (e.g., ["Q6A", "Q6B"])
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['quality_docs_summaries']
    
    # Find all summaries matching the criteria
    if len(quality_guidelines) > 0:
        summaries = list(collection.find({
            "quality_guideline": {"$in": quality_guidelines},
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))

    # summaries = list(collection.find({
    #     "summary_level": summary_level
    # }))

    print(f"Found {len(summaries)} summaries for section {section_info.get('section', '')} quality guidelines {quality_guidelines} at level {summary_level}")
    
    if not summaries:
        print(f"No summaries found for quality guidelines {quality_guidelines} at level {summary_level}")
        return []
    
    # Get checkpoints for the section
    checkpoints = section_info.get("checkpoint_list", [])
    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        # Generate checkpoints if not already available
        checkpoints = await generate_checkpoint_list(
            section_info.get("section", ""),
            section_info.get("title", ""),
            section_info.get("description", ""),
            section_info.get("referenced_quality_guidelines", []),
            section_info.get("enhanced_instructions_and_checkpoints", "")
        )
    
    # Evaluate each summary
    evaluated_summaries = []
    results = []
    for summary in summaries:
        is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary["content"], section_info, checkpoints)
        print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
        results.append(is_relevant)
    
    for summary, is_relevant in zip(summaries, results):
        if is_relevant:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT")
            summary["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
            evaluated_summaries.append(summary)
        else:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
    
    # No need to sort by relevance score since all relevant summaries have same score
    # evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries


# %%
def get_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['quality_docs_summaries']
    chunk_collection = db['quality_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks


# %%
def get_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['quality_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries


# %%
async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Extract section information
    section = section_info.get("section", "")
    title = section_info.get("title", "")
    description = section_info.get("description", "")
    referenced_quality_guidelines = section_info.get("referenced_quality_guidelines", [])
    
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_summaries_by_llm(
        section_info=section_info,
        quality_guidelines=referenced_quality_guidelines,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    for final_summary in final_summaries:
        summaries = get_summaries_for_final_summary(final_summary["_id"])
        
        # Evaluate each individual summary
        evaluated_summaries = []
        results = []
        for summary in summaries:
            is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary["content"], section_info, section_info.get("checkpoint_list", []))
            print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
            results.append(is_relevant)
        
        for summary, is_relevant in zip(summaries, results):
            if is_relevant:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT")
                summary["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
                evaluated_summaries.append(summary)
            else:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
        
        # No need to sort by relevance score since all relevant summaries have same score
        # evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
        individual_summaries.extend(evaluated_summaries)
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    # BATCH_SIZE = 50
    
    # for i in range(0, len(individual_summaries), BATCH_SIZE):
    #     batch = individual_summaries[i:i + BATCH_SIZE]
    #     batch_tasks = []
        
    #     for individual_summary in batch:
    #         summary_chunks = get_chunks_for_summary(individual_summary["_id"])
    #         # Create tasks for parallel evaluation of chunks
    #         tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
    #         batch_tasks.extend(zip(summary_chunks, tasks))
        
    #     # Execute all tasks in parallel
    #     results = await asyncio.gather(*[task for _, task in batch_tasks])
        
    #     # Process results
    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):
    #         if relevance_score >= get_relevance_threshold(0):
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    #             chunk["similarity_score"] = relevance_score
    #             chunks.append(chunk)
    #         else:
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks


# %%
async def extract_relevant_chunks(json_obj):
    """
    Recursively iterates through the nested JSON structure, extracts titles and descriptions,
    generates checkpoint lists, and finds relevant summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with added relevant summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs processing
            if (obj and "title" in obj and "description" in obj and 
                "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        # Generate checkpoint list for this section
        section["checkpoint_list"] = await generate_checkpoint_list(
            section["section"], 
            section["title"], 
            section["description"], 
            section.get("referenced_quality_guidelines", []),
            section.get("enhanced_instructions_and_checkpoints", "")
        )
        
        # Find relevant summaries and chunks for this section
        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(
            section
        )

        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks")
        
        # Add the results to the section
        section["relevant_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "quality_guideline": s["quality_guideline"], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            } for s in final_summaries
        ]
        
        section["relevant_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "quality_guideline": s["quality_guideline"], 
                "similarity_score": s.get("similarity_score", 0), 
                "content": s["content"]
            } for s in individual_summaries
        ]

        # Create a final summary from the relevant individual summaries
        # if len(section["relevant_individual_summaries"]) > 0:
        #     final_summary = await create_combined_summary([summary["content"] for summary in section["relevant_individual_summaries"]])
        #     section["quality_guideline_combined_summary"] = final_summary
        # else:
        #     # If no relevant summaries found, create a summary from section information
        #     final_summary = await generate_summary_from_section_info(section)
        #     section["quality_guideline_combined_summary"] = final_summary

        # Generate checkpoints incrementally from individual summaries
        if len(section["relevant_individual_summaries"]) > 0:
            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(
                section,
                [summary["content"] for summary in section["relevant_individual_summaries"]]
            )
        else:
            # If no relevant summaries found, generate checkpoints from section information
            checkpoints = await generate_checkpoint_list(
                section["section"],
                section["title"],
                section["description"],
                section.get("referenced_quality_guidelines", []),
                section.get("enhanced_instructions_and_checkpoints", "")
            )
        
        print(f"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}")
        section["quality_guideline_combined_summaries_checkpoint_list"] = checkpoints
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
processed_doc = await extract_relevant_chunks(doc_to_work_on)

# Save the results to a JSON file
write_to_json(processed_doc, "processed_ectd_guidelines_with_relevant_chunks.json")


# %%
processed_doc = read_json("processed_ectd_guidelines_with_relevant_chunks.json")


# %%
async def search_input_docs_by_llm(section_info, input_doc_tag, summary_level=1):
    """
    Search for input document summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        input_doc_tag: The input document tag to search for
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    collection = db['input_docs_summaries']
    
    # Find all summaries matching the criteria
    if input_doc_tag:
        summaries = list(collection.find({
            "input_doc_tag": input_doc_tag,
            "summary_level": summary_level
        }))
    else:
        summaries = list(collection.find({
            "summary_level": summary_level
        }))
    
    print(f"Found {len(summaries)} input document summaries for section {section_info.get('section', '')} with tag {input_doc_tag} at level {summary_level}")
    
    if not summaries:
        print(f"No input document summaries found for tag {input_doc_tag} at level {summary_level}")
        return []
    
    # Get checkpoints for the section - prefer using checkpoints from quality_guideline_combined_summary
    print(f"Using {len(section_info.get('quality_guideline_combined_summaries_checkpoint_list', []))} quality_guideline_combined_summaries_checkpoint_list from quality_guideline_combined_summary for section {section_info.get('section', '')}")
    checkpoints = section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])

    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        return []
    
    # Evaluate each summary
    evaluated_summaries = []
    results = []
    for summary in summaries:
        is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(summary["content"], section_info, checkpoints)
        print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
        results.append(is_relevant)
    
    for summary, is_relevant in zip(summaries, results):
        if is_relevant:
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT")
            summary["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
            evaluated_summaries.append(summary)
        else:
            print(f"search_input_docs_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
    
    # No need to sort by relevance score since all relevant summaries have same score
    # evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    client.close()
    return evaluated_summaries


# %%
def get_input_chunks_for_summary(summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the input document chunks associated with a summary.
    
    Args:
        summary_id: The ID of the summary
        
    Returns:
        List[Dict]: List of chunks with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['input_docs_summaries']
    chunk_collection = db['input_docs_chunks']
    
    # Get the summary
    summary = summary_collection.find_one({"_id": ObjectId(summary_id)})
    
    if not summary or "chunk_reference_ids" not in summary:
        print(f"No chunk references found for summary {summary_id}")
        return []
    
    # Get the chunks
    chunk_ids = summary["chunk_reference_ids"]
    chunks = list(chunk_collection.find({"_id": {"$in": chunk_ids}}))
    
    client.close()
    return chunks


# %%
def get_input_summaries_for_final_summary(final_summary_id: str) -> List[Dict[str, Any]]:
    """
    Get the individual input document summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    client = get_mongodb_client()
    db = client['mednova']
    summary_collection = db['input_docs_summaries']
    
    # Get the final summary
    final_summary = summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(summary_collection.find({"_id": {"$in": summary_ids}}))
    
    client.close()
    return individual_summaries


# %%
async def filter_input_docs_by_section(section_info: Dict[str, Any], input_doc_tag: str = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter input document summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        input_doc_tag: The input document tag to search for
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_input_docs_by_llm(
        section_info=section_info,
        input_doc_tag=input_doc_tag,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2)
    individual_summaries = []
    
    for final_summary in final_summaries:
        summaries = get_input_summaries_for_final_summary(final_summary["_id"])
        # Skip individual summary evaluation, directly check chunks instead
        for summary in summaries:
            individual_summaries.append(summary)
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []

    for individual_summary in individual_summaries:
        summary_chunks = get_input_chunks_for_summary(individual_summary["_id"])
        
        for chunk in summary_chunks:
            is_relevant, average_score, number_of_relevant_checkpoints = await evaluate_summary_relevance(
                chunk["content"], 
                section_info, 
                section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])
            )
            print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
            
            if is_relevant:
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is RELEVANT")
                chunk["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
                chunks.append(chunk)
            else:
                print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
    
    return final_summaries, individual_summaries, chunks


# %%
async def extract_relevant_input_docs(json_obj, input_doc_tag: str = None):
    """
    Recursively iterates through the nested JSON structure, finds relevant quality guideline summaries,
    creates a final summary, and then finds relevant input document summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        input_doc_tag: The input document tag to search for
        
    Returns:
        Dict: The processed JSON object with added relevant input document summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that has relevant quality guideline summaries
            if (obj and "relevant_final_summaries" in obj and 
                obj["relevant_final_summaries"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Processing section {section.get('section', 'unknown')} for input documents")

        # Find relevant input document summaries and chunks
        input_final_summaries, input_individual_summaries, input_chunks = await filter_input_docs_by_section(
            section,
            input_doc_tag
        )
        
        # Add the results to the section
        section["relevant_input_final_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "input_doc_tag": s["input_doc_tag"], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            } for s in input_final_summaries
        ]
        
        section["relevant_input_individual_summaries"] = [
            {
                "_id": str(s["_id"]), 
                "input_doc_tag": s["input_doc_tag"], 
                "similarity_score": s.get("similarity_score", 0),
                "content": s["content"]
            } for s in input_individual_summaries
        ]
        
        section["relevant_input_chunks"] = [
            {
                "_id": str(c["_id"]), 
                "input_doc_tag": c["input_doc_tag"], 
                "chunk_filename": c.get("chunk_filename", ""), 
                "similarity_score": c.get("similarity_score", 0), 
                "content": c.get("content", "")
            } for c in input_chunks
        ]
        
        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(input_final_summaries)} relevant input final summaries, {len(input_individual_summaries)} relevant input individual summaries, and {len(input_chunks)} relevant input chunks")
        
        return section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
# Process the document to find relevant input documents
processed_doc_with_input_docs = await extract_relevant_input_docs(processed_doc, input_doc_tag="input_docs")

# Save the results to a JSON file
write_to_json(processed_doc_with_input_docs, "processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")


# %%
processed_doc_with_input_docs = read_json("processed_ectd_guidelines_with_relevant_chunks_and_input_docs.json")
# %%
async def create_incremental_plan(section_info, individual_summaries):
    """
    Create a comprehensive NDA section plan by incrementally combining plans generated from individual quality guideline summaries.
    Uses batch processing to efficiently process multiple summaries.
    
    Args:
        section_info: Dictionary containing section information
        individual_summaries: List of individual relevant quality guideline summaries
        
    Returns:
        str: Comprehensive plan for NDA section content
    """
    
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def combine_plans(plans_to_combine):
        """Helper function to combine multiple plans while preserving all requirements."""
        total_plans = len(plans_to_combine)
        print(f"\nCombining {total_plans} plans:")
        
        total_tokens = sum(calculate_number_of_tokens(plan) for plan in plans_to_combine)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert with specialized expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine multiple section plans into one comprehensive unified plan that will guide the generation of a complete, regulatory-compliant NDA section."""

        user_prompt = f"""You will be provided with <total_plans>{total_plans}</total_plans> section plans. Here is the content of each plan: """
        
        # Add each plan with a header
        for i, plan in enumerate(plans_to_combine, 1):
            user_prompt += f"""

Plan <plan_number_{i}>{i}</plan_number_{i}>:
<plan_content_{i}>
{plan}
</plan_content_{i}>
"""

        user_prompt += f"""
Your goal is to create a unified plan that incorporates all unique elements from the provided plans while adhering to the following critical requirements:

1. Plan Comprehensiveness (HIGHEST PRIORITY):
   - Preserve every requirement, element, and consideration from all plans
   - Ensure no regulatory requirement is lost or diluted
   - Maintain all technical specifications from all plans
   - Include all validation and verification details
   - Preserve all cross-references and dependencies
   - Keep all acceptance criteria and thresholds
   - Maintain all risk assessments and control strategies
   - Include all testing requirements and methodologies
   - Preserve all regulatory compliance elements

2. ICH eCTD Compliance:
   - Follow ICH eCTD v3.2.2 structure requirements
   - Maintain proper section organization and hierarchy
   - Include all required eCTD elements and subsections
   - Preserve regulatory guideline references

3. Integration Strategy:
   - Create a logical, cohesive document structure
   - Group related requirements together
   - Eliminate redundancies without losing information
   - Organize requirements in order of regulatory importance
   - Ensure technical specifications are properly contextualized
   - Maintain relationships between different requirements
   - Create clear section and subsection organization
   - Establish information hierarchy and flow

4. Technical Depth:
   - Specify all technical parameters to include
   - Maintain detailed specifications for tests and analyses
   - Preserve methodological details
   - Include precise acceptance criteria
   - Specify validation requirements
   - Maintain detailed documentation standards
   - Include specific technical cross-references

5. Documentation Requirements:
   - Specify all tables, figures, and data presentations needed
   - Detail formatting requirements for technical information
   - Include citations and reference standards
   - Specify any appendices or supplementary materials
   - Maintain documentation conventions from all plans
   - Specify any required attachments or supporting documents

6. Quality Assurance Elements:
   - Include all quality control procedures
   - Maintain quality assurance checkpoints
   - Preserve change control processes
   - Include batch release criteria
   - Specify stability and shelf-life considerations
   - Maintain monitoring and surveillance requirements

7. Risk Management:
   - Include all risk assessment requirements
   - Maintain mitigation strategies
   - Preserve safety considerations
   - Include contingency requirements
   - Specify monitoring and review processes
   - Maintain risk-benefit analyses

Before creating the final unified plan, complete the following steps inside <plan_analysis> tags in your thinking block:

1. Summarize key points from each plan
2. Identify unique elements and potential overlaps between plans
3. Draft a structure for the unified plan
4. Consider how to maintain ICH eCTD compliance in the unified plan

After your analysis, create the unified plan using the following structure:

<unified_plan>
1. [Main Section Title]
   1.1 [Subsection Title]
       1.1.1 [Sub-subsection Title]
             - [Requirement or specification]
             - [Technical parameter]
             - [Acceptance criteria]
   1.2 [Subsection Title]
       ...

2. [Main Section Title]
   ...

[Include all necessary sections to cover all requirements]

</unified_plan>

Ensure that your unified plan:
- Is structured hierarchically with numbered sections and subsections
- Clearly delineates requirements, specifications, and expectations
- Specifically notates required tables, figures, and data presentations
- Precisely lists technical parameters and acceptance criteria
- Clearly indicates required cross-references and dependencies
- Is organized logically following ICH eCTD structure

After creating the unified plan, verify it against this checklist:

1. Does the combined plan include ALL requirements from all <total_plans>{total_plans}</total_plans> plans?
2. Are ALL technical specifications preserved?
3. Are ALL validation and verification details maintained?
4. Are ALL cross-references and dependencies included?
5. Are ALL regulatory compliance elements preserved?
6. Does the plan follow ICH eCTD structure?
7. Is the plan logically organized with clear sections and subsections?
8. Are ALL required tables, figures, and data presentations specified?
9. Are ALL acceptance criteria and thresholds preserved?
10. Are ALL risk assessment and management requirements included?

If any item on the checklist is not fully met, revise the unified plan accordingly before finalizing it.

Your final output should consist only of the unified plan and should not duplicate or rehash any of the work you did in the plan analysis section."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2,
                extra_body={
                    # "return_reasoning": True
                }
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"combine_plans token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)
    
    async def create_plan_from_one_summary(summary_content, section_info):
        """Generate a plan from a single summary content."""
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert specializing in ICH eCTD guidelines and NDA preparation. Your task is to create a detailed, structured plan for an NDA section based on the provided information."""

        user_prompt = f"""
First, review the following section information and quality guideline summary:

<section_info>
Section: <section>{section_info.get('section', '')}</section>
Title: <title>{section_info.get('title', '')}</title>
Description: <description>{section_info.get('description', '')}</description>
</section_info>

Golden Instructions and Checkpoints:
<instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</instructions_and_checkpoints>

Formatting Instructions:
<formatting_instructions>
{section_info.get("formatting_instructions", "")}
</formatting_instructions>

Quality Guideline Summary:
<quality_guideline_summary>
{summary_content}
</quality_guideline_summary>

Your goal is to create a comprehensive blueprint for generating complete, regulatory-compliant content for this NDA section. Follow these critical requirements:

1. Ensure full regulatory compliance with ICH eCTD v3.2.2 guidelines.
2. Address all elements specified in the section description and reference text.
3. Follow all provided instructions and checkpoints.
4. Meet all requirements from the quality guideline summary.
5. Adhere to all formatting instructions.

Create a hierarchical, logical document structure with clearly numbered sections and subsections. Organize requirements by regulatory importance and group related items together.

Specify all technical parameters, required tests, analyses, and methodologies. Include precise acceptance criteria, thresholds, and validation requirements.

Detail all required tables, figures, data presentations, and formatting requirements for technical information. Specify any necessary appendices, supplementary materials, or supporting documents.

Ensure comprehensive coverage of every point in the quality guideline summary, including all technical specifications, validation details, risk assessment and management requirements, and safety and efficacy considerations.

Before providing your final plan, conduct your regulatory analysis inside <regulatory_analysis> tags in your thinking block. This will help guarantee a thorough and accurate plan. In your analysis:

1. List out key regulatory requirements from the provided information.
2. Break down technical parameters and methodologies mentioned.
3. Outline potential risks and their mitigation strategies.
4. List required tables, figures, and data presentations.

Your output should follow this format:

<regulatory_analysis>
[Your step-by-step analysis of the requirements and approach to creating the plan]
</regulatory_analysis>

<plan>
1. [Main Section Title]
   1.1. [Subsection Title]
        - [Specific requirement or content item]
        - [Technical parameter or acceptance criterion]
        - [Required table, figure, or data presentation]
   1.2. [Subsection Title]
        - ...

2. [Main Section Title]
   2.1. [Subsection Title]
        - ...

[Continue with all necessary sections and subsections]
</plan>

Ensure your plan is detailed enough to serve as a comprehensive blueprint for generating the complete NDA section content, addressing all regulatory requirements and technical details. Your final output should consist only of the plan and should not duplicate or rehash any of the work you did in the regulatory analysis."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2,
                extra_body={
                    # "return_reasoning": True
                }
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"create_plan_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    # Handle empty input
    if not individual_summaries:
        return ""
        
    # Handle single summary case
    if len(individual_summaries) == 1:
        return await create_plan_from_one_summary(individual_summaries[0], section_info)
    
    # First generate initial plans from each summary
    tasks = [create_plan_from_one_summary(summary, section_info) for summary in individual_summaries]
    current_plans = await asyncio.gather(*tasks)
    
    # Process plans iteratively, combining batches in parallel
    while len(current_plans) > 1:
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for plan in current_plans:
            plan_tokens = calculate_number_of_tokens(plan)
            
            # Check if this single plan exceeds the token limit
            if plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                print(f"Warning: Single plan has {plan_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}")
                # Force this large plan to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(plan)
                    current_token_count += plan_tokens
                    print(f"Forcing merge of oversized plan with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next plan
                    # to avoid it being processed alone repeatedly
                    current_batch = [plan]
                    current_token_count = plan_tokens
                    print(f"Starting new batch with oversized plan ({plan_tokens} tokens)")
            # If adding this plan would exceed the token limit, start a new batch
            elif current_batch and current_token_count + plan_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                batches.append(current_batch)
                current_batch = [plan]
                current_token_count = plan_tokens
            else:
                current_batch.append(plan)
                current_token_count += plan_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        # Ensure no batch has only one plan to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one plan left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        print(f"Created {len(batches)} batches for plan processing")
        for i, batch in enumerate(batches):
            total_tokens = sum(calculate_number_of_tokens(p) for p in batch)
            print(f"Batch {i+1} contains {len(batch)} plans with {total_tokens} tokens")
        
        # Process all batches in parallel
        tasks = [combine_plans(batch) for batch in batches]
        
        # Wait for all combinations to complete
        current_plans = await asyncio.gather(*tasks)

    print("create_incremental_plan tokens: ", calculate_number_of_tokens(current_plans[0]))
    
    return current_plans[0]

async def create_incremental_content(plan, section_info, input_chunks, critique_feedback=None):
    """
    Generate content incrementally using the plan created in Stage 1 and input document chunks.
    Uses batch processing to efficiently process input chunks and combine content.
    
    Args:
        plan: The comprehensive plan for the NDA section
        section_info: Dictionary containing section information
        input_chunks: List of input document chunks to use for content generation
        critique_feedback: Optional critique feedback from previous iterations to address missing content
        
    Returns:
        str: Generated NDA section content
    """
    
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def generate_content_from_chunks(plan, chunks, section_info, critique_feedback=None):
        """Generate content from a batch of input chunks using the plan."""
        total_chunks = len(chunks)
        print(f"\nGenerating content from {total_chunks} chunks:")
        
        total_tokens = sum(calculate_number_of_tokens(chunk.get('content', '')) for chunk in chunks)
        print(f"Total input chunk tokens: {total_tokens}")
        
        system_prompt = """You are an expert pharmaceutical regulatory documentation specialist tasked with generating high-quality, detailed, and regulatory-compliant content for a New Drug Application (NDA) section. Your expertise includes deep knowledge of ICH eCTD guidelines and NDA preparation. Your goal is to create content that is indistinguishable from a professionally written NDA submission."""

        # Prepare input chunks text
        input_chunks_text = "\n\n".join([f"Input Chunk {i+1}:<input_chunk_{i+1}>{chunk.get('content', '')}</input_chunk_{i+1}>" for i, chunk in enumerate(chunks)])

        # Prepare critique feedback section if available
        critique_section = ""
        if critique_feedback:
            critique_section = f"""
<critique_feedback>
Overall Rating: {critique_feedback.get('overall_rating', 'N/A')}/10

Critical Issues to Address:
<critical_issues>
{chr(10).join(f"- {issue}" for issue in critique_feedback.get('critical_issues', []))}
</critical_issues>

Required Improvements to Implement:
<required_improvements>
{chr(10).join(f"- {improvement}" for improvement in critique_feedback.get('required_improvements', []))}
</required_improvements>

Additional Recommendations to Consider:
<additional_recommendations>
{chr(10).join(f"- {recommendation}" for recommendation in critique_feedback.get('additional_recommendations', []))}
</additional_recommendations>
</critique_feedback>
"""

        user_prompt = f"""
Here is the essential information for the section you will be working on:

<section_info>
Section: <section>{section_info.get('section', '')}</section>
Title: <title>{section_info.get('title', '')}</title>
Description: <description>{section_info.get('description', '')}</description>
Enhanced Instructions and Checkpoints: <instructions_and_checkpoints>{section_info.get("enhanced_instructions_and_checkpoints", "")}</instructions_and_checkpoints>
Formatting Instructions: <formatting_instructions>{section_info.get("formatting_instructions", "")}</formatting_instructions>
</section_info>

The following detailed plan will serve as your primary guide for content generation. Use this plan to filter and organize the information from the input chunks:

<detailed_plan>
{plan}
</detailed_plan>
        
To improve the quality of your output, consider the following critique feedback from a previous version. Use this feedback to enhance your content, but do not reference it directly in your output:

{critique_section}

You will be provided with input document chunks. Each chunk will be presented in the following format:

<input_chunk>
{input_chunks_text}
</input_chunk>

CRITICAL INSTRUCTIONS:

1. Plan-Guided Content Generation:
   - Use the detailed plan as your primary filter for content selection.
   - Only include information from input chunks that directly addresses elements in the plan.
   - Discard any chunk content that does not align with specific plan requirements.
   - For chunks with mixed content, extract only the portions relevant to the plan.
   - Prioritize chunks containing technical specifications, validation data, regulatory compliance information, test results, and analytical methods mentioned in the plan.

2. Regulatory Compliance:
   - Adhere strictly to ICH eCTD v3.2.2 standards.
   - Address all requirements specified in the plan.
   - Follow all formatting instructions provided.
   - Ensure compliance with all enhanced instructions and checkpoints.

3. Table Tag Handling:
   - Preserve all table tags exactly as they appear in input chunks (e.g., `<TABLE_TAG_table_1>`, `<TABLE_TAG_table_2>`).
   - Do not modify, rename, or change table tags in any way.
   - Maintain the exact position and order of table tags as they appear in the input chunks.
   - Include table tags in the exact location where the table should appear in your content.
   - Use table summary information to write brief contextual statements about tables if needed, but always preserve the tags.

4. Figure Preservation:
   - Maintain exact figure numbers and captions as they appear in source documents.
   - Include all figures from the input source documents that are relevant to the plan.
   - Do not modify the content of any figures.

5. Content Quality:
   - Generate comprehensive, technically precise content.
   - Include all required technical details, parameters, and specifications as specified in the plan.
   - Provide detailed explanations and justifications for plan-required elements.
   - Use formal, professional regulatory language appropriate for an NDA submission.
   - Ensure content is scientifically accurate and technically sound.
   - Focus on depth rather than breadth - cover plan requirements thoroughly.

6. Documentation Standards:
   - Use clear, precise, and professional language.
   - Format content according to regulatory standards.
   - Provide detailed captions and explanations for figures.
   - Use consistent terminology throughout.
   - Follow professional technical writing standards.
   - Maintain logical flow and organization.

7. Narrative Style:
   - Write in a cohesive, flowing narrative style.
   - Connect technical information with clear transitions.
   - Provide context and background for technical details as required by the plan.
   - Explain the significance of findings and data relevant to plan requirements.
   - Ensure information is presented in a logical sequence following the plan structure.

8. Output Format:
   - Use markdown format without backticks or code blocks.
   - Do not include section headings.
   - Preserve table tags exactly as they appear in input chunks.
   - Maintain exact figure numbers and captions.

9. Prohibited Content:
   - Do not include any meta-commentary about the generation process or compliance verification.
   - Do not include verification checklists or statements about compliance with plans.
   - Do not include process-related commentary or formatting instructions.
   - Generate only the actual NDA content that would appear in the final submission.

Before generating the final content, please analyze the task and outline your approach in <task_breakdown> tags inside your thinking block. This breakdown should be thorough and can be quite long. Include the following steps:

1. Review the section information and detailed plan.
2. Identify key requirements and priorities from the plan.
3. Evaluate the critique feedback and determine how to address critical issues and required improvements.
4. Outline the main topics to be covered based on the plan.
5. Review input chunks and map relevant information to the outlined topics.
   - For each topic, list relevant chunk numbers and key points.
   - Note any technical specifications, validation data, or analytical methods that align with the plan.
6. Plan the logical flow of information to ensure a cohesive narrative.
7. Identify areas where technical depth needs to be emphasized.
8. Note any table tags and figure references that need to be preserved.
9. Double-check alignment with regulatory requirements and ICH eCTD v3.2.2 standards.
10. Outline a strategy for maintaining consistent terminology and professional language throughout.
11. Plan how to incorporate critique feedback into the content generation process.
12. Identify any potential gaps in information and how to address them within the constraints of the available input.

Now, please generate the detailed, regulatory-compliant content for this NDA section based on the provided plan, input chunks, and the analysis you've just completed. Remember to adhere strictly to all instructions and requirements outlined above. Your final output should consist only of the NDA content and should not duplicate or rehash any of the work you did in the task breakdown."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                extra_body={
                    # "return_reasoning": True
                }
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"generate_content_from_chunks token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    async def combine_content_sections(content_sections):
        """Combine multiple content sections while ensuring proper flow and consistency."""
        total_sections = len(content_sections)
        print(f"\nCombining {total_sections} content sections:")
        
        total_tokens = sum(calculate_number_of_tokens(content) for content in content_sections)
        print(f"Total content section tokens: {total_tokens}")
        
        system_prompt = """You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to combine sections of NDA content into a cohesive, unified document that maintains regulatory compliance, technical accuracy, and narrative flow."""

        user_prompt = f"""Here are the content sections you need to integrate:
<content_sections>
"""
        
        # Add each content section with a header
        for i, content in enumerate(content_sections, 1):
            user_prompt += f"""
CONTENT SECTION {i}:
<content_section_{i}>
{content}
</content_section_{i}>
"""

        user_prompt += f"""
</content_sections>
"""
        
        user_prompt += f"""
You will be combining {total_sections} sections in total.

Before you begin the integration process, please conduct a thorough analysis of the content and your approach. Conduct this analysis inside <integration_planning> tags in your thinking block.

In your integration planning:
1. List out each section's title and key topics.
2. Identify and list all key technical information from each section.
3. Note all table tags and figure references.
4. Identify potential areas of overlap or contradiction between sections.
5. Create a rough outline of the integrated document, ensuring a logical flow of information.
6. Plan the structure of your integrated document, ensuring a logical flow of information.
7. Outline your strategy for maintaining regulatory compliance and technical accuracy.
8. Describe how you will ensure narrative coherence across the combined sections.
9. Explain your approach to eliminating redundancies without losing information.
10. Detail your method for harmonizing terminology and phrasing.
11. Describe how you will double-check your work against the critical requirements.

After your analysis, proceed with combining the content sections. Your output must adhere to the following critical requirements:

1. Content Preservation (HIGHEST PRIORITY):
   - Preserve ALL technical information from all content sections.
   - Ensure NO technical details, data, or regulatory elements are lost.
   - Maintain ALL parameters, specifications, measurements, methodologies, approaches, validation details, risk assessments, control strategies, and compliance justifications.

2. No Meta-Commentary:
   - Generate ONLY the actual NDA content that would appear in the final submission.
   - DO NOT include any commentary about the combination process, verification checklists, or statements about compliance.

3. Table and Figure Handling:
   - PRESERVE ALL table tags EXACTLY as they appear (e.g., `<TABLE_TAG_table_1>`). DO NOT modify these in any way.
   - MAINTAIN EXACT figure numbers and captions. DO NOT rename or modify any figures.
   - Include ALL figures and table tags in their original positions.

4. Integration Quality and Narrative Coherence:
   - Create seamless transitions between combined content.
   - Eliminate redundancies without losing information.
   - Harmonize terminology and phrasing.
   - Ensure the document reads as a single, cohesive narrative with logical progression.

5. Regulatory Standards and Technical Accuracy:
   - Maintain ICH eCTD v3.2.2 compliance throughout.
   - Ensure all combined technical information is accurate and consistent.
   - Resolve any contradictions in technical specifications.
   - Preserve scientific accuracy in all explanations.

Output Format:
- Professional, regulatory-compliant content with seamless integration.
- Well-structured paragraphs with logical flow.
- Consistent formatting throughout.
- No section headings.
- Markdown format without backticks or code blocks.
- No other formatting (XML, HTML, plaintext) should be included.

The combined content should appear as if it were written as a single, cohesive document from the beginning, with no awkward transitions or inconsistencies in style, terminology, or technical approach.

After you've completed the integration, use <final_review> tags to conduct a final review, ensuring all critical requirements have been met.

Your final output should consist only of the integrated content and should not duplicate or rehash any of the work you did in the integration planning section."""

        # Wrap the API call with retry_on_empty_response
        async def make_api_call():
            response = await generator_client.chat.completions.create(
                model=GENERATOR_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2,
                extra_body={
                    # "return_reasoning": True
                }
            )

            system_tokens = calculate_number_of_tokens(system_prompt)
            user_tokens = calculate_number_of_tokens(user_prompt)
            output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
            total_tokens = system_tokens + user_tokens + output_tokens
            print(f"combine_content_sections token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            return response.choices[0].message.content

        # Use the retry wrapper to handle empty responses
        return await retry_on_empty_response(make_api_call)

    # Handle empty input
    if not input_chunks:
        return ""

    # Process input chunks in batches based on token count
    batch_contents = []
    
    # Create optimal batches based on token count
    batches = []
    current_batch = []
    current_token_count = 0
    
    for chunk in input_chunks:
        chunk_tokens = calculate_number_of_tokens(chunk.get('content', ''))
        
        # Check if this single chunk exceeds the token limit
        if chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
            print(f"Warning: Single chunk has {chunk_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}")
            # Force this large chunk to be merged with the current batch to avoid infinite loop
            if current_batch:
                # Add to current batch despite exceeding limit
                current_batch.append(chunk)
                current_token_count += chunk_tokens
                print(f"Forcing merge of oversized chunk with current batch (total tokens: {current_token_count})")
                # Finalize this batch
                batches.append(current_batch)
                current_batch = []
                current_token_count = 0
            else:
                # If current_batch is empty, we need to pair this with the next chunk
                # to avoid it being processed alone repeatedly
                current_batch = [chunk]
                current_token_count = chunk_tokens
                print(f"Starting new batch with oversized chunk ({chunk_tokens} tokens)")
        # If adding this chunk would exceed the token limit, start a new batch
        elif current_batch and current_token_count + chunk_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
            batches.append(current_batch)
            current_batch = [chunk]
            current_token_count = chunk_tokens
        else:
            current_batch.append(chunk)
            current_token_count += chunk_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    # Ensure no batch has only one chunk to avoid infinite loop
    # If we have a single-item batch, try to merge it with another batch
    final_batches = []
    single_item_batch = None
    
    for batch in batches:
        if len(batch) == 1 and single_item_batch is None:
            single_item_batch = batch
        elif len(batch) == 1 and single_item_batch is not None:
            # Merge two single-item batches
            merged_batch = single_item_batch + batch
            final_batches.append(merged_batch)
            single_item_batch = None
        else:
            if single_item_batch is not None:
                # Merge the single-item batch with this multi-item batch
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                final_batches.append(batch)
    
    # If we still have a single-item batch left, we need to handle it
    if single_item_batch is not None:
        if final_batches:
            # Merge with the last batch
            final_batches[-1].extend(single_item_batch)
        else:
            # This is the only batch, which means we have only one chunk left
            # This should not happen in normal operation, but just in case
            final_batches.append(single_item_batch)
    
    batches = final_batches
    
    print(f"Created {len(batches)} batches for chunk processing")
    for i, batch in enumerate(batches):
        print(f"Batch {i+1} contains {len(batch)} chunks with {sum(calculate_number_of_tokens(c.get('content', '')) for c in batch)} tokens")
    
        # Process all batches in parallel
    tasks = [generate_content_from_chunks(plan, batch, section_info, critique_feedback) for batch in batches]
    batch_contents = await asyncio.gather(*tasks)
    
    # If only one batch, return it directly
    if len(batch_contents) == 1:
        return batch_contents[0]
    
    # Combine batch contents iteratively
    while len(batch_contents) > 1:
        # Create optimal batches based on token count
        content_batches = []
        current_batch = []
        current_token_count = 0
        
        for content in batch_contents:
            content_tokens = calculate_number_of_tokens(content)
            
            # Check if this single content exceeds the token limit
            if content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                print(f"Warning: Single content has {content_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}")
                # Force this large content to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(content)
                    current_token_count += content_tokens
                    print(f"Forcing merge of oversized content with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    content_batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next content
                    # to avoid it being processed alone repeatedly
                    current_batch = [content]
                    current_token_count = content_tokens
                    print(f"Starting new batch with oversized content ({content_tokens} tokens)")
            # If adding this content would exceed the token limit, start a new batch
            elif current_batch and current_token_count + content_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                content_batches.append(current_batch)
                current_batch = [content]
                current_token_count = content_tokens
            else:
                current_batch.append(content)
                current_token_count += content_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            content_batches.append(current_batch)
        
        # Ensure no batch has only one content to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in content_batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one content left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        content_batches = final_batches
        
        print(f"Created {len(content_batches)} batches for content combining")
        for i, batch in enumerate(content_batches):
            total_tokens = sum(calculate_number_of_tokens(c) for c in batch)
            print(f"Batch {i+1} contains {len(batch)} content sections with {total_tokens} tokens")
        
        # Process all batches in parallel
        tasks = [combine_content_sections(batch) for batch in content_batches]
        
        # Wait for all combinations to complete
        batch_contents = await asyncio.gather(*tasks)

    print("create_incremental_content tokens: ", calculate_number_of_tokens(batch_contents[0]))
    
    return batch_contents[0]


# %%
async def refine_content(draft_content, section_info, evaluation_feedback=None):
    """
    Refine the draft content based on evaluation feedback and ensure it meets all requirements.
    Can use both the original draft content and previous output (if available) to make improvements.
    
    Args:
        draft_content: The draft content generated in Stage 2
        section_info: Dictionary containing section information
        evaluation_feedback: Evaluation feedback JSON (optional)
        
    Returns:
        str: Refined NDA section content
    """
    
    system_prompt = """You are an elite pharmaceutical regulatory documentation specialist with deep expertise in ICH eCTD guidelines and NDA preparation. Your task is to refine and perfect draft NDA section content based on evaluation feedback and ensure it fully meets all regulatory requirements."""

    # Prepare evaluation feedback text
    feedback_text = ""
    previous_output_text = ""
    
    if evaluation_feedback:
        # Check if we have previous output in the enhanced feedback
        if "previous_output" in evaluation_feedback:
            previous_output_text = f"""{evaluation_feedback.get('previous_output', '')}"""
            
        # First try to use the structured feedback if we have a valid rating and sufficient feedback
        has_valid_structured_feedback = (
            evaluation_feedback.get("overall_rating") is not None and
            (evaluation_feedback.get("key_strengths") or
             evaluation_feedback.get("critical_issues") or
             evaluation_feedback.get("required_improvements"))
        )
        
        if has_valid_structured_feedback:
            # Use structured feedback
            feedback_text = f"""
<evaluation_feedback>
Overall Rating: <overall_rating>{evaluation_feedback.get('overall_rating', 'N/A')}/10</overall_rating>

Key Strengths:
<key_strengths>
{chr(10).join(f"- {strength}" for strength in evaluation_feedback.get('key_strengths', []))}
</key_strengths>

Critical Issues to Address:
<critical_issues>
{chr(10).join(f"- {issue}" for issue in evaluation_feedback.get('critical_issues', []))}
</critical_issues>

Required Improvements:
<required_improvements>
{chr(10).join(f"- {improvement}" for improvement in evaluation_feedback.get('required_improvements', []))}
</required_improvements>

Additional Recommendations:
<additional_recommendations>
{chr(10).join(f"- {recommendation}" for recommendation in evaluation_feedback.get('additional_recommendations', []))}
</additional_recommendations>
</evaluation_feedback>
"""
    
    user_prompt = f"""First, review the following information carefully:
    
1. Draft Content to Refine:
<draft_content>
{draft_content}
</draft_content>

2. Section Information:
<section_info>
Section: <section>{section_info.get('section', '')}</section>
Title: <title>{section_info.get('title', '')}</title>
Description: <description>{section_info.get('description', '')}</description>
Enhanced Instructions and Checkpoints: <instructions>{section_info.get("enhanced_instructions_and_checkpoints", "")}</instructions>
Formatting Instructions: <formatting>{section_info.get("formatting_instructions", "")}</formatting>
</section_info>
    
3. Evaluation Feedback:
{feedback_text}

4. Previous Output (that feedback applies to):
<previous_output>
{previous_output_text}
</previous_output>

Now, please follow these instructions to refine and perfect the draft content:

1. Analyze the Information:
   Begin by analyzing the provided information. Consider how to address all issues, implement improvements, and enhance the content while maintaining compliance with ICH eCTD v3.2.2 standards. Develop your refinement plan inside <refinement_plan> tags in your thinking block.

2. Content Refinement:
   a. Address all critical issues identified in the evaluation feedback.
   b. Implement all required improvements.
   c. Follow additional recommendations where applicable.
   d. Maintain and enhance the identified strengths.
   e. Ensure the refined content addresses all evaluation concerns.
   f. Improve technical precision, clarity, and explanations.
   g. Strengthen scientific rationale and justifications.
   h. Enhance data presentation and interpretation.
   i. Improve narrative flow and cohesion.
   j. Ensure comprehensive treatment of all required topics.

3. Regulatory Compliance:
   a. Strictly adhere to ICH eCTD v3.2.2 standards.
   b. Address all requirements from the section description.
   c. Follow all reference text specifications.
   d. Comply with all formatting instructions.
   e. Ensure all technical content meets regulatory standards.
   f. Verify all cross-references and dependencies are correct.

4. Documentation Standards:
   a. Use professional language and terminology consistently.
   b. Improve formatting and structure as specified.
   c. Enhance references to tables and figures.
   d. Strengthen justifications for critical decisions.
   e. Improve documentation of assumptions and limitations.
   f. Enhance overall technical documentation quality.

5. Table and Figure Handling:
   a. Maintain exact table references in the format "Table {{X}}: {{table heading}}".
   b. Do not modify or remove any `<TABLE_TAG_table_x>` tags.
   c. Keep all table tags in their original position in the text.
   d. Do not attempt to construct or format the tables yourself.
   e. Use provided table summaries to inform your writing, but maintain the table placeholders exactly as they appear.
   f. Maintain exact figure numbers and captions as they appear in the source documents.
   g. Do not rename any tables or figures.
   h. Include all tables and figures that appear in the input source documents or draft content.
   i. Do not modify the content of any figures.
   j. Include references to all tables and figures in your text.

6. Output Format:
   a. Generate professional, regulatory-compliant content with all issues resolved.
   b. Use well-structured paragraphs with improved logical flow.
   c. Do not include section headings.
   d. Use markdown format without backticks or code blocks.
   e. Do not include any other formatting (XML, HTML, plaintext).
   f. Preserve all `<TABLE_TAG_table_x>` tags exactly as they appear.

7. Critical Restrictions:
   a. Do not include any commentary about how you addressed the feedback.
   b. Do not include verification checklists or statements about compliance.
   c. Do not include meta-statements like "This addresses the previous critique" or "Based on feedback".
   d. Do not include process-related commentary.
   e. Do not include formatting instructions or comments about document structure in the output.
   f. Do not reference the feedback, plans, or improvement process in your output.
   g. Generate only the actual NDA content that would appear in the final submission.
   h. Do not include any self-referential statements about the refinement process.

After completing your refinement plan, generate the refined content that represents a significant improvement over previous versions, addressing all feedback points while maintaining the identified strengths. Your output should be indistinguishable from content written directly for an NDA submission, with no process commentary. Your final output should consist only of the refined content and should not duplicate or rehash any of the work you did in the refinement plan."""

    # Wrap the API call with retry_on_empty_response
    async def make_api_call():
        response = await generator_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3,
            extra_body={
                # "return_reasoning": True
            }
        )

        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)
        output_tokens = calculate_number_of_tokens(response.choices[0].message.content)
        total_tokens = system_tokens + user_tokens + output_tokens
        print(f"refine_content token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        
        return response.choices[0].message.content

    # Use the retry wrapper to handle empty responses
    return await retry_on_empty_response(make_api_call)


# %%
async def optimized_nda_output_generation(section_info, critique_feedback=None, reuse_plan=None):
    """
    Generate NDA output using a multi-stage, batch processing approach to reduce context size and optimize performance.
    
    Args:
        section_info: Dictionary containing section information
        critique_feedback: Optional critique feedback from previous iterations to address missing content
        reuse_plan: Optional pre-generated plan to reuse (skips Stage 1 for performance)
        
    Returns:
        tuple: (Generated NDA section content, plan used) if reuse_plan is None, else just content
    """
    print(f"Starting optimized NDA output generation for section {section_info.get('section', 'unknown')}")
    
    # Stage 1: Create plan from quality guideline summaries (skip if reusing plan)
    if reuse_plan is not None:
        print("Stage 1: Reusing existing plan (performance optimization)")
        plan = reuse_plan
    else:
        individual_summaries = []
        if "relevant_individual_summaries" in section_info:
            individual_summaries = [summary.get("content", "") for summary in section_info.get("relevant_individual_summaries", [])]
        
        # Fall back to combined summary if individual summaries aren't available
        if not individual_summaries and "quality_guideline_combined_summary" in section_info:
            individual_summaries = [section_info["quality_guideline_combined_summary"]]
        
        print(f"Stage 1: Creating incremental plan from {len(individual_summaries)} individual summaries")
        plan = await create_incremental_plan(section_info, individual_summaries)

        print("plan: ", plan)
    
    # Stage 2: Generate content from input chunks
    input_chunks = section_info.get("relevant_input_chunks", [])
    print(f"Stage 2: Creating incremental content from {len(input_chunks)} input chunks using the plan")
    draft_content = await create_incremental_content(plan, section_info, input_chunks, critique_feedback)

    print("draft_content: ", draft_content)
    
    # Stage 3: Refine content based on evaluation feedback
    evaluation_feedback = section_info.get("nda_output_evaluation_json", None)
    
    if evaluation_feedback:
        print(f"Stage 3: Refining content based on evaluation feedback")
        refined_content = await refine_content(draft_content, section_info, evaluation_feedback)

        print("refined_content: ", refined_content)

        # Return content and plan if this is the first iteration, else just content
        if reuse_plan is None:
            return refined_content, plan
        else:
            return refined_content
    else:
        print(f"No evaluation feedback available, returning draft content")
        # Return content and plan if this is the first iteration, else just content
        if reuse_plan is None:
            return draft_content, plan
        else:
            return draft_content
    
async def optimized_nda_critique(section_info, nda_output):
    """
    Generate a comprehensive NDA critique using a batch processing approach with individual quality guideline summaries.
    Uses parallel processing to generate critiques from multiple summaries simultaneously.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    print(f"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}")
    
    # Extract individual summaries
    individual_summaries = []
    if "relevant_individual_summaries" in section_info:
        individual_summaries = [summary.get("content", "") for summary in section_info.get("relevant_individual_summaries", [])]
    
    # Fall back to combined summary if individual summaries aren't available
    if not individual_summaries and "quality_guideline_combined_summary" in section_info:
        individual_summaries = [section_info["quality_guideline_combined_summary"]]
    
    # If no summaries available, generate critique based only on section info
    if not individual_summaries:
        print("No quality guideline summaries available, generating critique based only on section info")
        return await generate_critique_without_guidelines(section_info, nda_output)
    
    print(f"Generating individual critiques from {len(individual_summaries)} quality guideline summaries in parallel")
    
    # Generate individual critiques in parallel using asyncio.gather
    tasks = [generate_critique_from_one_summary(summary, section_info, nda_output) 
             for summary in individual_summaries]
    individual_critiques = await asyncio.gather(*tasks)
    
    # If only one critique, return it directly
    if len(individual_critiques) == 1:
        return individual_critiques[0]
    
    # Combine critiques using batch processing
    print("Combining individual critiques using batch processing")
    return await combine_critiques_incrementally(individual_critiques, section_info)


async def generate_critique_from_one_summary(summary_content, section_info, nda_output):
    """
    Generate a critique of NDA output based on a single quality guideline summary.
    
    Args:
        summary_content: Content of a single quality guideline summary
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    system_prompt = """You are a highly experienced regulatory compliance expert and quality assurance specialist in the pharmaceutical industry. Your task is to critically evaluate an NDA (New Drug Application) section output against specific quality guidelines and ICH eCTD v3.2.2 requirements. Your evaluation must be thorough, precise, and adhere strictly to the provided guidelines."""

    user_prompt = f"""First, review the following enhanced instructions and checkpoints for this section:

<enhanced_instructions_and_checkpoints>
{section_info.get("enhanced_instructions_and_checkpoints", "")}
</enhanced_instructions_and_checkpoints>

Next, thoroughly review the quality guideline summary that you will use as the basis for your evaluation:

<quality_guideline_summary>
{summary_content}
</quality_guideline_summary>

Now, you will evaluate the following NDA output against the provided quality guideline summary and ICH eCTD requirements:

<nda_output>
{nda_output}
</nda_output>

Now, review the following section information:

<section_info>
Section: {section_info.get('section', '')}
Title: {section_info.get('title', '')}
Description: {section_info.get('description', '')}
</section_info>

Take note of the specific formatting instructions:

<formatting_instructions>
{section_info.get("formatting_instructions", "")}
</formatting_instructions>

Conduct your evaluation by following these steps:

1. Golden Instructions and Formatting Compliance:
   - Strictly evaluate adherence to Golden Instructions and Checkpoints
   - Verify exact compliance with Formatting Instructions
   - Consider non-compliance with these instructions as critical issues

2. Table and Figure Preservation Assessment:
   - Verify that ALL table placeholders and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
   - Confirm that the exact position and order of all "`<TABLE_TAG_table_x>`" tags is maintained in the text
   - Check that NO attempt has been made to construct or format the tables
   - Verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents

3. Content Appropriateness and Relevance Assessment:
   - Verify that ALL content directly relates to and supports the quality guideline summary requirements
   - Identify any inappropriate tables, figures, or content that should NOT be in this section
   - Check if any content contradicts or conflicts with the quality guideline summary

4. ICH eCTD v3.2.2 Compliance Assessment:
   - Evaluate adherence to eCTD structure and format
   - Verify compliance with section description and reference text
   - Ensure all requirements from the ICH eCTD guidelines are met

5. Quality Guidelines Summary Coverage and Alignment:
   - Systematically verify all key requirements from the quality guideline summary are addressed
   - Assess if the depth of coverage matches quality guideline expectations

6. Technical Accuracy and Completeness:
   - Verify technical specifications align with quality guideline requirements
   - Check parameter accuracy against quality guideline standards

7. Hallucination and Fabrication Detection:
   - Identify any content not supported by the quality guideline summary
   - Check for fabricated data or claims not found in quality guidelines

8. Quality Guideline Gap Analysis:
   - Identify any quality guideline requirements completely missing from output
   - Flag incomplete implementation of quality guideline recommendations

For each step of your evaluation, use <evaluation_process> tags inside your thinking block to show your thought process and findings. In this section:

- List key points from the quality guideline summary
- Identify and quote relevant parts of the NDA output for each evaluation step
- For each evaluation step, consider both compliant and non-compliant aspects

After completing your analysis, provide a summary of your evaluation using the following structure:

<evaluation_summary>
  <overall_rating>
    [Provide a numerical rating from 1-10, where 1-3 indicates major deficiencies and significant non-compliance, 4-6 indicates moderate issues and partial compliance, 7-8 indicates minor issues and mostly compliant, and 9-10 indicates excellent compliance with minimal improvements needed]
  </overall_rating>
  
  <key_findings>
    [List the most important findings from your evaluation, both positive and negative]
  </key_findings>
  
  <critical_issues>
    [Highlight any critical issues or non-compliance that require immediate attention]
  </critical_issues>
  
  <recommendations>
    [Provide specific, actionable recommendations for improvement, focusing on aligning the NDA output with the quality guideline summary]
  </recommendations>
</evaluation_summary>

Remember to maintain a strict, critical perspective throughout your evaluation, ensuring that every aspect of the NDA output is scrutinized against the provided guidelines and requirements.

Your final output should consist only of the evaluation summary and should not duplicate or rehash any of the work you did in the evaluation process section."""

    system_tokens = calculate_number_of_tokens(system_prompt)
    user_tokens = calculate_number_of_tokens(user_prompt)

    critique_evaluation_agent = Agent(
        model=critique_model,
        system_prompt=system_prompt,
        output_type=CritiqueEvaluation,
        model_settings={
            "temperature": 0.2,
            "extra_body": {
                # "return_reasoning": True
            }
        },
        retries=3
    )

    response = await critique_evaluation_agent.run(user_prompt)

    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
    total_tokens = system_tokens + user_tokens + output_tokens

    print(f"generate_critique_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return response.output


async def generate_critique_without_guidelines(section_info, nda_output):
    """
    Generate a critique of NDA output based only on section information without quality guidelines.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        
    Returns:
        CritiqueEvaluation: Structured evaluation object
    """
    system_prompt = """You are a highly skilled regulatory compliance expert and quality assurance specialist for pharmaceutical documentation. Your task is to critically evaluate an NDA (New Drug Application) section output against ICH eCTD v3.2.2 requirements and provided section information."""

    user_prompt = f"""
    
First, review the following NDA output and section information:

<nda_output>
{nda_output}
</nda_output>

<section_info>
Section: {section_info.get('section', '')}
Title: {section_info.get('title', '')}
Description: {section_info.get('description', '')}
Golden Instructions and Checkpoints: {section_info.get("enhanced_instructions_and_checkpoints", "")}
Formatting Instructions: {section_info.get("formatting_instructions", "")}
</section_info>

Your evaluation should cover the following categories, in order of priority:

1. Golden Instructions and Formatting Compliance
2. Table and Figure Preservation Assessment
3. ICH eCTD v3.2.2 Compliance Assessment
4. Technical Accuracy and Completeness
5. Content Quality Assessment
6. Hallucination Detection
7. Regulatory Compliance
8. Documentation Standards
9. Improvement Areas
10. Table and Section Handling Evaluation

For each category, first list the specific elements you're looking for, then wrap your evaluation inside <detailed_assessment> tags to show your thought process and detailed assessment. For the Golden Instructions and Formatting Compliance category, quote relevant parts of the instructions and check them off as you go. For the Table and Figure Preservation Assessment, count and list each table and figure placeholder you find. After evaluating all categories, provide a numerical rating (1-10) and a summary of your findings.

Critical Requirements:
- Strictly adhere to Golden Instructions and Checkpoints.
- Verify exact compliance with Formatting Instructions.
- Ensure all table placeholders (in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`") are preserved exactly.
- Maintain the position and order of all table tags.
- Confirm that no attempt has been made to construct tables.
- Verify that all figure numbers and captions are preserved exactly.
- Check that all tables and figures from source documents are included.
- Assess technical accuracy, completeness, and documentation quality.
- Identify any hallucinations or unsupported claims.

Rating Criteria:
1-3: Major deficiencies, significant non-compliance
4-6: Moderate issues, partial compliance
7-8: Minor issues, mostly compliant
9-10: Excellent compliance, minimal improvements needed

Provide your evaluation in the given format.

Remember to be thorough, critical, and provide specific, actionable feedback for improvement in each category. Your final output should consist only of the evaluation in the format specified above and should not duplicate or rehash any of the work you did in the detailed assessment sections."""

    system_tokens = calculate_number_of_tokens(system_prompt)
    user_tokens = calculate_number_of_tokens(user_prompt)

    critique_evaluation_agent = Agent(
        model=critique_model,
        system_prompt=system_prompt,
        output_type=CritiqueEvaluation,
        model_settings={
            "temperature": 0.2,
            "extra_body": {
                # "return_reasoning": True
            }
        },
        retries=3
    )

    response = await critique_evaluation_agent.run(user_prompt)

    output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
    total_tokens = system_tokens + user_tokens + output_tokens

    print(f"generate_critique_without_guidelines token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

    return response.output


async def combine_critiques_incrementally(critiques, section_info):
    """
    Combine multiple critiques incrementally using a batch processing approach.
    
    Args:
        critiques: List of individual critiques to combine
        section_info: Dictionary containing section information
        
    Returns:
        str: Comprehensive combined critique
    """
    # Maximum token limit for input (keeping some buffer for the prompt and output)
    
    async def combine_critiques_batch(critiques_batch):
        """Helper function to combine multiple critiques in a batch."""
        total_critiques = len(critiques_batch)
        print(f"\nCombining {total_critiques} critiques:")
        
        total_tokens = sum(calculate_number_of_tokens(critique.model_dump_json()) for critique in critiques_batch)
        print(f"Total input tokens: {total_tokens}")
        
        system_prompt = """You are a senior pharmaceutical regulatory documentation expert with specialized expertise in critiquing NDA (New Drug Application) submissions. Your task is to combine multiple separate evaluations of an NDA section into one comprehensive, unified critique that captures all key insights and maintains a consistent, thorough evaluation structure."""

        # Convert CritiqueEvaluation objects to text format for processing
        critique_texts = []
        for critique in critiques_batch:
            if isinstance(critique, CritiqueEvaluation):
                # Convert CritiqueEvaluation to text format
                critique_text = f"""Overall Rating: {critique.overall_rating}

Key Strengths:
{chr(10).join(f"- {strength}" for strength in critique.key_strengths)}

Critical Issues:
{chr(10).join(f"- {issue}" for issue in critique.critical_issues)}

Required Improvements:
{chr(10).join(f"- {improvement}" for improvement in critique.required_improvements)}

Additional Recommendations:
{chr(10).join(f"- {recommendation}" for recommendation in critique.additional_recommendations)}"""
                critique_texts.append(critique_text)
            else:
                # Handle legacy text format
                critique_texts.append(str(critique))

        user_prompt = f"""Here are the evaluations you need to combine:
<evaluations>"""
        
        # Add each critique with a header
        for i, critique_text in enumerate(critique_texts, 1):
            user_prompt += f"""

EVALUATION {i}:
<evaluation_{i}>
{critique_text}
</evaluation_{i}>
"""
        
        user_prompt += f"""
{total_critiques} evaluations will be provided above. Each evaluation is labeled as EVALUATION {{i}}, where i is the evaluation number.
</evaluations>

Please follow these steps to create a combined critique:

1. Carefully read and analyze all evaluations.
2. For each evaluation, extract and categorize:
   - Critical issues
   - Required improvements
   - Key strengths
   - Additional recommendations
   - Table and figure information
   - Original rating
3. Combine all extracted information, eliminating redundancies while ensuring no important points are lost.
4. Group related issues and improvements together.
5. Resolve any contradictions between the critiques.
6. Organize feedback in order of regulatory importance.
7. Verify table and figure preservation:
   - Ensure ALL table placeholders and tags are preserved EXACTLY in the format "Table X: table heading" followed by "`<TABLE_TAG_table_x>`"
   - Maintain the exact position and order of all `<TABLE_TAG_table_x>` tags in the text
   - Confirm that NO attempt has been made to construct or format the tables
   - Verify that ALL figure numbers and captions are preserved EXACTLY as they appear in source documents
   - Check that all tables and figures are properly referenced in the text
8. Determine a balanced overall rating considering all original ratings.
9. Write a comprehensive critique that incorporates all key points while maintaining a cohesive structure.
10. Review the combined critique for consistency, accuracy, and adherence to ICH eCTD requirements.

Before writing the final combined critique, wrap your analysis inside <critique_analysis> tags in your thinking block. Your analysis should show your thought process and how you're addressing each of the above steps. Pay particular attention to how you're preserving critical information and maintaining accuracy. Include the following in your analysis:

- List out critical issues, required improvements, key strengths, and additional recommendations from each evaluation
- Explicitly note any contradictions between evaluations
- List out all table and figure references
- Propose an overall rating based on individual ratings

After your analysis, present your final combined critique within <combined_critique> tags. The combined critique should:
- Preserve ALL critical issues, required improvements, key strengths, and additional recommendations from all original critiques
- Maintain the most detailed and specific feedback
- Use consistent terminology and phrasing
- Maintain a formal, professional evaluation tone
- Ensure all feedback is specific and actionable
- Accurately reflect the combined feedback in the final rating

Remember, the preservation of table and figure information is of HIGHEST PRIORITY. Any alteration, removal, or change in position of table tags should be considered a CRITICAL issue.

Your final output should consist only of the combined critique and should not duplicate or rehash any of the work you did in the critique analysis."""

        system_tokens = calculate_number_of_tokens(system_prompt)
        user_tokens = calculate_number_of_tokens(user_prompt)

        critique_evaluation_agent = Agent(
            model=critique_model,
            system_prompt=system_prompt,
            output_type=CritiqueEvaluation,
            model_settings={
                "temperature": 0.2,
                "extra_body": {
                    # "return_reasoning": True
                }
            },
            retries=3
        )

        response = await critique_evaluation_agent.run(user_prompt)

        output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        total_tokens = system_tokens + user_tokens + output_tokens

        print(f"combine_critiques_incrementally token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        return response.output
    
    # Handle base cases
    if not critiques:
        return ""
    
    if len(critiques) == 1:
        return critiques[0]
    
    # Process critiques iteratively, combining in batches
    current_critiques = critiques.copy()
    
    while len(current_critiques) > 1:
        # Create optimal batches based on token count
        batches = []
        current_batch = []
        current_token_count = 0
        
        for critique in current_critiques:
            critique_tokens = calculate_number_of_tokens(critique.model_dump_json())
            
            # Check if this single critique exceeds the token limit
            if critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                print(f"Warning: Single critique has {critique_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT}")
                # Force this large critique to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(critique)
                    current_token_count += critique_tokens
                    print(f"Forcing merge of oversized critique with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next critique
                    # to avoid it being processed alone repeatedly
                    current_batch = [critique]
                    current_token_count = critique_tokens
                    print(f"Starting new batch with oversized critique ({critique_tokens} tokens)")
            # If adding this critique would exceed the token limit, start a new batch
            elif current_batch and current_token_count + critique_tokens > MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT:
                batches.append(current_batch)
                current_batch = [critique]
                current_token_count = critique_tokens
            else:
                current_batch.append(critique)
                current_token_count += critique_tokens
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        # Ensure no batch has only one critique to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one critique left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        print(f"Created {len(batches)} batches for critique combining")
        for i, batch in enumerate(batches):
            total_tokens = sum(calculate_number_of_tokens(c.model_dump_json()) for c in batch)
            print(f"Batch {i+1} contains {len(batch)} critiques with {total_tokens} tokens")
        
        # Process all batches in parallel
        tasks = [combine_critiques_batch(batch) for batch in batches]
        
        # Wait for all combinations to complete
        current_critiques = await asyncio.gather(*tasks)
    
    print(f"combine_critiques_incrementally tokens: {calculate_number_of_tokens(current_critiques[0].model_dump_json())}")
    return current_critiques[0]

# %%
async def optimized_nda_critique_with_parsing(section_info, nda_output, max_retries=3):
    """
    Generate a comprehensive NDA critique using structured output.
    
    Args:
        section_info: Dictionary containing section information
        nda_output: The NDA output to critique
        max_retries: Maximum number of retry attempts for failures
        
    Returns:
        Tuple[str, Dict]: Raw critique text representation and structured evaluation dict
    """
    print(f"Starting optimized NDA critique for section {section_info.get('section', 'unknown')}")
    
    for attempt in range(max_retries):
        try:
            # Generate the critique using structured output
            critique_evaluation = await optimized_nda_critique(section_info, nda_output)
            
            # Convert CritiqueEvaluation to text representation for compatibility
            critique_text = f"""1. Overall Rating:
   {critique_evaluation.overall_rating}

2. Key Strengths:
{chr(10).join(f"   - {strength}" for strength in critique_evaluation.key_strengths)}

3. Critical Issues:
{chr(10).join(f"   - {issue}" for issue in critique_evaluation.critical_issues)}

4. Required Improvements:
{chr(10).join(f"   - {improvement}" for improvement in critique_evaluation.required_improvements)}

5. Additional Recommendations:
{chr(10).join(f"   - {recommendation}" for recommendation in critique_evaluation.additional_recommendations)}"""
            
            # Convert to dict for compatibility
            evaluation_dict = {
                "overall_rating": critique_evaluation.overall_rating,
                "key_strengths": critique_evaluation.key_strengths,
                "critical_issues": critique_evaluation.critical_issues,
                "required_improvements": critique_evaluation.required_improvements,
                "additional_recommendations": critique_evaluation.additional_recommendations
            }
            
            print(f"Successfully generated critique with overall rating: {critique_evaluation.overall_rating}")
            return critique_text, evaluation_dict
            
        except Exception as e:
            print(f"Error in critique generation (attempt {attempt+1}/{max_retries}): {str(e)}")
            if attempt == max_retries - 1:
                # Last attempt, return a default response
                default_dict = {
                    "overall_rating": 5.0,  # Middle value as default
                    "key_strengths": ["Content provides basic information"],
                    "critical_issues": ["Unable to properly evaluate the content"],
                    "required_improvements": ["Review all regulatory requirements"],
                    "additional_recommendations": ["Ensure compliance with ICH eCTD guidelines"]
                }
                default_text = """1. Overall Rating:
   5.0

2. Key Strengths:
   - Content provides basic information

3. Critical Issues:
   - Unable to properly evaluate the content

4. Required Improvements:
   - Review all regulatory requirements

5. Additional Recommendations:
   - Ensure compliance with ICH eCTD guidelines"""
                print("Exhausted all retry attempts, returning default evaluation")
                return default_text, default_dict
    
    # This should not be reached due to the return in the last attempt, but just in case
    default_dict = {
        "overall_rating": 5.0,
        "key_strengths": ["Default evaluation"],
        "critical_issues": ["Parsing error occurred"],
        "required_improvements": ["Review output format"],
        "additional_recommendations": ["Ensure critique follows expected format"]
    }
    default_text = "Error: Unexpected execution path"
    return default_text, default_dict


# %%
async def rate_nda_output_and_self_improve(section_info):
    """
    Generate NDA output, evaluate it, and improve it based on feedback.
    Uses an optimized approach where the plan is generated once and reused
    in subsequent iterations for improved performance.
    
    Args:
        section_info: Dictionary containing section information
        
    Returns:
        Dict: Updated section information with NDA output and evaluation
    """
    overall_rating = 0
    counter = 0
    answers_list = []

    # Generate plan and content for the first iteration, save the plan for reuse
    original_nda_output, reusable_plan = await optimized_nda_output_generation(section_info)        
    current_nda_output = original_nda_output
    
    # Use the robust critique function that handles parsing errors
    nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)
    
    section_info["nda_output_evaluation_json"] = nda_output_evaluation_json
    section_info["nda_output_evaluation_text"] = nda_output_evaluation  # Save the raw critique text
    overall_rating = nda_output_evaluation_json["overall_rating"]  # Now guaranteed to be a valid number
    counter += 1

    answers_list.append(
        {
            "nda_output": current_nda_output,
            "nda_output_evaluation_json": nda_output_evaluation_json,
            "nda_output_evaluation_text": nda_output_evaluation,  # Include raw critique text
            "overall_rating": overall_rating
        }
    )

    print(f"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}")

    # Regenerate content in subsequent iterations using feedback and reusing the plan for performance
    while(overall_rating < 9 and counter < 3):
        # Create an enhanced feedback object that includes the previous output
        enhanced_feedback = nda_output_evaluation_json.copy()
        enhanced_feedback["previous_output"] = current_nda_output
        enhanced_feedback["evaluation_text"] = nda_output_evaluation  # Include the raw critique text
        
        # Regenerate content using critique feedback and reusing the plan (performance optimization)
        print(f"Regenerating content with critique feedback and reused plan to address issues (rating: {overall_rating})")
        regenerated_nda_output = await optimized_nda_output_generation(section_info, enhanced_feedback, reusable_plan)

        print("regenerated_nda_output: ", regenerated_nda_output)

        current_nda_output = regenerated_nda_output
        
        # Use the robust critique function that handles parsing errors
        nda_output_evaluation, nda_output_evaluation_json = await optimized_nda_critique_with_parsing(section_info, current_nda_output)
        
        section_info["nda_output_evaluation_json"] = nda_output_evaluation_json
        section_info["nda_output_evaluation_text"] = nda_output_evaluation  # Save the raw critique text
        overall_rating = nda_output_evaluation_json["overall_rating"]  # Now guaranteed to be a valid number
        counter += 1

        answers_list.append(
            {
                "nda_output": current_nda_output,
                "nda_output_evaluation_json": nda_output_evaluation_json,
                "nda_output_evaluation_text": nda_output_evaluation,  # Include raw critique text
                "overall_rating": overall_rating
            }
        )

        print(f"Section {section_info.get('section', 'unknown')}, counter {counter}, rating {overall_rating}")

    # Select best answer based on overall rating
    best_answer = max(answers_list, key=lambda x: x["overall_rating"])
    section_info["nda_output"] = best_answer["nda_output"]
    section_info["nda_output_evaluation_json"] = best_answer["nda_output_evaluation_json"]
    section_info["nda_output_evaluation_text"] = best_answer["nda_output_evaluation_text"]  # Save the raw critique text

    print(f"Selecting best answer for section {section_info.get('section', 'unknown')} with rating {best_answer['overall_rating']}")

    return section_info


# %%
async def generate_nda_for_each_section(json_obj):
    """
    Recursively process a JSON object and generate NDA output for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with NDA outputs
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs NDA output generation
            if (obj and "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        print(f"Generating NDA output for section {section.get('section', 'unknown')}")
        
        # Generate NDA output for this section
        processed_section = await rate_nda_output_and_self_improve(section)
        
        print(f"Completed NDA output generation for section {section.get('section', 'unknown')}")
        
        return processed_section
    
    # Process all sections in parallel
    if sections_to_process:
        processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        for i, section in enumerate(sections_to_process):
            section.update(processed_sections[i])

        # for section in sections_to_process:
        #     processed_section = await process_section(section)
        #     section.update(processed_section)
    
    return json_obj


# %%
# Process the document to find relevant input documents
nda_output_json = await generate_nda_for_each_section(processed_doc_with_input_docs)

# Save the results to a JSON file
write_to_json(nda_output_json, "nda_output_json.json")


# %%
import pypandoc

def format_answer(json_obj, formatted_text="", depth=1):
    """
    Recursively formats the answer JSON into a structured Markdown document.
    Uses '#' for headings based on depth.
    """
    if isinstance(json_obj, dict):
        if "section" in json_obj and "title" in json_obj:
            formatted_text += f"{'#' * (depth)} {json_obj['section']} {json_obj['title']}\n\n"
            if "nda_output" in json_obj:
                formatted_text += f"{json_obj['nda_output']}\n\n"

        for key, value in json_obj.items():
            if isinstance(value, dict):
                formatted_text = format_answer(value, formatted_text, depth + 1)  # Increase depth

    return formatted_text

def save_answer_file(json_obj, filename="formatted_answer.md"):
    """
    Saves the formatted answer text to a file.
    """
    formatted_text = format_answer(json_obj)
    
    # Replace table tags with backtick-enclosed versions
    import re
    # formatted_text = re.sub(r'<TABLE_TAG_table_(\d+)>', r'`<TABLE_TAG_table_\1>`', formatted_text)
    
    with open(filename, "w", encoding="utf-8") as file:
        file.write(formatted_text)
    print(f"Formatted answer saved to {filename}")

save_answer_file(nda_output_json)


# %%
pypandoc.convert_file("formatted_answer.md", 'docx', outputfile="formatted_answer.docx")


# %%
from docx import Document
from docx.document import Document as DocumentObject
from docx.shared import Inches
from copy import deepcopy
import os
import shutil
import zipfile
from docx.oxml.ns import qn
from docx.table import Table
from docx.section import Paragraph
from typing import List
import json
from docx.oxml import OxmlElement

def copy_page_setup(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy page setup (size, orientation, margins) from source to target document"""
    source_section = source_doc.sections[0]
    target_section = target_doc.sections[0]
    
    # Copy page size and orientation
    target_section.page_width = source_section.page_width
    target_section.page_height = source_section.page_height
    target_section.orientation = source_section.orientation
    
    # Copy margins
    target_section.top_margin = source_section.top_margin
    target_section.bottom_margin = source_section.bottom_margin
    target_section.left_margin = source_section.left_margin
    target_section.right_margin = source_section.right_margin
    
    print(f"📏 Copied page setup: {source_section.page_width} x {source_section.page_height}")

def copy_table_styles_completely(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy complete table style definitions including all visual formatting"""
    try:
        # Extract the styles.xml from both documents  
        source_styles_part = source_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        target_styles_part = target_doc.part.part_related_by("http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles")
        
        if source_styles_part and target_styles_part:
            # Get the root elements
            source_styles_root = source_styles_part.element
            target_styles_root = target_styles_part.element
            
            # Find all table styles in source
            source_table_styles = source_styles_root.findall('.//w:style[@w:type="table"]', source_styles_root.nsmap)
            
            for style in source_table_styles:
                style_id = style.get(qn('w:styleId'))
                
                # Check if this style exists in target
                existing_style = target_styles_root.find(f'.//w:style[@w:styleId="{style_id}"]', target_styles_root.nsmap)
                
                if existing_style is not None:
                    # Remove existing style
                    existing_style.getparent().remove(existing_style)
                
                # Add the complete style from source
                target_styles_root.append(deepcopy(style))
            
            print(f"🎨 Copied {len(source_table_styles)} table styles")
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not copy table styles: {e}")
        return False
    
    return False

def apply_table_style_explicitly(table: Table, style_name: str):
    """Apply table style and ensure all conditional formatting is applied"""
    try:
        # Set the table style
        table.style = style_name
        
        # Force conditional formatting by updating tblLook
        tbl_element = table._tbl
        tblPr = tbl_element.find(qn('w:tblPr'))
        
        if tblPr is not None:
            # Find or create tblLook element
            tblLook = tblPr.find(qn('w:tblLook'))
            if tblLook is None:
                tblLook = OxmlElement('w:tblLook')
                tblPr.append(tblLook)
            
            # Set attributes to enable all conditional formatting
            tblLook.set(qn('w:val'), '04A0')
            tblLook.set(qn('w:firstRow'), '1')
            tblLook.set(qn('w:lastRow'), '0')
            tblLook.set(qn('w:firstColumn'), '1')
            tblLook.set(qn('w:lastColumn'), '0')
            tblLook.set(qn('w:noHBand'), '0')
            tblLook.set(qn('w:noVBand'), '1')
            
            return True
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply table style: {e}")
        return False
    
    return False

def copy_document_styles(source_doc: DocumentObject, target_doc: DocumentObject):
    """Copy styles from source document to target document"""
    try:
        # First copy table styles completely (with full definitions)
        table_styles_copied = copy_table_styles_completely(source_doc, target_doc)
        
        if not table_styles_copied:
            # Fallback to basic style copying
            for style in source_doc.styles:
                if style.name not in [s.name for s in target_doc.styles]:
                    # Add the style to target document
                    target_doc.styles.add_style(style.name, style.type)
        
        print("🎨 Copied document styles")
    except Exception as e:
        print(f"⚠️ Warning: Could not copy all styles: {e}")

def copy_cell_formatting(source_cell, target_cell):
    """Copy complete cell formatting including background, borders, and text formatting"""
    try:
        # Copy cell background/shading
        source_cell_element = source_cell._tc
        target_cell_element = target_cell._tc
        
        # Find and copy cell properties (tcPr)
        source_tcPr = source_cell_element.find(qn('w:tcPr'))
        if source_tcPr is not None:
            # Remove existing tcPr if present
            existing_tcPr = target_cell_element.find(qn('w:tcPr'))
            if existing_tcPr is not None:
                target_cell_element.remove(existing_tcPr)
            
            # Copy the complete cell properties
            target_cell_element.insert(0, deepcopy(source_tcPr))
        
        # Copy paragraph and run formatting more thoroughly
        for i, source_para in enumerate(source_cell.paragraphs):
            if i < len(target_cell.paragraphs):
                target_para = target_cell.paragraphs[i]
                
                # Copy paragraph-level formatting
                source_para_element = source_para._element
                target_para_element = target_para._element
                
                # Copy paragraph properties (pPr)
                source_pPr = source_para_element.find(qn('w:pPr'))
                if source_pPr is not None:
                    existing_pPr = target_para_element.find(qn('w:pPr'))
                    if existing_pPr is not None:
                        target_para_element.remove(existing_pPr)
                    target_para_element.insert(0, deepcopy(source_pPr))
                
                # Copy run formatting more comprehensively
                for j, source_run in enumerate(source_para.runs):
                    if j < len(target_para.runs):
                        target_run = target_para.runs[j]
                        
                        # Copy run properties (rPr) at XML level
                        source_run_element = source_run._element
                        target_run_element = target_run._element
                        
                        source_rPr = source_run_element.find(qn('w:rPr'))
                        if source_rPr is not None:
                            existing_rPr = target_run_element.find(qn('w:rPr'))
                            if existing_rPr is not None:
                                target_run_element.remove(existing_rPr)
                            target_run_element.insert(0, deepcopy(source_rPr))
                        
    except Exception as e:
        print(f"⚠️ Warning: Could not copy cell formatting: {e}")

def copy_table_with_complete_formatting(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with complete formatting preservation including cell shading and text formatting"""
    # Create a new paragraph for the heading
    heading_paragraph = insert_after_paragraph.insert_paragraph_before()
    heading_run = heading_paragraph.add_run(table_heading)
    heading_run.bold = True
    
    # Get the table element and its style
    tbl_element = source_table._tbl
    source_table_style = None
    
    # Extract table style name
    tblPr = tbl_element.find(qn('w:tblPr'))
    if tblPr is not None:
        tblStyle = tblPr.find(qn('w:tblStyle'))
        if tblStyle is not None:
            source_table_style = tblStyle.get(qn('w:val'))
    
    # Create a deep copy of the table element with all formatting
    tbl_copy = deepcopy(tbl_element)
    
    # Insert the copied table after the heading paragraph
    heading_paragraph._element.addnext(tbl_copy)
    
    # Enhanced formatting preservation
    try:
        # Find the newly inserted table in the document
        target_doc = insert_after_paragraph._parent
        target_table = None
        
        # Get the table that was just inserted
        for table in target_doc.tables:
            if table._tbl == tbl_copy:
                target_table = table
                break
        
        if target_table and source_table:
            # Apply the table style explicitly if we found one
            if source_table_style:
                apply_table_style_explicitly(target_table, source_table_style)
            
            # Copy table-level properties more thoroughly
            source_tblPr = source_table._tbl.find(qn('w:tblPr'))
            if source_tblPr is not None:
                target_tblPr = target_table._tbl.find(qn('w:tblPr'))
                if target_tblPr is not None:
                    target_table._tbl.remove(target_tblPr)
                target_table._tbl.insert(0, deepcopy(source_tblPr))
            
            # Copy cell-by-cell formatting
            for i, source_row in enumerate(source_table.rows):
                if i < len(target_table.rows):
                    target_row = target_table.rows[i]
                    for j, source_cell in enumerate(source_row.cells):
                        if j < len(target_row.cells):
                            target_cell = target_row.cells[j]
                            copy_cell_formatting(source_cell, target_cell)
            
    except Exception as e:
        print(f"⚠️ Warning: Could not apply enhanced formatting: {e}")
    
    # Insert an empty paragraph after the table (acts as a line break)
    p = OxmlElement('w:p')
    tbl_copy.addnext(p)
    
    # Clear the original table tag paragraph instead of removing it
    insert_after_paragraph.clear()
    
    # Return the paragraph after the table for proper positioning of next elements
    return insert_after_paragraph._element.getnext()

def remove_all_tables(doc: DocumentObject):
    body = doc.element.body
    tables_to_remove = [child for child in body.iterchildren() if child.tag == qn('w:tbl')]
    for tbl in tables_to_remove:
        body.remove(tbl)
        
def extract_images(docx_path, extract_folder):
    with zipfile.ZipFile(docx_path, 'r') as zip_ref:
        zip_ref.extractall(extract_folder)

def find_all_paragraphs_by_text(doc: DocumentObject, search_text) -> List[Paragraph]:
    return [para for para in doc.paragraphs if search_text in para.text]

def copy_table_with_heading_and_break(source_table: Table, insert_after_paragraph: Paragraph, table_heading: str):
    """Copy table with heading and line break after - DEPRECATED, use copy_table_with_complete_formatting"""
    copy_table_with_complete_formatting(source_table, insert_after_paragraph, table_heading)

def load_table_headings(json_file_path: str):
    """Load table headings from JSON file"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        table_index = json.load(f)
    
    table_headings = {}
    for table_hash, table_info in table_index.items():
        table_id = table_info['id']
        table_heading = table_info['heading']
        table_headings[table_id] = table_heading
    
    return table_headings

def insert_image_before_paragraph(doc: DocumentObject, image_path: str, paragraph: Paragraph):
    run = paragraph.insert_paragraph_before().add_run()
    run.add_picture(image_path, width=Inches(4))  # Adjust width if needed

def main():
    source_docx = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report.docx'
    target_docx = '/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/formatted_answer.docx'
    extract_folder = 'source_extracted'
    
    # Path for table headings
    json_file_path = '/Users/<USER>/projects/scalegen/MedNova/development reports/Stability Section - Developmental Report_tables_with_summaries_and_tags/table_index.json'

    # Step 1: Extract images from source.docx
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
    extract_images(source_docx, extract_folder)

    media_folder = os.path.join(extract_folder, 'word', 'media')
    media_files = sorted(os.listdir(media_folder)) if os.path.exists(media_folder) else []

    # Load table headings from JSON file
    table_headings = load_table_headings(json_file_path)
    print(f"📊 Loaded headings for {len(table_headings)} tables")

    source_doc = Document(source_docx)
    target_doc = Document(target_docx)

    # Step 1.2: Copy page setup and styles from source to target
    copy_page_setup(source_doc, target_doc)
    copy_document_styles(source_doc, target_doc)

    # Step 1.5: Remove all existing tables from target doc
    remove_all_tables(target_doc)
    print("🗑️ Removed all existing tables from target document")

    # Step 2: Process tables with complete formatting preservation
    # First, collect all table tag matches
    table_matches = []
    for idx, source_table in enumerate(source_doc.tables, start=1):
        table_id = f"table_{idx}"
        reference_text = f"<TABLE_TAG_{table_id}>"
        
        # Get the heading for this table from JSON, fallback to generic heading
        table_heading = table_headings.get(table_id, f"Table {idx}")
        
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        if matched_paragraphs:
            for para in matched_paragraphs:
                table_matches.append((para, source_table, table_heading, reference_text, idx))
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")
    
    # Process matches in order
    for para, source_table, table_heading, reference_text, idx in table_matches:
        next_element = copy_table_with_complete_formatting(source_table, para, table_heading)
        print(f"✅ Inserted Table {idx} with complete formatting")

    # Step 3: Process figures/images
    for idx, image_file in enumerate(media_files, start=1):
        reference_text = f"Figure {idx}:"
        matched_paragraphs = find_all_paragraphs_by_text(target_doc, reference_text)
        image_path = os.path.join(media_folder, image_file)
        if matched_paragraphs:
            for para in matched_paragraphs:
                insert_image_before_paragraph(target_doc, image_path, para)
            print(f"✅ Inserted Figure {idx}")
        else:
            print(f"⚠️ Reference '{reference_text}' not found in target document")

    # Step 4: Save output
    target_doc.save('/Users/<USER>/projects/scalegen/MedNova/notebooks and scripts/Summarization and Filtering Pipeline/module 3 - Quality/table_and_image_formatted_answer.docx')
    print("✅ All done. Saved as 'table_and_image_formatted_answer.docx'")
    print("🎨 Complete visual formatting preserved from source document")
    
    if os.path.exists(extract_folder):
        shutil.rmtree(extract_folder)
        print(f"🗑️ Cleaned up temporary folder: {extract_folder}")

if __name__ == "__main__":
    main()