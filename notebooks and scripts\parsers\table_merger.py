from docx import Document

def merge_docx_files(docx_files: list[str], output_filename: str) -> None:
    # This will hold all rows (including header from the first doc)
    merged_rows = []

    for idx, file in enumerate(docx_files):
        doc = Document(file)
        
        # Assuming there's only 1 table in each document
        table = doc.tables[0]
        
        for row_idx, row in enumerate(table.rows):
            cells_text = [cell.text.strip() for cell in row.cells]
            
            # Include header row only from first document
            if idx == 0:
                merged_rows.append(cells_text)
            else:
                # Skip header row in subsequent documents
                if row_idx == 0:
                    continue
                merged_rows.append(cells_text)

    # Now write merged_rows into a new DOCX
    output_doc = Document()
    output_doc.add_heading("Biomedical Drug Data - Merged Table", level=1)

    # Create new table
    merged_table = output_doc.add_table(rows=1, cols=len(merged_rows[0]))
    merged_table.style = 'Light List'

    # Add header row
    hdr_cells = merged_table.rows[0].cells
    for i, header_text in enumerate(merged_rows[0]):
        hdr_cells[i].text = header_text

    # Add all data rows
    for row_data in merged_rows[1:]:
        row_cells = merged_table.add_row().cells
        for i, cell_text in enumerate(row_data):
            row_cells[i].text = cell_text
    
    output_doc.save(output_filename)

# List of your DOCX files to merge
docx_files = [
    "tables/biomedical_drug_data_part_1.docx",
    "tables/biomedical_drug_data_part_2.docx",
    "tables/biomedical_drug_data_part_3.docx",
    "tables/biomedical_drug_data_part_4.docx"
]
# Save the merged document
output_filename = "tables/biomedical_drug_data_merged.docx"
merge_docx_files(docx_files, output_filename)
print(f"Merged document saved as '{output_filename}'")
