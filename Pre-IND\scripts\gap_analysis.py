# %%
import random
import re
import json
import os
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv
from pymongo import MongoClient, AsyncMongoClient
from bson.objectid import ObjectId
from typing import List, Dict, Any, Tuple
import numpy as np
import asyncio
from transformers import AutoTokenizer
import traceback  # Added import for stack trace information
import time  # Added import for timestamp logging
import inspect    # For frame inspection
import hashlib  # For content hashing in caching
from functools import lru_cache
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.anthropic import AnthropicModel, AnthropicModelSettings
from pydantic_ai.providers.anthropic import AnthropicProvider
from pydantic_ai.models.openai import OpenAIModel, OpenAIResponsesModelSettings, ReasoningEffort
from pydantic_ai.providers.openai import OpenAIProvider

load_dotenv()

# Global semaphore for rate limiting LLM API calls
MAX_CONCURRENT_REQUESTS_FOR_GENERATOR = 50  # Configurable limit for concurrent API requests
api_semaphore_for_generator = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS_FOR_GENERATOR)
MAX_CONCURRENT_REQUESTS_FOR_CRITIQUE = 50  # Configurable limit for concurrent API requests
api_semaphore_for_critique = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS_FOR_CRITIQUE)

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

CRITIQUE_MODEL_NAME = os.getenv("CRITIQUE_MODEL_NAME")
CRITIQUE_MODEL_BASE_URL = os.getenv("CRITIQUE_MODEL_BASE_URL")
CRITIQUE_MODEL_API_KEY = os.getenv("CRITIQUE_MODEL_API_KEY")

MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT = 20000
MAX_TOKEN_LIMIT_FOR_TEXT_OUTPUT = 20000

RELEVANCE_THRESHOLD = 8.0

# Configuration for batched evaluation
USE_BATCHED_EVALUATION = True  # Set to False to use individual evaluation (for testing/comparison)
BATCH_SIZE_MAX_TOKENS = 20000
BATCH_SIZE_MAX_SUMMARIES = 10

# Configuration for batched chunk evaluation in gap analysis
USE_BATCHED_CHUNK_EVALUATION = True  # Set to False to use individual chunk evaluation (for testing/comparison)
CHUNK_BATCH_SIZE_MAX_TOKENS = 20000
CHUNK_BATCH_SIZE_MAX_CHUNKS = 10

DRUG_TYPES_JSON_PATH="/Users/<USER>/projects/scalegen/MedNova/structured jsons/drug_types.json"

async def rate_limited_agent_call(agent, prompt, semaphore):
    """
    Wrapper function that applies rate limiting to pydantic-ai agent calls using a semaphore.
    
    Args:
        agent: The pydantic-ai Agent instance
        prompt: The prompt to send to the agent
        
    Returns:
        The result of the agent call
    """
    async with semaphore:
        return await agent.run(prompt)

def get_generator_and_critique_model(generator_base_url, critique_base_url):
    generator_model = None
    critique_model = None

    # GENERATOR MODEL

    if "openai" in generator_base_url:
        generator_model = OpenAIModel(
            model_name=GENERATOR_MODEL_NAME,
            provider=OpenAIProvider(
                base_url=GENERATOR_MODEL_BASE_URL,
                api_key=GENERATOR_MODEL_API_KEY
            )
        )
    elif "anthropic" in generator_base_url:
        generator_model = AnthropicModel(
            model_name=GENERATOR_MODEL_NAME,
            provider=AnthropicProvider(
                api_key=GENERATOR_MODEL_API_KEY
            )
        )
    elif "theagentic" in generator_base_url:
        generator_model = OpenAIModel(
            model_name=GENERATOR_MODEL_NAME,
            provider=OpenAIProvider(
                base_url=GENERATOR_MODEL_BASE_URL,
                api_key=GENERATOR_MODEL_API_KEY
            )
        )

    # CRITIQUE MODEL

    if "openai" in critique_base_url:
        critique_model = OpenAIModel(
            model_name=CRITIQUE_MODEL_NAME,
            provider=OpenAIProvider(
                base_url=CRITIQUE_MODEL_BASE_URL,
                api_key=CRITIQUE_MODEL_API_KEY
            )
        )
    elif "anthropic" in critique_base_url:
        critique_model = AnthropicModel(
            model_name=CRITIQUE_MODEL_NAME,
            provider=AnthropicProvider(
                api_key=CRITIQUE_MODEL_API_KEY
            )
        )
    elif "theagentic" in critique_base_url:
        critique_model = OpenAIModel(
            model_name=CRITIQUE_MODEL_NAME,
            provider=OpenAIProvider(
                base_url=CRITIQUE_MODEL_BASE_URL,
                api_key=CRITIQUE_MODEL_API_KEY
            )
        )

    return generator_model, critique_model

def get_model_settings(base_url, temperature):
    if "openai" in base_url:
        return OpenAIResponsesModelSettings({
            "openai_reasoning_effort": "high",
            "openai_reasoning_generate_summary": "detailed"
        })
    elif "anthropic" in base_url:
        return AnthropicModelSettings({
            "max_tokens": 64000,
            "extra_body": {
                "stream": True
            },
            "anthropic_thinking": {
                "type": "enabled",
                "budget_tokens": 63999
            }
        })
    elif "theagentic" in base_url:
        return {
            "temperature": temperature,
            "extra_body": {
                # "return_reasoning": True
            }
        }
    
def get_structured_output_using_pydantic(base_url):
    if "openai" in base_url:
        return True
    elif "anthropic" in base_url:
        return False
    elif "theagentic" in base_url:
        return True

generator_model, critique_model = get_generator_and_critique_model(GENERATOR_MODEL_BASE_URL, CRITIQUE_MODEL_BASE_URL)

print("GENERATOR_MODEL_NAME", GENERATOR_MODEL_NAME)
print("GENERATOR_MODEL_BASE_URL", GENERATOR_MODEL_BASE_URL)
print("GENERATOR_MODEL_API_KEY", GENERATOR_MODEL_API_KEY)

print("CRITIQUE_MODEL_NAME", CRITIQUE_MODEL_NAME)
print("CRITIQUE_MODEL_BASE_URL", CRITIQUE_MODEL_BASE_URL)
print("CRITIQUE_MODEL_API_KEY", CRITIQUE_MODEL_API_KEY)

# Pydantic schema for structured output
class CheckpointList(BaseModel):
    """Schema for checkpoint list generation"""
    checkpoints: List[str] = Field(
        description="List of specific checkpoint questions that should be answered in summaries for this section",
        min_items=2
    )

class RelevanceEvaluation(BaseModel):
    """Schema for summary relevance evaluation"""
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well the summary answers the checkpoint questions",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of your score",
        default=""
    )

class CombinedJustifications(BaseModel):
    combined_supporting_evidence: str = Field(description="Markdown-formatted category-level explanation of what's present and working well", default="No relevant information available")
    combined_gap_analysis: str = Field(description="Markdown-formatted category-level explanation of what's missing or insufficient", default="No gaps identified")

class StrategicRecommendations(BaseModel):
    recommendations: List[str]

class CheckpointRelevanceEvaluation(BaseModel):
    """Schema for individual checkpoint relevance evaluation"""
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well the summary answers this specific checkpoint question.",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of your score for this specific checkpoint",
        default=""
    )

class SummaryEvaluationResult(BaseModel):
    """Schema for individual summary evaluation result in a batch"""
    summary_id: str = Field(description="Unique identifier for the summary")
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well this summary answers the checkpoint question",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of the relevance score for this summary",
        default=""
    )

class BatchedSummaryEvaluation(BaseModel):
    """Schema for batched summary evaluation against a single checkpoint"""
    checkpoint_question: str = Field(description="The checkpoint question being evaluated")
    evaluations: List[SummaryEvaluationResult] = Field(
        description="List of evaluation results for each summary in the batch",
        min_items=1
    )

class CritiqueEvaluation(BaseModel):
    """Schema for Pre-IND critique evaluation"""
    overall_rating: float = Field(
        description="Overall rating from 1-10 for the Pre-IND section quality",
        ge=1.0,
        le=10.0
    )
    key_strengths: List[str] = Field(
        description="List of main strengths identified in the Pre-IND section",
        default=[]
    )
    critical_issues: List[str] = Field(
        description="List of critical issues that need to be addressed",
        default=[]
    )
    required_improvements: List[str] = Field(
        description="List of specific improvements required",
        default=[]
    )
    additional_recommendations: List[str] = Field(
        description="List of additional recommendations for enhancement",
        default=[]
    )

class ChunkEvaluationResult(BaseModel):
    """Schema for individual chunk evaluation result in a batch"""
    chunk_id: str = Field(description="Unique identifier for the chunk")
    relevance_score: float = Field(
        description="Relevance score from 0-10 indicating how well this chunk answers the checkpoint question",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Brief explanation of the relevance score for this chunk",
        default=""
    )

class BatchedChunkEvaluation(BaseModel):
    """Schema for batched chunk evaluation against a single checkpoint"""
    checkpoint_question: str = Field(description="The checkpoint question being evaluated")
    evaluations: List[ChunkEvaluationResult] = Field(
        description="List of evaluation results for each chunk in the batch",
        min_items=1
    )

class DrugTypeScore(BaseModel):
    """Schema for individual drug type score with justification"""
    drug_type_name: str = Field(description="Name of the drug type (e.g., 'Small-Molecule Drugs')")
    score: float = Field(
        description="Confidence score from 0-10 for this drug type classification",
        ge=0.0,
        le=10.0
    )
    justification: str = Field(
        description="Detailed reasoning for this score based on evidence from the input documents",
        default=""
    )
    key_evidence: List[str] = Field(
        description="Specific text excerpts from documents that support this classification",
        default=[]
    )

class DrugTypeClassification(BaseModel):
    """Schema for comprehensive drug type classification result"""
    drug_type_scores: List[DrugTypeScore] = Field(
        description="Scores and justifications for all 6 drug types",
        min_items=6,
        max_items=6
    )
    primary_classification: str = Field(
        description="The most likely drug type based on highest confidence score"
    )
    confidence_level: str = Field(
        description="Overall confidence in the primary classification: 'high' (score ≥8), 'medium' (score 6-7.9), 'low' (score 4-5.9), or 'uncertain' (score <4)",
        default="uncertain"
    )
    overall_justification: str = Field(
        description="Summary explanation of the primary classification decision",
        default=""
    )

class AggregatedDrugTypeClassification(BaseModel):
    """Schema for final aggregated drug type classification from multiple batches"""
    final_classification: str = Field(description="Final determined drug type")
    aggregated_scores: List[DrugTypeScore] = Field(
        description="Aggregated scores across all batches for each drug type",
        min_items=6,
        max_items=6
    )
    confidence_level: str = Field(description="Final confidence level")
    batch_count: int = Field(description="Number of batches processed")
    consensus_strength: str = Field(
        description="Strength of consensus across batches: 'strong', 'moderate', 'weak'",
        default="weak"
    )
    final_justification: str = Field(
        description="Final reasoning for the classification decision",
        default=""
    )

def get_mongodb_collection_from_referenced_guidelines(ich_guidelines: List[str]):
    checklist = {
        "Q": False,
        "E": False,
        "S": False,
        "M": False
    }

    collection_mapping_for_summaries = {
        "Q": "quality_docs_summaries",
        "E": "efficacy_docs_summaries",
        "S": "safety_docs_summaries",
        "M": "multidisciplinary_docs_summaries"
    }

    collection_mapping_for_chunks = {
        "Q": "quality_docs_chunks",
        "E": "efficacy_docs_chunks",
        "S": "safety_docs_chunks",
        "M": "multidisciplinary_docs_chunks"
    }

    key_name_mapping = {
        "Q": "quality_guideline",
        "E": "efficacy_guideline",
        "S": "safety_guideline",
        "M": "multidisciplinary_guideline"
    }

    for ich_guideline in ich_guidelines:
        if ich_guideline.startswith("Q"):
            checklist["Q"] = True
        elif ich_guideline.startswith("E"):
            checklist["E"] = True
        elif ich_guideline.startswith("S"):
            checklist["S"] = True
        elif ich_guideline.startswith("M"):
            checklist["M"] = True

    mapping_to_return = {
        "summaries": [],
        "chunks": [],
        "key_name": []
    }

    for key, value in checklist.items():
        if value:
            mapping_to_return["summaries"].append(collection_mapping_for_summaries[key])
            mapping_to_return["chunks"].append(collection_mapping_for_chunks[key])
            mapping_to_return["key_name"].append(key_name_mapping[key])
    
    return mapping_to_return

def get_mongodb_collection_from_summary(summary: dict[str, Any]):
    all_keys = summary.keys()
    ich_ref_list = []
    for key in all_keys:
        if key.startswith("quality_guideline"):
            ich_ref_list.append(key[0].upper())
        elif key.startswith("efficacy_guideline"):
            ich_ref_list.append(key[0].upper())
        elif key.startswith("safety_guideline"):
            ich_ref_list.append(key[0].upper())
        elif key.startswith("multidisciplinary_guideline"):
            ich_ref_list.append(key[0].upper())
    collection_mapping = get_mongodb_collection_from_referenced_guidelines(ich_ref_list)
    summary_name = collection_mapping["summaries"][0]
    chunk_name = collection_mapping["chunks"][0]
    key_name = collection_mapping["key_name"][0]
    return summary_name, chunk_name, key_name

def format_list_as_markdown(items: List[str], title: str = None) -> str:
    """
    Format a list of items as clean markdown bullet points.
    
    Args:
        items: List of items to format
        title: Optional title for the list
        
    Returns:
        Markdown-formatted string
    """
    if not items:
        return "None available"
    
    formatted = ""
    if title:
        formatted += f"**{title}:**\n"
    
    for item in items:
        formatted += f"- {item}\n"
    
    return formatted.strip()

def format_dict_as_markdown(data: Dict, title: str = None) -> str:
    """
    Format a dictionary as clean markdown.
    
    Args:
        data: Dictionary to format
        title: Optional title for the section
        
    Returns:
        Markdown-formatted string
    """
    if not data:
        return "None available"
    
    formatted = ""
    if title:
        formatted += f"**{title}:**\n"
    
    for key, value in data.items():
        if isinstance(value, list):
            formatted += f"- **{key}:** {', '.join(str(v) for v in value)}\n"
        elif isinstance(value, dict):
            formatted += f"- **{key}:**\n"
            for sub_key, sub_value in value.items():
                formatted += f"  - {sub_key}: {sub_value}\n"
        else:
            formatted += f"- **{key}:** {value}\n"

    return formatted.strip()

def format_evidence_batches_as_markdown(batches: List[str]) -> str:
    """
    Format evidence batches as clean markdown sections.
    
    Args:
        batches: List of evidence batch strings
        
    Returns:
        Markdown-formatted string with clear batch separation
    """
    if not batches:
        return "No batches available"
    
    formatted = ""
    for i, batch in enumerate(batches, 1):
        formatted += f"### Batch {i}\n\n{batch}\n\n"
    
    return formatted.strip()

def format_category_summaries_as_markdown(categories: List[Dict]) -> str:
    """
    Format category summaries as clean markdown sections.
    
    Args:
        categories: List of category summary dictionaries
        
    Returns:
        Markdown-formatted string
    """
    if not categories:
        return "No categories available"
    
    formatted = ""
    for category in categories:
        category_name = category.get('category', 'Unknown Category')
        score_range = category.get('score_range', [0, 0])
        checkpoint_count = category.get('checkpoint_count', 0)
        supporting_evidence = category.get('supporting_evidence', 'None available')
        gap_analysis = category.get('gap_analysis', 'None available')
        
        formatted += f"### {category_name.replace('_', ' ').title()}\n"
        formatted += f"- **Score Range:** {score_range[0]}-{score_range[1]}\n"
        formatted += f"- **Number of Checkpoints:** {checkpoint_count}\n\n"
        
        formatted += f"**Supporting Evidence:**\n{supporting_evidence}\n\n"
        formatted += f"**Gap Analysis:**\n{gap_analysis}\n\n"
        formatted += "---\n\n"
    
    return formatted.strip()

async def check_if_llm_is_available(model, base_url, semaphore):
    try:
        agent = Agent(
            model=model,
            system_prompt="You are a helpful assistant.",
            model_settings=get_model_settings(base_url, 1),
            retries=3
        )
        prompt = "Hello World!"

        result = None
        if "anthropic" in base_url:
            # Use the semaphore properly for streaming calls
            async with semaphore:
                async with agent.run_stream(prompt) as response:
                    result = await response.get_output()
        else:    
            result = await rate_limited_agent_call(agent, prompt, semaphore)
            result = result.output

        print(f"LLM {model.model_name} is available, response: {result}")
        return True
    except Exception as e:
        print(f"Error in checking if LLM {model} is available: {e}")
        return False

def write_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"JSON saved to {filename}")

def read_json(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

async def get_mongodb_client():
    """Get MongoDB client connection."""
    return AsyncMongoClient(os.getenv("MONGO_DB_URL"))

def extract_content_from_tags(content: str, tag_name: str) -> str:
    """
    Extract content from specific XML-style tags if present, otherwise return content as is.
    
    Args:
        content: The raw content from the LLM response
        tag_name: The tag name to look for (without < > brackets)
        
    Returns:
        Extracted content or original content if tags not found
    """
    if not content:
        return content
        
    # Look for specified tags (case-insensitive)
    import re
    tag_match = re.search(fr'<{tag_name}>(.*?)</{tag_name}>', content, re.DOTALL | re.IGNORECASE)
    
    if tag_match:
        # Extract and clean the content
        extracted = tag_match.group(1).strip()
        return extracted
    else:
        # Return original content if no tags found
        return content

def extract_and_parse_response(content: str, expected_output_type: str, tag_name: str = None) -> Any:
    """
    Universal function to extract content from tags and parse based on expected output type.
    
    Args:
        content: Raw LLM response content
        expected_output_type: Type of expected output ('json', 'text', 'markdown', 'structured')
        tag_name: Optional tag name to extract from (e.g., 'final_checkpoints', 'final_recommendations')
        
    Returns:
        Processed content based on expected output type
    """
    if not content:
        return content
    
    # Step 1: Extract from tags if tag_name is provided
    if tag_name:
        content = extract_content_from_tags(content, tag_name)
    
    # Step 2: Process based on expected output type
    if expected_output_type == 'json':
        return parse_json_response(content)
    elif expected_output_type in ['text', 'markdown']:
        return content.strip()
    elif expected_output_type == 'structured':
        # For structured output, return as-is (already processed by pydantic)
        return content
    else:
        # Default: return as-is
        return content

def parse_json_response(response_text: str) -> Any:
    """
    Parse a JSON response that may be wrapped in backticks.
    
    Args:
        response_text: The response text to parse
        
    Returns:
        Any: The parsed JSON object
    """
    # Remove any markdown code block syntax
    response_text = re.sub(r'```json\n?', '', response_text)
    response_text = re.sub(r'```\n?', '', response_text)
    response_text = response_text.strip()
    
    try:
        # print(f"parse_json_response, response_text: {json.loads(response_text)}")
        return json.loads(response_text)
    except json.JSONDecodeError:
        # If the response is not valid JSON, try to extract a list from the text
        # Look for lines that start with numbers, bullets, or dashes
        lines = re.findall(r'^[\d\-\*\.]+\.?\s*(.+)$', response_text, re.MULTILINE)
        if lines:
            return lines
        # If no lines found, split by newlines and clean up
        # print(f"parse_json_response error, response_text: {[line.strip() for line in response_text.split('\n') if line.strip()]}")
        return [line.strip() for line in response_text.split('\n') if line.strip()]
    
# Global cache for token calculations
_token_cache = {}

# Global cache for enhanced section context
_enhanced_context_cache = {}

def calculate_number_of_tokens(text: str) -> int:
    """
    Calculate number of tokens for given text with caching for performance.
    Uses content hash as cache key to avoid memory bloat.
    
    Args:
        text: The text to tokenize
        
    Returns:
        int: Number of tokens
    """
    if not text:
        return 0
    
    # Create a hash of the content for cache key (more memory efficient than storing full text)
    content_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
    
    # Check cache first
    if content_hash in _token_cache:
        return _token_cache[content_hash]
    
    # Load tokenizer for Mistral model (this could also be cached globally)
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-32B")

    def count_tokens(text):
        tokens = tokenizer.encode(text, add_special_tokens=False)
        return len(tokens)

    token_count = count_tokens(text)
    
    # Cache the result
    _token_cache[content_hash] = token_count
    
    # Prevent cache from growing too large (keep last 10000 calculations)
    if len(_token_cache) > 10000:
        # Remove oldest 2000 entries to prevent frequent cleanup
        oldest_keys = list(_token_cache.keys())[:2000]
        for key in oldest_keys:
            del _token_cache[key]
    
    return token_count

def create_summary_batches(summaries: List[Dict], checkpoint_question: str, max_batch_tokens: int = BATCH_SIZE_MAX_TOKENS, max_summaries_per_batch: int = BATCH_SIZE_MAX_SUMMARIES) -> List[List[Dict]]:
    """
    Create batches of summaries for efficient evaluation against a single checkpoint.
    
    Args:
        summaries: List of summary dictionaries with content and metadata
        checkpoint_question: The checkpoint question (used for token calculation)
        max_batch_tokens: Maximum tokens per batch
        max_summaries_per_batch: Maximum number of summaries per batch
        
    Returns:
        List of batches, where each batch is a list of summaries
    """
    if not summaries:
        return []
    
    # Calculate base tokens for checkpoint and system prompt (estimated)
    base_tokens = calculate_number_of_tokens(checkpoint_question) + 1000  # Add buffer for system prompt
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for summary in summaries:
        # Calculate tokens for this summary including XML tags
        summary_content = summary.get('content', '')
        summary_id = str(summary.get('_id', 'unknown'))
        
        # Estimate tokens including XML formatting
        summary_tokens = calculate_number_of_tokens(f"<summary_{len(current_batch) + 1}>\n{summary_id}\n{summary_content}\n</summary_{len(current_batch) + 1}>")
        
        # Check if adding this summary would exceed limits
        if (current_tokens + summary_tokens > max_batch_tokens or 
            len(current_batch) >= max_summaries_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add summary to current batch
        current_batch.append(summary)
        current_tokens += summary_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

def create_chunk_batches(chunks: List[Dict], checkpoint_question: str, max_batch_tokens: int = CHUNK_BATCH_SIZE_MAX_TOKENS, max_chunks_per_batch: int = CHUNK_BATCH_SIZE_MAX_CHUNKS) -> List[List[Dict]]:
    """
    Create batches of chunks for efficient evaluation against a single checkpoint.
    
    Args:
        chunks: List of chunk dictionaries with content and metadata
        checkpoint_question: The checkpoint question (used for token calculation)
        max_batch_tokens: Maximum tokens per batch
        max_chunks_per_batch: Maximum number of chunks per batch
        
    Returns:
        List of batches, where each batch is a list of chunks
    """
    if not chunks:
        return []
    
    # Calculate base tokens for checkpoint and system prompt (estimated)
    base_tokens = calculate_number_of_tokens(checkpoint_question) + 1000  # Add buffer for system prompt
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for chunk in chunks:
        # Calculate tokens for this chunk including XML tags
        chunk_content = chunk.get('content', '')
        chunk_id = str(chunk.get('_id', 'unknown'))
        
        # Estimate tokens including XML formatting
        chunk_tokens = calculate_number_of_tokens(f"<chunk_{len(current_batch) + 1}>\n{chunk_id}\n{chunk_content}\n</chunk_{len(current_batch) + 1}>")
        
        # Check if adding this chunk would exceed limits
        if (current_tokens + chunk_tokens > max_batch_tokens or 
            len(current_batch) >= max_chunks_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add chunk to current batch
        current_batch.append(chunk)
        current_tokens += chunk_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

async def retry_on_empty_response(async_func, *args, max_retries=5, **kwargs):
    """
    Wrapper function that retries an async LLM API call when the response is empty.
    
    Args:
        async_func: The async function to call (usually an LLM API call)
        *args: Positional arguments to pass to async_func
        max_retries: Maximum number of retry attempts (default: 3)
        **kwargs: Keyword arguments to pass to async_func
        
    Returns:
        The result of the async_func call, ensuring it's not empty
        
    Raises:
        Exception: If max_retries is reached and the response is still empty
    """
    # Create logs directory if it doesn't exist
    log_dir = "error_logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Extract the function name for logging purposes
    func_name = async_func.__name__ if hasattr(async_func, "__name__") else "unknown_function"
    
    # Try to get the caller's name from the stack
    try:
        caller_frame = inspect.currentframe().f_back
        caller_name = caller_frame.f_code.co_name if caller_frame else "unknown_caller"
    except Exception:
        caller_name = "unknown_caller"
    
    for attempt in range(max_retries):
        try:
            result = await async_func(*args, **kwargs)
            
            # Check if result is None, empty string, just whitespace, or contains tool calls
            is_empty = result is None or (isinstance(result, str) and result.strip() == "")
            contains_tool_call = False
            
            if isinstance(result, str):
                # Check for tool_call patterns
                tool_call_pattern = re.search(r'<tool_call>.*?</tool_call>', result, re.DOTALL | re.IGNORECASE)
                contains_tool_call_text = 'tool_call' in result.lower()
                contains_tool_call = tool_call_pattern is not None or contains_tool_call_text
            
            if is_empty or contains_tool_call:
                if is_empty:
                    reason = "empty response"
                    error_type = 'empty_response'
                elif contains_tool_call:
                    reason = "response contains tool calls"
                    error_type = 'tool_call_response'
                
                print(f"Warning: Received {reason} from LLM (attempt {attempt+1}/{max_retries}), retrying...")
                
                # Get debug information to log
                debug_info = {
                    'error_type': error_type,
                    'function': func_name,
                    'caller': caller_name,
                    'attempt': attempt + 1,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'response_preview': result[:500] if isinstance(result, str) else str(result)[:500]
                }
                
                # Extract prompt information based on different API patterns
                # For the direct messages pattern in kwargs
                if 'messages' in kwargs:
                    debug_info['messages'] = kwargs['messages']
                    
                # For the pattern where the func is a closure with local make_api_call
                # Try to get source code of the async_func to check for patterns
                try:
                    source = inspect.getsource(async_func)
                    if "chat.completions.create" in source:
                        debug_info['api_pattern'] = "chat_completions_closure"
                except Exception:
                    pass
                
                # Try to extract system_prompt and user_prompt from the caller's frame if available
                try:
                    if caller_frame:
                        caller_locals = caller_frame.f_locals
                        # Capture common patterns in this codebase
                        if 'system_prompt' in caller_locals:
                            debug_info['system_prompt'] = caller_locals['system_prompt']
                        if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                            debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                        # If this is using the OpenAI client pattern, get the model too
                        if 'model' in caller_locals:
                            debug_info['model'] = caller_locals['model']
                        # For the antropic calls
                        if 'CRITIQUE_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                        elif 'GENERATOR_MODEL_NAME' in caller_locals:
                            debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                        elif 'MODEL' in caller_locals:
                            debug_info['model'] = caller_locals['MODEL']
                except Exception as e:
                    debug_info['frame_inspection_error'] = str(e)
                
                # Save the debug information
                timestamp = int(time.time())
                log_filename = f"{log_dir}/{timestamp}_empty_response_{caller_name}_{func_name}_attempt{attempt+1}.json"
                
                try:
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        json.dump(debug_info, f, indent=2, ensure_ascii=False)
                    print(f"Logged empty response details to {log_filename}")
                except Exception as log_error:
                    print(f"Failed to log empty response details: {str(log_error)}")
                
                # Continue to the next retry attempt
                continue
                
            # If we get here, we have a non-empty response
            return result
            
        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            print(f"Error in LLM API call (attempt {attempt+1}/{max_retries}): {error_type}: {error_msg}")
            
            # Get debug information to log
            debug_info = {
                'error_type': error_type,
                'error_message': error_msg,
                'function': func_name,
                'caller': caller_name,
                'attempt': attempt + 1,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'stack_trace': traceback.format_exc()
            }
            
            # Extract prompt information based on different API patterns
            # For the direct messages pattern in kwargs
            if 'messages' in kwargs:
                debug_info['messages'] = kwargs['messages']
                
            # For the pattern where the func is a closure with local make_api_call
            # Try to get source code of the async_func to check for patterns
            try:
                source = inspect.getsource(async_func)
                if "chat.completions.create" in source:
                    debug_info['api_pattern'] = "chat_completions_closure"
            except Exception:
                pass
            
            # Try to extract system_prompt and user_prompt from the caller's frame if available
            try:
                if caller_frame:
                    caller_locals = caller_frame.f_locals
                    # Capture common patterns in this codebase
                    if 'system_prompt' in caller_locals:
                        debug_info['system_prompt'] = caller_locals['system_prompt']
                    if 'user_prompt' in caller_locals or 'prompt' in caller_locals:
                        debug_info['user_prompt'] = caller_locals['user_prompt'] if 'user_prompt' in caller_locals else caller_locals['prompt']
                    # If this is using the OpenAI client pattern, get the model too
                    if 'model' in caller_locals:
                        debug_info['model'] = caller_locals['model']
                    # For the antropic calls
                    if 'CRITIQUE_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['CRITIQUE_MODEL_NAME']
                    elif 'GENERATOR_MODEL_NAME' in caller_locals:
                        debug_info['model'] = caller_locals['GENERATOR_MODEL_NAME']
                    elif 'MODEL' in caller_locals:
                        debug_info['model'] = caller_locals['MODEL']
            except Exception as frame_error:
                debug_info['frame_inspection_error'] = str(frame_error)
            
            # Save the debug information
            timestamp = int(time.time())
            log_filename = f"{log_dir}/{timestamp}_{error_type}_{caller_name}_{func_name}_attempt{attempt+1}.json"
            
            try:
                with open(log_filename, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, indent=2, ensure_ascii=False)
                print(f"Logged error details to {log_filename}")
            except Exception as log_error:
                print(f"Failed to log error details: {str(log_error)}")
            
            if attempt == max_retries - 1:
                # If we've exhausted all retries and still have an error
                print(f"Failed to get non-empty response after {max_retries} attempts")
                return None
            
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** attempt))
    
    # If we've exhausted all retries and still have an empty response
    print(f"Failed to get non-empty response after {max_retries} attempts")
    return None

# MongoDB client and LLM availability checks will be handled in async functions when needed
mongodb_client = await get_mongodb_client()
await check_if_llm_is_available(generator_model, GENERATOR_MODEL_BASE_URL, api_semaphore_for_generator)
await check_if_llm_is_available(critique_model, CRITIQUE_MODEL_BASE_URL, api_semaphore_for_critique)


# %%
pre_ind_briefing_template = read_json("/Users/<USER>/projects/scalegen/MedNova/structured jsons/pre_ind_briefing_template.json")

doc_to_work_on = pre_ind_briefing_template

# %%
def extract_outer_section_info(section_number: str) -> Dict:
    """
    Extract outer section information given a section number from the briefing template.
    
    Args:
        section_number: Section like "5.3" or "1.2.3" or "5"
        
    Returns:
        Dict containing outer section info or None if no outer section or section not found
    """
    if not section_number:
        return None
    
    # Handle sections that are already top-level (no dots)
    if '.' not in section_number:
        return None  # No outer section for top-level sections like "5"
    
    # Parse section number to get outer section
    # "5.3" -> "5", "1.2.3" -> "1", "3.2.1.4" -> "3"
    outer_section_number = section_number.split('.')[0]
    
    def find_section_recursively(obj, target_section):
        """Recursively search for a section in the nested JSON structure."""
        if isinstance(obj, dict):
            # Check if current object has the target section as a key
            if target_section in obj:
                section_obj = obj[target_section]
                if isinstance(section_obj, dict) and 'section' in section_obj:
                    return {
                        'section': section_obj.get('section', ''),
                        'title': section_obj.get('title', ''),
                        'description': section_obj.get('description', ''),
                        'referenced_ich_guidelines': section_obj.get('referenced_ich_guidelines', [])
                    }
            
            # Recursively search in all nested dictionaries
            for key, value in obj.items():
                if isinstance(value, dict):
                    result = find_section_recursively(value, target_section)
                    if result:
                        return result
        
        return None
    
    # Navigate JSON structure to find the outer section
    try:
        return find_section_recursively(pre_ind_briefing_template, outer_section_number)
    except Exception as e:
        print(f"Warning: Could not extract outer section info for {section_number}: {e}")
    
    return None

def get_enhanced_section_context(section_number: str, title: str, description: str, 
                               referenced_ich_guidelines: List[str]) -> Dict:
    """
    Get enhanced section context including outer section information if available.
    Uses caching to avoid rebuilding the same context repeatedly.
    
    Args:
        section_number: Current section number
        title: Current section title
        description: Current section description
        referenced_ich_guidelines: Current section ICH guidelines
        
    Returns:
        Dict containing both current and outer section context
    """
    # Create cache key from all parameters
    cache_key = f"{section_number}|{title}|{description}|{','.join(sorted(referenced_ich_guidelines))}"
    
    # Check cache first
    if cache_key in _enhanced_context_cache:
        return _enhanced_context_cache[cache_key]
    
    # Build context if not cached
    outer_section_info = extract_outer_section_info(section_number)
    
    context = {
        'current_section': {
            'section': section_number,
            'title': title,
            'description': description,
            'referenced_ich_guidelines': referenced_ich_guidelines
        },
        'outer_section': outer_section_info,
        'has_outer_section': outer_section_info is not None
    }
    
    # Cache the result
    _enhanced_context_cache[cache_key] = context
    
    # Prevent cache from growing too large (keep last 1000 contexts)
    if len(_enhanced_context_cache) > 1000:
        # Remove oldest 200 entries to prevent frequent cleanup
        oldest_keys = list(_enhanced_context_cache.keys())[:200]
        for key in oldest_keys:
            del _enhanced_context_cache[key]
    
    return context

# %%
async def _generate_checkpoints_from_one_summary(summary_content: str, section_info: Dict[str, Any]) -> List[str]:
    """
    Generate checkpoint questions from a single individual summary.
    
    Args:
        summary_content: The content of the individual summary
        section_info: Dictionary containing section information
        
    Returns:
        List[str]: A list of checkpoint questions generated from the individual summary
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive checklist of specific questions based on the provided summary for a section of a Pre-IND document."
            
            prompt = f"""
First, review the following information:

Summary Content:
<summary_content>
{summary_content}
</summary_content>

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            prompt += f"""
Current Section:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                prompt += f"""
Context: You are generating checkpoints for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            prompt += """Before creating the checklist, analyze the provided information and formulate your approach inside your thinking block using <checklist_preparation> tags:

1. Summarize the key points from the summary content.
2. Quote relevant phrases from the summary content.
3. Identify the main topics and requirements mentioned in the instructions.
4. List any specific technical or regulatory aspects that need to be addressed.
5. Note any particular areas of focus (e.g., physical characteristics, chemical properties, manufacturing processes, quality control measures, stability data, packaging specifications, compatibility information).
6. Brainstorm potential questions based on the quoted phrases and identified topics.
7. Ensure alignment with ICH guidelines.
8. Consider how to phrase each point as a specific, answerable question.

Now, create a detailed checklist of specific questions based on your analysis. The checklist should:

1. Include all key requirements and recommendations from the summary expressed as specific questions.
2. Cover all aspects mentioned in the summary.
3. Strictly adhere to the instructions.
4. Be specific enough to clearly determine if an input document addresses each point.
5. Format EACH checkpoint as a question that can be answered with yes/no or specific information.
6. Focus on technical and regulatory content needed for Pre-IND documentation.
7. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.
8. Cover stability data, packaging specifications, and compatibility information where appropriate.
9. Include questions about the validation and verification methods used.
10. Use the material prepared in the thinking block to ensure comprehensive coverage.

After creating the checklist, review it to ensure:
- All points from the summary are covered
- Questions align with the instructions
- Each question is specific and answerable
- The checklist is comprehensive and accurate

Present your final checklist after this review. Your output should consist only of the checklist and should not duplicate or rehash any of the work you did in the thinking block.
"""
            
            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(prompt)

            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                retries=3
            )

            response = await rate_limited_agent_call(checkpoint_list_agent, prompt, api_semaphore_for_generator)

            # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            # total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"_generate_checkpoints_from_one_summary token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            retry_count += 1
            print(f"Error in _generate_checkpoints_from_one_summary (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Generating basic checkpoints from summary as fallback.")
                # Generate basic checkpoints as fallback
                try:
                    # Extract key sentences from summary as basic checkpoints
                    sentences = [s.strip() + "?" for s in summary_content.split(".") if len(s.strip()) > 20]
                    # Convert statements to questions where possible
                    questions = []
                    for sentence in sentences[:10]:  # Limit to 10 questions to avoid overload
                        if not sentence.endswith("?"):
                            questions.append(f"Does the document address: {sentence}?")
                        else:
                            questions.append(sentence)
                    return questions
                except Exception:
                    print("Fallback checkpoint generation failed. Returning empty list.")
                    return []
            # Add a small delay before retry with exponential backoff
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []

async def generate_checkpoints_incrementally_using_individual_summaries(section_info: Dict[str, Any], list_of_individual_summary_contents: List[str]) -> List[str]:
    """
    Generate checkpoints incrementally from a list of individual summaries.
    
    Args:
        section_info: Dictionary containing section information
        list_of_individual_summary_contents: List of individual summary contents
        
    Returns:
        List[str]: A comprehensive list of checkpoints generated from all individual summaries
    """
    # Handle empty input
    if not list_of_individual_summary_contents:
        return []
        
    # Handle single summary case
    if len(list_of_individual_summary_contents) == 1:
        return await _generate_checkpoints_from_one_summary(list_of_individual_summary_contents[0], section_info)
    
    # Maximum token limit for checkpoint list batches
    
    # Generate initial checkpoints from each summary in parallel using asyncio.gather
    current_checkpoints = await asyncio.gather(*[
        _generate_checkpoints_from_one_summary(summary, section_info) 
        for summary in list_of_individual_summary_contents
    ])
    
    async def merge_multiple_checkpoint_lists(checkpoint_lists, section_info):
        """Helper function to combine multiple checkpoint lists while preserving all unique checkpoints."""
        total_lists = len(checkpoint_lists)
        print(f"\nMerging {total_lists} checkpoint lists with a total of {sum(len(cp) for cp in checkpoint_lists)} checkpoints")
        
        # Calculate token count for all lists combined
        combined_text = "\n".join(["\n".join(cp) for cp in checkpoint_lists])
        # total_tokens = calculate_number_of_tokens(combined_text)
        # print(f"Total input tokens for checkpoint merging: {total_tokens}")
        
        # Get enhanced section context including outer section info
        enhanced_context = get_enhanced_section_context(
            section_info.get('section', ''),
            section_info.get('title', ''),
            section_info.get('description', ''),
            section_info.get('referenced_ich_guidelines', [])
        )
        
        system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive merged list of checkpoint questions for a specific section of a Pre-IND document."
        
        user_prompt = f"""
First, review the following information:

"""
        
        # Add outer section context if available
        if enhanced_context['has_outer_section']:
            outer = enhanced_context['outer_section']
            user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
        
        # Add current section information
        current = enhanced_context['current_section']
        user_prompt += f"""
Current Section:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
        
        # Add context explanation if there's an outer section
        if enhanced_context['has_outer_section']:
            user_prompt += f"""
Context: You are merging checkpoint lists for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
        
        user_prompt += f"""
You will be processing {total_lists} lists of checkpoint questions. 

"""
        
        # Add each checkpoint list
        for i, checkpoints in enumerate(checkpoint_lists, 1):
            user_prompt += f"""
Checkpoint List {i}:
<checkpoints_{i}>
{format_list_as_markdown(checkpoints)}
</checkpoints_{i}>

"""

        user_prompt += f"""
Your goal is to merge these lists into a single comprehensive list while adhering to the following requirements:

1. Preserve ALL unique questions from all lists
2. Remove any duplicate or redundant questions
3. Ensure the merged list is comprehensive and covers all aspects
4. Maintain the specificity and clarity of each question
5. Keep the question format consistent (e.g., "Does the document mention...")
6. Ensure each question focuses on a single specific point
7. Group related questions together when possible
8. Strictly adhere to the referenced list of instructions
9. Keep all questions focused on technical regulatory content for Pre-IND documentation

Before producing the final merged list, wrap your analysis inside <checkpoint_analysis> tags in your thinking block. In this analysis:
1. Summarize the key points from the section_info, section_title.
2. Analyze the first list of checkpoints, noting any patterns or themes.
3. Plan how you will approach merging subsequent lists (even though we only have the first list now).
4. Ensure all requirements are met and pay special attention to accuracy and best practices in regulatory documentation.

After your analysis, provide the merged list of checkpoint questions. 

Your final output should consist only of the merged list of checkpoint questions and should not duplicate or rehash any of the work you did in the checkpoint analysis section.
        """
        
        # system_tokens = calculate_number_of_tokens(system_prompt)
        # user_tokens = calculate_number_of_tokens(user_prompt)

        checkpoint_list_agent = Agent(
            model=generator_model,
            system_prompt=system_prompt,
            output_type=CheckpointList,
            model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
            retries=3
        )

        response = await rate_limited_agent_call(checkpoint_list_agent, user_prompt, api_semaphore_for_generator)

        # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
        # total_tokens = system_tokens + user_tokens + output_tokens

        # print(f"merge_multiple_checkpoint_lists token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

        merged_checkpoints = response.output.checkpoints
        
        # Validate the output
        if len(merged_checkpoints) < 2:
            raise ValueError(f"Expected at least 2 checkpoints, got: {len(merged_checkpoints)}")
        
        print(f"Merged {total_lists} checkpoint lists into one with {len(merged_checkpoints)} checkpoints")
        
        return merged_checkpoints
    
    # Iteratively merge checkpoint lists in optimal batches
    while len(current_checkpoints) > 1:
        # Create optimal batches based on token count and number of checkpoints
        batches = []
        current_batch = []
        current_token_count = 0
        current_checkpoint_count = 0
        
        for checkpoint_list in current_checkpoints:
            # Calculate tokens for this checkpoint list
            checkpoint_text = "\n".join(checkpoint_list)
            checkpoint_tokens = calculate_number_of_tokens(checkpoint_text)
            
            # Check if this single checkpoint list exceeds the token limit
            if checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT:
                print(f"Warning: Single checkpoint list has {checkpoint_tokens} tokens, which exceeds the limit of {MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT}")
                # Force this large list to be merged with the current batch to avoid infinite loop
                if current_batch:
                    # Add to current batch despite exceeding limit
                    current_batch.append(checkpoint_list)
                    current_token_count += checkpoint_tokens
                    current_checkpoint_count += len(checkpoint_list)
                    print(f"Forcing merge of oversized checkpoint list with current batch (total tokens: {current_token_count})")
                    # Finalize this batch
                    batches.append(current_batch)
                    current_batch = []
                    current_token_count = 0
                    current_checkpoint_count = 0
                else:
                    # If current_batch is empty, we need to pair this with the next checkpoint list
                    # to avoid it being processed alone repeatedly
                    current_batch = [checkpoint_list]
                    current_token_count = checkpoint_tokens
                    current_checkpoint_count = len(checkpoint_list)
                    print(f"Starting new batch with oversized checkpoint list ({checkpoint_tokens} tokens)")
            # If adding this checkpoint list would exceed the token limit or if the batch is already large, start a new batch
            elif (current_batch and (current_token_count + checkpoint_tokens > MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT)):
                batches.append(current_batch)
                current_batch = [checkpoint_list]
                current_token_count = checkpoint_tokens
                current_checkpoint_count = len(checkpoint_list)
            else:
                current_batch.append(checkpoint_list)
                current_token_count += checkpoint_tokens
                current_checkpoint_count += len(checkpoint_list)
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        # Ensure no batch has only one checkpoint list to avoid infinite loop
        # If we have a single-item batch, try to merge it with another batch
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, we need to handle it
        if single_item_batch is not None:
            if final_batches:
                # Merge with the last batch
                final_batches[-1].extend(single_item_batch)
            else:
                # This is the only batch, which means we have only one checkpoint list left
                # This should not happen in the while loop condition, but just in case
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        print(f"Created {len(batches)} batches for checkpoint list merging")
        for i, batch in enumerate(batches):
            total_checkpoints = sum(len(cp) for cp in batch)
            # total_tokens = sum(calculate_number_of_tokens("\n".join(cp)) for cp in batch)
            # print(f"Batch {i+1} contains {len(batch)} checkpoint lists with {total_checkpoints} total checkpoints and {total_tokens} total tokens")
        
        # Process all batches in parallel
        tasks = [merge_multiple_checkpoint_lists(batch, section_info) for batch in batches]
        
        # Wait for all merges to complete
        current_checkpoints = await asyncio.gather(*tasks)
    
    # print(f"Generated {len(current_checkpoints[0])} checkpoints incrementally from {len(list_of_individual_summary_contents)} individual summaries. Number of tokens: {calculate_number_of_tokens(str(current_checkpoints[0]))}")
    return current_checkpoints[0]

# %%
async def generate_checkpoint_list(section, title, description, referenced_ich_guidelines):
    """
    Generate a checklist of questions that should be answered by summaries for this section.
    
    Args:
        section: The section identifier (e.g., "3.2.P.2.2.1")
        title: The title of the section
        description: The description of the section
        referenced_ich_guidelines: List of ICH guidelines referenced
        
    Returns:
        List[str]: A list of checkpoint questions that should be answered in summaries
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section, title, description, referenced_ich_guidelines
            )
            
            # Create a prompt for the LLM to generate a checklist
            system_prompt = "You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to generate a comprehensive list of checkpoint questions for a specific section of of a Pre-IND document."

            # Build the prompt with outer section context if available
            prompt = f"""
First, carefully review the following information:

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            prompt += f"""
Current Section:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                prompt += f"""
Context: You are generating checkpoints for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this subsection contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            prompt += """Your goal is to create a detailed checklist of specific questions that should be answered in summaries for this section. Follow these guidelines:

1. Include all key requirements and recommendations, expressed as specific questions.
2. Cover all aspects mentioned in the title and description.
3. Strictly adhere to the instructions.
4. Address all points from the referenced ICH guidelines.
5. Ensure each question is specific enough to clearly determine if a summary addresses the point.
6. Format each checkpoint as a question that can be answered with yes/no or specific information.
7. Focus on technical and regulatory content needed for Pre-IND documentation.
8. Include questions about physical characteristics, chemical properties, manufacturing processes, and quality control measures where relevant.
9. Cover stability data, packaging specifications, and compatibility information where appropriate.
10. Include questions about the validation and verification methods used.
11. Do not include references to sections/guidelines in the questions themselves.

Before formulating the final list, conduct your analysis within <checkpoint_planning> tags inside your thinking block:
a) Quote key phrases from each input section
b) Break down main themes and requirements
c) List and number potential areas for checkpoint questions

After your analysis, present the final list of checkpoint questions

Each checkpoint should be a string in the list of checkpoints.

Your final output should consist only of the checkpoint list and should not duplicate or rehash any of the work you did in the thinking block.

Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.
"""

            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(prompt)
            
            checkpoint_list_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                retries=3
            )

            response = await rate_limited_agent_call(checkpoint_list_agent, prompt, api_semaphore_for_generator)

            # output_tokens = calculate_number_of_tokens(response.output.model_dump_json())
            # total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 2:
                raise ValueError(f"Expected at least 2 checkpoints, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            traceback.print_exc()
            retry_count += 1
            print(f"Error in generate_checkpoint_list (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning empty list.")
                return []
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # If we somehow exit the loop without returning, return empty list
    return []


# %%
async def evaluate_batch_checkpoint_relevance(summary_batch: List[Dict], section_info: Dict, checkpoint_question: str) -> List[CheckpointRelevanceEvaluation]:
    """
    Evaluate multiple summaries against a single checkpoint question in a batch.
    
    Args:
        summary_batch: List of summary dictionaries to evaluate
        section_info: Dictionary containing section information
        checkpoint_question: Single checkpoint question to evaluate against
        
    Returns:
        List[CheckpointRelevanceEvaluation]: List of evaluation results for each summary
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate multiple summaries against a single checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details."""

            # Build batched summaries content
            batched_summaries_content = "<batched_summaries>\n"
            for i, summary in enumerate(summary_batch, 1):
                summary_id = str(summary.get('_id', f'summary_{i}'))
                summary_content = summary.get('content', '')
                batched_summaries_content += f"<summary_{i}>\nID: {summary_id}\nContent: {summary_content}\n</summary_{i}>\n\n"
            batched_summaries_content += "</batched_summaries>"

            prompt = f"""
Evaluate the relevance of the provided summaries to a specific checkpoint question for the provided Pre-IND section:

{batched_summaries_content}

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                prompt += f"""
Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how each summary contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            prompt += f"""
Please evaluate the relevance of each summary to this specific checkpoint question:
<checkpoint_question>
{checkpoint_question}
</checkpoint_question>

IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.

**Evaluation Instructions:**
1. Evaluate each summary independently against the checkpoint question
2. Apply consistent evaluation criteria across all summaries
3. Use the same scoring standards for all summaries in this batch
4. Consider both direct and indirect relevance to the checkpoint

**Scoring Criteria (0-10 scale):**
- 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information valuable for regulatory submission
- 6-7: Partially addresses the question with some relevant information contributing to understanding
- 4-5: Provides limited but potentially useful information related to the question
- 1-3: Provides minimal information with some connection to the question
- 0: Does not address the question at all

For each summary, provide:
- The summary ID (as provided in the input)
- A relevance score from 0-10
- A brief justification for your score

Your response should evaluate all {len(summary_batch)} summaries in the batch.
"""

            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(prompt)

            output_type = str
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                output_type = BatchedSummaryEvaluation

            critique_evaluation_agent = Agent(
                model=critique_model,
                system_prompt=system_prompt,
                output_type=output_type,
                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),
                retries=3
            )

            result = None
            if "anthropic" in CRITIQUE_MODEL_BASE_URL:
                # Use the semaphore properly for streaming calls
                async with api_semaphore_for_critique:
                    async with critique_evaluation_agent.run_stream(prompt) as response:
                        result = await response.get_output()
            else:
                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)
                result = result.output

            # Process the result
            evaluation_results = []
            
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                # Extract evaluations from structured output
                for eval_result in result.evaluations:
                    evaluation_results.append(CheckpointRelevanceEvaluation(
                        relevance_score=eval_result.relevance_score,
                        justification=eval_result.justification
                    ))
            else:
                # Parse JSON response for non-structured output
                try:
                    # Try to extract JSON from the response
                    json_match = re.search(r'\{.*\}', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        evaluation_data = json.loads(json_str)
                        
                        # Extract evaluations
                        evaluations = evaluation_data.get("evaluations", [])
                        for eval_dict in evaluations:
                            evaluation_results.append(CheckpointRelevanceEvaluation(
                                relevance_score=eval_dict.get("relevance_score", 0.0),
                                justification=eval_dict.get("justification", "Error occurred during evaluation")
                            ))
                    else:
                        # Fallback: create default evaluations
                        print("Warning: Could not extract JSON from batched response")
                        for summary in summary_batch:
                            evaluation_results.append(CheckpointRelevanceEvaluation(
                                relevance_score=0.0,
                                justification="Error occurred during evaluation"
                            ))
                except Exception as e:
                    print(f"Error parsing JSON response: {e}")
                    # Fallback evaluations
                    for summary in summary_batch:
                        evaluation_results.append(CheckpointRelevanceEvaluation(
                            relevance_score=0.0,
                            justification="Error occurred during evaluation"
                        ))

            # output_tokens = calculate_number_of_tokens(str([e.model_dump() for e in evaluation_results]))
            # total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"evaluate_batch_checkpoint_relevance: Evaluated {len(summary_batch)} summaries in batch - Total tokens: {total_tokens}")

            return evaluation_results
            
        except Exception as e:
            retry_count += 1
            print(f"Error in evaluate_batch_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning default evaluations.")
                # Return default evaluations for all summaries in batch
                return [CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Error occurred during evaluation"
                ) for _ in summary_batch]
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return [CheckpointRelevanceEvaluation(
        relevance_score=0.0,
        justification="Unexpected error"
    ) for _ in summary_batch]

async def evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint_question):
    """
    Evaluate if a summary is relevant to a single checkpoint question.
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoint_question: Single checkpoint question to evaluate against
        
    Returns:
        CheckpointRelevanceEvaluation: Structured evaluation result
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate if a given summary addresses a specific checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details."""

            prompt = f"""
Evaluate the relevance of the provided summary to a specific checkpoint question for the provided Pre-IND section:

Summary Content to Evaluate:
<summary_content>
{summary_content}
</summary_content>

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                prompt += f"""
Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this checkpoint contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            prompt += f"""
Please evaluate the relevance of the summary to this specific checkpoint question:
<checkpoint_question>
{checkpoint_question}
</checkpoint_question>

IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.

Evaluation Process:
1. Carefully read the checkpoint question to understand the specific information it's requesting.
2. Analyze the summary content to identify information that directly, indirectly, or partially addresses the checkpoint question.
3. Consider the depth, specificity, and quality of the information provided.
4. Remember that the summary doesn't need to explicitly mention the question itself, only provide relevant information that would help answer it.
5. Give credit to partial relevance - information that addresses part of the question or provides context is valuable.

Conduct your evaluation in the relevance_evaluation thinking block:

1. Quote Extraction and Relevance Assessment:
   - List specific parts of the summary that relate to the checkpoint question.
   - For each quote, explain how well it addresses the question (completely, partially, or provides relevant context).
   - Include information that might be indirectly relevant but still useful for regulatory purposes.

2. Partial Relevance Analysis:
   - Identify content that doesn't fully answer the question but provides relevant background, context, or related information.
   - Consider whether this partial information would be valuable for regulatory professionals working on this section.

3. Depth and Specificity Assessment:
   - Evaluate the level of detail and precision in the relevant information.
   - Consider if the information provides useful context even if not perfectly specific to the checkpoint question.

4. Regulatory Perspective:
   - Elaborate on how a regulatory professional could use this information to address the checkpoint.
   - Consider the value of having comprehensive information for Pre-IND submissions.
   - Assess whether the information contributes to the overall understanding of the regulatory requirements.

5. Scoring Justification:
   - Based on your analysis, assign a preliminary score from 0 to 10 using the following criteria:
     - 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information that would be valuable for regulatory submission
     - 6-7: Partially addresses the question with some relevant information that contributes to understanding the requirements
     - 4-5: Provides limited but potentially useful information related to the question or relevant context
     - 1-3: Provides minimal information that has some connection to the question but is mostly tangential
     - 0: Does not address the question at all or only contains completely irrelevant information
   - Provide a detailed justification for your score, referencing specific elements of your analysis.

IMPORTANT: Be inclusive in your evaluation while maintaining quality standards. For Pre-IND submissions, information that partially addresses a checkpoint or provides valuable context should be scored favorably (8+ range) as comprehensive information gathering is critical for regulatory success. However, still exclude content that is merely tangential or only contains very indirect mentions without substantial relevance.

After your analysis, provide your final evaluation in the following JSON format:

{{
  "relevance_score": <number from 0-10>,
  "justification": "<detailed explanation of your score>"
}}

Your final evaluation should be concise and should not duplicate the detailed work from your relevance evaluation thinking block."""

            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(prompt)

            output_type = str
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                output_type = RelevanceEvaluation

            critique_evaluation_agent = Agent(
                model=critique_model,
                system_prompt=system_prompt,
                output_type=output_type,
                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),
                retries=3
            )

            result = None
            if "anthropic" in CRITIQUE_MODEL_BASE_URL:
                # Use the semaphore properly for streaming calls
                async with api_semaphore_for_critique:
                    async with critique_evaluation_agent.run_stream(prompt) as response:
                        result = await response.get_output()
            else:
                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)
                result = result.output

            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                checkpoint_evaluation = result
            else:
                # Parse the JSON response
                try:
                    # Extract JSON from the response
                    json_match = re.search(r'\{.*\}', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        evaluation_data = json.loads(json_str)
                        
                        # Create CheckpointRelevanceEvaluation object
                        checkpoint_evaluation = CheckpointRelevanceEvaluation(
                            relevance_score=evaluation_data.get("relevance_score", 0.0),
                            justification=evaluation_data.get("justification", "Error occurred during evaluation")
                        )
                    else:
                        # Fallback if JSON parsing fails
                        print("Warning: Could not extract JSON from response, using default evaluation")
                        checkpoint_evaluation = CheckpointRelevanceEvaluation(
                            relevance_score=0.0,
                            justification="Error occurred during evaluation"
                        )
                except Exception as e:
                    print(f"Error parsing JSON response: {e}")
                    # Fallback evaluation
                    checkpoint_evaluation = CheckpointRelevanceEvaluation(
                        relevance_score=0.0,
                        justification="Error occurred during evaluation"
                    )

            # output_tokens = calculate_number_of_tokens(checkpoint_evaluation.model_dump_json())
            # total_tokens = system_tokens + user_tokens + output_tokens

            # Uncomment for detailed token tracking
            # print(f"evaluate_individual_checkpoint_relevance token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")

            return checkpoint_evaluation
            
        except Exception as e:
            retry_count += 1
            print(f"Error in evaluate_individual_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning default evaluation.")
                # Return default evaluation
                return CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Error occurred during evaluation"
                )
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return CheckpointRelevanceEvaluation(
        relevance_score=0.0,
        justification="Unexpected error"
    )

async def evaluate_batch_chunks_checkpoint_relevance(chunk_batch: List[Dict], section_info: Dict, checkpoint_question: str) -> List[CheckpointRelevanceEvaluation]:
    """
    Evaluate multiple chunks against a single checkpoint question in a batch.
    
    Args:
        chunk_batch: List of chunk dictionaries to evaluate
        section_info: Dictionary containing section information
        checkpoint_question: Single checkpoint question to evaluate against
        
    Returns:
        List[CheckpointRelevanceEvaluation]: List of evaluation results for each chunk
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and ICH and FDA guidelines, with exceptional abilities in evaluating the relevance of content to specific technical questions. Your task is to evaluate multiple input document chunks against a single checkpoint question for a Pre-IND section, with a focus on comprehensive information gathering for Pre-IND submissions where having more relevant information is preferred over missing potentially important details."""

            # Build batched chunks content
            batched_chunks_content = "<batched_chunks>\n"
            for i, chunk in enumerate(chunk_batch, 1):
                chunk_id = str(chunk.get('_id', f'chunk_{i}'))
                chunk_content = chunk.get('content', '')
                batched_chunks_content += f"<chunk_{i}>\nID: {chunk_id}\nContent: {chunk_content}\n</chunk_{i}>\n\n"
            batched_chunks_content += "</batched_chunks>"

            prompt = f"""
Evaluate the relevance of the provided input document chunks to a specific checkpoint question for the provided Pre-IND section:

{batched_chunks_content}

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
Referenced FDA Guidelines: {', '.join(current.get('referenced_fda_guidelines', []))}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                prompt += f"""
Context: You are evaluating relevance for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how each chunk contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            prompt += f"""
Please evaluate the relevance of each chunk to this specific checkpoint question:
<checkpoint_question>
{checkpoint_question}
</checkpoint_question>

IMPORTANT CONTEXT: This evaluation is for Pre-IND regulatory submissions where comprehensiveness is crucial. It's better to include potentially relevant information than to miss important details that could impact regulatory review.

**Evaluation Instructions:**
1. Evaluate each chunk independently against the checkpoint question
2. Apply consistent evaluation criteria across all chunks
3. Use the same scoring standards for all chunks in this batch
4. Consider both direct and indirect relevance to the checkpoint

**Scoring Criteria (0-10 scale):**
- 8-10: Addresses the question adequately with specific, relevant information OR provides substantial partial information valuable for regulatory submission
- 6-7: Partially addresses the question with some relevant information contributing to understanding
- 4-5: Provides limited but potentially useful information related to the question
- 1-3: Provides minimal information with some connection to the question
- 0: Does not address the question at all

For each chunk, provide:
- The chunk ID (as provided in the input)
- A relevance score from 0-10
- A brief justification for your score

Your response should evaluate all {len(chunk_batch)} chunks in the batch.
"""

            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(prompt)

            output_type = str
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                output_type = BatchedChunkEvaluation

            critique_evaluation_agent = Agent(
                model=critique_model,
                system_prompt=system_prompt,
                output_type=output_type,
                model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),
                retries=3
            )

            result = None
            if "anthropic" in CRITIQUE_MODEL_BASE_URL:
                # Use the semaphore properly for streaming calls
                async with api_semaphore_for_critique:
                    async with critique_evaluation_agent.run_stream(prompt) as response:
                        result = await response.get_output()
            else:
                result = await rate_limited_agent_call(critique_evaluation_agent, prompt, api_semaphore_for_critique)
                result = result.output

            # Process the result
            evaluation_results = []
            
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                # Extract evaluations from structured output
                for eval_result in result.evaluations:
                    evaluation_results.append(CheckpointRelevanceEvaluation(
                        relevance_score=eval_result.relevance_score,
                        justification=eval_result.justification
                    ))
            else:
                # Parse JSON response for non-structured output
                try:
                    # Try to extract JSON from the response
                    json_match = re.search(r'\{.*\}', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        evaluation_data = json.loads(json_str)
                        
                        # Extract evaluations
                        evaluations = evaluation_data.get("evaluations", [])
                        for eval_dict in evaluations:
                            evaluation_results.append(CheckpointRelevanceEvaluation(
                                relevance_score=eval_dict.get("relevance_score", 0.0),
                                justification=eval_dict.get("justification", "Error occurred during evaluation")
                            ))
                    else:
                        # Fallback: create default evaluations
                        print("Warning: Could not extract JSON from batched chunk response")
                        for chunk in chunk_batch:
                            evaluation_results.append(CheckpointRelevanceEvaluation(
                                relevance_score=0.0,
                                justification="Error occurred during evaluation"
                            ))
                except Exception as e:
                    print(f"Error parsing JSON response: {e}")
                    # Fallback evaluations
                    for chunk in chunk_batch:
                        evaluation_results.append(CheckpointRelevanceEvaluation(
                            relevance_score=0.0,
                            justification="Error occurred during evaluation"
                        ))

            # output_tokens = calculate_number_of_tokens(str([e.model_dump() for e in evaluation_results]))
            # total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"evaluate_batch_chunks_checkpoint_relevance: Evaluated {len(chunk_batch)} chunks in batch - Total tokens: {total_tokens}")

            return evaluation_results
            
        except Exception as e:
            retry_count += 1
            print(f"Error in evaluate_batch_chunks_checkpoint_relevance (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached. Returning default evaluations.")
                # Return default evaluations for all chunks in batch
                return [CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Error occurred during evaluation"
                ) for _ in chunk_batch]
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return [CheckpointRelevanceEvaluation(
        relevance_score=0.0,
        justification="Unexpected error"
    ) for _ in chunk_batch]


# %%
async def evaluate_summary_relevance(summary_content, section_info, checkpoints):
    """
    Use an LLM to evaluate if a summary is relevant to a section by evaluating each checkpoint individually.
    This approach provides more accurate relevance determination by:
    1. Evaluating each checkpoint question individually in parallel
    2. Using a clear 8+ threshold for relevance determination
    3. Aggregating individual scores and applying threshold to final average
    
    Args:
        summary_content: The content of the summary to evaluate
        section_info: Dictionary containing section information
        checkpoints: List of checkpoint questions that should be answered
        
    Returns:
        Tuple[bool, float, int]: Tuple containing boolean relevance, average score, and number of relevant checkpoints
    """
    # Handle empty checkpoint list
    if not checkpoints:
        print("No checkpoints provided for evaluation")
        return False, 0.0, 0
    
    try:
        print(f"Evaluating summary relevance using {len(checkpoints)} individual checkpoints")
        
        # Create tasks for parallel evaluation of each checkpoint
        tasks = [
            evaluate_individual_checkpoint_relevance(summary_content, section_info, checkpoint)
            for checkpoint in checkpoints
        ]
        
        # Execute all evaluations in parallel
        checkpoint_evaluations = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle any exceptions
        valid_evaluations = []
        for i, evaluation in enumerate(checkpoint_evaluations):
            if isinstance(evaluation, Exception):
                print(f"Error evaluating checkpoint {i+1}: {evaluation}")
                # Create default evaluation for failed checkpoint
                valid_evaluations.append(CheckpointRelevanceEvaluation(
                    relevance_score=0.0,
                    justification="Evaluation failed"
                ))
            else:
                valid_evaluations.append(evaluation)
        
        # Calculate simple average of all individual scores
        total_checkpoints = len(valid_evaluations)
        total_score = sum(eval.relevance_score for eval in valid_evaluations)
        average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0
        number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)
        
        # Determine if summary is relevant based on average score
        is_relevant = number_of_relevant_checkpoints >= 1

        if not is_relevant:
            print("Summary is not relevant, evaluations:", "\n".join([f"Relevance Score: {eval.relevance_score:.2f}, Justification: {eval.justification}" for eval in valid_evaluations]))

        return is_relevant, average_score, number_of_relevant_checkpoints
        
    except Exception as e:
        print(f"Error in evaluate_summary_relevance: {e}")
        traceback.print_exc()
        return False, 0.0, 0

async def evaluate_multiple_summaries_relevance(summaries: List[Dict], section_info: Dict, checkpoints: List[str]) -> List[Tuple[bool, float, int]]:
    """
    Efficiently evaluate multiple summaries against multiple checkpoints using parallel batched evaluation.
    
    PERFORMANCE OPTIMIZATION:
    - Batches summaries together for each checkpoint (reduces API calls by 5-10x)
    - Processes ALL checkpoints in parallel (instead of sequentially)
    - Uses semaphore-controlled concurrency for optimal resource usage
    
    Example: 10 summaries × 15 checkpoints = 150 individual calls
    → Becomes ~8 parallel batch calls (18x faster!)
    
    Args:
        summaries: List of summary dictionaries to evaluate
        section_info: Dictionary containing section information
        checkpoints: List of checkpoint questions that should be answered
        
    Returns:
        List of tuples for each summary containing (is_relevant, average_score, number_of_relevant_checkpoints)
    """
    # Handle empty inputs
    if not summaries or not checkpoints:
        print("No summaries or checkpoints provided for evaluation")
        return [(False, 0.0, 0) for _ in summaries]
    
    try:        
        print(f"Evaluating {len(summaries)} summaries against {len(checkpoints)} checkpoints using batched approach")
        
        # Initialize results matrix: [summary_index][checkpoint_index] = evaluation
        summary_evaluations = [[None for _ in checkpoints] for _ in summaries]
        
        # Prepare all checkpoint processing tasks for parallel execution
        print(f"Preparing parallel processing for {len(checkpoints)} checkpoints...")
        
        # Create all batch tasks for all checkpoints
        checkpoint_tasks = []
        checkpoint_batch_info = []  # Store info needed for result processing
        
        for checkpoint_idx, checkpoint in enumerate(checkpoints):
            print(f"Preparing checkpoint {checkpoint_idx + 1}/{len(checkpoints)}: {checkpoint[:100]}...")
            
            # Create batches for this checkpoint
            batches = create_summary_batches(summaries, checkpoint)
            print(f"Created {len(batches)} batches for checkpoint {checkpoint_idx + 1}")
            
            # Create tasks for all batches of this checkpoint
            for batch_idx, batch in enumerate(batches):
                task = evaluate_batch_checkpoint_relevance(batch, section_info, checkpoint)
                checkpoint_tasks.append(task)
                
                # Store metadata for result processing
                checkpoint_batch_info.append({
                    'checkpoint_idx': checkpoint_idx,
                    'batch_idx': batch_idx,
                    'batch_size': len(batch),
                    'batch_start_summary_idx': sum(len(b) for b in batches[:batch_idx])  # Calculate starting summary index for this batch
                })
        
        print(f"🚀 Executing {len(checkpoint_tasks)} batch evaluation tasks in parallel across {len(checkpoints)} checkpoints...")
        
        # Execute ALL batch tasks across ALL checkpoints in parallel
        all_batch_results = await asyncio.gather(*checkpoint_tasks, return_exceptions=True)
        
        print(f"✅ Completed parallel execution of {len(checkpoint_tasks)} batch evaluation tasks")
        
        # Process results and assign to summary_evaluations matrix
        for task_idx, (batch_result, batch_info) in enumerate(zip(all_batch_results, checkpoint_batch_info)):
            checkpoint_idx = batch_info['checkpoint_idx']
            batch_idx = batch_info['batch_idx']
            batch_size = batch_info['batch_size']
            batch_start_idx = batch_info['batch_start_summary_idx']
            
            if isinstance(batch_result, Exception):
                print(f"Error evaluating batch {batch_idx + 1} for checkpoint {checkpoint_idx + 1}: {batch_result}")
                # Create default evaluations for failed batch
                for i in range(batch_size):
                    summary_idx = batch_start_idx + i
                    summary_evaluations[summary_idx][checkpoint_idx] = CheckpointRelevanceEvaluation(
                        relevance_score=0.0,
                        justification="Batch evaluation failed"
                    )
            else:
                # Assign evaluations to corresponding summaries
                for i, evaluation in enumerate(batch_result):
                    summary_idx = batch_start_idx + i
                    summary_evaluations[summary_idx][checkpoint_idx] = evaluation
        
        # Calculate final results for each summary
        final_results = []
        for summary_idx, summary in enumerate(summaries):
            evaluations = summary_evaluations[summary_idx]
            
            # Filter out None evaluations (shouldn't happen but safety check)
            valid_evaluations = [eval for eval in evaluations if eval is not None]
            
            if not valid_evaluations:
                final_results.append((False, 0.0, 0))
                continue
            
            # Calculate metrics
            total_checkpoints = len(valid_evaluations)
            total_score = sum(eval.relevance_score for eval in valid_evaluations)
            average_score = total_score / total_checkpoints if total_checkpoints > 0 else 0.0
            number_of_relevant_checkpoints = sum(1 for eval in valid_evaluations if eval.relevance_score >= RELEVANCE_THRESHOLD)
            
            # Determine if summary is relevant
            is_relevant = number_of_relevant_checkpoints >= 1
            
            if not is_relevant:
                print(f"Summary {summary.get('_id', summary_idx)} is not relevant, avg score: {average_score:.2f}")
            else:
                print(f"Summary {summary.get('_id', summary_idx)} is relevant, avg score: {average_score:.2f}, relevant checkpoints: {number_of_relevant_checkpoints}")
            
            final_results.append((is_relevant, average_score, number_of_relevant_checkpoints))
        
        return final_results
        
    except Exception as e:
        print(f"Error in evaluate_multiple_summaries_relevance: {e}")
        traceback.print_exc()
        return [(False, 0.0, 0) for _ in summaries]

# %%
async def search_summaries_by_llm(section_info, ich_guidelines, summary_level=1):
    """
    Search for summaries in MongoDB based on relevance to the section using an LLM.
    
    Args:
        section_info: Dictionary containing section information
        ich_guidelines: List of ICH guidelines to search in (e.g., ["Q6A", "Q6B"])
        summary_level: Level of summary to search (1 for final summary, 2 for individual summaries)
        
    Returns:
        List[Dict]: List of relevant summaries with their content and metadata
    """
    db = mongodb_client['mednova']
    summaries = []

    collection_mapping = get_mongodb_collection_from_referenced_guidelines(ich_guidelines)
    
    # Create tasks for parallel collection queries
    async def fetch_from_collection(collection_name, key_name):
        collection = db[collection_name]
        return await collection.find({
            key_name: {"$in": ich_guidelines},
            "summary_level": summary_level
        }).to_list()
    
    # Execute all collection queries in parallel
    tasks = [
        fetch_from_collection(collection_name, collection_mapping["key_name"][index])
        for index, collection_name in enumerate(collection_mapping["summaries"])
    ]
    
    results = await asyncio.gather(*tasks)
    
    # Combine all results
    for summaries_for_collection in results:
        summaries.extend(summaries_for_collection)

    print(f"Found {len(summaries)} summaries for section {section_info.get('section', '')} ICH guidelines {ich_guidelines} at level {summary_level}")
    
    if not summaries:
        print(f"No summaries found for ICH guidelines {ich_guidelines} at level {summary_level}")
        return []
    
    # Get checkpoints for the section
    checkpoints = section_info.get("checkpoint_list", [])
    if not checkpoints:
        print(f"No checkpoints found for section {section_info.get('section', '')}")
        # Generate checkpoints if not already available
        checkpoints = await generate_checkpoint_list(
            section_info.get("section", ""),
            section_info.get("title", ""),
            section_info.get("description", ""),
            section_info.get("referenced_ich_guidelines", [])
        )
    
    # Choose evaluation method based on configuration
    if USE_BATCHED_EVALUATION and len(summaries) > 1:
        # Use batched approach for better performance with fallback to individual evaluation
        try:
            results = await evaluate_multiple_summaries_relevance(summaries, section_info, checkpoints)
        except Exception as e:
            print(f"Batched evaluation failed: {e}, falling back to individual evaluation")
            # Fallback to individual evaluation
            evaluation_tasks = [
                evaluate_summary_relevance(summary["content"], section_info, checkpoints)
                for summary in summaries
            ]
            results = await asyncio.gather(*evaluation_tasks)
    else:
        # Use individual evaluation (for testing/comparison or single summary)
        print(f"Using individual evaluation for {len(summaries)} summaries")
        evaluation_tasks = [
            evaluate_summary_relevance(summary["content"], section_info, checkpoints)
            for summary in summaries
        ]
        results = await asyncio.gather(*evaluation_tasks)
    
    # Process results and print evaluation outcomes
    evaluated_summaries = []
    for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(summaries, results):
        print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
        
        if is_relevant:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT")
            summary["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
            evaluated_summaries.append(summary)
        else:
            print(f"search_summaries_by_llm, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
    
    # No need to sort by relevance score since all relevant summaries have same score
    # evaluated_summaries.sort(key=lambda x: x["similarity_score"], reverse=True)
    
    return evaluated_summaries


# %%
async def get_summaries_for_final_summary(final_summary_id: str, summary_name: str, chunk_name: str, key_name: str) -> List[Dict[str, Any]]:
    """
    Get the individual summaries associated with a final summary.
    
    Args:
        final_summary_id: The ID of the final summary
        
    Returns:
        List[Dict]: List of individual summaries with their content and metadata
    """
    db = mongodb_client['mednova']
    summary_collection = db[summary_name]
    
    # Get the final summary
    final_summary = await summary_collection.find_one({"_id": ObjectId(final_summary_id)})
    
    if not final_summary or "summary_reference_ids" not in final_summary:
        print(f"No summary references found for final summary {final_summary_id}")
        return []
    
    # Get the individual summaries
    summary_ids = final_summary["summary_reference_ids"]
    individual_summaries = list(await summary_collection.find({"_id": {"$in": summary_ids}}).to_list())
    
    return individual_summaries


# %%
async def filter_summaries_by_section(section_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Filter summaries based on section information, going from summary level 1 to summary level 2 to chunks.
    
    Args:
        section_info: Dictionary containing section information (section, title, description, etc.)
        
    Returns:
        Tuple[List, List, List]: Lists of relevant final summaries, individual summaries, and chunks
    """
    # Extract section information
    section = section_info.get("section", "")
    title = section_info.get("title", "")
    description = section_info.get("description", "")
    referenced_ich_guidelines = section_info.get("referenced_ich_guidelines", [])
    
    # Step 1: Search for relevant final summaries (level 1)
    final_summaries = await search_summaries_by_llm(
        section_info=section_info,
        ich_guidelines=referenced_ich_guidelines,
        summary_level=1
    )
    
    # Step 2: For each final summary, get relevant individual summaries (level 2) - parallel processing
    individual_summaries = []
    
    # Create tasks for parallel processing of final summaries
    async def process_final_summary(final_summary):
        summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(final_summary)
        summaries = await get_summaries_for_final_summary(final_summary["_id"], summary_name, chunk_name, key_name)
        
        # Evaluate all individual summaries using batched approach for better performance
        evaluated_summaries = []
        
        # Choose evaluation method based on configuration
        if USE_BATCHED_EVALUATION and len(summaries) > 1:
            # Use batched evaluation for better performance with fallback to individual evaluation
            try:
                results = await evaluate_multiple_summaries_relevance(summaries, section_info, section_info.get("checkpoint_list", []))
            except Exception as e:
                print(f"Batched evaluation failed: {e}, falling back to individual evaluation")
                # Fallback to individual evaluation
                evaluation_tasks = [
                    evaluate_summary_relevance(summary["content"], section_info, section_info.get("checkpoint_list", []))
                    for summary in summaries
                ]
                results = await asyncio.gather(*evaluation_tasks)
        else:
            # Use individual evaluation (for testing/comparison or single summary)
            print(f"Using individual evaluation for {len(summaries)} summaries")
            evaluation_tasks = [
                evaluate_summary_relevance(summary["content"], section_info, section_info.get("checkpoint_list", []))
                for summary in summaries
            ]
            results = await asyncio.gather(*evaluation_tasks)
        
        # Process results and print evaluation outcomes
        for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(summaries, results):
            print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
            
            if is_relevant:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is RELEVANT")
                summary["similarity_score"] = 1.0  # Set to 1.0 since it's relevant
                evaluated_summaries.append(summary)
            else:
                print(f"filter_summaries_by_section, Summary {summary['_id']} section {section_info.get('section', '')} is NOT RELEVANT")
        
        return evaluated_summaries
    
    # Execute all final summary processing tasks in parallel
    final_summary_tasks = [process_final_summary(final_summary) for final_summary in final_summaries]
    final_summary_results = await asyncio.gather(*final_summary_tasks)
    
    # Flatten the results
    for evaluated_summaries in final_summary_results:
        individual_summaries.extend(evaluated_summaries)
    
    # Step 3: For each individual summary, get relevant chunks
    chunks = []
    # BATCH_SIZE = 50
    
    # for i in range(0, len(individual_summaries), BATCH_SIZE):
    #     batch = individual_summaries[i:i + BATCH_SIZE]
    #     batch_tasks = []
        
    #     for individual_summary in batch:
    #         summary_chunks = get_chunks_for_summary(individual_summary["_id"])
    #         # Create tasks for parallel evaluation of chunks
    #         tasks = [evaluate_summary_relevance(chunk["content"], section_info, section_info.get("quality_guideline_combined_summaries_checkpoint_list", [])) for chunk in summary_chunks]
    #         batch_tasks.extend(zip(summary_chunks, tasks))
        
    #     # Execute all tasks in parallel
    #     results = await asyncio.gather(*[task for _, task in batch_tasks])
        
    #     # Process results
    #     for (chunk, _), (relevance_score) in zip(batch_tasks, results):
    #         if relevance_score >= get_relevance_threshold(0):
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    #             chunk["similarity_score"] = relevance_score
    #             chunks.append(chunk)
    #         else:
    #             print(f"filter_input_docs_by_section, Chunk {chunk['_id']} section {section_info.get('section', '')} with relevance score {relevance_score}, threshold {get_relevance_threshold(0)}")
    
    return final_summaries, individual_summaries, chunks


# %%
async def extract_relevant_chunks(json_obj):
    """
    Recursively iterates through the nested JSON structure, extracts titles and descriptions,
    generates checkpoint lists, and finds relevant summaries and chunks for each section.
    
    Args:
        json_obj: The JSON object to process
        
    Returns:
        Dict: The processed JSON object with added relevant summaries and chunks
    """
    # First, collect all sections that need processing
    sections_to_process = []
    
    def collect_sections(obj):
        if isinstance(obj, dict):
            # Check if this is a section that needs processing
            if (obj and "title" in obj and "description" in obj and 
                "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"]):
                sections_to_process.append(obj)
            
            # Process each key-value pair in the dictionary
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value)
    
    # Collect all sections that need processing
    collect_sections(json_obj)
    
    # Process all sections in parallel
    async def process_section(section):
        # Generate checkpoint list for this section
        section["checkpoint_list"] = await generate_checkpoint_list(
            section["section"], 
            section["title"], 
            section["description"], 
            section.get("referenced_ich_guidelines", [])
        )
        
        # Find relevant summaries and chunks for this section
        final_summaries, individual_summaries, chunks = await filter_summaries_by_section(
            section
        )

        # Print summary of results
        print(f"Section {section.get('section', 'unknown')}: Found {len(final_summaries)} relevant final summaries, {len(individual_summaries)} relevant individual summaries, and {len(chunks)} relevant chunks")

        section["relevant_final_summaries"] = []
        section["relevant_individual_summaries"] = []

        for s in final_summaries:
            summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(s)
            section["relevant_final_summaries"].append({
                "_id": str(s["_id"]), 
                "ich_guideline": s[key_name], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            })
        
        for s in individual_summaries:
            summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(s)
            section["relevant_individual_summaries"].append({
                "_id": str(s["_id"]), 
                "ich_guideline": s[key_name], 
                "similarity_score": s["similarity_score"], 
                "content": s["content"]
            })

        # Create a final summary from the relevant individual summaries
        # if len(section["relevant_individual_summaries"]) > 0:
        #     final_summary = await create_combined_summary([summary["content"] for summary in section["relevant_individual_summaries"]])
        #     section["quality_guideline_combined_summary"] = final_summary
        # else:
        #     # If no relevant summaries found, create a summary from section information
        #     final_summary = await generate_summary_from_section_info(section)
        #     section["quality_guideline_combined_summary"] = final_summary

        # Generate checkpoints incrementally from individual summaries
        if len(section["relevant_individual_summaries"]) > 0:
            checkpoints = await generate_checkpoints_incrementally_using_individual_summaries(
                section,
                [summary["content"] for summary in section["relevant_individual_summaries"]]
            )
        else:
            # If no relevant summaries found, generate checkpoints from section information
            checkpoints = await generate_checkpoint_list(
                section["section"],
                section["title"],
                section["description"],
                section.get("referenced_ich_guidelines", [])
            )
        
        print(f"Generated {len(checkpoints)} checkpoints incrementally for section {section.get('section', '')}")
        section["ich_guideline_combined_summaries_checkpoint_list"] = checkpoints
        
        return section
    
    # Process all sections sequentially with intermediate saves
    if sections_to_process:
        # processed_sections = await asyncio.gather(*[process_section(section) for section in sections_to_process])
        
        # Update the original sections with the processed results
        # for i, section in enumerate(sections_to_process):
        #     section.update(processed_sections[i])

        for i, section in enumerate(sections_to_process):
            print(f"Processing section {i+1}/{len(sections_to_process)}: {section.get('section', 'unknown')}")
            processed_section = await process_section(section)
            section.update(processed_section)
            
            # Save intermediate progress after each section
            write_to_json(json_obj, "processed_ectd_guidelines_with_relevant_chunks.json")
            
            print(f"✅ Completed section {i+1}/{len(sections_to_process)}: {section.get('section', 'unknown')}")
    
    return json_obj


# %%
processed_doc = await extract_relevant_chunks(doc_to_work_on)

# Save the results to a JSON file
write_to_json(processed_doc, "processed_ectd_guidelines_with_relevant_chunks.json")


# %%

def load_drug_type_definitions(drug_types_json_path: str = "structured jsons/drug_types.json") -> Dict:
    """
    Load and format drug type definitions for classification prompts.
    
    Args:
        drug_types_json_path: Path to the drug types JSON file
        
    Returns:
        Dict containing formatted drug type definitions
    """
    try:
        drug_types_data = read_json(drug_types_json_path)
        
        # Extract the 6 main drug types (sections 1-6)
        formatted_definitions = {}
        
        for section_num in ["1", "2", "3", "4", "5", "6"]:
            if section_num in drug_types_data:
                section_data = drug_types_data[section_num]
                formatted_definitions[section_data["title"]] = {
                    "title": section_data["title"],
                    "description": section_data["description"],
                    "referenced_ich_guidelines": section_data.get("referenced_ich_guidelines", []),
                    "referenced_fda_guidelines": section_data.get("referenced_fda_guidelines", [])
                }
        
        return formatted_definitions
        
    except Exception as e:
        print(f"Error loading drug type definitions: {e}")
        # Return basic fallback definitions
        return {
            "Small-Molecule Drugs": {"title": "Small-Molecule Drugs", "description": "Chemical drugs with low molecular weight"},
            "Biologic Therapeutics: Therapeutic Proteins (including Monoclonal Antibodies - mAbs)": {"title": "Therapeutic Proteins", "description": "Protein-based therapeutics"},
            "Biologic Therapeutics: Vaccines": {"title": "Vaccines", "description": "Biological preparations for immunity"},
            "Cell Therapy Products": {"title": "Cell Therapy Products", "description": "Live cell therapeutics"},
            "Gene Therapy Products": {"title": "Gene Therapy Products", "description": "Genetic modification therapeutics"},
            "Combination Products": {"title": "Combination Products", "description": "Multi-component products"}
        }

async def generate_drug_type_checkpoints(drug_type_name: str, drug_type_definition: Dict) -> List[str]:
    """
    Generate checkpoint questions for a specific drug type for Pre-IND/IND/NDA submissions.
    
    Args:
        drug_type_name: Name of the drug type (e.g., 'Small-Molecule Drugs')
        drug_type_definition: Dictionary containing drug type definition and guidelines
        
    Returns:
        List[str]: A list of checkpoint questions relevant to the drug type
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = """You are an expert pharmaceutical regulatory scientist specializing in drug development and regulatory submissions (Pre-IND, IND, NDA). Your task is to generate comprehensive checkpoint questions for a specific drug type that will be used to identify the most relevant regulatory guidelines and requirements."""

            user_prompt = f"""
You are creating checkpoint questions for drug type classification and regulatory guideline identification.

<drug_type_information>
<drug_type_name>{drug_type_name}</drug_type_name>
<description>{drug_type_definition.get('description', '')}</description>
<referenced_ich_guidelines>{', '.join(drug_type_definition.get('referenced_ich_guidelines', []))}</referenced_ich_guidelines>
<referenced_fda_guidelines>{', '.join(drug_type_definition.get('referenced_fda_guidelines', []))}</referenced_fda_guidelines>
</drug_type_information>

Your goal is to create a comprehensive list of checkpoint questions that will help identify the most relevant regulatory guidelines and requirements for this specific drug type. These questions will be used to:

1. Filter relevant ICH and FDA guideline summaries
2. Identify drug-type-specific regulatory requirements
3. Guide Pre-IND, IND, and NDA preparation for this drug type

**CRITICAL REQUIREMENTS:**

1. **Regulatory Context**: Focus on Pre-IND, IND, and NDA submission requirements specific to this drug type
2. **Technical Specificity**: Include drug-type-specific technical and scientific considerations
3. **Manufacturing & Quality**: Address drug-type-specific manufacturing, quality control, and characterization requirements
4. **Safety & Efficacy**: Include drug-type-specific safety evaluation and efficacy assessment considerations
5. **Regulatory Pathway**: Consider unique regulatory pathways and requirements for this drug type

**Guidelines for Checkpoint Creation:**

1. Each checkpoint should be a specific question that can help identify relevant regulatory guidance
2. Focus on what makes this drug type unique from a regulatory perspective
3. Include questions about drug-type-specific manufacturing processes and controls
4. Address unique safety and efficacy considerations for this drug type
5. Include questions about drug-type-specific regulatory pathways and requirements
6. Cover CMC (Chemistry, Manufacturing, and Controls) aspects specific to this drug type
7. Include questions about drug-type-specific preclinical and clinical considerations
8. Format each checkpoint as a clear, specific question
9. Do not include references to specific guidelines in the questions themselves
10. Ensure questions are applicable across Pre-IND, IND, and NDA contexts

Before generating the final list, conduct your analysis in <checkpoint_planning> tags inside your thinking block:
a) Identify key regulatory characteristics of this drug type
b) List unique manufacturing and quality considerations
c) Identify specific safety and efficacy assessment needs
d) Consider unique regulatory pathway requirements
e) Plan checkpoint questions to cover all critical areas

After your analysis, provide the final list of checkpoint questions.

Each checkpoint should be a specific question that helps identify relevant regulatory guidance for this drug type.

Begin your response with your checkpoint planning, then provide the final checkpoint list in the specified format.
"""

            checkpoint_agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=CheckpointList,
                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                retries=3
            )

            response = await rate_limited_agent_call(checkpoint_agent, user_prompt, api_semaphore_for_generator)
            checkpoints = response.output.checkpoints
            
            # Validate the output
            if len(checkpoints) < 3:
                raise ValueError(f"Expected at least 3 checkpoints for drug type {drug_type_name}, got: {len(checkpoints)}")
            
            return checkpoints
            
        except Exception as e:
            retry_count += 1
            print(f"Error in generate_drug_type_checkpoints for {drug_type_name} (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print(f"Maximum retries reached for {drug_type_name}. Returning empty list.")
                return []
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    return []

async def search_ich_guidelines_for_drug_type(drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:
    """
    Search for relevant ICH guideline summaries for a specific drug type.
    
    Args:
        drug_type_info: Dictionary containing drug type information including referenced ICH guidelines
        checkpoints: List of checkpoint questions for filtering
        
    Returns:
        List[Dict]: Relevant ICH guideline summaries
    """
    referenced_ich_guidelines = drug_type_info.get('referenced_ich_guidelines', [])
    
    if not referenced_ich_guidelines:
        print(f"No ICH guidelines referenced for drug type: {drug_type_info.get('title', 'Unknown')}")
        return []
    
    # Create a section_info-like structure for compatibility with existing functions
    drug_type_section_info = {
        'section': f"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}",
        'title': drug_type_info.get('title', ''),
        'description': drug_type_info.get('description', ''),
        'referenced_ich_guidelines': referenced_ich_guidelines,
        'checkpoint_list': checkpoints
    }
    
    # Use the existing search function
    relevant_summaries = await search_summaries_by_llm(
        section_info=drug_type_section_info,
        ich_guidelines=referenced_ich_guidelines,
        summary_level=1
    )
    
    return relevant_summaries

async def search_fda_guidelines_for_drug_type(drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:
    """
    Search for relevant FDA guideline summaries for a specific drug type using batched evaluation.
    
    Args:
        drug_type_info: Dictionary containing drug type information including referenced FDA guidelines
        checkpoints: List of checkpoint questions for filtering
        
    Returns:
        List[Dict]: Relevant FDA guideline summaries
    """
    referenced_fda_guidelines = drug_type_info.get('referenced_fda_guidelines', [])
    
    if not referenced_fda_guidelines:
        print(f"No FDA guidelines referenced for drug type: {drug_type_info.get('title', 'Unknown')}")
        return []
    
    print(f"Searching FDA guidelines for drug type {drug_type_info.get('title', '')}: {referenced_fda_guidelines}")
    
    # Create a section_info-like structure for compatibility
    drug_type_section_info = {
        'section': f"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}",
        'title': drug_type_info.get('title', ''),
        'description': drug_type_info.get('description', ''),
        'referenced_fda_guidelines': referenced_fda_guidelines,
        'checkpoint_list': checkpoints
    }
    
    # Fetch from FDA collections using parallel queries (same pattern as search_summaries_by_llm)
    db = mongodb_client['mednova']
    fda_summaries_collection = db['fda_docs_summaries']
    
    # Create tasks for parallel collection queries
    async def fetch_fda_summaries_for_guideline(fda_guideline):
        return await fda_summaries_collection.find({
            "fda_guideline": fda_guideline,
            "summary_level": 1  # Get top-level summaries first
        }).to_list()
    
    # Execute all FDA guideline queries in parallel
    tasks = [
        fetch_fda_summaries_for_guideline(fda_guideline)
        for fda_guideline in referenced_fda_guidelines
    ]
    
    results = await asyncio.gather(*tasks)
    
    # Combine all results and add source metadata
    all_fda_summaries = []
    for fda_guideline, fda_summaries_for_guideline in zip(referenced_fda_guidelines, results):
        print(f"Found {len(fda_summaries_for_guideline)} FDA summaries for guideline: {fda_guideline}")
        for summary in fda_summaries_for_guideline:
            # Add source metadata to each summary
            summary["source_type"] = "FDA"
            summary["source_guideline"] = fda_guideline
            all_fda_summaries.append(summary)

    print(f"Found {len(all_fda_summaries)} total FDA summaries for drug type {drug_type_info.get('title', '')} across {len(referenced_fda_guidelines)} guidelines")
    
    if not all_fda_summaries:
        print(f"No FDA summaries found for guidelines {referenced_fda_guidelines}")
        return []
    
    # Use batched evaluation (same pattern as search_summaries_by_llm)
    if USE_BATCHED_EVALUATION and len(all_fda_summaries) > 1:
        # Use batched approach for better performance with fallback to individual evaluation
        try:
            results = await evaluate_multiple_summaries_relevance(all_fda_summaries, drug_type_section_info, checkpoints)
        except Exception as e:
            print(f"Batched evaluation failed: {e}, falling back to individual evaluation")
            # Fallback to individual evaluation
            evaluation_tasks = [
                evaluate_summary_relevance(summary["content"], drug_type_section_info, checkpoints)
                for summary in all_fda_summaries
            ]
            results = await asyncio.gather(*evaluation_tasks)
    else:
        # Use individual evaluation (for testing/comparison or single summary)
        print(f"Using individual evaluation for {len(all_fda_summaries)} FDA summaries")
        evaluation_tasks = [
            evaluate_summary_relevance(summary["content"], drug_type_section_info, checkpoints)
            for summary in all_fda_summaries
        ]
        results = await asyncio.gather(*evaluation_tasks)
    
    # Process results and collect relevant summaries (same pattern as search_summaries_by_llm)
    relevant_fda_summaries = []
    for summary, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(all_fda_summaries, results):
        print(f"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is {'RELEVANT' if is_relevant else 'NOT RELEVANT'} with average score {average_score} and {number_of_relevant_checkpoints} relevant checkpoints")
        
        if is_relevant:
            print(f"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is RELEVANT")
            summary["similarity_score"] = average_score
            relevant_fda_summaries.append(summary)
        else:
            print(f"search_fda_guidelines_for_drug_type, FDA Summary {summary['_id']} for {summary['source_guideline']} is NOT RELEVANT")
    
    print(f"Found {len(relevant_fda_summaries)} relevant FDA summaries for drug type")
    return relevant_fda_summaries

async def get_individual_summaries_for_drug_type_guidelines(relevant_summaries: List[Dict], drug_type_info: Dict, checkpoints: List[str]) -> List[Dict]:
    """
    Get individual summaries for the relevant drug type guidelines.
    
    Args:
        relevant_summaries: List of relevant top-level summaries (ICH and FDA)
        drug_type_info: Drug type information
        checkpoints: Checkpoint questions for filtering
        
    Returns:
        List[Dict]: Relevant individual summaries
    """
    individual_summaries = []
    
    # Create section_info for compatibility
    drug_type_section_info = {
        'section': f"DRUG_TYPE_{drug_type_info.get('title', '').replace(' ', '_').upper()}",
        'title': drug_type_info.get('title', ''),
        'description': drug_type_info.get('description', ''),
        'checkpoint_list': checkpoints
    }
    
    async def process_guideline_summary(summary):
        try:
            source_type = summary.get('source_type', 'ICH')  # Default to ICH if not specified
            
            if source_type == 'FDA':
                # Handle FDA summaries
                summary_id = summary["_id"]
                db = mongodb_client['mednova']
                fda_summaries_collection = db['fda_docs_summaries']
                
                # Get individual summaries for this FDA guideline
                individual_fda_summaries = await fda_summaries_collection.find({
                    "fda_guideline": summary.get('source_guideline', ''),
                    "summary_level": 2  # Individual level summaries
                }).to_list()
                
            else:
                # Handle ICH summaries (existing logic)
                summary_name, chunk_name, key_name = get_mongodb_collection_from_summary(summary)
                individual_fda_summaries = await get_summaries_for_final_summary(
                    summary["_id"], summary_name, chunk_name, key_name
                )
            
            # Evaluate individual summaries
            evaluated_summaries = []
            
            if individual_fda_summaries:
                try:
                    results = await evaluate_multiple_summaries_relevance(
                        individual_fda_summaries, drug_type_section_info, checkpoints
                    )
                    
                    for summary_item, (is_relevant, average_score, number_of_relevant_checkpoints) in zip(individual_fda_summaries, results):
                        if is_relevant:
                            print(f"Individual summary {summary_item['_id']} for drug type is RELEVANT with score {average_score}")
                            summary_item["similarity_score"] = average_score
                            summary_item["source_type"] = source_type
                            evaluated_summaries.append(summary_item)
                        else:
                            print(f"Individual summary {summary_item['_id']} for drug type is NOT RELEVANT")
                            
                except Exception as e:
                    print(f"Error evaluating individual summaries: {e}")
            
            return evaluated_summaries
            
        except Exception as e:
            print(f"Error processing guideline summary {summary.get('_id', 'unknown')}: {e}")
            return []
    
    # Process all summaries in parallel
    summary_tasks = [process_guideline_summary(summary) for summary in relevant_summaries]
    summary_results = await asyncio.gather(*summary_tasks)
    
    # Flatten results
    for evaluated_summaries in summary_results:
        individual_summaries.extend(evaluated_summaries)
    
    print(f"Found {len(individual_summaries)} relevant individual summaries for drug type")
    return individual_summaries

async def combine_drug_type_summaries(individual_summaries: List[Dict], drug_type_info: Dict) -> str:
    """
    Combine individual drug type guideline summaries into a comprehensive summary.
    
    Args:
        individual_summaries: List of relevant individual summaries
        drug_type_info: Drug type information
        
    Returns:
        str: Combined comprehensive summary for the drug type
    """
    if not individual_summaries:
        return f"No relevant guideline summaries found for {drug_type_info.get('title', 'this drug type')}."
    
    # Extract content from summaries
    summary_contents = [summary.get('content', '') for summary in individual_summaries if summary.get('content', '').strip()]
    
    if not summary_contents:
        return f"No valid content found in summaries for {drug_type_info.get('title', 'this drug type')}."
    
    # Use divide and conquer approach similar to checkpoint merging
    return await combine_summaries_incrementally(summary_contents, drug_type_info)

def create_summary_content_batches(summary_contents: List[str], drug_type_info: Dict, max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_summaries_per_batch: int = 3) -> List[List[str]]:
    """
    Create batches of summary contents for efficient combination using token-based logic.
    
    Args:
        summary_contents: List of summary content strings
        drug_type_info: Drug type information (used for token calculation context)
        max_batch_tokens: Maximum tokens per batch
        max_summaries_per_batch: Maximum number of summaries per batch
        
    Returns:
        List of batches, where each batch is a list of summary content strings
    """
    if not summary_contents:
        return []
    
    # Calculate base tokens for system prompt and drug type context (estimated)
    drug_type_context = f"Drug Type: {drug_type_info.get('title', '')}\nDescription: {drug_type_info.get('description', '')}"
    base_tokens = calculate_number_of_tokens(drug_type_context) + 2000  # Buffer for system prompt and formatting
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for summary_content in summary_contents:
        # Calculate tokens for this summary content including XML formatting
        summary_tokens = calculate_number_of_tokens(f"<summary_{len(current_batch) + 1}>\n{summary_content}\n</summary_{len(current_batch) + 1}>")
        
        # Check if adding this summary would exceed limits
        if (current_tokens + summary_tokens > max_batch_tokens or 
            len(current_batch) >= max_summaries_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add summary to current batch
        current_batch.append(summary_content)
        current_tokens += summary_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

async def combine_summaries_incrementally(summary_contents: List[str], drug_type_info: Dict) -> str:
    """
    Combine summary contents incrementally using divide and conquer approach with token-based batching.
    
    Args:
        summary_contents: List of summary content strings
        drug_type_info: Drug type information
        
    Returns:
        str: Combined comprehensive summary
    """
    if not summary_contents:
        return ""
    
    if len(summary_contents) == 1:
        return await _combine_single_summary_for_drug_type(summary_contents[0], drug_type_info)
    
    print(f"Combining {len(summary_contents)} summary contents incrementally with token-based batching")
    
    # Iteratively merge summaries using token-based batching
    current_summaries = summary_contents.copy()
    
    while len(current_summaries) > 1:
        # Create optimal batches based on token count and number of summaries
        batches = create_summary_content_batches(current_summaries, drug_type_info)
        
        print(f"Created {len(batches)} batches for summary combination")
        for i, batch in enumerate(batches):
            total_tokens = sum(calculate_number_of_tokens(summary) for summary in batch)
            print(f"Batch {i+1} contains {len(batch)} summaries with {total_tokens} total tokens")
        
        # Ensure no batch has only one item to avoid infinite loop
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, merge it with the last batch
        if single_item_batch is not None:
            if final_batches:
                final_batches[-1].extend(single_item_batch)
            else:
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        # Process all batches in parallel
        batch_tasks = [
            _combine_summary_batch_for_drug_type(batch, drug_type_info)
            for batch in batches
        ]
        
        # Wait for all batches to complete
        current_summaries = await asyncio.gather(*batch_tasks)
        
        # Filter out any empty results
        current_summaries = [summary for summary in current_summaries if summary and summary.strip()]
        
        print(f"Combined {len(batches)} batches into {len(current_summaries)} intermediate results")
        
        # Safety check to prevent infinite loops
        if len(current_summaries) == len(batches) and all(len(batch) == 1 for batch in batches):
            # If we're not making progress and all batches are single items, just concatenate
            print("Warning: No progress in incremental combination, concatenating results")
            return "\n\n---\n\n".join(current_summaries)
    
    print(f"Final incremental combination complete")
    return current_summaries[0] if current_summaries else ""

async def _combine_single_summary_for_drug_type(summary_content: str, drug_type_info: Dict) -> str:
    """
    Process a single summary for drug type context.
    
    Args:
        summary_content: The summary content
        drug_type_info: Drug type information
        
    Returns:
        str: Processed summary with drug type context
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = """You are an expert pharmaceutical regulatory scientist. Your task is to process and contextualize regulatory guideline summaries specifically for a given drug type in the context of Pre-IND, IND, and NDA submissions."""
            
            user_prompt = f"""
You are processing a regulatory guideline summary for the drug type: **{drug_type_info.get('title', '')}**

Drug Type Description: {drug_type_info.get('description', '')}

Regulatory Summary to Process:
<summary_content>
{summary_content}
</summary_content>

Your task is to process this summary and present it in a way that is specifically relevant to {drug_type_info.get('title', 'this drug type')} for regulatory submissions (Pre-IND, IND, NDA).

Before processing the summary, conduct a thorough analysis in your thinking block using <summary_processing_analysis> tags:

1. **Content Analysis**: 
   - Read through the regulatory summary and identify key themes
   - Note specific regulatory requirements, guidelines, and recommendations
   - Identify technical terminology and regulatory language used

2. **Drug Type Relevance Assessment**:
   - Analyze which parts of the summary are most relevant to {drug_type_info.get('title', 'this drug type')}
   - Identify content that may not be applicable to this specific drug type
   - Consider unique characteristics of {drug_type_info.get('title', 'this drug type')} from the description

3. **Regulatory Areas Mapping**:
   - Categorize relevant content by regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)
   - Identify which regulatory submission stages (Pre-IND, IND, NDA) each requirement applies to
   - Note any drug-type-specific regulatory pathways or considerations

4. **Content Organization Strategy**:
   - Plan how to structure the processed summary for maximum clarity
   - Determine the logical flow of information for regulatory scientists
   - Consider how to emphasize the most critical requirements for this drug type

5. **Processing Approach**:
   - Decide what information to emphasize, de-emphasize, or exclude
   - Plan how to maintain technical accuracy while improving drug-type relevance
   - Consider how to make the content actionable for regulatory submissions

Now, based on your analysis, process the summary following these guidelines:

**Processing Instructions:**
1. Extract and emphasize information that is specifically relevant to {drug_type_info.get('title', 'this drug type')}
2. Organize the content by key regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)
3. Maintain the technical accuracy and regulatory specificity
4. Focus on requirements that would be applicable to Pre-IND, IND, and NDA submissions
5. Present the information in a clear, structured format with proper headings
6. Remove or de-emphasize information that is not relevant to this drug type
7. Use markdown formatting for better readability and structure
8. Highlight critical requirements and drug-type-specific considerations

Present your final processed summary in <final_processed_summary> tags. The content within these tags should be a comprehensive, well-organized summary that regulatory scientists can use to understand the specific requirements for {drug_type_info.get('title', 'this drug type')}. Your final output should not duplicate or rehash any of the work you did in the thinking block.
"""

            agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=str,
                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                retries=3
            )

            response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
            
            # Extract content from final_processed_summary tags
            extracted_content = extract_content_from_tags(response.output, 'final_processed_summary')
            return extracted_content
            
        except Exception as e:
            retry_count += 1
            print(f"Error in _combine_single_summary_for_drug_type (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                return summary_content  # Return original content if all retries fail
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    return summary_content

async def _combine_summary_batch_for_drug_type(summary_batch: List[str], drug_type_info: Dict) -> str:
    """
    Combine a batch of summaries for drug type context.
    
    Args:
        summary_batch: List of summary contents to combine
        drug_type_info: Drug type information
        
    Returns:
        str: Combined summary
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = """You are an expert pharmaceutical regulatory scientist. Your task is to combine multiple regulatory guideline summaries into a comprehensive, coherent summary specifically for a given drug type in the context of Pre-IND, IND, and NDA submissions."""
            
            # Prepare summary content
            summaries_text = ""
            for i, summary in enumerate(summary_batch, 1):
                summaries_text += f"<summary_{i}>\n{summary}\n</summary_{i}>\n\n"
            
            user_prompt = f"""
You are combining multiple regulatory guideline summaries for the drug type: **{drug_type_info.get('title', '')}**

Drug Type Description: {drug_type_info.get('description', '')}

Regulatory Summaries to Combine:
{summaries_text}

Your task is to create a comprehensive, unified summary that combines all the relevant information from these summaries specifically for {drug_type_info.get('title', 'this drug type')}.

Before combining the summaries, conduct a thorough analysis in your thinking block using <summary_combination_analysis> tags:

1. **Individual Summary Review**:
   - Read through each regulatory summary and identify their key contributions
   - Note unique information and overlapping content across summaries
   - Identify the strongest regulatory guidance from each source

2. **Content Categorization**:
   - Group similar content from different summaries by regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)
   - Identify which summaries provide the most comprehensive guidance for each area
   - Note any conflicting or contradictory information between summaries

3. **Drug Type Relevance Assessment**:
   - Analyze which combined content is most critical for {drug_type_info.get('title', 'this drug type')}
   - Consider the unique regulatory challenges and requirements for this drug type
   - Prioritize information based on Pre-IND, IND, and NDA submission needs

4. **Integration Strategy**:
   - Plan how to merge overlapping information without redundancy
   - Determine how to resolve any contradictions (prioritize more comprehensive/recent guidance)
   - Design the structure and flow for the combined summary

5. **Quality Enhancement Approach**:
   - Identify opportunities to strengthen the combined guidance
   - Plan how to create logical connections between different regulatory areas
   - Consider how to make the final summary more actionable for regulatory scientists

Now, based on your analysis, combine the summaries following these guidelines:

**Combination Instructions:**
1. Combine all relevant information while avoiding redundancy
2. Organize by key regulatory areas (CMC, Safety, Efficacy, Manufacturing, Quality Control, etc.)
3. Emphasize information most critical to {drug_type_info.get('title', 'this drug type')}
4. Ensure the combined summary is coherent and well-structured with clear headings
5. Maintain technical accuracy and regulatory specificity
6. Focus on Pre-IND, IND, and NDA submission requirements
7. Resolve any contradictions by providing the most comprehensive guidance
8. Create a unified narrative that flows logically
9. Use markdown formatting for better readability and organization
10. Highlight the most critical requirements and drug-type-specific considerations

Present your final combined summary in <final_combined_summary> tags. The content within these tags should be a comprehensive, well-organized combined summary that regulatory scientists can use as a single reference for {drug_type_info.get('title', 'this drug type')} requirements. Your final output should not duplicate or rehash any of the work you did in the thinking block.
"""

            agent = Agent(
                model=generator_model,
                system_prompt=system_prompt,
                output_type=str,
                model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                retries=3
            )

            response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
            
            # Extract content from final_combined_summary tags
            extracted_content = extract_content_from_tags(response.output, 'final_combined_summary')
            return extracted_content
            
        except Exception as e:
            retry_count += 1
            print(f"Error in _combine_summary_batch_for_drug_type (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                # Fallback: concatenate summaries with basic formatting
                return "\n\n---\n\n".join(summary_batch)
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    return "\n\n---\n\n".join(summary_batch)

async def process_drug_type_guidelines(drug_type_name: str, drug_type_definition: Dict) -> Dict:
    """
    Process guidelines for a single drug type.
    
    Args:
        drug_type_name: Name of the drug type
        drug_type_definition: Drug type definition from JSON
        
    Returns:
        Dict: Processed drug type guideline information
    """
    print(f"\n{'='*80}")
    print(f"PROCESSING DRUG TYPE: {drug_type_name}")
    print(f"{'='*80}")
    
    # Step 1: Generate checkpoints for this drug type
    print(f"Step 1: Generating checkpoints for {drug_type_name}...")
    checkpoints = await generate_drug_type_checkpoints(drug_type_name, drug_type_definition)
    print(f"Generated {len(checkpoints)} checkpoints for {drug_type_name}")
    
    if not checkpoints:
        print(f"No checkpoints generated for {drug_type_name}, skipping...")
        return {
            "drug_type_name": drug_type_name,
            "drug_type_definition": drug_type_definition,
            "checkpoints": [],
            "relevant_ich_summaries": [],
            "relevant_fda_summaries": [],
            "individual_summaries": [],
            "combined_summary": f"No guidelines found for {drug_type_name}.",
            "processing_status": "failed"
        }
    
    # Step 2: Search for relevant ICH guidelines
    print(f"Step 2: Searching relevant ICH guidelines for {drug_type_name}...")
    relevant_ich_summaries = await search_ich_guidelines_for_drug_type(drug_type_definition, checkpoints)
    print(f"Found {len(relevant_ich_summaries)} relevant ICH summaries for {drug_type_name}")
    
    # Step 3: Search for relevant FDA guidelines
    print(f"Step 3: Searching relevant FDA guidelines for {drug_type_name}...")
    relevant_fda_summaries = await search_fda_guidelines_for_drug_type(drug_type_definition, checkpoints)
    print(f"Found {len(relevant_fda_summaries)} relevant FDA summaries for {drug_type_name}")
    
    # Step 4: Get individual summaries
    print(f"Step 4: Getting individual summaries for {drug_type_name}...")
    all_relevant_summaries = relevant_ich_summaries + relevant_fda_summaries
    individual_summaries = await get_individual_summaries_for_drug_type_guidelines(
        all_relevant_summaries, drug_type_definition, checkpoints
    )
    print(f"Found {len(individual_summaries)} relevant individual summaries for {drug_type_name}")
    
    # Step 5: Combine summaries into final comprehensive summary
    print(f"Step 5: Combining summaries for {drug_type_name}...")
    combined_summary = await combine_drug_type_summaries(individual_summaries, drug_type_definition)
    print(f"Generated combined summary for {drug_type_name} ({len(combined_summary)} characters)")
    
    result = {
        "drug_type_name": drug_type_name,
        "drug_type_definition": drug_type_definition,
        "checkpoints": checkpoints,
        "relevant_ich_summaries": [
            {
                "_id": str(summary["_id"]),
                "content": summary.get("content", "")[:500] + "..." if len(summary.get("content", "")) > 500 else summary.get("content", ""),
                "similarity_score": summary.get("similarity_score", 0.0),
                "source_type": "ICH"
            } for summary in relevant_ich_summaries
        ],
        "relevant_fda_summaries": [
            {
                "_id": str(summary["_id"]),
                "content": summary.get("content", "")[:500] + "..." if len(summary.get("content", "")) > 500 else summary.get("content", ""),
                "similarity_score": summary.get("similarity_score", 0.0),
                "source_type": "FDA"
            } for summary in relevant_fda_summaries
        ],
        "individual_summaries": [
            {
                "_id": str(summary["_id"]),
                "content": summary.get("content", "")[:300] + "..." if len(summary.get("content", "")) > 300 else summary.get("content", ""),
                "similarity_score": summary.get("similarity_score", 0.0),
                "source_type": summary.get("source_type", "ICH")
            } for summary in individual_summaries
        ],
        "combined_summary": combined_summary,
        "processing_status": "success"
    }
    
    print(f"✅ Completed processing {drug_type_name}")
    return result

async def preprocess_all_drug_type_guidelines(drug_types_json_path: str = "structured jsons/drug_types.json") -> Dict:
    """
    Preprocess guidelines for all drug types and store results.
    
    Args:
        drug_types_json_path: Path to drug types JSON file
        
    Returns:
        Dict: Complete drug type guidelines processing results
    """
    print(f"\n{'='*100}")
    print("STARTING DRUG TYPE GUIDELINES PREPROCESSING")
    print(f"{'='*100}")
    
    # Load drug type definitions
    drug_type_definitions = load_drug_type_definitions(drug_types_json_path)
    print(f"Loaded {len(drug_type_definitions)} drug type definitions")
    
    # Process all drug types in parallel
    drug_type_tasks = [
        process_drug_type_guidelines(drug_type_name, drug_type_definition)
        for drug_type_name, drug_type_definition in drug_type_definitions.items()
    ]
    
    print(f"🚀 Processing all {len(drug_type_tasks)} drug types in parallel...")
    drug_type_results = await asyncio.gather(*drug_type_tasks, return_exceptions=True)
    
    # Process results and handle exceptions
    processed_drug_types = {}
    successful_count = 0
    failed_count = 0
    
    for i, result in enumerate(drug_type_results):
        drug_type_name = list(drug_type_definitions.keys())[i]
        
        if isinstance(result, Exception):
            print(f"❌ Error processing {drug_type_name}: {result}")
            failed_count += 1
            processed_drug_types[drug_type_name] = {
                "drug_type_name": drug_type_name,
                "processing_status": "failed",
                "error": str(result),
                "combined_summary": f"Error processing guidelines for {drug_type_name}: {result}"
            }
        else:
            print(f"✅ Successfully processed {drug_type_name}")
            successful_count += 1
            processed_drug_types[drug_type_name] = result
    
    # Create final result structure
    final_result = {
        "drug_type_guidelines": processed_drug_types,
        "metadata": {
            "processing_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_drug_types": len(drug_type_definitions),
            "successful_processing": successful_count,
            "failed_processing": failed_count,
            "drug_types_processed": list(drug_type_definitions.keys())
        }
    }
    
    print(f"\n{'='*100}")
    print("DRUG TYPE GUIDELINES PREPROCESSING COMPLETE")
    print(f"{'='*100}")
    print(f"✅ Successfully processed: {successful_count}/{len(drug_type_definitions)} drug types")
    print(f"❌ Failed processing: {failed_count}/{len(drug_type_definitions)} drug types")
    
    return final_result

# %%

# DRUG TYPE GUIDELINES PREPROCESSING
# Preprocess guidelines for all drug types based on the classification results
print("\n" + "="*100)
print("STARTING DRUG TYPE GUIDELINES PREPROCESSING")
print("="*100)

# Preprocess all drug type guidelines
drug_type_guidelines_result = await preprocess_all_drug_type_guidelines(drug_types_json_path=DRUG_TYPES_JSON_PATH)

# Save the drug type guidelines processing results
write_to_json(drug_type_guidelines_result, "drug_type_guidelines_processed.json")

print(f"\n🎯 Drug Type Guidelines Preprocessing Results:")
print(f"📋 Total Drug Types: {drug_type_guidelines_result['metadata']['total_drug_types']}")
print(f"✅ Successfully Processed: {drug_type_guidelines_result['metadata']['successful_processing']}")
print(f"❌ Failed Processing: {drug_type_guidelines_result['metadata']['failed_processing']}")
print(f"📄 Detailed report saved to: drug_type_guidelines_processed.json")

# %%

def inject_drug_type_context_into_prompt(
    base_prompt: str,
    drug_type_context: Dict
) -> str:
    """
    Inject drug type regulatory context at the very start of prompts.
    
    Args:
        base_prompt: The original prompt string
        drug_type_context: Drug type context from get_drug_type_context_for_gap_analysis()
        
    Returns:
        Enhanced prompt with drug type context at the beginning
    """
    if not drug_type_context.get('classification_available', False):
        return base_prompt
    
    enhanced_context = drug_type_context.get('enhanced_context', '')
    
    if not enhanced_context.strip():
        return base_prompt
    
    # Simply add context at the very beginning of the prompt
    enhanced_prompt = enhanced_context + base_prompt
    
    return enhanced_prompt

def create_drug_classification_batches(chunks: List[Dict], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_chunks_per_batch: int = 15) -> List[List[Dict]]:
    """
    Create batches of chunks for efficient drug type classification.
    
    Args:
        chunks: List of chunk dictionaries to classify
        max_batch_tokens: Maximum tokens per batch
        max_chunks_per_batch: Maximum number of chunks per batch
        
    Returns:
        List of batches, where each batch is a list of chunks
    """
    if not chunks:
        return []
    
    # Calculate base tokens for system prompt and drug type definitions (estimated)
    base_tokens = 8000  # Buffer for system prompt, drug type definitions, and structured output
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for chunk in chunks:
        # Calculate tokens for this chunk including XML formatting
        chunk_content = chunk.get('content', '')
        chunk_id = str(chunk.get('_id', 'unknown'))
        
        # Estimate tokens including XML formatting
        chunk_tokens = calculate_number_of_tokens(f"<chunk_{len(current_batch) + 1}>\nID: {chunk_id}\nContent: {chunk_content}\n</chunk_{len(current_batch) + 1}>")
        
        # Check if adding this chunk would exceed limits
        if (current_tokens + chunk_tokens > max_batch_tokens or 
            len(current_batch) >= max_chunks_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add chunk to current batch
        current_batch.append(chunk)
        current_tokens += chunk_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

# %%

async def classify_drug_type_batch(chunk_batch: List[Dict], drug_type_definitions: Dict) -> DrugTypeClassification:
    """
    Classify drug type for a single batch of chunks using LLM.
    
    Args:
        chunk_batch: List of chunk dictionaries to classify
        drug_type_definitions: Dictionary containing drug type definitions
        
    Returns:
        DrugTypeClassification: Structured classification result with scores for all 6 drug types
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            system_prompt = """You are an expert pharmaceutical regulatory scientist specializing in drug development and classification for regulatory submissions. Your task is to analyze input document chunks and classify the drug type described across all 6 regulatory categories used in Pre-IND, IND, and NDA submissions."""

            # Build batched chunks content
            batched_chunks_content = "<input_document_chunks>\n"
            for i, chunk in enumerate(chunk_batch, 1):
                chunk_id = str(chunk.get('_id', f'chunk_{i}'))
                chunk_content = chunk.get('content', '')
                batched_chunks_content += f"<chunk_{i}>\nID: {chunk_id}\nContent: {chunk_content}\n</chunk_{i}>\n\n"
            batched_chunks_content += "</input_document_chunks>"

            # Build drug type definitions content
            drug_types_content = "<drug_type_definitions>\n"
            for drug_type_name, definition in drug_type_definitions.items():
                drug_types_content += f"<drug_type>\n"
                drug_types_content += f"<name>{drug_type_name}</name>\n"
                drug_types_content += f"<description>{definition['description']}</description>\n"
                if definition.get('referenced_ich_guidelines'):
                    drug_types_content += f"<referenced_ich_guidelines>{', '.join(definition['referenced_ich_guidelines'])}</referenced_ich_guidelines>\n"
                if definition.get('referenced_fda_guidelines'):
                    drug_types_content += f"<referenced_fda_guidelines>{', '.join(definition['referenced_fda_guidelines'])}</referenced_fda_guidelines>\n"
                drug_types_content += f"</drug_type>\n\n"
            drug_types_content += "</drug_type_definitions>"

            user_prompt = f"""
You are analyzing input document chunks for drug type classification in the context of regulatory submissions (Pre-IND, IND, NDA). 

{batched_chunks_content}

{drug_types_content}

**CRITICAL CLASSIFICATION REQUIREMENTS:**

1. **Regulatory Context**: This classification is for regulatory submission purposes (Pre-IND/IND/NDA), so focus on regulatory-relevant characteristics and terminology used in submissions.

2. **Mandatory Analysis**: You MUST evaluate ALL 6 drug types and provide scores for each, even if some seem unlikely.

3. **High Standards for High Scores**: Only assign scores ≥8 when you have strong, clear evidence from the input documents. Be conservative and rigorous in your scoring.

4. **Evidence-Based Scoring**: Base your scores on specific textual evidence from the input chunks, not general assumptions.

Before providing your final classification, conduct a thorough analysis in your thinking block using <drug_classification_analysis> tags:

1. **Document Content Review**: 
   - Summarize the key content themes across all chunks
   - Identify manufacturing processes, mechanisms of action, regulatory terminology
   - Note any specific technical details that indicate drug type

2. **Evidence Mapping for Each Drug Type**:
   For each of the 6 drug types, analyze:
   - What evidence FROM THE INPUT DOCUMENTS supports this classification
   - What evidence contradicts this classification  
   - Quote specific phrases or technical terms that are relevant
   - Consider regulatory context and terminology used

3. **Scoring Rationale**:
   - Explain your confidence level for each drug type (0-10 scale)
   - Justify why certain types score higher than others
   - Ensure you're being appropriately conservative (high scores only for strong evidence)

4. **Primary Classification Logic**:
   - Determine which drug type has the strongest evidence
   - Consider if this is a clear case or if multiple types are possible
   - Assess overall confidence level

**SCORING GUIDELINES:**
- **9-10**: Overwhelming evidence, multiple clear indicators, high regulatory confidence
- **8-8.9**: Strong evidence, clear indicators, good regulatory confidence  
- **6-7.9**: Moderate evidence, some indicators, medium confidence
- **4-5.9**: Weak evidence, few indicators, low confidence
- **1-3.9**: Minimal evidence, contradictory indicators, very low confidence
- **0**: No evidence or strong contradictory evidence

Your final response must include:
- Scores (0-10) for ALL 6 drug types with detailed justifications
- Key evidence excerpts from the input documents for each score
- Primary classification (highest scoring drug type)
- Overall confidence level assessment
- Summary justification for the primary classification

Focus on regulatory submission context and be rigorous in your evidence requirements for high confidence scores.
"""

            output_type = str
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                output_type = DrugTypeClassification

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=critique_model,
                    system_prompt=system_prompt,
                    output_type=output_type,
                    model_settings=get_model_settings(CRITIQUE_MODEL_BASE_URL, 0.1),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_critique)
                return response.output

            # Use the retry wrapper to handle empty responses
            result = await retry_on_empty_response(make_api_call)

            # Process the result
            if get_structured_output_using_pydantic(CRITIQUE_MODEL_BASE_URL):
                return result
            else:
                # Parse JSON response for non-structured output
                try:
                    # Try to extract JSON from the response
                    json_match = re.search(r'\{.*\}', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        classification_data = json.loads(json_str)
                        
                        # Create DrugTypeClassification object
                        drug_type_scores = []
                        for score_data in classification_data.get("drug_type_scores", []):
                            drug_type_scores.append(DrugTypeScore(
                                drug_type_name=score_data.get("drug_type_name", ""),
                                score=score_data.get("score", 0.0),
                                justification=score_data.get("justification", ""),
                                key_evidence=score_data.get("key_evidence", [])
                            ))
                        
                        return DrugTypeClassification(
                            drug_type_scores=drug_type_scores,
                            primary_classification=classification_data.get("primary_classification", "Unknown"),
                            confidence_level=classification_data.get("confidence_level", "uncertain"),
                            overall_justification=classification_data.get("overall_justification", "")
                        )
                    else:
                        # Fallback if JSON parsing fails
                        print("Warning: Could not extract JSON from drug type classification response")
                        retry_count += 1
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON response from drug type classification: {e}")
                    retry_count += 1
                
        except Exception as e:
            retry_count += 1
            print(f"Error in classify_drug_type_batch (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Returning default classification.")
                # Return default classification with all drug types scored at 0
                drug_type_names = list(drug_type_definitions.keys())
                default_scores = [
                    DrugTypeScore(
                        drug_type_name=name,
                        score=0.0,
                        justification="Error occurred during classification",
                        key_evidence=[]
                    ) for name in drug_type_names
                ]
                return DrugTypeClassification(
                    drug_type_scores=default_scores,
                    primary_classification="Unknown",
                    confidence_level="uncertain",
                    overall_justification="Error occurred during classification"
                )
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    drug_type_names = list(drug_type_definitions.keys())
    default_scores = [
        DrugTypeScore(
            drug_type_name=name,
            score=0.0,
            justification="Unexpected error during classification",
            key_evidence=[]
        ) for name in drug_type_names
    ]
    return DrugTypeClassification(
        drug_type_scores=default_scores,
        primary_classification="Unknown",
        confidence_level="uncertain",
        overall_justification="Unexpected error during classification"
    )

async def aggregate_drug_type_classifications(batch_classifications: List[DrugTypeClassification], drug_type_definitions: Dict) -> AggregatedDrugTypeClassification:
    """
    Aggregate drug type classifications from multiple batches into a final classification.
    
    Args:
        batch_classifications: List of classification results from different batches
        drug_type_definitions: Dictionary containing drug type definitions
        
    Returns:
        AggregatedDrugTypeClassification: Final aggregated classification result
    """
    if not batch_classifications:
        # Return default classification if no batches
        drug_type_names = list(drug_type_definitions.keys())
        default_scores = [
            DrugTypeScore(
                drug_type_name=name,
                score=0.0,
                justification="No batches available for classification",
                key_evidence=[]
            ) for name in drug_type_names
        ]
        return AggregatedDrugTypeClassification(
            final_classification="Unknown",
            aggregated_scores=default_scores,
            confidence_level="uncertain",
            batch_count=0,
            consensus_strength="weak",
            final_justification="No classification batches available"
        )
    
    # Collect all drug type names from the first classification (should be consistent)
    drug_type_names = [score.drug_type_name for score in batch_classifications[0].drug_type_scores]
    
    # Aggregate scores for each drug type
    aggregated_scores = []
    for drug_type_name in drug_type_names:
        # Collect scores for this drug type across all batches
        type_scores = []
        type_justifications = []
        type_evidence = []
        
        for classification in batch_classifications:
            for score_obj in classification.drug_type_scores:
                if score_obj.drug_type_name == drug_type_name:
                    type_scores.append(score_obj.score)
                    if score_obj.justification:
                        type_justifications.append(score_obj.justification)
                    type_evidence.extend(score_obj.key_evidence)
                    break
        
        # Calculate aggregated score (weighted average with higher weights for higher scores)
        if type_scores:
            # Sort scores and weight them (higher scores get more weight)
            sorted_scores = sorted(type_scores, reverse=True)
            if len(sorted_scores) >= 3:
                # Weight top 3 scores: 50%, 30%, 20%
                aggregated_score = (sorted_scores[0] * 0.5 + sorted_scores[1] * 0.3 + sorted_scores[2] * 0.2)
            elif len(sorted_scores) == 2:
                # Weight top 2 scores: 70%, 30%
                aggregated_score = (sorted_scores[0] * 0.7 + sorted_scores[1] * 0.3)
            else:
                # Single score
                aggregated_score = sorted_scores[0]
        else:
            aggregated_score = 0.0
        
        # Combine justifications (take top justifications)
        combined_justification = " | ".join(type_justifications[:3]) if type_justifications else "No justification available"
        
        # Deduplicate evidence
        unique_evidence = list(set(type_evidence))[:5]  # Top 5 unique evidence pieces
        
        aggregated_scores.append(DrugTypeScore(
            drug_type_name=drug_type_name,
            score=round(aggregated_score, 2),
            justification=combined_justification,
            key_evidence=unique_evidence
        ))
    
    # Determine final classification (highest scoring drug type)
    if aggregated_scores:
        best_score = max(aggregated_scores, key=lambda x: x.score)
        final_classification = best_score.drug_type_name
        max_score = best_score.score
    else:
        final_classification = "Unknown"
        max_score = 0.0
    
    # Determine final confidence level
    if max_score >= 8.0:
        confidence_level = "high"
    elif max_score >= 6.0:
        confidence_level = "medium"
    elif max_score >= 4.0:
        confidence_level = "low"
    else:
        confidence_level = "uncertain"
    
    # Calculate consensus strength
    batch_count = len(batch_classifications)
    if batch_count <= 1:
        consensus_strength = "weak"
    else:
        # Check how many batches agree on the top classification
        top_classifications = [cls.primary_classification for cls in batch_classifications]
        top_count = top_classifications.count(final_classification)
        consensus_ratio = top_count / batch_count
        
        if consensus_ratio >= 0.8:
            consensus_strength = "strong"
        elif consensus_ratio >= 0.6:
            consensus_strength = "moderate"
        else:
            consensus_strength = "weak"
    
    # Create final justification
    final_justification = f"Based on analysis of {batch_count} document batches, {final_classification} scored highest ({max_score:.2f}/10) with {consensus_strength} consensus across batches."
    
    return AggregatedDrugTypeClassification(
        final_classification=final_classification,
        aggregated_scores=aggregated_scores,
        confidence_level=confidence_level,
        batch_count=batch_count,
        consensus_strength=consensus_strength,
        final_justification=final_justification
    )

async def perform_drug_type_classification(all_input_chunks: List[Dict], drug_types_json_path: str = "structured jsons/drug_types.json") -> AggregatedDrugTypeClassification:
    """
    Perform comprehensive drug type classification on all input document chunks.
    
    Args:
        all_input_chunks: List of all input document chunks to classify
        drug_types_json_path: Path to the drug types JSON file
        
    Returns:
        AggregatedDrugTypeClassification: Final classification result with confidence scores
    """
    print("\n" + "="*80)
    print("STARTING DRUG TYPE CLASSIFICATION")
    print("="*80)
    
    # Load drug type definitions
    drug_type_definitions = load_drug_type_definitions(drug_types_json_path)
    print(f"Loaded {len(drug_type_definitions)} drug type definitions")
    
    # Filter out chunks with empty content
    valid_chunks = [chunk for chunk in all_input_chunks if chunk.get('content', '').strip()]
    
    if not valid_chunks:
        print("No valid chunks found for classification")
        return await aggregate_drug_type_classifications([], drug_type_definitions)
    
    print(f"Processing {len(valid_chunks)} valid input document chunks...")
    
    # Create batches for processing
    batches = create_drug_classification_batches(valid_chunks)
    print(f"Created {len(batches)} batches for drug type classification")
    
    # Process all batches in parallel
    print(f"🚀 Processing all {len(batches)} batches in parallel...")
    
    # Create tasks for all batches
    batch_tasks = [
        classify_drug_type_batch(batch, drug_type_definitions)
        for batch in batches
    ]
    
    # Execute all batch tasks in parallel
    batch_classifications = await asyncio.gather(*batch_tasks, return_exceptions=True)
    
    # Process results and handle any exceptions
    valid_classifications = []
    for i, classification in enumerate(batch_classifications):
        if isinstance(classification, Exception):
            print(f"Error processing batch {i+1}: {classification}")
            # Create default classification for failed batch
            drug_type_names = list(drug_type_definitions.keys())
            default_scores = [
                DrugTypeScore(
                    drug_type_name=name,
                    score=0.0,
                    justification="Batch processing failed",
                    key_evidence=[]
                ) for name in drug_type_names
            ]
            default_classification = DrugTypeClassification(
                drug_type_scores=default_scores,
                primary_classification="Unknown",
                confidence_level="uncertain",
                overall_justification="Batch processing failed"
            )
            valid_classifications.append(default_classification)
        else:
            valid_classifications.append(classification)
    
    print(f"✅ Completed processing {len(valid_classifications)} batches")
    
    # Aggregate results from all batches
    print("Aggregating results from all batches...")
    final_classification = await aggregate_drug_type_classifications(valid_classifications, drug_type_definitions)
    
    print("\n" + "="*80)
    print("DRUG TYPE CLASSIFICATION COMPLETE")
    print("="*80)
    print(f"Final Classification: {final_classification.final_classification}")
    print(f"Confidence Level: {final_classification.confidence_level}")
    print(f"Consensus Strength: {final_classification.consensus_strength}")
    print(f"Batches Processed: {final_classification.batch_count}")
    
    # Print top scores for all drug types
    print("\nScores for all drug types:")
    for score in final_classification.aggregated_scores:
        print(f"- {score.drug_type_name}: {score.score:.2f}/10")
    
    print("="*80)
    
    return final_classification

# %%

# Get all input chunks from MongoDB for drug type classification
print("Fetching all input document chunks for drug type classification...")
db = mongodb_client['mednova']
chunk_collection = db['pre_ind_input_docs_chunks']

# Fetch all chunks and convert ObjectIds to strings for JSON serialization in parallel
async def process_chunk_for_classification(chunk):
    # Convert ObjectId to string for JSON serialization
    if '_id' in chunk:
        chunk['_id'] = str(chunk['_id'])
    return chunk

# Fetch all chunks first
raw_chunks = await chunk_collection.find({}).to_list()

# Process all chunks in parallel
all_input_chunks_for_classification = await asyncio.gather(*[process_chunk_for_classification(chunk) for chunk in raw_chunks])

print(f"Retrieved {len(all_input_chunks_for_classification)} total input document chunks for drug type classification")

# Perform drug type classification
drug_classification_result = await perform_drug_type_classification(all_input_chunks=all_input_chunks_for_classification, drug_types_json_path=DRUG_TYPES_JSON_PATH)

# Save the classification results
classification_report = {
    'drug_type_classification': {
        'metadata': {
            'classification_date': time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_chunks_analyzed': len(all_input_chunks_for_classification),
            'final_classification': drug_classification_result.final_classification,
            'confidence_level': drug_classification_result.confidence_level,
            'consensus_strength': drug_classification_result.consensus_strength,
            'batch_count': drug_classification_result.batch_count
        },
        'detailed_results': drug_classification_result.model_dump()
    }
}

write_to_json(classification_report, "drug_type_classification_report.json")

print(f"\n🎯 Drug Type Classification Results:")
print(f"📋 Final Classification: {drug_classification_result.final_classification}")
print(f"🎯 Confidence Level: {drug_classification_result.confidence_level}")
print(f"🤝 Consensus Strength: {drug_classification_result.consensus_strength}")
print(f"📊 Chunks Analyzed: {len(all_input_chunks_for_classification)}")
print(f"📄 Detailed report saved to: drug_type_classification_report.json")

# %%

def load_drug_type_classification_result(classification_file_path: str = "drug_type_classification_report.json") -> Dict:
    """
    Load the drug type classification result from the saved JSON file.
    
    Args:
        classification_file_path: Path to the classification report JSON file
        
    Returns:
        Dict containing classification metadata and detailed results
    """
    try:
        classification_data = read_json(classification_file_path)
        return classification_data.get('drug_type_classification', {})
    except Exception as e:
        print(f"Error loading drug type classification result: {e}")
        return {}

def load_drug_type_processed_guidelines(guidelines_file_path: str = "drug_type_guidelines_processed.json") -> Dict:
    """
    Load the processed drug type guidelines from the saved JSON file.
    
    Args:
        guidelines_file_path: Path to the processed guidelines JSON file
        
    Returns:
        Dict containing processed guidelines for all drug types
    """
    try:
        guidelines_data = read_json(guidelines_file_path)
        return guidelines_data.get('drug_type_guidelines', {})
    except Exception as e:
        print(f"Error loading drug type processed guidelines: {e}")
        return {}

def get_drug_type_context_for_gap_analysis(
    classification_file_path: str = "drug_type_classification_report.json",
    guidelines_file_path: str = "drug_type_guidelines_processed.json"
) -> Dict:
    """
    Get simplified drug type context for gap analysis enhancement.
    
    Args:
        classification_file_path: Path to the classification report JSON file
        guidelines_file_path: Path to the processed guidelines JSON file
        
    Returns:
        Dict containing:
        {
            'classification_available': bool,
            'primary_drug_type': str,
            'relevant_guidelines_summary': str,
            'enhanced_context': str  # formatted context ready for prompt injection
        }
    """
    # Load classification result
    classification_result = load_drug_type_classification_result(classification_file_path)
    
    if not classification_result:
        return {
            'classification_available': False,
            'primary_drug_type': 'Unknown',
            'relevant_guidelines_summary': 'No relevant guidelines context available',
            'enhanced_context': ''
        }
    
    # Extract classification details - only the drug type
    detailed_results = classification_result.get('detailed_results', {})
    primary_drug_type = detailed_results.get('final_classification', 'Unknown')
    
    # Load processed guidelines
    processed_guidelines = load_drug_type_processed_guidelines(guidelines_file_path)
    
    # Get relevant guidelines summary for the classified drug type - NO TRUNCATION
    relevant_guidelines_summary = ""
    if primary_drug_type in processed_guidelines:
        drug_type_data = processed_guidelines[primary_drug_type]
        if drug_type_data.get('processing_status') == 'success':
            relevant_guidelines_summary = drug_type_data.get('combined_summary', '')
        else:
            relevant_guidelines_summary = f"Guidelines processing failed for {primary_drug_type}"
    else:
        relevant_guidelines_summary = f"No processed guidelines available for {primary_drug_type}"
    
    # Create enhanced context string for prompt injection with explicit drug type focus
    enhanced_context = f"""<drug_type_regulatory_context>
**CRITICAL INSTRUCTION**: You are working exclusively with **{primary_drug_type}** products. ALL your thinking, evaluations, justifications, gap analysis and recommendations must be specifically tailored for **{primary_drug_type}** ONLY.

**Drug Type**: {primary_drug_type}

**IMPORTANT**: The regulatory guidelines summary provided below contains comprehensive information specifically relevant to **{primary_drug_type}**. This includes all applicable ICH and FDA guidelines that are pertinent to this drug type. Use this information as your primary reference for all regulatory requirements, evaluation criteria, and recommendations.

**Relevant Regulatory Guidelines for {primary_drug_type}**:
{relevant_guidelines_summary}

**ANALYSIS REQUIREMENTS**:
- Focus EXCLUSIVELY on {primary_drug_type} regulatory requirements
- Apply ONLY {primary_drug_type}-specific evaluation criteria  
- Generate recommendations suitable ONLY for {primary_drug_type} development
- Consider ONLY {primary_drug_type}-relevant ICH/FDA guidelines from the summary above
- Ignore any requirements not applicable to {primary_drug_type}

</drug_type_regulatory_context>

"""
    
    return {
        'classification_available': True,
        'primary_drug_type': primary_drug_type,
        'relevant_guidelines_summary': relevant_guidelines_summary,
        'enhanced_context': enhanced_context
    }

# Initialize drug type context globally for use throughout gap analysis
print("\n🔍 Loading drug type context for gap analysis enhancement...")
DRUG_TYPE_CONTEXT = get_drug_type_context_for_gap_analysis()

if DRUG_TYPE_CONTEXT['classification_available']:
    print(f"✅ Drug type context loaded successfully:")
    print(f"   📋 Primary Drug Type: {DRUG_TYPE_CONTEXT['primary_drug_type']}")
    print(f"   📄 Guidelines Summary Length: {len(DRUG_TYPE_CONTEXT['relevant_guidelines_summary'])} characters")
    print("Prompt:\n", inject_drug_type_context_into_prompt("User Prompt:\n", DRUG_TYPE_CONTEXT))
    print(f"   🧬 Gap analysis will be enhanced with {DRUG_TYPE_CONTEXT['primary_drug_type']}-specific regulatory context")
else:
    print("⚠️ Drug type context not available - gap analysis will proceed without drug-type-specific context")

processed_doc = read_json("processed_ectd_guidelines_with_relevant_chunks.json")

# %%

async def combine_justifications_for_checkpoint(
    supporting_justifications: List[str],
    gap_justifications: List[str], 
    checkpoint: str,
    section_info: Dict,
) -> Dict:
    """
    Combine justifications using LLM to create comprehensive supporting evidence and gap analysis.
    
    Args:
        supporting_justifications: List of justifications from high-scoring chunks (≥8)
        gap_justifications: List of justifications from low-scoring chunks (<8)
        checkpoint: The checkpoint question being analyzed
        section_info: Dictionary containing section information
        
    Returns:
        Dict containing:
        {
            'combined_supporting_evidence': str,  # Combined explanation of what's present
            'combined_gap_analysis': str         # Combined explanation of what's missing
        }
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and ICH guidelines. Your task is to analyze and combine justifications from document chunk evaluations to create comprehensive supporting evidence summaries and gap analyses."""
            
            user_prompt = f"""
You are analyzing justifications from a checkpoint coverage assessment for a Pre-IND briefing document.

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            user_prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                user_prompt += f"""
Context: You are analyzing justifications for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this checkpoint contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            # Inject drug type context for enhanced justification combination
            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)
            
            user_prompt += f"""
Checkpoint Question:
<checkpoint>
{checkpoint}
</checkpoint>

You have been provided with two types of justifications:

1. SUPPORTING JUSTIFICATIONS (from high-scoring chunks that address the checkpoint well):
<supporting_justifications>
{format_list_as_markdown(supporting_justifications) if supporting_justifications else "None available"}
</supporting_justifications>

2. GAP JUSTIFICATIONS (from low-scoring chunks that partially address or miss the checkpoint):
<gap_justifications>
{format_list_as_markdown(gap_justifications) if gap_justifications else "None available"}
</gap_justifications>

Your task is to analyze these justifications and create two comprehensive summaries:

1. **Combined Supporting Evidence**: If supporting justifications are available, combine them into a single, non-redundant explanation of what information IS present in the input documents and why it addresses the checkpoint well. Remove redundancy while preserving specific details and evidence.

2. **Combined Gap Analysis**: If gap justifications are available, combine them into a single, non-redundant explanation of what information is MISSING or insufficient in the input documents. Focus on specific gaps, missing data, or insufficient detail that prevents full checkpoint coverage.

Guidelines for combination:
- Preserve specific technical details and evidence
- Remove redundant statements while maintaining comprehensiveness
- Focus on actionable insights for regulatory professionals
- Use clear, professional language appropriate for regulatory documentation
- If no justifications are available for a category, state "No relevant information available"

Your response should be in JSON format:
{{
  "combined_supporting_evidence": "Combined explanation of what's present and working well",
  "combined_gap_analysis": "Combined explanation of what's missing or insufficient"
}}
"""
            output_type = str
            if get_structured_output_using_pydantic(GENERATOR_MODEL_BASE_URL):
                output_type = CombinedJustifications

            # Calculate tokens for monitoring
            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(user_prompt)

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=generator_model,
                    system_prompt=system_prompt,
                    output_type=output_type,
                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
                return response.output

            # Use the retry wrapper to handle empty responses
            response: CombinedJustifications = await retry_on_empty_response(make_api_call)

            if get_structured_output_using_pydantic(GENERATOR_MODEL_BASE_URL):
                return response.model_dump()
            else:
                if response:
                    # Parse the JSON response
                    try:
                        # Extract JSON from the response
                        json_match = re.search(r'\{.*\}', response, re.DOTALL)
                        if json_match:
                            json_str = json_match.group(0)
                            combined_result = json.loads(json_str)
                            
                            # output_tokens = calculate_number_of_tokens(json.dumps(combined_result))
                            # total_tokens = system_tokens + user_tokens + output_tokens
                            
                            # print(f"combine_justifications_for_checkpoint token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
                            
                            return {
                                'combined_supporting_evidence': combined_result.get('combined_supporting_evidence', 'No relevant information available'),
                                'combined_gap_analysis': combined_result.get('combined_gap_analysis', 'No gaps identified')
                            }
                        else:
                            # Fallback if JSON parsing fails
                            print("Warning: Could not extract JSON from justification combination response")
                            retry_count += 1
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON response from justification combination: {e}")
                        retry_count += 1
                else:
                    print("Empty response received from justification combination")
                    retry_count += 1
                
        except Exception as e:
            retry_count += 1
            print(f"Error in combine_justifications_for_checkpoint (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Returning default justification combination.")
                return {
                    'combined_supporting_evidence': 'Error occurred during justification combination',
                    'combined_gap_analysis': 'Error occurred during justification combination'
                }
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return {
        'combined_supporting_evidence': 'Unexpected error in justification combination',
        'combined_gap_analysis': 'Unexpected error in justification combination'
    }

def categorize_checkpoints_by_coverage(checkpoint_details: List[Dict]) -> Dict:
    """
    Categorize checkpoints based on their coverage scores using fixed ranges.
    
    Args:
        checkpoint_details: List of checkpoint detail dictionaries with coverage scores
        
    Returns:
        Dict containing categorized checkpoints:
        {
            'excellent_coverage': {...},
            'good_coverage': {...},
            'partial_coverage': {...},
            'poor_coverage': {...},
            'no_coverage': {...}
        }
    """
    # Define fixed score ranges
    categories = {
        'excellent_coverage': {'range': (8.0, 10.0), 'checkpoints': []},
        'good_coverage': {'range': (6.0, 7.9), 'checkpoints': []},
        'partial_coverage': {'range': (4.0, 5.9), 'checkpoints': []},
        'poor_coverage': {'range': (2.0, 3.9), 'checkpoints': []},
        'no_coverage': {'range': (0.0, 1.9), 'checkpoints': []}
    }
    
    # Categorize each checkpoint
    for checkpoint_detail in checkpoint_details:
        score = checkpoint_detail.get('coverage_score', 0.0)
        
        # Find the appropriate category
        for category_name, category_info in categories.items():
            min_score, max_score = category_info['range']
            if min_score <= score <= max_score:
                categories[category_name]['checkpoints'].append(checkpoint_detail)
                break
    
    # Create summary for each category
    categorized_result = {}
    for category_name, category_info in categories.items():
        checkpoints = category_info['checkpoints']
        categorized_result[category_name] = {
            'checkpoint_count': len(checkpoints),
            'score_range': category_info['range'],
            'specific_checkpoints': [cp['checkpoint'] for cp in checkpoints],
            'checkpoint_details': checkpoints  # Keep full details for further processing
        }
    
    return categorized_result

async def assess_checkpoint_coverage_against_all_chunks(checkpoint_question: str, all_input_chunks: List[Dict], section_info: Dict) -> Dict:
    """
    Evaluates how well a checkpoint is covered by all available input chunks.
    
    Args:
        checkpoint_question: The checkpoint question to evaluate
        all_input_chunks: List of all input document chunks to evaluate against
        section_info: Dictionary containing section information
        
    Returns:
        Dict containing coverage assessment with structure:
        {
            'checkpoint': str,
            'total_chunks_evaluated': int,
            'relevant_chunks': List[Dict],  # chunks that address this checkpoint
            'coverage_score': float,  # 0-10 aggregate score
            'coverage_quality': str,  # 'excellent', 'good', 'partial', 'poor', 'none'
            'best_supporting_evidence': str,  # excerpt from best chunk
            'coverage_gaps': List[str]  # what aspects are missing
        }
    """
    print(f"Assessing checkpoint coverage: {checkpoint_question[:100]}...")
    
    if not all_input_chunks:
        return {
            'checkpoint': checkpoint_question,
            'total_chunks_evaluated': 0,
            'relevant_chunks': [],
            'coverage_score': 0.0,
            'coverage_quality': 'none',
            'best_supporting_evidence': '',
            'coverage_gaps': ['No input documents available for evaluation']
        }
    
    # Evaluate each chunk against the checkpoint
    chunk_evaluations = []
    relevant_chunks = []
    
    print(f"Evaluating {len(all_input_chunks)} chunks against checkpoint...")
    
    # Filter out chunks with empty content
    valid_chunks = [chunk for chunk in all_input_chunks if chunk.get('content', '').strip()]
    
    if not valid_chunks:
        print("No valid chunks found for evaluation")
        return {
            'checkpoint': checkpoint_question,
            'total_chunks_evaluated': len(all_input_chunks),
            'relevant_chunks': [],
            'coverage_score': 0.0,
            'coverage_quality': 'none',
            'best_supporting_evidence': '',
            'coverage_gaps': ['No valid chunks available for evaluation'],
            'supporting_evidence': 'No relevant information available',
            'identified_gaps': 'No chunks available for evaluation'
        }
    
    # Choose evaluation method based on configuration
    if USE_BATCHED_CHUNK_EVALUATION and len(valid_chunks) > 1:
        # Use batched approach for better performance
        try:
            print(f"Using batched evaluation for {len(valid_chunks)} chunks")
            
            # Create batches for chunks
            batches = create_chunk_batches(valid_chunks, checkpoint_question)
            print(f"Created {len(batches)} batches for chunk evaluation")
            
            # Create tasks for all batches
            batch_tasks = []
            for batch in batches:
                task = evaluate_batch_chunks_checkpoint_relevance(batch, section_info, checkpoint_question)
                batch_tasks.append((batch, task))
            
            # Execute all batches in parallel
            batch_results = await asyncio.gather(*[task for _, task in batch_tasks])
            
            # Process results from all batches
            for (batch, _), batch_evaluations in zip(batch_tasks, batch_results):
                for chunk, evaluation in zip(batch, batch_evaluations):
                    chunk_evaluations.append({
                        'chunk': chunk,
                        'evaluation': evaluation,
                        'relevance_score': evaluation.relevance_score
                    })
                    
                    # Consider chunk relevant if it meets the threshold
                    if evaluation.relevance_score >= RELEVANCE_THRESHOLD:
                        relevant_chunk = chunk.copy()
                        relevant_chunk['relevance_score'] = evaluation.relevance_score
                        relevant_chunk['justification'] = evaluation.justification
                        relevant_chunks.append(relevant_chunk)
        except Exception as e:
            print(f"Batched chunk evaluation failed: {e}, falling back to individual evaluation")
            # Fallback to individual evaluation
            batch_tasks = []
            for chunk in valid_chunks:
                chunk_content = chunk.get('content', '')
                task = evaluate_individual_checkpoint_relevance(chunk_content, section_info, checkpoint_question)
                batch_tasks.append((chunk, task))
            
            # Execute batch in parallel
            batch_results = await asyncio.gather(*[task for _, task in batch_tasks])
            
            # Process results
            for (chunk, _), evaluation in zip(batch_tasks, batch_results):
                chunk_evaluations.append({
                    'chunk': chunk,
                    'evaluation': evaluation,
                    'relevance_score': evaluation.relevance_score
                })
                
                # Consider chunk relevant if it meets the threshold
                if evaluation.relevance_score >= RELEVANCE_THRESHOLD:
                    relevant_chunk = chunk.copy()
                    relevant_chunk['relevance_score'] = evaluation.relevance_score
                    relevant_chunk['justification'] = evaluation.justification
                    relevant_chunks.append(relevant_chunk)
    else:
        # Use individual evaluation (for testing/comparison or single chunk)
        print(f"Using individual evaluation for {len(valid_chunks)} chunks")
        batch_tasks = []
        for chunk in valid_chunks:
            chunk_content = chunk.get('content', '')
            task = evaluate_individual_checkpoint_relevance(chunk_content, section_info, checkpoint_question)
            batch_tasks.append((chunk, task))
        
        # Execute batch in parallel
        batch_results = await asyncio.gather(*[task for _, task in batch_tasks])
        
        # Process results
        for (chunk, _), evaluation in zip(batch_tasks, batch_results):
            chunk_evaluations.append({
                'chunk': chunk,
                'evaluation': evaluation,
                'relevance_score': evaluation.relevance_score
            })
            
            # Consider chunk relevant if it meets the threshold
            if evaluation.relevance_score >= RELEVANCE_THRESHOLD:
                relevant_chunk = chunk.copy()
                relevant_chunk['relevance_score'] = evaluation.relevance_score
                relevant_chunk['justification'] = evaluation.justification
                relevant_chunks.append(relevant_chunk)
    
    # Calculate aggregate coverage score
    if not chunk_evaluations:
        coverage_score = 0.0
    else:
        # Use weighted average of top-scoring chunks (not just arithmetic mean)
        scores = [eval_data['relevance_score'] for eval_data in chunk_evaluations]
        scores.sort(reverse=True)
        
        # Weight the top scores more heavily
        if len(scores) >= 3:
            # Take top 3 scores with weights 0.5, 0.3, 0.2
            coverage_score = (scores[0] * 0.5 + scores[1] * 0.3 + scores[2] * 0.2)
        elif len(scores) == 2:
            # Take top 2 scores with weights 0.7, 0.3
            coverage_score = (scores[0] * 0.7 + scores[1] * 0.3)
        elif len(scores) == 1:
            # Single score
            coverage_score = scores[0]
        else:
            coverage_score = 0.0
    
    # Determine coverage quality
    if coverage_score >= 8.0:
        coverage_quality = 'excellent'
    elif coverage_score >= 6.0:
        coverage_quality = 'good'
    elif coverage_score >= 4.0:
        coverage_quality = 'partial'
    elif coverage_score >= 2.0:
        coverage_quality = 'poor'
    else:
        coverage_quality = 'none'
    
    # Get best supporting evidence
    best_supporting_evidence = ''
    if relevant_chunks:
        best_chunk = max(relevant_chunks, key=lambda x: x['relevance_score'])
        # Extract a meaningful excerpt (first 300 chars)
        content = best_chunk.get('content', '')
        best_supporting_evidence = content
    
    # Collect justifications by score category
    supporting_justifications = []  # From chunks scoring >= 8
    gap_justifications = []         # From chunks scoring < 8
    
    for chunk_eval in chunk_evaluations:
        if chunk_eval['relevance_score'] >= 8.0:
            supporting_justifications.append(chunk_eval['evaluation'].justification)
        else:
            gap_justifications.append(chunk_eval['evaluation'].justification)
    
    # Combine justifications using LLM
    combined_justifications = await combine_justifications_for_checkpoint(
        supporting_justifications, 
        gap_justifications, 
        checkpoint_question, 
        section_info
    )
    
    # Generate coverage gaps (simplified for now, can be enhanced with LLM later)
    coverage_gaps = []
    if coverage_score < 8.0:
        if coverage_score < 2.0:
            coverage_gaps.append('No relevant information found in input documents')
        elif coverage_score < 4.0:
            coverage_gaps.append('Limited information available - insufficient detail')
        elif coverage_score < 6.0:
            coverage_gaps.append('Partial information available - some aspects missing')
        else:
            coverage_gaps.append('Good information available - minor gaps in coverage')
    
    result = {
        'checkpoint': checkpoint_question,
        'total_chunks_evaluated': len(all_input_chunks),
        'relevant_chunks': relevant_chunks,
        'coverage_score': round(coverage_score, 2),
        'coverage_quality': coverage_quality,
        'best_supporting_evidence': best_supporting_evidence,
        'coverage_gaps': coverage_gaps,
        'supporting_evidence': combined_justifications['combined_supporting_evidence'],
        'identified_gaps': combined_justifications['combined_gap_analysis']
    }
    
    print(f"Coverage assessment complete: {coverage_score:.2f}/10 ({coverage_quality}) - {len(relevant_chunks)} relevant chunks")
    return result

async def generate_gap_recommendations(checkpoint: str, coverage_assessment: Dict, section_info: Dict) -> List[str]:
    """
    Generates specific actionable recommendations for addressing a gap using LLM.
    
    Args:
        checkpoint: The checkpoint question that has a gap
        coverage_assessment: The coverage assessment result from assess_checkpoint_coverage_against_all_chunks
        section_info: Dictionary containing section information
        
    Returns:
        List[str]: List of specific actionable recommendations
    """
    print(f"Generating recommendations for gap: {checkpoint[:80]}...")
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are a senior regulatory affairs expert specializing in pharmaceutical development and Pre-IND submissions. Your task is to provide specific, actionable recommendations for addressing regulatory gaps identified in Pre-IND documentation."""

            user_prompt = f"""You are analyzing a regulatory gap in a Pre-IND briefing document. Based on the information provided, generate specific, actionable recommendations to address this gap.

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            user_prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                user_prompt += f"""
Context: You are generating recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how addressing this gap contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            # Inject drug type context for enhanced gap recommendations
            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)
            
            user_prompt += f"""
Gap Details:
<gap_analysis>
Checkpoint Question: {checkpoint}
Coverage Score: {coverage_assessment.get('coverage_score', 0)}/10
Coverage Quality: {coverage_assessment.get('coverage_quality', 'none')}
</gap_analysis>

Specific Gap Analysis:
<identified_gaps>
{coverage_assessment.get('identified_gaps', 'No specific gap analysis available')}
</identified_gaps>

Supporting Evidence Available:
<supporting_evidence>
{coverage_assessment.get('supporting_evidence', 'No supporting evidence available')}
</supporting_evidence>

Current Coverage Status:
<coverage_status>
Total Chunks Evaluated: {coverage_assessment.get('total_chunks_evaluated', 0)}
Relevant Chunks Found: {len(coverage_assessment.get('relevant_chunks', []))}
Coverage Gaps: {coverage_assessment.get('coverage_gaps', [])}
</coverage_status>

Before providing recommendations, analyze the gap information in your thinking block using <gap_analysis_thinking> tags:

1. Review the specific gaps identified and their regulatory context
2. Prioritize gaps by regulatory importance and impact
3. Consider feasible solutions and required resources
4. Map gaps to specific ICH guidelines and FDA expectations
5. Plan the most effective sequence of recommendations

Your task is to provide 3-5 specific, actionable recommendations that address this gap. Use the identified gaps section to understand exactly what information is missing or insufficient. Present your final recommendations in <final_recommendations> tags. Each recommendation should be:

1. **Specific**: Include concrete actions, not vague suggestions
2. **Actionable**: Clear steps that can be implemented
3. **Regulatory-focused**: Aligned with FDA Pre-IND expectations
4. **Prioritized**: Most critical actions first
5. **Feasible**: Realistic given typical pharmaceutical development timelines
6. **Targeted**: Directly address the specific gaps identified in the gap analysis

Format your recommendations as a numbered list. Each recommendation should be 1-2 sentences and include specific deliverables or actions.

Examples of good recommendations:
- "Conduct a comprehensive impurity profiling study using HPLC-MS/MS to identify and quantify all impurities >0.1% per ICH Q3A guidelines"
- "Prepare a detailed manufacturing process validation protocol including critical process parameters and acceptance criteria"
- "Generate 6-month stability data at 25°C/60%RH and 40°C/75%RH conditions following ICH Q1A guidelines"

Focus on practical steps that will directly address the identified gaps and satisfy regulatory requirements for Pre-IND submissions.
"""
            # system_tokens = calculate_number_of_tokens(system_prompt)
            # user_tokens = calculate_number_of_tokens(user_prompt)

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=generator_model,
                    system_prompt=system_prompt,
                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.3),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
                
                return response.output

            # Use the retry wrapper to handle empty responses
            response = await retry_on_empty_response(make_api_call)

            # output_tokens = calculate_number_of_tokens(response)
            # total_tokens = system_tokens + user_tokens + output_tokens

            # print(f"generate_checkpoint_list token counts - System: {system_tokens}, User: {user_tokens}, Output: {output_tokens}, Total: {total_tokens}")
            
            if response:
                # Extract content from final_recommendations tags first
                extracted_content = extract_content_from_tags(response, 'final_recommendations')
                
                # Parse the response to extract recommendations
                recommendations = parse_recommendations_from_response(extracted_content)
                
                if recommendations:
                    print(f"Generated {len(recommendations)} recommendations for gap")
                    return recommendations
                else:
                    print("No recommendations could be parsed from response")
                    retry_count += 1
            else:
                print("Empty response received from LLM")
                retry_count += 1
                
        except Exception as e:
            retry_count += 1
            print(f"Error generating recommendations (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Generating fallback recommendations.")
                # Generate basic fallback recommendations
                return []
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback if all retries failed
    return []

def parse_recommendations_from_response(response: str) -> List[str]:
    """
    Parse recommendations from LLM response text.
    
    Args:
        response: The raw LLM response text
        
    Returns:
        List[str]: Parsed recommendations
    """
    recommendations = []
    
    # Split by lines and look for numbered items
    lines = response.split('\n')
    current_recommendation = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Look for numbered items (1., 2., etc.)
        if re.match(r'^\d+\.', line):
            # Save previous recommendation if exists
            if current_recommendation:
                recommendations.append(current_recommendation.strip())
            # Start new recommendation
            current_recommendation = re.sub(r'^\d+\.\s*', '', line)
        elif current_recommendation:
            # Continue current recommendation
            current_recommendation += " " + line
    
    # Add the last recommendation
    if current_recommendation:
        recommendations.append(current_recommendation.strip())
    
    # If no numbered items found, try bullet points
    if not recommendations:
        for line in lines:
            line = line.strip()
            if line.startswith('- ') or line.startswith('• '):
                recommendations.append(line[2:].strip())
    
    # Filter out empty recommendations
    recommendations = [rec for rec in recommendations if rec and len(rec) > 10]
    
    return recommendations[:5]  # Limit to 5 recommendations

async def generate_section_gap_analysis_report(section_info: Dict, all_input_chunks: List[Dict]) -> Dict:
    """
    Generates a comprehensive gap analysis report for a single section using the new approach.
    
    Args:
        section_info: Dictionary containing section information including checkpoints
        all_input_chunks: List of all input document chunks available for analysis
        
    Returns:
        Dict: Structured comprehensive gap analysis report for the section
    """
    section = section_info.get('section', 'Unknown')
    title = section_info.get('title', 'Unknown Title')
    
    print(f"\n=== Starting Comprehensive Gap Analysis for Section {section}: {title} ===")
    
    # Get checkpoints for this section
    checkpoints = section_info.get('ich_guideline_combined_summaries_checkpoint_list', [])
    referenced_guidelines = section_info.get('referenced_ich_guidelines', [])
    
    if not checkpoints:
        print(f"No checkpoints found for section {section}")
        return {
            'section': section,
            'section_title': title,
            'error': 'No checkpoints available for gap analysis',
            'summary': {
                'total_checkpoints': 0,
                'covered_checkpoints': 0,
                'coverage_percentage': 0.0,
            }
        }
    
    print(f"Analyzing {len(checkpoints)} checkpoints against {len(all_input_chunks)} input chunks...")
    
    # Step 1: Process all checkpoints to get coverage assessments
    print("Step 1: Assessing coverage for all checkpoints...")

    checkpoint_details = []
    
    # Process all checkpoints in parallel
    print(f"Processing all {len(checkpoints)} checkpoints...")
    
    # Create tasks for all checkpoints
    checkpoint_tasks = []
    for checkpoint in checkpoints:
        task = assess_checkpoint_coverage_against_all_chunks(checkpoint, all_input_chunks, section_info)
        checkpoint_tasks.append((checkpoint, task))
    
    # Execute all checkpoints in parallel
    checkpoint_results = await asyncio.gather(*[task for _, task in checkpoint_tasks])
    
    # Process results for each checkpoint
    for (checkpoint, _), coverage_assessment in zip(checkpoint_tasks, checkpoint_results):
        # Create checkpoint detail entry
        checkpoint_detail = {
            'checkpoint': checkpoint,
            'ich_guideline_source': ' '.join(referenced_guidelines),
            'coverage_score': coverage_assessment['coverage_score'],
            'coverage_quality': coverage_assessment['coverage_quality'],
            'supporting_evidence': coverage_assessment.get('supporting_evidence', 'No supporting evidence available'),
            'identified_gaps': coverage_assessment.get('identified_gaps', 'No gaps identified'),
            'supporting_chunks': []
        }
        
        # Add supporting chunks information (limit to top 3 for readability)
        relevant_chunks = coverage_assessment.get('relevant_chunks', [])
        for chunk in relevant_chunks[:3]:
            chunk_info = {
                'chunk_id': chunk.get('_id', 'unknown'),
                'document_source': chunk.get('input_doc_tag', 'unknown'),
                'relevance_score': chunk.get('relevance_score', 0),
                'evidence_excerpt': chunk.get('content', '')[:200] + '...' if len(chunk.get('content', '')) > 200 else chunk.get('content', '')
            }
            checkpoint_detail['supporting_chunks'].append(chunk_info)
        
        checkpoint_details.append(checkpoint_detail)
    
    # Step 2: Categorize checkpoints by coverage scores
    print("Step 2: Categorizing checkpoints by coverage scores...")
    categorized_checkpoints = categorize_checkpoints_by_coverage(checkpoint_details)
    
    # Step 3: Combine evidence within each category
    print("Step 3: Combining evidence within each coverage category...")
    enhanced_categories = await combine_evidence_by_category(categorized_checkpoints, section_info)
    
    # Step 4: Generate strategic recommendations
    print("Step 4: Generating strategic recommendations...")
    strategic_recommendations = await generate_strategic_recommendations(enhanced_categories, section_info)
    
    # Step 5: Calculate summary statistics
    total_checkpoints = len(checkpoint_details)
    covered_checkpoints = len([cp for cp in checkpoint_details if cp['coverage_score'] >= 8.0])
    coverage_percentage = (covered_checkpoints / total_checkpoints * 100) if total_checkpoints > 0 else 0.0
    
    # Create a mapping from checkpoint text to detailed analysis
    checkpoint_to_details = {detail['checkpoint']: detail for detail in checkpoint_details}
    
    # Create category summary for the report with detailed checkpoint analysis included
    category_summary = {}
    for category_name, category_data in enhanced_categories.items():
        # Get detailed analysis for each checkpoint in this category
        checkpoint_details_for_category = []
        for checkpoint_text in category_data['specific_checkpoints']:
            if checkpoint_text in checkpoint_to_details:
                checkpoint_details_for_category.append(checkpoint_to_details[checkpoint_text])
        
        category_summary[category_name] = {
            'checkpoint_count': category_data['checkpoint_count'],
            'score_range': category_data['score_range'],
            'combined_supporting_evidence': category_data['combined_supporting_evidence'],
            'combined_gap_analysis': category_data['combined_gap_analysis'],
            'checkpoint_details': checkpoint_details_for_category  # Include detailed analysis within category
        }
    
    # Create the comprehensive report
    report = {
        'section': section,
        'section_title': title,
        'summary': {
            'total_checkpoints': total_checkpoints,
            'covered_checkpoints': covered_checkpoints,
            'coverage_percentage': round(coverage_percentage, 1),
            'total_input_chunks_analyzed': len(all_input_chunks)
        },
        'coverage_categories': category_summary,
        'strategic_recommendations': strategic_recommendations
        # Removed detailed_checkpoint_analysis as it's now within each category
    }
    
    print(f"=== Comprehensive Gap Analysis Complete for Section {section} ===")
    print(f"Coverage: {covered_checkpoints}/{total_checkpoints} ({coverage_percentage:.1f}%)")
    print(f"Generated strategic recommendations")
    
    return report

async def perform_comprehensive_gap_analysis(processed_doc: Dict) -> Dict:
    """
    Performs comprehensive gap analysis across all sections in the Pre-IND briefing document.
    Uses all available input document chunks from MongoDB for comprehensive coverage analysis.
    
    Args:
        processed_doc: The main JSON object containing all sections (after extract_relevant_chunks)
        
    Returns:
        Dict: Comprehensive gap analysis report for all sections
    """
    print("\n" + "="*80)
    print("STARTING COMPREHENSIVE GAP ANALYSIS FOR PRE-IND BRIEFING DOCUMENT")
    print("="*80)
    
    # Collect all sections that need gap analysis
    sections_to_analyze = []
    
    def collect_sections(obj, path=""):
        nonlocal sections_to_analyze
        
        if isinstance(obj, dict):
            # Check if this is a section with gap analysis requirements
            if (obj and "have_to_generate_output_for_this_section" in obj and 
                obj["have_to_generate_output_for_this_section"] and
                "ich_guideline_combined_summaries_checkpoint_list" in obj):
                
                sections_to_analyze.append({
                    'section_info': obj,
                    'path': path
                })
                print(f"Found section {obj.get('section', 'unknown')} for gap analysis")
            
            # Recursively process nested sections
            for key, value in obj.items():
                if isinstance(value, dict):
                    collect_sections(value, f"{path}.{key}" if path else key)
    
    # Collect all sections
    collect_sections(processed_doc)
    
    print(f"\nCollected {len(sections_to_analyze)} sections for gap analysis")
    
    if not sections_to_analyze:
        print("No sections found for gap analysis")
        return {
            'gap_analysis_report': {
                'metadata': {
                    'analysis_date': '2024-01-15',
                    'sections_analyzed': 0,
                    'total_checkpoints': 0,
                    'overall_coverage_percentage': 0.0,
                    'error': 'No sections found for gap analysis'
                }
            }
        }
    
    # Get all input chunks from MongoDB for comprehensive analysis
    print("Fetching all input document chunks from MongoDB...")
    db = mongodb_client['mednova']
    chunk_collection = db['pre_ind_input_docs_chunks']
    
    # Get all chunks and convert ObjectIds to strings for JSON serialization in parallel
    async def process_chunk(chunk):
        # Convert ObjectId to string for JSON serialization
        if '_id' in chunk:
            chunk['_id'] = str(chunk['_id'])
        return chunk
    
    # Fetch all chunks first
    raw_chunks = await chunk_collection.find({}).to_list()
    
    # Process all chunks in parallel
    all_input_chunks = await asyncio.gather(*[process_chunk(chunk) for chunk in raw_chunks])
    
    print(f"Retrieved {len(all_input_chunks)} total input document chunks for analysis")
    # Process all sections in parallel using semaphores for rate limiting
    section_analyses = {}
    
    print(f"\nProcessing {len(sections_to_analyze)} sections sequentially...")
    
    # Process sections sequentially with intermediate saves
    for i, section_data in enumerate(sections_to_analyze):
        section_info = section_data['section_info']
        section_key = section_info.get('section', f'unknown_{section_data["path"]}')
        
        print(f"Processing gap analysis for section {i+1}/{len(sections_to_analyze)}: {section_key}...")
        report = await generate_section_gap_analysis_report(section_info, all_input_chunks)
        section_analyses[section_key] = report
        
        # Calculate current statistics for intermediate save
        current_total_sections = len(section_analyses)
        current_total_checkpoints = sum(report['summary']['total_checkpoints'] for report in section_analyses.values())
        current_total_covered = sum(report['summary']['covered_checkpoints'] for report in section_analyses.values())
        current_overall_coverage = (current_total_covered / current_total_checkpoints * 100) if current_total_checkpoints > 0 else 0.0
        
        # Create intermediate gap analysis report
        intermediate_gap_analysis_report = {
            'gap_analysis_report': {
                'metadata': {
                    'sections_analyzed': current_total_sections,
                    'total_checkpoints': current_total_checkpoints,
                    'overall_coverage_percentage': round(current_overall_coverage, 1),
                    'total_input_chunks_analyzed': len(all_input_chunks),
                    'progress_status': f"Completed {i+1} of {len(sections_to_analyze)} sections"
                },
                'section_analyses': section_analyses
            }
        }
        
        # Save intermediate progress
        write_to_json(intermediate_gap_analysis_report, "comprehensive_gap_analysis_report.json")
        
        print(f"✅ Completed gap analysis for section {i+1}/{len(sections_to_analyze)}: {section_key}")
        print(f"   Current coverage: {current_overall_coverage:.1f}% ({current_total_covered}/{current_total_checkpoints} checkpoints)")
        print(f"   Progress saved to: comprehensive_gap_analysis_report.json")
    
    # Calculate overall statistics
    total_sections = len(section_analyses)
    total_checkpoints = sum(report['summary']['total_checkpoints'] for report in section_analyses.values())
    total_covered = sum(report['summary']['covered_checkpoints'] for report in section_analyses.values())
    overall_coverage = (total_covered / total_checkpoints * 100) if total_checkpoints > 0 else 0.0
    # Identify critical gaps and strengths
    critical_gaps = []
    strengths = []
    
    for section_key, report in section_analyses.items():
        # Critical gaps - now iterate through coverage categories
        for category_name, category_data in report['coverage_categories'].items():
            for checkpoint in category_data['checkpoint_details']:
                if checkpoint['coverage_score'] < 4.0:
                    critical_gaps.append({
                        'section': section_key,
                        'checkpoint': checkpoint['checkpoint'],
                    })
        
        # Strengths (high coverage percentage)
        if report['summary']['coverage_percentage'] >= 90:
            strengths.append(f"Section {section_key}: Excellent coverage ({report['summary']['coverage_percentage']:.1f}%)")
        elif report['summary']['coverage_percentage'] >= 80:
            strengths.append(f"Section {section_key}: Good coverage ({report['summary']['coverage_percentage']:.1f}%)")

    # Create the final comprehensive report
    gap_analysis_report = {
        'gap_analysis_report': {
            'metadata': {
                'sections_analyzed': total_sections,
                'total_checkpoints': total_checkpoints,
                'overall_coverage_percentage': round(overall_coverage, 1),
                'total_input_chunks_analyzed': len(all_input_chunks),
                'status': 'complete'
            },
            'section_analyses': section_analyses
        }
    }
    
    # Save final complete report
    write_to_json(gap_analysis_report, "comprehensive_gap_analysis_report.json")
    
    print("\n" + "="*80)
    print("GAP ANALYSIS COMPLETE")
    print("="*80)
    print(f"Sections Analyzed: {total_sections}")
    print(f"Total Checkpoints: {total_checkpoints}")
    print(f"Overall Coverage: {overall_coverage:.1f}% ({total_covered}/{total_checkpoints})")
    print(f"Final report saved to: comprehensive_gap_analysis_report.json")
    print("="*80)
    
    return gap_analysis_report

async def combine_evidence_by_category(categorized_checkpoints: Dict, section_info: Dict) -> Dict:
    """
    Combine supporting evidence and identified gaps within each coverage category using LLM.
    
    Args:
        categorized_checkpoints: Output from categorize_checkpoints_by_coverage()
        section_info: Dictionary containing section information
        
    Returns:
        Dict with enhanced category information including combined evidence
    """
    enhanced_categories = {}
    
    # Create tasks for categories that need LLM processing
    category_tasks = []
    categories_to_process = []
    
    for category_name, category_data in categorized_checkpoints.items():
        # Skip categories with no checkpoints
        if category_data['checkpoint_count'] == 0:
            enhanced_categories[category_name] = {
                **category_data,
                'combined_supporting_evidence': 'No checkpoints in this category',
                'combined_gap_analysis': 'No checkpoints in this category'
            }
            continue
        
        # Extract supporting evidence and gaps from all checkpoints in this category
        supporting_evidence_list = []
        gap_analysis_list = []
        
        for checkpoint_detail in category_data['checkpoint_details']:
            supporting_evidence = checkpoint_detail.get('supporting_evidence', '')
            identified_gaps = checkpoint_detail.get('identified_gaps', '')
            
            if supporting_evidence and supporting_evidence != 'No relevant information available':
                supporting_evidence_list.append(supporting_evidence)
            
            if identified_gaps and identified_gaps != 'No gaps identified':
                gap_analysis_list.append(identified_gaps)
        
        # Create task for LLM processing if we have evidence to combine
        if supporting_evidence_list or gap_analysis_list:
            task = combine_category_evidence_with_llm(
                category_name,
                supporting_evidence_list,
                gap_analysis_list,
                category_data,
                section_info
            )
            category_tasks.append(task)
            categories_to_process.append((category_name, category_data))
        else:
            # Handle categories with no evidence immediately
            enhanced_categories[category_name] = {
                **category_data,
                'combined_supporting_evidence': 'No supporting evidence available',
                'combined_gap_analysis': 'No gaps identified'
            }
            # Remove checkpoint_details to keep the final report clean (optional)
            if 'checkpoint_details' in enhanced_categories[category_name]:
                del enhanced_categories[category_name]['checkpoint_details']
    
    # Execute all LLM tasks in parallel
    if category_tasks:
        combined_evidence_results = await asyncio.gather(*category_tasks)
        
        # Process results for each category
        for (category_name, category_data), combined_evidence in zip(categories_to_process, combined_evidence_results):
            # Create enhanced category data
            enhanced_categories[category_name] = {
                **category_data,
                'combined_supporting_evidence': combined_evidence['combined_supporting_evidence'],
                'combined_gap_analysis': combined_evidence['combined_gap_analysis']
            }
            
            # Remove checkpoint_details to keep the final report clean (optional)
            if 'checkpoint_details' in enhanced_categories[category_name]:
                del enhanced_categories[category_name]['checkpoint_details']
    
    return enhanced_categories

def create_evidence_batches(evidence_list: List[str], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_evidence_per_batch: int = 10) -> List[List[str]]:
    """
    Create batches of evidence items for efficient combination.
    
    Args:
        evidence_list: List of evidence strings
        max_batch_tokens: Maximum tokens per batch
        max_evidence_per_batch: Maximum number of evidence items per batch
        
    Returns:
        List of batches, where each batch is a list of evidence strings
    """
    if not evidence_list:
        return []
    
    # Calculate base tokens for system prompt and context (estimated)
    base_tokens = 2000  # Buffer for system prompt and context
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for evidence in evidence_list:
        # Calculate tokens for this evidence item
        evidence_tokens = calculate_number_of_tokens(evidence)
        
        # Check if adding this evidence would exceed limits
        if (current_tokens + evidence_tokens > max_batch_tokens or 
            len(current_batch) >= max_evidence_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add evidence to current batch
        current_batch.append(evidence)
        current_tokens += evidence_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

async def combine_single_evidence_batch(
    evidence_batch: List[str],
    evidence_type: str,  # "supporting_evidence" or "gap_analysis"
    category_name: str,
    category_data: Dict,
    section_info: Dict,
) -> str:
    """
    Combine a single batch of evidence using LLM.
    
    Args:
        evidence_batch: List of evidence strings in this batch
        evidence_type: Type of evidence ("supporting_evidence" or "gap_analysis")
        category_name: Name of the coverage category
        category_data: Category information
        section_info: Section information
        
    Returns:
        String containing combined evidence for this batch
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to analyze and combine evidence within a specific coverage category to create comprehensive category-level insights."""
            
            user_prompt = f"""
You are analyzing evidence within a specific coverage category for a Pre-IND briefing document section.

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            user_prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                user_prompt += f"""
Context: You are analyzing evidence for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how this coverage category contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            # Inject drug type context for enhanced evidence combination
            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)
            
            user_prompt += f"""
Coverage Category: {category_name}
Score Range: {category_data['score_range'][0]}-{category_data['score_range'][1]}
Evidence Type: {evidence_type}

Evidence to combine from {len(evidence_batch)} items:
<evidence_batch>
{format_list_as_markdown(evidence_batch)}
</evidence_batch>

Before combining the evidence, analyze the content in your thinking block using <evidence_analysis> tags:

1. Review all evidence items for common themes and patterns
2. Identify key technical details that must be preserved
3. Note any redundant or overlapping information
4. Plan the structure and organization of the combined summary
5. Consider the regulatory context and coverage score range

Your task is to combine this batch of evidence into a single, comprehensive summary in **markdown format** while removing redundancy. Present your final combined evidence in <final_combined_evidence> tags:

"""
            if evidence_type == "supporting_evidence":
                user_prompt += """
Create a **Combined Supporting Evidence** summary that:
- Combines all evidence into a single, non-redundant explanation
- Focuses on what information IS present for this coverage category
- Highlights common strengths and evidence themes
- Preserves specific technical details while removing redundancy
"""
            else:
                user_prompt += """
Create a **Combined Gap Analysis** summary that:
- Combines all gaps into a single, non-redundant explanation
- Focuses on what information is MISSING or insufficient for this coverage category
- Highlights common gap themes and missing information patterns
- Preserves specific technical details while removing redundancy
"""

            user_prompt += """
Guidelines for combination:
- Remove redundancy while preserving specific technical details
- Focus on category-level themes and patterns
- Use clear, professional language appropriate for regulatory documentation
- Consider the coverage score range when interpreting the evidence

**Markdown Formatting Requirements:**
- Use proper headings (## for main sections, ### for subsections)
- Use bullet points (-) for lists of evidence or gaps
- Use **bold** for key terms and important points
- Use *italics* for emphasis where appropriate
- Use > blockquotes for important regulatory quotes or citations
- Structure content with clear sections and subsections
- Make the output visually appealing and easy to read in a frontend interface

Your response should be the combined evidence in <final_combined_evidence> tags in markdown format.
"""

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=generator_model,
                    system_prompt=system_prompt,
                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
                return response.output

            # Use the retry wrapper to handle empty responses
            response = await retry_on_empty_response(make_api_call)

            if response:
                # Extract content from final_combined_evidence tags first
                extracted_content = extract_content_from_tags(response, 'final_combined_evidence')
                return extracted_content.strip()
            else:
                print("Empty response received from evidence batch combination")
                retry_count += 1
                    
        except Exception as e:
            retry_count += 1
            print(f"Error in combine_single_evidence_batch (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Returning default evidence batch combination.")
                return f"Error occurred during {evidence_type} batch combination"
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return f"Unexpected error in {evidence_type} batch combination"

async def combine_evidence_hierarchically(
    evidence_list: List[str],
    evidence_type: str,
    category_name: str,
    category_data: Dict,
    section_info: Dict,
) -> str:
    """
    Combine evidence hierarchically using divide and conquer approach.
    
    Args:
        evidence_list: List of evidence strings to combine
        evidence_type: Type of evidence ("supporting_evidence" or "gap_analysis")
        category_name: Name of the coverage category
        category_data: Category information
        section_info: Section information
        
    Returns:
        String containing final combined evidence
    """
    # Handle empty or single evidence
    if not evidence_list:
        return "No relevant information available for this category"
    
    if len(evidence_list) == 1:
        return evidence_list[0]
    
    print(f"Combining {len(evidence_list)} {evidence_type} items hierarchically for category {category_name}")
    
    # Start with creating initial batches
    current_evidence = evidence_list[:]
    
    # Iteratively combine evidence in batches until we have a single result
    while len(current_evidence) > 1:
        # Create optimal batches based on token count and number of evidence items
        batches = create_evidence_batches(current_evidence)
        
        print(f"Created {len(batches)} batches for {evidence_type} combination")
        
        # Ensure no batch has only one item to avoid infinite loop
        final_batches = []
        single_item_batch = None
        
        for batch in batches:
            if len(batch) == 1 and single_item_batch is None:
                single_item_batch = batch
            elif len(batch) == 1 and single_item_batch is not None:
                # Merge two single-item batches
                merged_batch = single_item_batch + batch
                final_batches.append(merged_batch)
                single_item_batch = None
            else:
                if single_item_batch is not None:
                    # Merge the single-item batch with this multi-item batch
                    merged_batch = single_item_batch + batch
                    final_batches.append(merged_batch)
                    single_item_batch = None
                else:
                    final_batches.append(batch)
        
        # If we still have a single-item batch left, merge it with the last batch
        if single_item_batch is not None:
            if final_batches:
                final_batches[-1].extend(single_item_batch)
            else:
                final_batches.append(single_item_batch)
        
        batches = final_batches
        
        # Process all batches in parallel
        batch_tasks = [
            combine_single_evidence_batch(batch, evidence_type, category_name, category_data, section_info)
            for batch in batches
        ]
        
        # Wait for all batches to complete
        current_evidence = await asyncio.gather(*batch_tasks)
        
        # Filter out any empty results
        current_evidence = [evidence for evidence in current_evidence if evidence and evidence.strip()]
        
        print(f"Combined {len(batches)} batches into {len(current_evidence)} intermediate results")
        
        # Safety check to prevent infinite loops
        if len(current_evidence) == len(batches) and all(len(batch) == 1 for batch in batches):
            # If we're not making progress and all batches are single items, just concatenate
            print("Warning: No progress in hierarchical combination, concatenating results")
            return "\n\n".join(current_evidence)
    
    print(f"Final hierarchical combination complete for {evidence_type}")
    return current_evidence[0] if current_evidence else f"No {evidence_type} available"

async def combine_category_evidence_with_llm(
    category_name: str,
    supporting_evidence_list: List[str],
    gap_analysis_list: List[str],
    category_data: Dict,
    section_info: Dict,
) -> Dict:
    """
    Use LLM to combine evidence within a specific coverage category using hierarchical approach.
    
    Args:
        category_name: Name of the coverage category
        supporting_evidence_list: List of supporting evidence from checkpoints in this category
        gap_analysis_list: List of gap analyses from checkpoints in this category
        category_data: Category information
        section_info: Section information
        
    Returns:
        Dict with combined evidence
    """
    try:
        # Process supporting evidence and gap analysis in parallel using hierarchical approach
        supporting_evidence_task = combine_evidence_hierarchically(
            supporting_evidence_list,
            "supporting_evidence",
            category_name,
            category_data,
            section_info
        )
        
        gap_analysis_task = combine_evidence_hierarchically(
            gap_analysis_list,
            "gap_analysis",
            category_name,
            category_data,
            section_info
        )
        
        # Execute both tasks in parallel
        combined_supporting_evidence, combined_gap_analysis = await asyncio.gather(
            supporting_evidence_task,
            gap_analysis_task
        )
        
        return {
            'combined_supporting_evidence': combined_supporting_evidence,
            'combined_gap_analysis': combined_gap_analysis
        }
        
    except Exception as e:
        print(f"Error in combine_category_evidence_with_llm: {e}")
        return {
            'combined_supporting_evidence': 'Error occurred during category evidence combination',
            'combined_gap_analysis': 'Error occurred during category evidence combination'
        }

def create_category_summary_batches(category_summaries: List[Dict], max_batch_tokens: int = MAX_TOKEN_LIMIT_FOR_STRUCTURED_OUTPUT, max_categories_per_batch: int = 5) -> List[List[Dict]]:
    """
    Create batches of category summaries for efficient strategic recommendation generation.
    
    Args:
        category_summaries: List of category summary dictionaries
        max_batch_tokens: Maximum tokens per batch
        max_categories_per_batch: Maximum number of categories per batch
        
    Returns:
        List of batches, where each batch is a list of category summaries
    """
    if not category_summaries:
        return []
    
    # Calculate base tokens for system prompt and context (estimated)
    base_tokens = 2000  # Buffer for system prompt and context
    
    batches = []
    current_batch = []
    current_tokens = base_tokens
    
    for category_summary in category_summaries:
        # Calculate tokens for this category summary
        category_tokens = calculate_number_of_tokens(json.dumps(category_summary))
        
        # Check if adding this category would exceed limits
        if (current_tokens + category_tokens > max_batch_tokens or 
            len(current_batch) >= max_categories_per_batch):
            
            # Start new batch if current batch is not empty
            if current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = base_tokens
        
        # Add category to current batch
        current_batch.append(category_summary)
        current_tokens += category_tokens
    
    # Add the last batch if it's not empty
    if current_batch:
        batches.append(current_batch)
    
    return batches

async def generate_recommendations_from_batch(
    category_batch: List[Dict],
    section_info: Dict,
) -> str:
    """
    Generate strategic recommendations from a single batch of category summaries.
    
    Args:
        category_batch: List of category summaries in this batch
        section_info: Dictionary containing section information
        
    Returns:
        String containing markdown-formatted strategic recommendations for this batch
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to generate strategic, prioritized recommendations based on comprehensive gap analysis across coverage categories."""
            
            user_prompt = f"""
You are analyzing a comprehensive gap analysis for a Pre-IND briefing document section and need to provide strategic recommendations.

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            user_prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                user_prompt += f"""
Context: You are generating strategic recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how addressing these gaps contributes to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            # Inject drug type context for enhanced strategic recommendations
            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)
            
            user_prompt += f"""
Coverage Category Analysis for {len(category_batch)} categories:
<category_analysis>
{format_category_summaries_as_markdown(category_batch)}
</category_analysis>

Before generating recommendations, analyze the gap information in your thinking block using <recommendation_analysis> tags:

1. Review each category's coverage status and identified gaps
2. Prioritize gaps by regulatory impact and feasibility
3. Identify patterns and systematic issues across categories
4. Consider existing strengths that can be leveraged
5. Plan the most effective strategic recommendations

Based on this gap analysis, generate strategic recommendations that:

1. **Prioritize by Impact**: Focus on the most critical gaps first (poor and no coverage categories)
2. **Consider Regulatory Importance**: Emphasize actions needed for ICH guideline compliance
3. **Be Specific and Actionable**: Provide concrete steps the user can take
4. **Leverage Existing Strengths**: Build on what's already working well
5. **Address Systematic Issues**: Look for patterns across categories

Present your final recommendations in <final_strategic_recommendations> tags.

Guidelines for recommendations:
- Start with highest priority actions (addressing poor/no coverage first)
- Be specific about what documents, data, or studies are needed
- Consider the regulatory timeline and submission requirements
- Build on existing supporting evidence where possible
- Focus on compliance with the referenced ICH guidelines
- Provide 3-8 strategic recommendations for this batch

Your response should be a markdown-formatted bullet point list of recommendations:

- **Recommendation 1**: Specific actionable recommendation
- **Recommendation 2**: Another specific actionable recommendation
- **Recommendation 3**: Continue with more recommendations...

Focus on the most critical and actionable recommendations for these specific categories.
"""

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=generator_model,
                    system_prompt=system_prompt,
                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
                return response.output

            # Use the retry wrapper to handle empty responses
            response = await retry_on_empty_response(make_api_call)

            if response:
                # Extract content from final_strategic_recommendations tags first
                extracted_content = extract_content_from_tags(response, 'final_strategic_recommendations')
                return extracted_content.strip()
            else:
                print("Empty response received from recommendations batch generation")
                retry_count += 1
                    
        except Exception as e:
            retry_count += 1
            print(f"Error in generate_recommendations_from_batch (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Returning default recommendations batch.")
                return "- Error occurred during recommendations batch generation"
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return "- Unexpected error in recommendations batch generation"

async def merge_recommendation_batches(
    recommendation_batches: List[str],
    section_info: Dict,
) -> str:
    """
    Merge multiple batches of recommendations into a single prioritized markdown string.
    
    Args:
        recommendation_batches: List of markdown recommendation strings to merge
        section_info: Dictionary containing section information
        
    Returns:
        Single markdown string with merged and prioritized recommendations
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Get enhanced section context including outer section info
            enhanced_context = get_enhanced_section_context(
                section_info.get('section', ''),
                section_info.get('title', ''),
                section_info.get('description', ''),
                section_info.get('referenced_ich_guidelines', [])
            )
            
            system_prompt = """You are an expert in pharmaceutical regulatory documentation and Pre-IND submissions. Your task is to merge and prioritize strategic recommendations from multiple batches while removing redundancy and ensuring logical flow."""
            
            user_prompt = f"""
You are merging strategic recommendations from multiple batches for a Pre-IND briefing document section.

"""
            
            # Add outer section context if available
            if enhanced_context['has_outer_section']:
                outer = enhanced_context['outer_section']
                user_prompt += f"""
Outer Section Context:
<outer_section>
Section: {outer['section']}
Title: {outer['title']}
Description: {outer['description']}
</outer_section>

"""
            
            # Add current section information
            current = enhanced_context['current_section']
            user_prompt += f"""
Current Section Information:
<current_section>
Section: {current['section']}{' (subsection of ' + enhanced_context['outer_section']['section'] + ')' if enhanced_context['has_outer_section'] else ''}
Title: {current['title']}
Description: {current['description']}
Referenced ICH Guidelines: {', '.join(current['referenced_ich_guidelines'])}
</current_section>

"""
            
            # Add context explanation if there's an outer section
            if enhanced_context['has_outer_section']:
                user_prompt += f"""
Context: You are merging recommendations for section {current['section']} which is a subsection within the broader {enhanced_context['outer_section']['title']} section. Consider how these recommendations contribute to the overall requirements outlined in the outer section while focusing on the specific requirements of the current subsection.

"""
            
            # Inject drug type context for enhanced recommendation merging
            user_prompt = inject_drug_type_context_into_prompt(user_prompt, DRUG_TYPE_CONTEXT)
            
            user_prompt += f"""
Recommendation Batches to Merge:
<recommendation_batches>
{format_evidence_batches_as_markdown(recommendation_batches)}
</recommendation_batches>

Before merging recommendations, analyze the content in your thinking block using <merge_analysis> tags:

1. Review all recommendation batches for common themes and overlaps
2. Identify the most critical and impactful recommendations
3. Note any redundant or conflicting recommendations
4. Plan the logical sequence and prioritization
5. Consider regulatory importance and feasibility

Your task is to merge these recommendations into a single, prioritized list. Present your final merged recommendations in <final_merged_recommendations> tags:

1. **Removes Redundancy**: Eliminate duplicate or overlapping recommendations
2. **Maintains Priorities**: Keep the most critical actions first
3. **Ensures Clarity**: Each recommendation should be clear and actionable
4. **Logical Flow**: Organize recommendations in a logical sequence
5. **Regulatory Focus**: Maintain focus on ICH guideline compliance and Pre-IND requirements

Guidelines for merging:
- Combine similar recommendations into more comprehensive ones
- Prioritize recommendations that address the most critical gaps
- Limit final list to 5-10 strategic recommendations maximum
- Ensure each recommendation is specific and actionable
- Remove any recommendations that are too vague or redundant

Your response should be a markdown-formatted bullet point list of the final merged recommendations:

- **Priority 1**: Most critical actionable recommendation
- **Priority 2**: Second most critical actionable recommendation  
- **Priority 3**: Continue with remaining recommendations...

Focus on the most impactful and feasible recommendations for regulatory compliance.
"""

            # Wrap the API call with retry_on_empty_response
            async def make_api_call():
                agent = Agent(
                    model=generator_model,
                    system_prompt=system_prompt,
                    model_settings=get_model_settings(GENERATOR_MODEL_BASE_URL, 0.1),
                    retries=3
                )

                response = await rate_limited_agent_call(agent, user_prompt, api_semaphore_for_generator)
                return response.output

            # Use the retry wrapper to handle empty responses
            response = await retry_on_empty_response(make_api_call)

            if response:
                # Extract content from final_merged_recommendations tags first
                extracted_content = extract_content_from_tags(response, 'final_merged_recommendations')
                return extracted_content.strip()
            else:
                print("Empty response received from recommendations merging")
                retry_count += 1
                    
        except Exception as e:
            retry_count += 1
            print(f"Error in merge_recommendation_batches (attempt {retry_count}/{max_retries}): {e}")
            if retry_count >= max_retries:
                print("Maximum retries reached. Returning concatenated recommendations.")
                # Fallback: just concatenate all recommendations
                return "\n\n".join(recommendation_batches)
            await asyncio.sleep(1 * (2 ** (retry_count - 1)))
    
    # Fallback return (shouldn't reach here)
    return "\n\n".join(recommendation_batches)

async def generate_strategic_recommendations(enhanced_categories: Dict, section_info: Dict) -> str:
    """
    Generate strategic recommendations based on comprehensive gap analysis using hierarchical approach.
    
    Args:
        enhanced_categories: Output from combine_evidence_by_category() with combined evidence
        section_info: Dictionary containing section information
        
    Returns:
        str: Markdown-formatted string of strategic, prioritized recommendations
    """
    try:
        # Prepare category summaries for processing
        category_summaries = []
        for category_name, category_data in enhanced_categories.items():
            if category_data['checkpoint_count'] > 0:
                category_summaries.append({
                    'category': category_name,
                    'score_range': category_data['score_range'],
                    'checkpoint_count': category_data['checkpoint_count'],
                    'supporting_evidence': category_data['combined_supporting_evidence'],
                    'gap_analysis': category_data['combined_gap_analysis']
                })
        
        # Handle empty case
        if not category_summaries:
            return "- No categories available for strategic recommendation generation"
        
        # Handle single category case
        if len(category_summaries) == 1:
            return await generate_recommendations_from_batch(category_summaries, section_info)
        
        print(f"Generating strategic recommendations hierarchically from {len(category_summaries)} categories")
        
        # Create batches for category summaries
        batches = create_category_summary_batches(category_summaries)
        
        print(f"Created {len(batches)} batches for strategic recommendation generation")
        
        # Generate recommendations from all batches in parallel
        batch_tasks = [
            generate_recommendations_from_batch(batch, section_info)
            for batch in batches
        ]
        
        # Execute all batch tasks in parallel
        recommendation_batches = await asyncio.gather(*batch_tasks)
        
        # Filter out empty batches
        recommendation_batches = [batch for batch in recommendation_batches if batch and batch.strip()]
        
        # If only one batch, return it directly
        if len(recommendation_batches) == 1:
            return recommendation_batches[0]
        
        # Merge multiple batches into final recommendations
        print(f"Merging {len(recommendation_batches)} recommendation batches")
        final_recommendations = await merge_recommendation_batches(recommendation_batches, section_info)
        
        print(f"Generated strategic recommendations hierarchically")
        return final_recommendations
        
    except Exception as e:
        print(f"Error in generate_strategic_recommendations: {e}")
        return """- Error occurred during strategic recommendation generation
- Please review the gap analysis manually and create appropriate recommendations"""

# %%
# ===========================
# COMPREHENSIVE GAP ANALYSIS
# ===========================

# Perform comprehensive gap analysis using all available input chunks
# This now uses all input chunks from MongoDB by default (no filtering required)
gap_analysis_results = await perform_comprehensive_gap_analysis(processed_doc)

# Save the gap analysis report
write_to_json(gap_analysis_results, "comprehensive_gap_analysis_report.json")

print("\n📊 Gap Analysis Report Summary:")
print(f"📋 Sections Analyzed: {gap_analysis_results['gap_analysis_report']['metadata']['sections_analyzed']}")
print(f"✅ Overall Coverage: {gap_analysis_results['gap_analysis_report']['metadata']['overall_coverage_percentage']}%")

print(f"\n📄 Detailed report saved to: comprehensive_gap_analysis_report.json")