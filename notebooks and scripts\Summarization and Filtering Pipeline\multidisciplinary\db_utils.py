import os
from pymongo import Mongo<PERSON>lient
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List
from bson.objectid import ObjectId

# Load environment variables
load_dotenv()

MONGO_DB_URL = os.getenv("MONGO_DB_URL")

# MongoDB connection
def get_mongodb_client():
    """Get MongoDB client connection."""
    # You can add MongoDB connection string to .env file if needed
    # For now using default localhost connection
    return MongoClient(MONGO_DB_URL)

def insert_chunks_in_db_for_multidisciplinary_guideline(chunks_data: str, chunk_filename: str, multidisciplinary_guideline: str, is_summary: bool = False, summary_level: int = 1, chunk_reference_ids: List[str] = None, summary_reference_ids: List[str] = None) -> ObjectId:
    """
    Insert chunks into MongoDB database.
    
    Args:
        chunks_data: Dictionary containing chunk data
        multidisciplinary_guideline: The multidisciplinary guideline identifier (e.g., "Q6A")
        is_summary: <PERSON><PERSON><PERSON> indicating if this is a summary chunk
        summary_level: Level of the summary (1 for final summary, 2 for individual summaries)
        chunk_reference_ids: List of ObjectIds of the original chunks if this is a summary
        summary_reference_ids: List of ObjectIds of the individual summaries if this is a final summary
        
    Returns:
        ObjectId: The ID of the inserted document
    """
    client = get_mongodb_client()
    db = client['mednova']
    
    # Determine which collection to use
    collection_name = "multidisciplinary_docs_summaries" if is_summary else "multidisciplinary_docs_chunks"
    collection = db[collection_name]
    
    # Prepare document for insertion
    document = {
        "multidisciplinary_guideline": multidisciplinary_guideline,
        "content": chunks_data
    }

    if not is_summary:
        document["chunk_filename"] = chunk_filename

    if is_summary:
        document["summary_level"] = summary_level
    
    # Add chunk reference if this is a summary
    if is_summary:
        if summary_reference_ids:
            # Convert string IDs to ObjectIds
            document["summary_reference_ids"] = [ObjectId(id) for id in summary_reference_ids]
        if chunk_reference_ids:
            # Convert string IDs to ObjectIds
            document["chunk_reference_ids"] = [ObjectId(id) for id in chunk_reference_ids]
    
    # Insert document
    result = collection.insert_one(document)
    
    # Close connection
    client.close()
    
    return result.inserted_id

def insert_chunks_in_db_for_input_docs(chunks_data: str, chunk_filename: str, input_doc_tag: str, is_summary: bool = False, summary_level: int = 1, chunk_reference_ids: List[str] = None, summary_reference_ids: List[str] = None) -> ObjectId:
    """
    Insert chunks into MongoDB database.
    
    Args:
        chunks_data: Dictionary containing chunk data
        input_doc_tag: The input document tag (e.g., "Tables_to_support_32P21")
        is_summary: Boolean indicating if this is a summary chunk
        summary_level: Level of the summary (1 for final summary, 2 for individual summaries)
        chunk_reference_ids: List of ObjectIds of the original chunks if this is a summary
        summary_reference_ids: List of ObjectIds of the individual summaries if this is a final summary
        
    Returns:
        ObjectId: The ID of the inserted document
    """
    client = get_mongodb_client()
    db = client['mednova']
    
    # Determine which collection to use
    collection_name = "input_docs_summaries" if is_summary else "input_docs_chunks"
    collection = db[collection_name]
    
    # Prepare document for insertion
    document = {
        "input_doc_tag": input_doc_tag,
        "content": chunks_data
    }

    if not is_summary:
        document["chunk_filename"] = chunk_filename

    if is_summary:
        document["summary_level"] = summary_level
    
    # Add chunk reference if this is a summary
    if is_summary:
        if summary_reference_ids:
            # Convert string IDs to ObjectIds
            document["summary_reference_ids"] = [ObjectId(id) for id in summary_reference_ids]
        if chunk_reference_ids:
            # Convert string IDs to ObjectIds
            document["chunk_reference_ids"] = [ObjectId(id) for id in chunk_reference_ids]
    
    # Insert document
    result = collection.insert_one(document)
    
    # Close connection
    client.close()
    
    return result.inserted_id

def get_chunk_by_id(chunk_id: str, collection_name: str = "") -> Dict[str, Any]:
    """
    Retrieve a chunk from MongoDB by its ID.
    
    Args:
        chunk_id: The ID of the chunk to retrieve (can be string or ObjectId)
        collection_name: The name of the collection to retrieve the chunk from
        
    Returns:
        Dict: The chunk data
    """
    client = get_mongodb_client()
    db = client['mednova']
    
    collection = db[collection_name]
    
    # Convert string ID to ObjectId if needed
    if isinstance(chunk_id, str):
        chunk_id = ObjectId(chunk_id)
    
    chunk = collection.find_one({"_id": chunk_id})
    
    client.close()
    
    return chunk 