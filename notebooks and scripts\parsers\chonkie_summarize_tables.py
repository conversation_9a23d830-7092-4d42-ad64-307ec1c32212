from chonkie import <PERSON><PERSON><PERSON><PERSON><PERSON>, Recursive<PERSON>hun<PERSON>, <PERSON>euralC<PERSON><PERSON>
from chonkie.genie import GeminiGenie, OpenAIGenie
from chonkie.chunker.base import Chunk
import os
import re
import json
import sys
import argparse
import asyncio
from typing import List, Dict, <PERSON><PERSON>
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv

load_dotenv()

GENERATOR_MODEL_NAME = os.getenv("GENERATOR_MODEL_NAME")
GENERATOR_MODEL_BASE_URL = os.getenv("GENERATOR_MODEL_BASE_URL")
GENERATOR_MODEL_API_KEY = os.getenv("GENERATOR_MODEL_API_KEY")

generator_client = OpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)
async_generator_client = AsyncOpenAI(base_url=GENERATOR_MODEL_BASE_URL, api_key=GENERATOR_MODEL_API_KEY)

async def create_summary_of_table_async(table_id: str, table_heading: str, table_content: str) -> <PERSON><PERSON>[str, str]:
    """
    Asynchronously create a summary of a table.
    
    Args:
        table_id: The ID of the table
        table_content: The content of the table
        
    Returns:
        Tuple of (table_id, summary) or (table_id, None) if failed
    """
    try:
        response = await async_generator_client.chat.completions.create(
            model=GENERATOR_MODEL_NAME, 
            messages=[  
                {"role": "system", "content": f"""You are a specialized pharmaceutical documentation expert tasked with creating concise, accurate summaries of technical tables from pharmaceutical development reports.

Your summaries will be used in New Drug Application (NDA) documentation that follows ICH eCTD (Electronic Common Technical Document) guidelines, specifically for Module 3 (Quality) sections.

When summarizing tables:
- Maintain scientific accuracy and precision in all data interpretations
- Preserve all critical numerical values, specifications, and acceptance criteria
- Identify key trends, patterns, or significant findings in the data
- Use standardized pharmaceutical terminology consistent with ICH guidelines
- Focus on the regulatory significance of the data (stability, quality attributes, etc.)
- Structure the summary logically, moving from test parameters to results to conclusions
- Be concise but comprehensive, capturing all essential information for regulatory review
- Highlight any results that approach or exceed specification limits
- Include relevant timepoints, conditions, and methodologies when summarizing stability or testing data

Your summary should be suitable for direct incorporation into regulatory documentation that will be reviewed by health authorities.
                 
In the output just return the summary, no other text or comments."""},
                {"role": "user", "content": f"""Please create a precise, scientifically accurate summary of the following table from a pharmaceutical development report. This summary will be incorporated into an NDA submission following ICH eCTD guidelines.

Focus on:
1. The key quality attributes or parameters being tested
2. The testing conditions or timepoints represented
3. Significant numerical values and their relationship to specifications
4. Any trends, patterns, or notable findings
5. The regulatory significance of these results

Table content:
{table_heading}
{table_content}"""}
            ]
        )

        return (table_id, response.choices[0].message.content)
    except Exception as e:
        print(f"Error creating summary for table {table_id}: {e}")
        return (table_id, None)

async def create_all_table_summaries_async(tables: Dict[str, Dict[str, str]]) -> Dict[str, str]:
    """
    Create summaries for all tables in parallel using async processing.
    
    Args:
        tables: Dictionary mapping table_id to table content
        
    Returns:
        Dictionary mapping table_id to table summary
    """
    if not tables:
        return {}
    
    print(f"Creating summaries for {len(tables)} tables in parallel...")
    
    # Create async tasks for all tables
    tasks = [create_summary_of_table_async(table_id, content['heading'], content['content']) for table_id, content in tables.items()]
    
    # Run all tasks in parallel
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    table_summaries = {}
    successful_summaries = 0
    
    for result in results:
        if isinstance(result, Exception):
            print(f"Task failed with exception: {result}")
            continue
            
        table_id, summary = result
        if summary is not None:
            table_summaries[table_id] = summary
            successful_summaries += 1
        else:
            print(f"Failed to create summary for table {table_id}")
    
    print(f"Successfully created {successful_summaries}/{len(tables)} table summaries")
    return table_summaries

def load_extracted_tables(tables_dir: str) -> Dict[str, str]:
    """
    Load extracted tables from the tables directory.
    
    Args:
        tables_dir (str): Directory containing extracted tables
        
    Returns:
        Dict mapping table_id to table content
    """
    tables = {}
    
    # Load table index if it exists
    index_file = os.path.join(tables_dir, "table_index.json")
    if os.path.exists(index_file):
        with open(index_file, 'r', encoding='utf-8') as f:
            table_index = json.load(f)
        
        print(f"Table index contains: {list(table_index.keys())}")
        
        for table_id, table_info in table_index.items():
            table_file = os.path.join(tables_dir, table_info['file'])
            if os.path.exists(table_file):
                with open(table_file, 'r', encoding='utf-8') as f:
                    tables[table_id] = {
                        'content': f.read(),
                        'heading': table_info['heading']
                    }
                print(f"Loaded {table_id} from {table_info['file']}")
            else:
                print(f"WARNING: Table file {table_info['file']} not found for {table_id}")
    else:
        print("No table_index.json found, scanning directory...")
        for filename in os.listdir(tables_dir):
            if filename.startswith('table_') and filename.endswith('.txt'):
                table_id = filename[:-4]  
                table_file = os.path.join(tables_dir, filename)
                with open(table_file, 'r', encoding='utf-8') as f:
                    tables[table_id] = {
                        'content': f.read(),
                        'heading': table_info['heading']
                    }
                print(f"Loaded {table_id} from {filename}")
    
    print(f"Final loaded table IDs: {list(tables.keys())}")
    return tables

def find_table_references_in_chunks(chunks: List, tables: Dict[str, str]) -> Dict[str, List[int]]:
    """
    Find which chunks reference which tables using simple placeholder matching.
    
    Returns:
        Dict mapping table_id to list of chunk indices that reference it
    """
    table_references = {table_id: [] for table_id in tables.keys()}
    
    print(f"Looking for tables with IDs: {list(tables.keys())}")
    
    for chunk_idx, chunk in enumerate(chunks):
        chunk_text = chunk.text
        
        placeholder_pattern = r'\{TABLE_([^}]+)\}'
        found_placeholders = re.findall(placeholder_pattern, chunk_text)
        if found_placeholders:
            print(f"Chunk {chunk_idx} contains placeholders: {found_placeholders}")
        
        for table_id in tables.keys():
            placeholder = f"{{TABLE_{table_id}}}"
            if placeholder in chunk_text:
                table_references[table_id].append(chunk_idx)
                print(f"Found {placeholder} in chunk {chunk_idx}")
            else:
                for found_placeholder in found_placeholders:
                    if found_placeholder == table_id:
                        print(f"Found matching placeholder {found_placeholder} for table {table_id} in chunk {chunk_idx}")
                        table_references[table_id].append(chunk_idx)
                        break
    
    return table_references

async def insert_tables_into_chunks_async(chunks: List, table_summaries: Dict[str, str], table_references: Dict[str, List[int]]) -> List:
    """
    Insert pre-generated table summaries into the appropriate chunks based on placeholder references.
    """
    updated_chunks = []
    
    for chunk_idx, chunk in enumerate(chunks):
        chunk_text = chunk.text
        original_length = len(chunk_text)
        
        tables_to_insert = []
        for table_id, chunk_indices in table_references.items():
            if chunk_idx in chunk_indices:
                tables_to_insert.append(table_id)
        
        print(f"Chunk {chunk_idx}: Tables to insert: {tables_to_insert}")
        
        for table_id in tables_to_insert:
            placeholder = f"{{TABLE_{table_id}}}"
            print(f"Looking for placeholder: '{placeholder}' in chunk {chunk_idx}")
            
            if placeholder in chunk_text:
                old_text = chunk_text
                if table_id in table_summaries:
                    table_summary = table_summaries[table_id]
                    chunk_text = chunk_text.replace(placeholder, f"\n\n{table_summary}\n\n{placeholder}\n\n")
                    print(f"Replaced {placeholder} with table content in chunk {chunk_idx}")
                    print(f"Text length changed from {len(old_text)} to {len(chunk_text)}")
                else:
                    print(f"WARNING: No summary available for table {table_id}")
                    chunk_text = chunk_text.replace(placeholder, f"\n\n[TABLE {table_id} - Summary not available]\n\n")
            else:
                print(f"WARNING: Placeholder {placeholder} not found in chunk {chunk_idx}")
                placeholder_pattern = r'\{TABLE_([^}]+)\}'
                found_placeholders = re.findall(placeholder_pattern, chunk_text)
                print(f"Chunk {chunk_idx} actually contains: {found_placeholders}")
        
        chunk_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', chunk_text).strip()
        
        chunk.text = chunk_text
        chunk.end_index = chunk.start_index + len(chunk_text)
        chunk.token_count = len(chunk_text.split())
        
        updated_chunks.append(chunk)
    
    return updated_chunks

async def main_async(txt_file_path, chunker_type="neural", output_dir=None):
    """
    Async version of the main function to process a text file with Chonkie chunking.
    
    Args:
        txt_file_path (str): Path to the input text file
        chunker_type (str): Type of chunker to use ("neural", "recursive", "slumber")
        output_dir (str): Directory to save chunks (optional)
    """
    # Initialize the chunker based on type
    if chunker_type == "neural":
        chunker = NeuralChunker(
            model="mirth/chonky_modernbert_base_1",                          
            min_characters_per_chunk=500,             
            return_type="chunks"                    
        )
    elif chunker_type == "recursive":
        chunker = RecursiveChunker(chunk_size=2000)
    elif chunker_type == "slumber":
        # Note: Slumber chunker requires API keys
        try:
            genie = OpenAIGenie("gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
            chunker = SlumberChunker(
                genie=genie,                       
                tokenizer_or_token_counter="gpt2",  
                chunk_size=2000,                    
                candidate_size=1000,                 
                min_characters_per_chunk=800,        
                verbose=True                      
            )
        except Exception as e:
            print(f"Failed to initialize Slumber chunker: {e}")
            print("Falling back to Neural chunker...")
            chunker = NeuralChunker(
                model="mirth/chonky_modernbert_base_1",
                min_characters_per_chunk=500,
                return_type="chunks"
            )
    else:
        raise ValueError(f"Unknown chunker type: {chunker_type}")

    print(f"Using {chunker_type} chunker")
    
    # Validate input file
    if not os.path.exists(txt_file_path):
        print(f"Error: Input file '{txt_file_path}' not found.")
        return False
    
    # Read the document (should already have table references instead of tables)
    with open(txt_file_path, "r") as f:
        document_text = f.read()

    print("Original document length:", len(document_text))
    
    placeholder_pattern = r'\{TABLE_([^}]+)\}'
    found_placeholders = re.findall(placeholder_pattern, document_text)
    print(f"Found {len(found_placeholders)} table placeholders in document: {found_placeholders}")
    
    if not found_placeholders:
        print("WARNING: No table placeholders found in document. This suggests:")
        print("1. The document may not have been processed with the updated PDF to TXT script")
        print("2. Or the document doesn't contain any tables")
        print("3. Consider running the PDF to TXT conversion first if this document came from a PDF")

    print("\n=== Loading extracted tables ===")
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    tables_dir = os.path.join(os.path.dirname(txt_file_path), f"{base_name}_tables")
    
    if not os.path.exists(tables_dir):
        print(f"Tables directory '{tables_dir}' not found.")
        if not found_placeholders:
            print("This confirms the document hasn't been processed with table extraction.")
            print("Please run the PDF to TXT conversion script first.")
            return
        else:
            print("Creating empty tables dictionary...")
            tables = {}
    else:
        tables = load_extracted_tables(tables_dir)
        print(f"Loaded {len(tables)} tables from '{tables_dir}'")

    print("\n=== Creating table summaries in parallel ===")
    table_summaries = await create_all_table_summaries_async(tables)

    print("\n=== Chunking text with table placeholders ===")
    chunks = chunker(document_text)
    print(f"Created {len(chunks)} chunks")
    
    for i, chunk in enumerate(chunks):
        preview = chunk.text[:100].replace('\n', ' ')
        print(f"  Chunk {i}: {len(chunk.text)} characters - '{preview}...'")
        
        chunk_placeholders = re.findall(placeholder_pattern, chunk.text)
        if chunk_placeholders:
            print(f"    Contains table placeholders: {chunk_placeholders}")

    print("\n=== Finding table placeholders in chunks ===")
    table_references = find_table_references_in_chunks(chunks, tables)
    
    total_tables_referenced = sum(len(refs) for refs in table_references.values())
    tables_with_references = len([refs for refs in table_references.values() if refs])
    
    print(f"Tables with references: {tables_with_references}/{len(tables)}")
    print(f"Total table placements: {total_tables_referenced}")
    
    for table_id, chunk_indices in table_references.items():
        if chunk_indices:
            print(f"Table {table_id} will be inserted in chunks: {chunk_indices}")
        else:
            print(f"WARNING: Table {table_id} has no references - this shouldn't happen!")

    print("\n=== Inserting tables into chunks ===")
    final_chunks = await insert_tables_into_chunks_async(chunks, table_summaries, table_references)
    
    print(f"Final chunks count: {len(final_chunks)}")
    
    print("\n=== Validation ===")
    remaining_placeholders = 0
    for i, chunk in enumerate(final_chunks):
        chunk_placeholders = re.findall(placeholder_pattern, chunk.text)
        if chunk_placeholders:
            print(f"WARNING: Chunk {i} still contains unreplaced placeholders: {chunk_placeholders}")
            remaining_placeholders += len(chunk_placeholders)
        print(f"  Final Chunk {i}: {len(chunk.text)} characters")
    
    if remaining_placeholders == 0:
        print("✓ All table placeholders successfully replaced")
    else:
        print(f"✗ {remaining_placeholders} placeholders remain unreplaced")

    # Save the chunks
    if output_dir is None:
        chunks_dir = os.path.join(os.path.dirname(txt_file_path), "chunks_with_tables")
    else:
        chunks_dir = os.path.join(output_dir, "chunks_with_tables")
    
    os.makedirs(chunks_dir, exist_ok=True)
    
    for i, chunk in enumerate(final_chunks):
        with open(os.path.join(chunks_dir, f"chunk_{i}.txt"), "w", encoding='utf-8') as f:
            f.write(chunk.text)

    print(f"\n=== Summary ===")
    print(f"Saved {len(final_chunks)} chunks to '{chunks_dir}' directory")
    print(f"Tables loaded: {len(tables)}")
    print(f"Table summaries created: {len(table_summaries)}")
    print(f"Average chunk length: {sum(len(chunk.text) for chunk in final_chunks) / len(final_chunks):.0f} characters")
    
    return True

def main(txt_file_path, chunker_type="neural", output_dir=None):
    """
    Synchronous wrapper for the main async function.
    """
    return asyncio.run(main_async(txt_file_path, chunker_type, output_dir))

if __name__ == "__main__":
    ## Usage: Neural Chunker performing best
    # python chonkie_test.py "dev reports/Stability Section - Developmental Report.txt" --output-dir chunks
    parser = argparse.ArgumentParser(
        description="Chunk text files with Chonkie and reinsert extracted tables",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python chonkie_test.py "document.txt"
    python chonkie_test.py "document.txt" --chunker recursive
    python chonkie_test.py "document.txt" --chunker slumber --output-dir "./chunks"
    python chonkie_test.py "document.txt" --chunker neural --output-dir "./output"
        """
    )
    
    parser.add_argument(
        'input_file',
        help='Path to the input text file (should contain table placeholders)'
    )
    
    parser.add_argument(
        '--chunker',
        choices=['neural', 'recursive', 'slumber'],
        default='neural',
        help='Type of chunker to use (default: neural)'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Directory to save chunks (default: same as input file directory)'
    )
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found.")
        sys.exit(1)
    
    if not args.input_file.lower().endswith('.txt'):
        print(f"Warning: Input file '{args.input_file}' is not a .txt file.")
        print("Proceeding anyway...")
    
    # Run the chunking process
    print("=" * 60)
    print("CHONKIE TEXT CHUNKING WITH TABLE REINSERTION")
    print("=" * 60)
    print(f"Input file: {args.input_file}")
    print(f"Chunker type: {args.chunker}")
    print(f"Output directory: {args.output_dir or 'same as input file'}")
    print()
    
    success = main(
        txt_file_path=args.input_file,
        chunker_type=args.chunker,
        output_dir=args.output_dir
    )
    
    if success:
        print("\n" + "=" * 60)
        print("✓ CHUNKING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("✗ CHUNKING FAILED!")
        print("=" * 60)
        sys.exit(1)